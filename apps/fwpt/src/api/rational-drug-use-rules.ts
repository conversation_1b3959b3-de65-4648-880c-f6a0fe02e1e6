import { defHttp } from '@ft/request';

export interface DrugUseRule {
  /**成人剂量*/
  adultDosage: number;
  /**成人频次*/
  adultFrequency: number;
  /**成人用药时间最大*/
  adultMaxDuration: number;
  /**成人用法CODE*/
  adultUsageCode: string;
  /**成人用法描述*/
  adultUsageDesc: string;
  /**儿童剂量*/
  childDosage: number;
  /**儿童频次*/
  childFrequency: number;
  /**儿童用药时间最大(天)*/
  childMaxDuration: number;
  /**儿童用法CODE*/
  childUsageCode: string;
  /**儿童用法描述*/
  childUsageDesc: string;
  /**剂型CODE*/
  dosageFormCode: string;
  /**剂型描述*/
  dosageFormDesc: string;
  /**药物类别CODE*/
  drugCategoryCode: string;
  /**药物类别描述*/
  drugCategoryDesc: string;
  /**药品CODE*/
  drugCode: string;
  /**药品ID*/
  drugId: string;
  /**主键*/
  id: string;
  /**适应症*/
  indications: string;
  /**主要成分*/
  mainIngredients: string;
  /**副作用-常见*/
  sideEffectsCommon: string;
  /**副作用-罕见但严重*/
  sideEffectsRareSerious: string;
  /**副作用-少见*/
  sideEffectsUncommon: string;
}

/**
 * 保存药品合理用药规则
 * /medicationRule/addOrEdit
 */
export function addOrEditMedicationRule(data: Partial<DrugUseRule>) {
  return defHttp.post({
    url: '/medicationRule/addOrEdit',
    data: data,
  });
}

/**
 * 根据药品ID查询详情 GET /medicationRule/detail/{drugId}
 */
export function getMedicationRuleDetail(drugId: string) {
  return defHttp.get<DrugUseRule>({
    url: `/medicationRule/detail/${drugId}`,
  });
}

interface ValidationInput {
  /**年龄*/
  age: number;
  /**校验类型 1 剂量 2 频次 3 天数 4 用法*/
  checkType: number;
  /**药品ID*/
  drugId: string;
  /**校验输入值*/
  inputValue: string | number;
}

/**
 * 根据药品ID校验值 /medicationRule/checkByDrugId
 */
export function checkByDrugId(data: ValidationInput) {
  return defHttp.post({
    url: '/medicationRule/checkByDrugId',
    data: data,
  });
}

/**
 * 根据药品ID查询副作用(无需远程调用查询字典) GET /medicationRule/simpleDetail/{drugId}
 */
export function getSimpleDetail(drugId: string) {
  return defHttp.get<DrugUseRule>({
    url: `/medicationRule/simpleDetail/${drugId}`,
  });
}
