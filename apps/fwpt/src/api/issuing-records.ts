import { defHttp } from '@ft/request';
import { UPLOAD_URL } from './template-mg';

/** 分页查询 */
export const getPage = (data) =>
  defHttp.post({
    url: '/relieveIsolateProveRecord/page',
    data,
  });

/** 导出 */
export const doExport = (data) =>
  defHttp.post(
    {
      url: '/relieveIsolateProveRecord/export',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );

/** 查询详情 */
export const getDetail = (id: string) =>
  defHttp.post({
    url: '/relieveIsolateProveRecord/detail/' + id,
  });

/** 审核通过 */
export const examinePass = (data) =>
  defHttp.post({
    url: '/relieveIsolateProveRecord/examinePass?id=',
    data,
  });

/** 生成证明 */
export const buildCert = (data) =>
  defHttp.post({
    url: '/relieveIsolateProveRecord/doctor/buildPdf',
    data,
  });

/** 医院端-开具证明 */
export const issueProve = (data) =>
  defHttp.post({
    url: '/relieveIsolateProveRecord/doctor/issueProve',
    data,
  });
/** 医院端-编辑证明 */
export const editProve = (data) =>
  defHttp.post({
    url: '/relieveIsolateProveRecord/doctor/editProve',
    data,
  });
/** 医院端-删除证明 */
export const deleteProve = (id: string) =>
  defHttp.post({
    url: `/relieveIsolateProveRecord/doctor/deleteProve/${id}`,
  });
export const upload = (file) =>
  defHttp
    .uploadFile(
      {
        url: UPLOAD_URL,
      },
      {
        file,
      },
    )
    .then((res) => {
      if (parseInt(res.data.code) === 0) {
        return res.data.data;
      } else {
        throw new Error(res?.data?.data || res.data?.msg || res.data?.message);
      }
    });

/** 获取统计表头 */
export const getStatisticTableHeader = () =>
  defHttp.post({
    url: '/relieveIsolateProveRecord/getStatisticTableHeader',
  });

/** 获取统计数据 */
export const getStatisticData = (data: any) =>
  defHttp.post({
    url: '/relieveIsolateProveRecord/getStatisticData',
    data,
  });

/** 导出excel */
export const exportExcel = (data) =>
  defHttp.post(
    {
      url: '/relieveIsolateProveRecord/exportExcel',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );

/** 验证签名 */
export const checkSign = () =>
  defHttp.post({
    url: '/relieveIsolateProveRecord/checkSign',
  });

/** 撤销 */
export const revokeRecord = (id: string) =>
  defHttp.post({
    url: '/relieveIsolateProveRecord/revoke/' + id,
  });

/** 移动端申请列表 */
export const getApplyList = (data) =>
  defHttp.post({
    url: '/relieveIsolateProveApplyRecord/page',
    data,
  });

/** 移动端申请-驳回 */
export const rejectApply = (data) =>
  defHttp.post({
    url: '/relieveIsolateProveApplyRecord/reject',
    data,
  });

/** 移动端申请-详情 */
export const getApplyDetail = (id: string) =>
  defHttp.get({
    url: '/relieveIsolateProveApplyRecord/detail',
    params: {
      id,
    },
  });

export function verifySignData(data: any) {
  return defHttp.post({ url: '/relieveIsolateProveRecord/verifySignData', data });
}

/** 移动端申请-生成证明 */
export const buildApplyCert = (data) =>
  defHttp.post({
    url: '/relieveIsolateProveApplyRecord/issueProve',
    data,
  });

export function saveUserOperationLog(data: any) {
  return defHttp.post({ url: '/userOperationLog/save', data });
}
