import { defHttp } from '@ft/request';
/**
 * @description 分页查询宣教栏目
 * /educationColumn/page
 */
export interface EducationColumnItem {
  id: string;
  columnName: string;
  columnRemark: string;
  columnStatus: number;
  deleteFlag: number;
  createUser?: any;
  createTime: string;
  updateUser?: any;
  updateTime: string;
}

export const queryEducationColumnPage = (data: any) => {
  return defHttp.post<EducationColumnItem[]>({
    url: '/educationColumn/page',
    data,
  });
};
/**
 * @description 新增宣教栏目
 * /educationColumn/add
 */
export const addEducationColumn = (data: any) => {
  return defHttp.post({
    url: '/educationColumn/add',
    data,
  });
};
/**
 * @description 修改宣教栏目
 * /educationColumn/update
 */
export const updateEducationColumn = (data: any) => {
  return defHttp.post({
    url: '/educationColumn/update',
    data,
  });
};
/**
 * @description 删除宣教栏目
 * /educationColumn/delete
 */
export const deleteEducationColumn = (id: string) => {
  return defHttp.post(
    {
      url: '/educationColumn/delete',
      params: {
        id,
      },
    },
    {
      joinParamsToUrl: true,
    },
  );
};
/**
 * @description 查询宣教栏目列表
 * /educationColumn/list
 */
export const queryEducationColumnList = (data: any) => {
  return defHttp.post({
    url: '/educationColumn/list',
    data,
  });
};
