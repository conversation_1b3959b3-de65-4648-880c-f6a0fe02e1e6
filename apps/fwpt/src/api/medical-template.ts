import { defHttp } from '@ft/request';

export interface IEmrTemplatePage {
  applyDiagnosticCode: string;
  applyDiagnosticName: string;
  content: string;
  id: string;
  ownerType: number;
  ownerTypeCode: string;
  tname: string;
}

/**
 * @description 病历模板-分页查询
 * /emrTemplate/queryPage
 */
export const getEmrTemplatePage = (data?: any) => {
  return defHttp.post<IEmrTemplatePage[]>({
    url: '/emrTemplate/queryPage',
    data,
  });
};

/**
 * @description 病历模板-删除
 * /emrTemplate/delete/{id}
 */
export const deleteEmrTemplate = (id: string) => {
  return defHttp.delete({
    url: `/emrTemplate/delete/${id}`,
  });
};

/**
 * @description 病历模板-编辑新增
 * /emrTemplate/saveOrUpdate
 */
export const saveOrUpdateEmrTemplate = (data?: any) => {
  return defHttp.post({
    url: '/emrTemplate/saveOrUpdate',
    data,
  });
};
