import { defHttp } from '@ft/request';
import type { IFollowTemplate } from './follow-template';

export interface IFollowVisitRecordPost {
  /** 现住址 */
  address: string;
  /** 年龄 */
  age: number;
  /** 联系人1电话 */
  contactOnePhone: string;
  /** 联系人2电话 */
  contactTwoPhone: string;
  /** 诊断结果名称(艾滋病等) */
  diagnosticResults: string;
  /** 出院日期 */
  dischargeDate: any;
  /** 主键ID */
  id: string;
  /** 身份证号 */
  idCardNo: string;
  /** 住院号 */
  inId: string;
  /** 传染病病种类型 字典:【INFECTIOUS_DISEASE】 1:病毒性肝炎 2:艾滋病 3:结核病 */
  infectionDisease: number;
  /** 下次随访时间 */
  lastVisitTime: any;
  /** 门诊/急诊住院号; */
  outInId: string;
  /** 患者编号ID */
  patientId: string;
  /** 患者姓名 */
  patientName: string;
  /** 个案登记号 */
  recordNo: string;
  /** 性别 */
  sexName: string;
  /** 随访内容数据集合 */
  visitContentDataList: VisitDataList[];
  /** 就诊科室名称 */
  visitDeptName: string;
  /** 随访对象数据集合 */
  visitObjectDataList: VisitDataList[];
  /** 就诊机构名称 */
  visitOrgName: string;
  /** 随访关闭原因 */
  visitReason: string;
  /** 随访状态：0 待随访，1 已随访，2 随访关闭 */
  visitStatus: number;
  /** 随访任务ID */
  visitTaskId: string;
  /** 随访任务名称 */
  visitTaskName: string;
  /** 随访模板ID */
  visitTemplateId: string;
  /** 随访模板名称 */
  visitTemplateName: string;
  /** 随访日期 */
  visitTime: any;
  /** 随访人 */
  visitUser: string;
  /** 随访方式：1 电话，2 家庭，3 门诊 */
  visitWay: number;
  /** 住院号 */
  admissionNumber: string;
  /** 出生日期 */
  birthDay: any;
  /** 随访类型：1 就诊记录 2 随访任务 */
  visitType: number;
}

export interface VisitDataList {
  dataKey: string;
  dataName: string;
  dataValue: string;
  id: string;
  templateDataId: string;
  visitRecordId: string;
  visitTemplateId: string;
  /** 数据模块：1 随访对象，2 随访内容 */
  dataModel: number;
}

export interface IFollowVisitRecord
  extends Omit<IFollowVisitRecordPost, 'visitContentDataList' | 'visitObjectDataList'> {
  age: number;
  /** 诊断结果名称(艾滋病等) */
  diagnosticResults: string;
  id: string;
  /** 身份证号 */
  idCardNo: string;
  /** 性别 */
  sex: string;
  updateTime: string;
  updateUser: string;
  /** 就诊科室名称 */
  visitDeptName: string;
  /** 就诊机构名称 */
  visitOrgName: string;
  /** 就诊流水号 */
  visitSerialNo: string;
}

/**
 * 随访记录服务-分页查询随访记录列表
 */
export function getFollowVisitPage(data = {}) {
  return defHttp.post({ url: `/followVisitRecord/queryPage`, data });
}

export interface CloseFollowVisitParams {
  id: string;
  lastVisitTime: string;
  visitReason: string;
  visitStatus: number;
  visitUser: string;
}
/**
 * 随访记录服务-召回
 * followVisitRecord/backUp
 */
export function backUpFollowVisit(data) {
  return defHttp.post({ url: `/followVisitRecord/backUp`, data });
}

/**
 * 随访记录服务-随访关闭
 */
export function closeFollowVisit(data: CloseFollowVisitParams) {
  return defHttp.post({ url: `/followVisitRecord/closeRecord`, data });
}

/**
 * 随访记录服务-随访登记生成随访记录
 */
export function saveFollowVisitRecord(data: DeepPartial<IFollowVisitRecordPost>) {
  return defHttp.post({ url: `/followVisitRecord/save`, data });
}

/**
 * 随访记录服务-随访登记修改
 */
export function updateFollowVisitRecord(data: DeepPartial<IFollowVisitRecordPost>) {
  return defHttp.post({ url: `/followVisitRecord/update`, data });
}
/**
 * 随访记录服务-查询患者既往随访模板列表
 */
export function getFollowVisitTemplateList(patientId: string) {
  return defHttp.get<IFollowTemplate[]>({
    url: `/followVisitRecord/queryTemplateList?patientId=${patientId}`,
  });
}

/**
 * 随访记录服务-查询患者既往随访记录详情
 */
export function getFollowVisitRecordExistList(
  patientId: string,
  visitTemplateId: string,
  visitType: number,
) {
  return defHttp.get<IFollowVisitRecordPost[]>({
    url: `/followVisitRecord/queryExistList?patientId=${patientId}&visitTemplateId=${visitTemplateId}&visitType=${visitType}`,
  });
}

/**
 * 随访记录服务-查询随访记录详情
 */
export function getFollowVisitRecordDetail(id: string) {
  return defHttp.get<IFollowVisitRecordPost>({ url: `/followVisitRecord/queryDetail?id=${id}` });
}

export interface IMedicationRecord {
  /** 天数*/
  days: string;
  /** 诊断*/
  diagnoseName: string;
  /** 医生*/
  doctorName: string;
  /** 药品名*/
  drugName: string;
  /** 规格*/
  drugSpec: string;
  /**  用法 */
  drugUsage: string;
  /** 频次*/
  drugUseDrequency: string;
  /** 用量*/
  drugUsed: string;
  /** 用量单位 */
  douseUnit: string;
  /** 就诊机构*/
  hospitalOrg: string;
  /** 医嘱执行日期时间;*/
  medicOrdersExcuteDate: any;
  /** 医嘱项目名称;*/
  medicOrdersItem: string;
  /** 医嘱项目代码;*/
  medicOrdersItemCode: string;
  /** 医嘱项目类型名称-检查类、检验类等;*/
  medicOrdersItemType: string;
  /** 开嘱日期时间;*/
  medicOrdersStartDate: any;
  /** 医嘱停止日期时间;*/
  medicOrdersStopDate: any;
  /** 医嘱类别名称-临时、长期等;*/
  medicOrdersType: string;
  /** 0-临时、1-长期*/
  medicOrdersTypeCode: string;
  /** 护士*/
  nurseName: string;
  /** 单价*/
  price: string;
  /** 金额*/
  totalPrice: string;
  /** 总量*/
  totalSize: string;
  /** 就诊科室*/
  visitDept: string;
}

/**
 * 随访记录服务-查询患者用药记录
 */
export function queryMedicationRecordList(inId: string) {
  return defHttp.get<IMedicationRecord[]>({
    url: `/followVisitRecord/queryMedicationRecordList`,
    params: { inId },
  });
}
/**
 * 随访记录服务-查询异常检查项目
 */
export interface IInspectionIndex {
  itemName: string;
  exceptionIndexList: ExceptionIndexList[];
}

export interface ExceptionIndexList {
  indexName: string;
  exceptionFlag: string;
}
export function queryInspectionIndex(inId: string) {
  return defHttp.get<IInspectionIndex[]>({
    url: `/followVisitRecord/queryInspectionIndex`,
    params: { inId },
  });
}

/**
 * 随访记录服务-自动生成随访记录
 */
export function autoGenerate() {
  return defHttp.post({ url: `/followVisitRecord/autoGenerate` });
}

export interface IVisitCount {
  /** 科室随访工作量 */
  deptCount: IDetailCount;
  /** 全院随访任务汇总 */
  hospitalCount: IDetailCount;
  /** 今日个人随访工作量 */
  oneselfCount: IDetailCount;
}

export interface IDetailCount {
  /** 今日已随访数量 */
  existVisitNum: number;
  /** 历史已随访数量 */
  historyVisitNum: number;
  /** 今日任务 */
  todayTaskNum: number;
  /** 历史总任务 */
  totalTaskNum: number;
  /** 随访召回总人数  */
  visitBackNum: number;
  /** 随访未成功数量 */
  visitFailNum: number;
  /** 今日待随访数量 */
  waitVisitNum: number;
}

/**
 * 随访记录服务-查询随访任务统计
 */
export function queryVisitCount() {
  return defHttp.get<IVisitCount>({ url: `/followVisitRecord/queryVisitCount` });
}

/**
 * 随访工作台-随访科室列表查询下拉框
 * /followVisitRecord/queryFollowDeptList
 */
export function queryFollowDeptList() {
  return defHttp.get({ url: `/followVisitRecord/queryFollowDeptList` });
}

/**
 * @description 区域传染病患者跟踪管理-医护端 - 工作台-导出慢病患者列表
 * /followVisitRecord/exportExcel
 */
export function followVisitRecordExportExcel(data: any) {
  return defHttp.post(
    {
      url: '/followVisitRecord/exportExcel',
      responseType: 'blob',
      data,
    },
    { isReturnNativeResponse: true },
  );
}

/**
 * 随访提醒
 * /followVisitRecord/followReminders
 *
 */
export function getFollowReminders() {
  return defHttp.get({ url: `/followVisitRecord/followReminders` });
}

/**
 * 查询本人工作台配置
 * /visitConfig/query
 */
export function getMyWorkbenchConfig() {
  return defHttp.get({ url: `/visitConfig/query` });
}

export interface WorkbenchConfigPost {
  /** 用户ID */
  userId: string;
  /** 配置内容 */
  configContent: string;
}
/**
 * 保存本人工作台配置
 * /visitConfig/save
 */
export function saveWorkbenchConfig(data: WorkbenchConfigPost) {
  return defHttp.post({ url: `/visitConfig/save`, data });
}
