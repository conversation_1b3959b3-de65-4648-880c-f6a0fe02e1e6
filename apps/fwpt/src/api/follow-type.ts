import { defHttp } from '@ft/request';

export interface VisitTypeReq {
  status: number;
  visitTypeName: string;
}

/**
 * 分页查询随访类型列表信息
 * /visitType/queryPage
 */
export const getVisitTypePage = (data: VisitTypeReq) => {
  return defHttp.post({
    url: '/visitType/queryPage',
    data,
  });
};

/**
 * 查询随访类型列表信息
 * /visitType/queryList
 *
 */
export const getVisitTypeList = (data?: Partial<VisitTypeReq>) => {
  return defHttp.post<IVisitType[]>({
    url: '/visitType/queryList',
    data,
  });
};

export interface IVisitType {
  id: string;
  remark: string;
  scopeCode: string;
  scopeName: string;
  status: number;
  visitTypeName: string;
  updateTime: string;
  updateUser: string;
}

/**
 * 保存随访类型信息
 * /visitType/save
 */
export const saveVisitType = (data: Omit<IVisitType, 'id'>) => {
  return defHttp.post({
    url: '/visitType/save',
    data,
  });
};

/**
 * 修改随访类型信息
 * /visitType/update
 */
export const updateVisitType = (data: Partial<IVisitType>) => {
  return defHttp.post({
    url: '/visitType/update',
    data,
  });
};

/**
 * 获取随访类型详情
 * /visitType/detail
 */
export const getVisitTypeDetail = (id: string) => {
  return defHttp.get({
    url: '/visitType/detail',
    params: {
      id,
    },
  });
};

/**
 * 删除随访类型信息
 * /visitType/delete
 */
export const deleteVisitType = (id: string) => {
  return defHttp.post({
    url: '/visitType/delete?id=' + id,
  });
};
