import { defHttp } from '@ft/request';

interface FollowVisitPlanReq {
  planName: string;
  status: number;
}

export interface IFollowVisitPlan {
  createTime: string;
  createUser: string;
  deptScope: string;
  deptScopeName: string;
  id: string;
  planName: string;
  remark: string;
  scopeName: string;
  status: number;
  updateTime: string;
  updateUser: string;
  visitTaskName: string;
  visitTypeId: string;
  visitTypeName: string;
}

/**
 *  分页查询随访计划列表信息
 * /followVisitPlan/queryPage
 */
export function getFollowVisitPlanPage(data?: Partial<FollowVisitPlanReq>) {
  return defHttp.post({
    url: '/followVisitPlan/queryPage',
    data,
  });
}

/**
 * 查询随访计划详情
 * /followVisitPlan/detail
 */

export function getFollowVisitPlanDetail(id: string) {
  return defHttp.get<IFollowVisitPlan>({
    url: '/followVisitPlan/detail?id=' + id,
  });
}

/**
 * 保存随访计划
 * /followVisitPlan/save
 */

export function saveFollowVisitPlan(data: Partial<IFollowVisitPlan>) {
  return defHttp.post({
    url: '/followVisitPlan/save',
    data,
  });
}

/**
 * 修改随访计划
 * /followVisitPlan/update
 */

export function updateFollowVisitPlan(data: Partial<IFollowVisitPlan>) {
  return defHttp.post({
    url: '/followVisitPlan/update',
    data,
  });
}

/**
 * 删除随访计划
 * /followVisitPlan/delete
 */

export function deleteFollowVisitPlan(id: string) {
  return defHttp.post({
    url: '/followVisitPlan/delete?id=' + id,
  });
}

export interface IFollowVisitTask {
  createTime: string;
  createUser: string;
  id: string;
  lastDay: number;
  loopDay: number;
  loopNum: number;
  taskFrequency: number;
  taskName: string;
  taskObject: number;
  taskStatus: number;
  updateTime: string;
  updateUser: string;
  visitPlanId: string;
  visitTemplateId: string;
}

/**
 * 查询随访任务列表
 * /followVisitTask/queryList
 */
export function getFollowVisitTaskList(data?: any) {
  return defHttp.post({
    url: '/followVisitTask/queryList',
    data,
  });
}

/**
 * 查询随访任务详情
 * /followVisitTask/detail
 */
export function getFollowVisitTaskDetail(id: string) {
  return defHttp.get({
    url: '/followVisitTask/detail?id=' + id,
  });
}

/**
 * 保存随访任务
 * /followVisitTask/save
 */
export function saveFollowVisitTask(data: Partial<IFollowVisitTask>) {
  return defHttp.post({
    url: '/followVisitTask/save',
    data,
  });
}

/**
 * 修改随访任务
 * /followVisitTask/update
 */
export function updateFollowVisitTask(data: Partial<IFollowVisitTask>) {
  return defHttp.post({
    url: '/followVisitTask/update',
    data,
  });
}

/**
 * 删除随访任务
 * /followVisitTask/delete
 */
export function deleteFollowVisitTask(id: string) {
  return defHttp.post({
    url: '/followVisitTask/delete?id=' + id,
  });
}

export interface IFollowVisitTaskPatient {
  /**身份证号*/
  idCardNo: string;
  /**患者姓名*/
  patientName: string;
  /**手机号*/
  phone: string;
  /**用户ID*/
  userId: string;
}
/**
 * 获取随访任务患者列表
 * /followVisitTask/queryPatientList
 */
export function getFollowVisitTaskPatientList(data?: { patientName: string }) {
  return defHttp.post<IFollowVisitTaskPatient[]>({
    url: '/followVisitTask/queryPatientList',
    data,
  });
}
