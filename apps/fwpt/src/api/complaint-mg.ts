import { defHttp } from '@ft/request';

export interface IComplaintRecord {
  complainant: string;
  complainantContact: string;
  complaintChannel: number;
  complaintDate: string;
  complaintDept: string;
  complaintDeptCode: string;
  complaintPersonnel: string;
  complaintPersonnelContact: string;
  complaintPersonnelNumber: string;
  complaintPersonnelStatus: number;
  complaintPersonnelTitle: string;
  complaintReason: string;
  complaintStatus: number;
  createUser: string;
  id: string;
  mainReason: string;
}

/**
 * @description 投诉管理-投诉记录分页查询
 * /complaintRecord/page
 */
export const getComplaintRecordPage = (data?: any) => {
  return defHttp.post({
    url: '/complaintRecord/page',
    data,
  });
};

/**
 * @description 投诉登记
 * /complaintRecord/registration
 */
export const addComplaintRecord = (data?: Partial<IComplaintRecord>) => {
  return defHttp.post({
    url: '/complaintRecord/registration',
    data,
  });
};

/**
 * @description 投诉管理-投诉记录详情
 * /complaintRecord/{id}
 */
export const getComplaintRecordDetail = (id: string) => {
  return defHttp.get({
    url: `/complaintRecord/${id}`,
  });
};

/**
 * @description 投诉管理-撤销投诉
 * /complaintRecord/cancel/{id}
 */
export const cancelComplaintRecord = (id: string) => {
  return defHttp.put({
    url: `/complaintRecord/cancel/${id}`,
  });
};
