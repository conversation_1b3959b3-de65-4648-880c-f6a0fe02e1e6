import { useGlobSetting } from '@ft/internal/hooks/setting';
import { defHttp } from '@ft/request';

type Template = {
  id: string;
  orgIcon: string;
  orgId: string;
  orgName: string;
  templateName: string;
  createTime: string;
  signature: string;
};

export const templateApi = {
  /** 列表查询 */
  list: (data: { templateName?: string; orgId?: string }) =>
    defHttp.post<Template[]>({
      url: '/relieveIsolateProveTemplate/list',
      data,
    }),
  /** 新增 */
  add: (data: Template) =>
    defHttp.post({
      url: '/relieveIsolateProveTemplate/add',
      data,
      timeout: 30 * 1000,
    }),
  /** 删除 */
  delete: (templateId: string) =>
    defHttp.post({
      url: '/relieveIsolateProveTemplate/delete?templateId=' + templateId,
    }),
  /** 编辑 */
  update: (data: Template) =>
    defHttp.post({
      url: '/relieveIsolateProveTemplate/update',
      data,
    }),
};

const { apiUrl, urlPrefix } = useGlobSetting();

export const UPLOAD_URL = `${apiUrl}${urlPrefix}/file/upload`;
