import { useGlobSetting } from '@ft/internal/hooks/setting';
import { defHttp } from '@ft/request';

export interface INurseOrder {
  /**订单地址*/
  address: string;
  /**年龄*/
  age: string;
  /**订单总金额*/
  amount: number;
  /**预约日期及时间段*/
  appointTime: string;
  /**订单编号*/
  code: string;
  /**完成时间*/
  finishTime: string;
  /**主键ID*/
  id: string;
  /**身份证号*/
  idCardNo: string;
  /**组套编码*/
  itemCode: string;
  /**组套名称*/
  itemName: string;
  /**服务分类ID*/
  itemTypeId: string;
  /**服务分类名称*/
  itemTypeName: string;
  /**护理人员ID*/
  nurseId: string;
  /**护理人员姓名*/
  nurseName: string;
  /**护理评估总结*/
  nurseSummary: string;
  /**订单取消对象*/
  orderCancelObject: number;
  /**订单取消原因*/
  orderCancelReason: string;
  /**订单取消时间*/
  orderCancelTime: string;
  /**订单状态*/
  orderStatus: number;
  /**订单生成时间*/
  orderTime: string;
  /**订单所属机构编码*/
  orgCode: string;
  /**订单所属机构名称*/
  orgName: string;
  /**申请人/患者姓名*/
  patientName: string;
  /**缴费时间*/
  paymentTime: string;
  /**电话*/
  phone: string;
  /**拒单原因*/
  rejectReason: string;
  /**用户订单备注*/
  remark: string;
  /**性别*/
  sex: string;
  /**用户ID*/
  userId: string;
  /**上门时间*/
  visitTime: string;
  /**排班管理id */
  classManageId: string;
  /**预约班别ID*/
  mshItemClassId: string;
}

export interface INurseOrderTopIndex {
  /**待分配订单量*/
  distributedAmount: number;
  /**已完成订单量*/
  finishAmount: number;
  /**上门进行中订单量*/
  inProcessAmount: number;
  /**预约护理总订单量*/
  sumAmount: number;
  /**已拒订单量*/
  turnDownAmount: number;
  /**待上门订单量*/
  waitToSeeAmount: number;
  /**护理文书编写订单量*/
  writAmount: number;
}
/**
 * @description: 医服到家 - 护理订单统计
 * /nurseOrder/topIndex
 */
export const getNurseOrderTopIndex = (data?: any) =>
  defHttp.post<INurseOrderTopIndex>({ url: '/nurseOrder/topIndex', data });
/*
 * @description: 医服到家 - 护理订单列表
 * /nurseOrder/pageList
 */
export const getNurseOrderPage = (data?: any) =>
  defHttp.post<INurseOrder[]>({ url: '/nurseOrder/pageList', data });

/**
 * @description: 医服到家 - 指派执行人员
 * /nurseOrder/designate
 */
export const getDesignate = (data?: any) => defHttp.post({ url: '/nurseOrder/designate', data });

export interface INurseOrderDetail {
  /**服务地址*/
  address: string;
  /**年龄*/
  age: string;
  /**服务金额*/
  amount: string;
  /**服务时间*/
  appointTime: string;
  /**订单号*/
  code: string;
  /**病情症状*/
  diseaseSymptom: string;
  /**身份证号*/
  idCardNo: string;
  /**服务项目*/
  itemName: string;
  /**服务包信息-耗材包*/
  medicalConsumableList: MedicalConsumable[];
  /**服务包信息-护理项目*/
  nurseItemList: NurseItem[];
  nurseOrder: INurseOrder;
  /**订单ID*/
  orderId: string;
  /**患者住址*/
  patientAddress: string;
  /**服务对象姓名*/
  patientName: string;
  /**联系电话*/
  phone: string;
  /**服务备注*/
  remark: string;
  /**性别*/
  sex: string;
  /**病情图片集合*/
  symptomPictureList: string[];
  /**订单追踪状态 */
  status: number;
}
export interface MedicalConsumable {
  /**数量*/
  amount: number;
  /**护理项目/耗材名称*/
  itemName: string;
  /**单价*/
  unitPrice: number;
}
export interface NurseItem {
  /**数量*/
  amount: number;
  /**护理项目/耗材名称*/
  itemName: string;
  /**单价*/
  unitPrice: number;
}
/**
 * @description: 医服到家 - 订单详情
 * /nurseOrder/getDetailById/{id}
 */
export const getNurseOrderDetailById = (id: string) =>
  defHttp.get<INurseOrderDetail>({ url: `/nurseOrder/getDetailById/${id}` });

/**
 * @description: 医服到家 - 确认拒单
 * /nurseOrder/reject
 */
export const getReject = (data?: any) => defHttp.post({ url: '/nurseOrder/reject', data });

/**
 * @description: 医服到家 - 确认接单
 * /nurseOrder/take
 */
export const getTake = (id?: any) => defHttp.get({ url: `/nurseOrder/take/${id}` });

/**
 * @description: 医服到家 - 列表导出
 * /nurseOrder/export
 */
export function nurseOrderExport(data?: any) {
  return defHttp.post(
    {
      url: '/nurseOrder/export',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );
}

/**
 * @description: 医服到家 - 查看护理文书内容
 * nurseOrder/viewNurseDocument/{orderId}
 * visitContentDataList	随访内容数据集合	array
 * visitObjectDataList	随访对象数据集合
 */
export const viewNurseDocument = (orderId: string) =>
  defHttp.get({ url: `/nurseOrder/viewNurseDocument/${orderId}` });

/**
 * @description: 医服到家 - 查询护士科室列表
 * /mshNurseClass/getNurseListByClass
 */
export const getNurseListByClass = (data?: any) =>
  defHttp.post({ url: '/mshNurseClass/getNurseListByClass', data });

/**
 * @description: 医服到家 - 消息提醒
 * /nurseOrder/sseConnect
 */
const { apiUrl, urlPrefix } = useGlobSetting();

export const NurseOrderSSEConnect = `${apiUrl}${urlPrefix}/nurseOrder/sseConnect`;
