import { defHttp } from '@ft/request';

export interface NursingEvaluateTemplate {
  /**启用状态*/
  enableStatus: string;
  /**评价等级*/
  evaluationLevel: string;
  /**主键*/
  id: string;
  /**满意程度*/
  satisfactionLevel: string;
  /**满意度评分*/
  satisfactionScore: number;
  /**标签1*/
  tag1: string;
  /**标签2*/
  tag2: string;
  /**标签3*/
  tag3: string;
  /**标签4*/
  tag4: string;
  /**标签5*/
  tag5: string;
  /**标签6*/
  tag6: string;
}

/**
 * 分页查询
 * /nursingServiceEvaluateTemplate/page
 */
export const getNursingEvaluateTemplatePage = (data: {
  enableStatus?: number;
  evaluationLevel?: number;
  pageNum?: number;
  pageSize?: number;
}) =>
  defHttp.post<NursingEvaluateTemplate[]>({
    url: '/nursingServiceEvaluateTemplate/page',
    data,
  });

/**
 * 医服到家-护理服务评价模板
 * /nursingServiceEvaluateTemplate/save
 */
export const saveNursingEvaluateTemplate = (data: NursingEvaluateTemplate) =>
  defHttp.post<any>({
    url: '/nursingServiceEvaluateTemplate/save',
    data,
  });

/**
 * 更新
 * /nursingServiceEvaluateTemplate/update
 */
export const updateNursingEvaluateTemplate = (data: NursingEvaluateTemplate) =>
  defHttp.post<any>({
    url: '/nursingServiceEvaluateTemplate/update',
    data,
  });

/**
 * 查看详情
 * GET
 * /nursingServiceEvaluateTemplate/detail
 */
export const getNursingEvaluateTemplateDetail = (id: string) =>
  defHttp.get<NursingEvaluateTemplate>({
    url: `/nursingServiceEvaluateTemplate/detail`,
    params: { id },
  });

/**
 * 删除
 * /nursingServiceEvaluateTemplate/delete
 */
export const deleteNursingEvaluateTemplate = (id: string) =>
  defHttp.delete<any>({
    url: `/nursingServiceEvaluateTemplate/delete?id=${id}`,
  });
