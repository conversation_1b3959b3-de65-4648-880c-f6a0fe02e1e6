import { defHttp } from '@ft/request';

export interface IClassPage {
  /**
   * 班别名称
   */
  className: string;

  /**
   * 班别起止时间，格式为 hh:mm-hh:mm
   * 例如: "08:00-16:00"
   */
  classTime: string;

  /**
   * 护理分类编码
   */
  classTypeCode: string;

  /**
   * 护理分类编码名称
   */
  classTypeCodeName: string;

  /**
   * 创建时间，通常为 ISO 8601 格式的字符串
   * 例如: "2023-01-01T08:00:00Z"
   */
  createTime: string;

  /**
   * 创建用户的用户名或ID
   */
  createUser: string;

  /**
   * 主键，唯一标识符
   */
  id: string;

  /**
   * 机构ID，用于标识所属机构
   */
  orgId: string;

  /**
   * 机构名称
   */
  orgName: string;

  /**
   * 班别状态
   * 1 - 启用
   * 0 - 禁用
   */
  status: 0 | 1;

  /**
   * 修改时间，通常为 ISO 8601 格式的字符串
   * 例如: "2023-01-01T08:00:00Z"
   */
  updateTime: string;

  /**
   * 修改用户的用户名或ID
   */
  updateUser: string;
}

/**
 * 班别管理 获取分页
 */
export const getClassPage = (data) =>
  defHttp.post<IClassPage[]>({
    url: '/mshClassManage/getPage',
    data,
  });

/**
 * 班别管理 获取不分页
 */
export const getClassList = (data) =>
  defHttp.post<IClassPage[]>({
    url: `/mshClassManage/getList`,
    data,
  });

/**
 * 新增
 */
export const addClass = (data) =>
  defHttp.post({
    url: '/mshClassManage/save',
    data,
  });

/**
 * 编辑
 */
export const editClass = (data) =>
  defHttp.post({
    url: '/mshClassManage/edit',
    data,
  });

/**
 * 修改状态
 */
export const editClassStatus = (id: string) =>
  defHttp.post({
    url: `/mshClassManage/editStatus/${id}`,
  });
/**
 * 删除
 */
export const deleteClass = (id: string) =>
  defHttp.post({
    url: `/mshClassManage/delete/${id}`,
  });

/**
 * 获取护士排班列表
 * /mshNurseClass/getList
 */
export const getNurseClassList = (data) =>
  defHttp.post({
    url: '/mshNurseClass/getList',
    data,
  });

/**
 * 获取人员排班列表审核状态
 * /mshNurseClass/getAuditStatus
 */
export const getNurseAuditStatus = (data) =>
  defHttp.post({
    url: '/mshNurseClass/getAuditStatus',
    data,
  });

/**
 * 更新护士排班
 * /mshNurseClass/saveOrUpdate
 */
export const saveOrUpdateNurseClass = (data) =>
  defHttp.post({
    url: '/mshNurseClass/saveOrUpdate',
    data,
  });

/**
 * 获取项目排班列表审核状态
 * /mshItemGroupClass/getAuditStatus
 */
export const getProjectAuditStatus = (data) =>
  defHttp.post({
    url: '/mshItemGroupClass/getAuditStatus',
    data,
  });

/**
 * 获取项目组排班列表
 * /mshItemGroupClass/getList
 */
export const getProjectClassList = (data) =>
  defHttp.post({
    url: '/mshItemGroupClass/getList',
    data,
  });

/**
 * 获取项目组排班列表
 * /mshItemGroupClass/saveOrUpdate
 */
export const saveOrUpdateClass = (data) =>
  defHttp.post({
    url: '/mshItemGroupClass/saveOrUpdate',
    data,
  });

/**
 * 护理组套分页列表
 * /nursingPack/getPack
 */
export const getItemList = (data) =>
  defHttp.post({
    url: '/nursingPack/getPack',
    data,
  });

/**
 * 获取护理分类排班列表
 * /mshClassManageAudit/getList
 */
export const getClassAuditList = (data) =>
  defHttp.post({
    url: '/mshClassManageAudit/getList',
    data,
  });

/**
 * 审核排班
 * /mshClassManageAudit/audit
 */
export const auditClass = (data) =>
  defHttp.post({
    url: '/mshClassManageAudit/audit',
    data,
  });
