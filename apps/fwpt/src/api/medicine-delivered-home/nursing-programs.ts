import { ContentTypeEnum } from '@ft/internal/enums/httpEnum';
import { defHttp } from '@ft/request';
import type { RcFile } from 'ant-design-vue/lib/vc-upload/interface';

/**
 * @description: 医服到家 - 护理项目分页列表
 * /nursingProjectManage/getPageProject
 */

export function getNursingProjectList(data: any) {
  return defHttp.post({
    url: '/nursingProjectManage/getPageProject',
    data,
  });
}

/**
 * @description: 医服到家 - 新增护理项目
 * /nursingProjectManage/addProject
 */
export function addNursingProject(data: any) {
  return defHttp.post({
    url: '/nursingProjectManage/addProject',
    data,
  });
}

/**
 * @description: 医服到家 - 修改护理项目
 * /nursingProjectManage/saveProject/{id}
 */
export function editNursingProject(data: any) {
  return defHttp.post({
    url: `/nursingProjectManage/saveProject/${data.id}`,
    data,
  });
}

/**
 * @description: 医服到家 - 删除护理项目
 * /nursingProjectManage/delete/{id}
 */
export function deleteNursingProject(id: string) {
  return defHttp.delete({
    url: `/nursingProjectManage/delete/${id}`,
  });
}

/**
 * @description: 医服到家 - 护理项目下载模版
 * /nursingProjectManage/templateProject
 */
export function downloadNursingProjectTemplate() {
  return defHttp.post(
    {
      url: '/nursingProjectManage/templateProject',
      responseType: 'blob',
    },
    { isReturnNativeResponse: true },
  );
}

/**
 * @description: 医服到家 - 护理项目批量导入
 * /nursingProjectManage/importProject
 */
export function importNursingProject(data: { file: RcFile }) {
  const formData = new FormData();
  formData.append('file', data.file);
  return defHttp.post({
    url: '/nursingProjectManage/importProject',
    data: formData,
    headers: {
      'Content-Type': ContentTypeEnum.FORM_DATA,
    },
  });
}

/**
 * @description: 医服到家 - 护理项目批量导出
 * /nursingProjectManage/exportProject
 */
export function exportNursingProject(data: any) {
  return defHttp.post(
    {
      url: '/nursingProjectManage/exportProject',
      responseType: 'blob',
      data,
    },
    { isReturnNativeResponse: true },
  );
}

/**
 * @description: 医服到家 - 护理耗材分页列表
 * /nursingProjectManage/getPageConsumables
 */
export function getNursingConsumablesList(data: any) {
  return defHttp.post({
    url: '/nursingProjectManage/getPageConsumables',
    data,
  });
}

/**
 * @description: 医服到家 - 新增护理耗材
 * /nursingProjectManage/addConsumables
 */
export function addNursingConsumables(data: any) {
  return defHttp.post({
    url: '/nursingProjectManage/addConsumables',
    data,
  });
}

/**
 * @description: 医服到家 - 修改护理耗材
 * /nursingProjectManage/saveConsumables/{id}
 */
export function editNursingConsumables(data: any) {
  return defHttp.post({
    url: `/nursingProjectManage/saveConsumables/${data.id}`,
    data,
  });
}

/**
 * @description: 医服到家 - 删除护理耗材
 * /nursingProjectManage/delConsumables/{id}
 */
export function deleteNursingConsumables(id: string) {
  return defHttp.delete({
    url: `/nursingProjectManage/delConsumables/${id}`,
  });
}

/**
 * @description: 医服到家 - 护理耗材下载模版
 * /nursingProjectManage/templateConsumables
 */
export function downloadNursingConsumablesTemplate() {
  return defHttp.post(
    {
      url: '/nursingProjectManage/templateConsumables',
      responseType: 'blob',
    },
    { isReturnNativeResponse: true },
  );
}

/**
 * @description: 医服到家 - 护理耗材批量导入
 * /nursingProjectManage/importConsumables
 */
export function importNursingConsumables(data: { file: RcFile }) {
  const formData = new FormData();
  formData.append('file', data.file);
  return defHttp.post({
    url: '/nursingProjectManage/importConsumables',
    data: formData,
    headers: {
      'Content-Type': ContentTypeEnum.FORM_DATA,
    },
  });
}

/**
 * @description: 医服到家 - 护理耗材批量导出
 * /nursingProjectManage/exportConsumables
 */
export function exportNursingConsumables(data: any) {
  return defHttp.post(
    {
      url: '/nursingProjectManage/exportConsumables',
      responseType: 'blob',
      data,
    },
    { isReturnNativeResponse: true },
  );
}

/**
 * @description: 医服到家 - 护理组套分页列表
 * /nursingPack/getPack
 */
export function getNursingPackList(data: any) {
  return defHttp.post({
    url: '/nursingPack/getPack',
    data,
  });
}

/**
 * @description: 医服到家 - 新增护理组套
 * /nursingPack/addPack
 */
export function addNursingPack(data: any) {
  return defHttp.post({
    url: '/nursingPack/addPack',
    data,
  });
}

/**
 * @description: 医服到家 - 修改护理组套
 * /nursingPack/savePack/{id}
 */
export function editNursingPack(data: any) {
  return defHttp.post({
    url: `/nursingPack/savePack/${data.packId}`,
    data,
  });
}

/**
 * @description: 医服到家 - 删除护理组套
 * /nursingPack/delPack/{id}
 */
export function deleteNursingPack(id: string) {
  return defHttp.delete({
    url: `/nursingPack/delPack/${id}`,
  });
}

/**
 * @description: 医服到家 - 护理组套指定类型分页列表
 * /nursingPack/getPackDetail
 */
export function getNursingPackDetailList(data: any) {
  return defHttp.post({
    url: '/nursingPack/getPackDetail',
    data,
  });
}

/**
 * @description: 医服到家 - 护理组套指定类型细项批量修改
 * /nursingPack/savePackContent
 */
export function batchSavePackContent(data: any) {
  return defHttp.post({
    url: '/nursingPack/savePackContent',
    data,
  });
}

/**
 * @description: 医服到家 - 护理组套修改数量
 * /nursingPack/savePackContentQuantity
 */
export function editPackContentQuantity(data: any) {
  return defHttp.post({
    url: '/nursingPack/savePackContentQuantity',
    data,
  });
}
