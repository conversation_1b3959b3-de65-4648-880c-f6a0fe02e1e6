import { defHttp } from '@ft/request';

// export interface INurseOrderStatisticsPage {}
/*
 * @description: 医服到家 - 护理统计列表
 * /nurseOrder/statisticsPage
 */
export const getNurseOrderStatisticsPage = (data?: any) =>
  defHttp.post({ url: '/nurseOrder/statisticsPage', data });

export interface INurseOrderGoingDown {
  /**订单地址*/
  address: string;
  /**年龄*/
  age: string;
  /**报警时间*/
  alarmTime: string;
  /**订单总金额*/
  amount: number;
  /**预约日期及时间段*/
  appointTime: string;
  /**预约班别ID*/
  classManageId: string;
  /**订单编号*/
  code: string;
  /**护理人员科室编码*/
  deptCode: string;
  /**护理人员科室名称*/
  deptName: string;
  /**完成时间*/
  finishTime: string;
  /**订单id*/
  id: string;
  /**身份证号*/
  idCardNo: string;
  /**组套编码*/
  itemCode: string;
  /**组套名称*/
  itemName: string;
  /**服务分类ID*/
  itemTypeId: string;
  /**服务分类名称*/
  itemTypeName: string;
  /**护理人员ID*/
  nurseId: string;
  /**护理人员姓名*/
  nurseName: string;
  /**护理评估总结*/
  nurseSummary: string;
  /**订单取消对象*/
  orderCancelObject: number;
  /**订单取消原因*/
  orderCancelReason: string;
  /**订单取消时间*/
  orderCancelTime: string;
  /**订单状态*/
  orderStatus: string;
  /**订单状态值*/
  orderstatusValue: string;
  /**订单生成时间*/
  orderTime: string;
  /**订单所属机构编码*/
  orgCode: string;
  /**订单所属机构名称*/
  orgName: string;
  /**申请人/患者姓名*/
  patientName: string;
  /**缴费时间*/
  paymentTime: string;
  /**电话*/
  phone: string;
  /**拒单原因*/
  rejectReason: string;
  /**用户订单备注*/
  remark: string;
  /**性别*/
  sex: string;
  /**用户ID*/
  userId: string;
  /**上门时间*/
  visitTime: string;
}
/**
 * @description: 医服到家 - 护理记录列表
 * /nurseOrder/goingDown
 */
export const getNurseOrderGoingDown = (data?: any) =>
  defHttp.post<INurseOrderGoingDown>({ url: '/nurseOrder/goingDown', data });

/**
 *  @description: 医服到家 - 查询护士科室列表
 * /sysUser/queryNurseDeptList/{orgId}
 */
export const getNurseDeptList = (orgId?: string) =>
  defHttp.get({ url: `/sysUser/queryNurseDeptList/${orgId}` });

/**
 * @description: 医服到家 - 导出-护理记录列表
 * /nurseOrder/exportRecords
 */
export function nurseOrderRecordsExport(data?: any) {
  return defHttp.post(
    {
      url: '/nurseOrder/exportRecords',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );
}
/**
 * @description: 医服到家 - 导出-护理统计列表
 * /nurseOrder/exportStatistics
 */
export function nurseOrderStatisticsExport(data?: any) {
  return defHttp.post(
    {
      url: '/nurseOrder/exportStatistics',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );
}
