import { defHttp } from '@ft/request';
import type { VisitContentList } from '../follow-template';
import type { VisitDataList } from '../workbench';

export interface NursingDocumentTemplateVO {
  /**创建时间*/
  createTime: string;
  /**创建人*/
  createUser: string;
  /**模板ID*/
  id: string;
  /**服务分类*/
  itemTypeDesc: string;
  /**启用状态*/
  status: number;
  /**启用状态描述*/
  statusDesc: string;
  /**护理模板名称*/
  templateName: string;
}

/**
 * 分页列表
 * /nursingDocumentTemplate/getPageList
 */
export const getNursingDocumentTemplatePage = (data: any) =>
  defHttp.post<NursingDocumentTemplateVO>({
    url: '/nursingDocumentTemplate/getPageList',
    data,
  });

export interface NursingServiceItemTypeList {
  itemId: string;
  itemName: string;
}

export interface NursingVisitContentList extends VisitContentList {
  dataHeader: string;
}
export interface NursingDocumentTemplateDTO {
  id?: string;
  /** 	服务分类项集合 */
  itemTypeList: NursingServiceItemTypeList[];
  status: number;
  /**	护理模板名称 */
  templateName: string;
  /** 护理内容数据集合 */
  visitContentList: NursingVisitContentList[];
}
/**
 * 新增护理模板
 * /nursingDocumentTemplate/save
 */
export const saveNursingDocumentTemplate = (data: Partial<NursingDocumentTemplateDTO>) =>
  defHttp.post({
    url: '/nursingDocumentTemplate/save',
    data,
  });

/**
 * 编辑护理模板
 * /nursingDocumentTemplate/edit
 */
export const editNursingDocumentTemplate = (data: Partial<NursingDocumentTemplateDTO>) =>
  defHttp.post({
    url: '/nursingDocumentTemplate/edit',
    data,
  });

/**
 * 护理模板详情
 * /nursingDocumentTemplate/detail/{templateId}
 */
export const getNursingDocumentTemplateDetail = (templateId: string) =>
  defHttp.get<NursingDocumentTemplateDTO>({
    url: `/nursingDocumentTemplate/detail/${templateId}`,
  });

/**
 * 删除护理模板
 * /nursingDocumentTemplate/delete/{templateId}
 */
export const deleteNursingDocumentTemplate = (templateId: string) =>
  defHttp.delete({
    url: `/nursingDocumentTemplate/delete/${templateId}`,
  });

export interface NurseDocumentPatientBaseInfo {
  age: string;
  orderId: string;
  patientAddress: string;
  patientName: string;
  phone: string;
  sex: string;
  itemTypeDesc: string;
}
/**
 * 护理文书-患者基本信息
 * /nurseOrder/nurseDocumentPatientBaseInfo/{orderId}
 */
export const getNurseDocumentPatientBaseInfo = (orderId: string) =>
  defHttp.get<NurseDocumentPatientBaseInfo>({
    url: `/nurseOrder/nurseDocumentPatientBaseInfo/${orderId}`,
  });

export interface NurseDataList extends VisitDataList {
  dataHeader: string;
}

export interface NurseDocumentContent {
  visitContentDataList: NurseDataList[];
}

/**查看护理文书内容
 * GET /nurseOrder/viewNurseDocument/{orderId}
 */
export const getNurseDocumentContent = (orderId: string) =>
  defHttp.get<NurseDocumentContent>({
    url: `/nurseOrder/viewNurseDocument/${orderId}`,
  });
