import { defHttp } from '@ft/request';

export interface INurseOrderEvaluatePage {
  /**报警时间*/
  alarmTime: string;
  /**预约上门时间*/
  appointTime: string;
  /**投诉内容*/
  complaintContent: string;
  /**评价等级*/
  evaluationLevel: number;
  /**完成时间*/
  finishTime: string;
  /**主键*/
  id: string;
  /**护理人员*/
  nurseName: string;
  /**护理项目*/
  nursingItemName: string;
  /**患者地址*/
  orderAddress: string;
  /**订单编号*/
  orderNo: string;
  /**订单时间*/
  orderTime: string;
  /**患者年龄*/
  patientAge: string;
  /**患者姓名*/
  patientName: string;
  /**患者性别*/
  patientSex: string;
  /**满意程度*/
  satisfactionLevel: number;
  /**满意度评分*/
  satisfactionScore: number;
  /**标签内容*/
  tagContent: string;
  /**上门时间*/
  visitTime: string;
}
/*
 * @description: 医服到家 - 护理服务评价分页查询
 * /nursingServiceEvaluateRecord/queryList
 */
export const getNurseOrderEvaluatePage = (data?: any) =>
  defHttp.post<INurseOrderEvaluatePage[]>({
    url: '/nursingServiceEvaluateRecord/queryList',
    data,
  });

export interface INurseOrderEvaluateDetail {
  /**评价内容*/
  evaluateContent: string;
  /**评价标签*/
  evaluateTags: string;
  /**评价等级*/
  evaluationLevel: number;
  /**护理人员头像*/
  nurseImg: string;
  /**护理人员职称*/
  nurseJobTitle: string;
  /**护理人员姓名*/
  nurseName: string;
  /**满意程度*/
  satisfactionLevel: number;
  /**满意度评分*/
  satisfactionScore: number;
}
/**
 * 根据订单编号获取护理服务评价详情信息
 * /nursingServiceEvaluateRecord/queryDetailByOrderNo
 */
export const getNurseOrderEvaluateDetail = (orderNo?: string) =>
  defHttp.get<INurseOrderEvaluateDetail>({
    url: `/nursingServiceEvaluateRecord/queryDetailByOrderNo?orderNo=${orderNo}`,
  });
