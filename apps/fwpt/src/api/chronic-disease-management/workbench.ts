import { defHttp } from '@ft/request';

/**
 * @description 慢病干预计划-分页查询
 * /chronicDiseaseIntervenePlan/pageList
 */
export interface IPageList {
  dietaryGuidance: string;
  diseaseCode: string;
  diseaseName: string;
  id: string;
  inspection: string;
  interveneTypeCode: number;
  interveneTypeName: string;
  livingHabitGuide: string;
  monitorIndexList: MonitorIndexList[];
  planName: string;
  psychologicalGuide: string;
  recommendedMedication: string;
  remark: string;
  sportsInstruction: string;
  status: number;
  updateTime: string;
  updateUser: string;
  isInspection: string;
  inspectionItemList: InspectionItemList[];
  reVisitRule: string;
  reVisitTime: string;
  medicationDate: string;
}
export interface InspectionItemList {
  appointmentTime: string;
  itemName: string;
}

export interface MonitorIndexList {
  id: string;
  indexType: number;
  status: number;
}
export function getPlanPage(data?: any) {
  return defHttp.post<IPageList[]>({ url: '/chronicDiseaseIntervenePlan/pageList', data });
}
/**
 * @description 慢病干预计划-列表查询
 * /chronicDiseaseIntervenePlan/list
 */
export function getPlanList(data?: any) {
  return defHttp.post<IPageList[]>({ url: '/chronicDiseaseIntervenePlan/list', data });
}

export interface ChronicDiseaseIntervenePlanDTO {
  /** 饮食指导 */
  dietaryGuidance?: string;
  /** 干预病种编码 */
  diseaseCode?: string;
  /** 干预病种名称 */
  diseaseName?: string;
  /** 营养运动处方-周运动处方-执行时间（周） */
  exercisePrescriptionExecuteTime?: number;
  /** 营养运动处方-周运动处方集合 */
  exercisePrescriptionInfoList?: ExercisePrescriptionInfo[];
  /** 主键ID */
  id?: string;
  /** 就诊流水号 */
  inpatientNo?: string;
  /** 检查检验计划内容 */
  inspection?: string;
  /** 检查检验计划项目 */
  inspectionList?: InspectionItemDTO[];
  /** 检查检验计划项目 详情返回的 inspectionList */
  inspectionItemList?: InspectionItemDTO[];
  /** 干预结束时间(yyyy-MM-dd) */
  interveneEndTime?: string;
  /** 干预开始时间(yyyy-MM-dd) */
  interveneStartTime?: string;
  /** 干预方式编码[字典:CD_INTERVENE_WAY] */
  interveneTypeCode?: number;
  /** 干预方式名称 */
  interveneTypeName?: string;
  /** 是否定期检查检验 0:否 1:是 */
  isInspection?: string;
  /** 是否推荐用药 0:否 1:是 */
  isRecommendedMedication?: number;
  /** 生活习惯指导 */
  livingHabitGuide?: string;
  /** 推荐下次购药时间(yyyy-MM-dd) */
  medicationDate?: string;
  /** 用药计划 */
  medicationSchedule?: string;
  /** 监测指标列表 */
  monitorIndexList?: IntervenePlanMonitorIndexDTO[];
  /** 营养运动处方-周营养处方-执行时间（周） */
  nutritionPrescriptionExecuteTime?: number;
  /** 营养运动处方-周营养处方集合 */
  nutritionPrescriptionInfoList?: NutritionPrescriptionInfo[];
  /** 干预计划名称 */
  planName?: string;
  /** 心理指导 */
  psychologicalGuide?: string;
  /** 复诊规则 */
  reVisitRule?: string;
  /** 复诊时间 */
  reVisitTime?: string;
  /** 推荐用药 */
  recommendedMedication?: string;
  /** 备注 */
  remark?: string;
  /** 运动指导 */
  sportsInstruction?: string;
  /** 启用状态 0:启用 1:禁用 */
  status?: number;
}

export interface ExercisePrescriptionInfo {
  /** 动作示例（负荷/组数） */
  actionExample?: string;
  /** 血压/血糖预警 */
  bpbsEarlyWarning?: string;
  /** 时长 */
  duration?: string;
  /** 强度区间 */
  intensityRange?: string;
  /** 热量消耗（kcal） */
  kcal?: string;
  /** 关联的干预计划ID */
  planId?: string;
  /** 训练类型 */
  trainingType?: string;
  /** 星期 */
  week?: string;
}

interface InspectionItemDTO {
  /** 预约时间 */
  appointmentTime?: string;
  /** 项目名称 */
  itemName?: string;
}

interface IntervenePlanMonitorIndexDTO {
  /** 主键ID */
  id?: string;
  /** 1-血压，2-血糖，3-体重[字典:MONITOR_INDEX] */
  indexType?: number;
  /** 勾选状态 0:勾选 1:未勾选 */
  status?: number;
}

export interface NutritionPrescriptionInfo {
  /** 热量占比 */
  heatPercentage?: string;
  /** 摄入量（g/kg） */
  intake?: string;
  /** 餐次分配（早/中/晚/加餐） */
  mealAllocation?: string;
  /** 营养素 */
  nutrient?: string;
  /** 关联的干预计划ID */
  planId?: string;
  /** 具体示例 */
  specificExample?: string;
  /** 总量（g） */
  total?: string;
}

/**
 * @description 慢病干预计划-新增
 * /chronicDiseaseIntervenePlan/add
 */
export function addPlan(data: ChronicDiseaseIntervenePlanDTO) {
  return defHttp.post({ url: '/chronicDiseaseIntervenePlan/add', data });
}

/**
 * @description 慢病干预计划-编辑
 * /chronicDiseaseIntervenePlan/edit
 */
export function editPlan(data: ChronicDiseaseIntervenePlanDTO) {
  return defHttp.post({ url: '/chronicDiseaseIntervenePlan/edit', data });
}

/**
 * @description 慢病干预计划-删除
 * /chronicDiseaseIntervenePlan/delete/{id}
 */
export function deletePlan(id: string) {
  return defHttp.delete({ url: `/chronicDiseaseIntervenePlan/delete/${id}` });
}

/**
 * @description 慢病干预计划-详情
 * /chronicDiseaseIntervenePlan/detail/{id}
 */
export function getPlanDetail(id: string, inpatientNo = '') {
  return defHttp.post<ChronicDiseaseIntervenePlanDTO>({
    url: `/chronicDiseaseIntervenePlan/detail/${id}?inpatientNo=${inpatientNo}`,
  });
}

/**
 * @description 慢病干预计划-干预记录-指标监测折线图
 * /patientMonitorIndexContent/indexLineChart
 */
export interface ILineChart {
  dataList: DataList[];
  name: string;
  type: number;
}

export interface DataList {
  sort: number;
  x: string;
  y: string;
  yvalue: number;
}

export function getLineChart(data: any) {
  return defHttp.post<ILineChart[]>({ url: '/patientMonitorIndexContent/indexLineChart', data });
}

/**
 * @description 慢病干预计划-工作台-慢病患者列表
 * /chronicDiseaseManagement/queryPatientList
 */
export interface IPatientList {
  age: string;
  id: string;
  idCardNo: string;
  interveneStatus: number; //干预计划关联状态 0:未关联 1:关联
  leaveDate: string;
  patientName: string;
  planId: string;
  sex: string;
  telephone: string;
  visitDiagnosisName: string;
  visitHospitalName: string;
  visitType: string;
}
export function getPatientList(data: any) {
  return defHttp.post<IPatientList[]>({ url: '/chronicDiseaseManagement/queryPatientList', data });
}

/**
 * @description 慢病干预计划-医护端-工作台-获取慢病病种列表
 * /chronicDiseaseManagement/getChronicDiseaseList
 */
export function getChronicDiseaseList() {
  return defHttp.get({ url: '/chronicDiseaseManagement/getChronicDiseaseList' });
}

/**
 * @description 慢病干预计划-医护端-慢病管理统计-导出慢病管理统计表
 * /chronicDiseaseManagement/exportManagementStatisticsReport
 */
export function exportManagementStatisticsReport(data: any) {
  return defHttp.post(
    {
      url: '/chronicDiseaseManagement/exportManagementStatisticsReport',
      responseType: 'blob',
      data,
    },
    { isReturnNativeResponse: true },
  );
}

/**
 * @description 慢病干预计划-医护端-导出慢病患者列表
 * /chronicDiseaseManagement/exportPatientList
 */
export function exportPatientList(data: any) {
  return defHttp.post(
    {
      url: '/chronicDiseaseManagement/exportPatientList',
      responseType: 'blob',
      data,
    },
    { isReturnNativeResponse: true },
  );
}

/**
 * @description 慢病干预计划-医护端-慢病管理统计-当前年度慢病干预量分布情况
 * /chronicDiseaseManagement/queryDistributionOfInterventionLevels
 */
export function queryDistributionOfInterventionLevels(data: any) {
  return defHttp.get({
    url: '/chronicDiseaseManagement/queryDistributionOfInterventionLevels',
    data,
  });
}

/**
 * @description 慢病干预计划-医护端-慢病管理统计-慢病管理统计表
 * /chronicDiseaseManagement/queryManagementStatisticsReport
 */
export function queryManagementStatisticsReport(data: any) {
  return defHttp.post({
    url: '/chronicDiseaseManagement/queryManagementStatisticsReport',
    data,
  });
}

/**
 * @description 医护端-慢病管理统计-慢病月度干预情况
 * /chronicDiseaseManagement/queryMonthlyInterventionSituation
 */
export function queryMonthlyInterventionSituation(data: any) {
  return defHttp.get({
    url: '/chronicDiseaseManagement/queryMonthlyInterventionSituation',
    data,
  });
}

/**
 * @description 医护端-慢病管理统计-当前年度慢病线上咨询量统计
 * /chronicDiseaseManagement/queryOnlineConsultationVolumeStatistics
 */
export function queryOnlineConsultationVolumeStatistics(data?: any) {
  return defHttp.get({
    url: '/chronicDiseaseManagement/queryOnlineConsultationVolumeStatistics',
    data,
  });
}

/**
 * @description 医护端-工作台-患者详情
 * /chronicDiseaseManagement/queryPatientDetail
 */
export interface IPatientDetail {
  age: number;
  divisionName: string;
  id: string;
  idCardNo: string;
  inpatientNo: string;
  leaveDate: string;
  outpatientNo: string;
  patientName: string;
  sex: string;
  telephone: string;
  visitDeptName: string;
  visitDiagnosisName: string;
  visitDoctor: string;
  visitHospitalName: string;
  visitType: string;
}

export function queryPatientDetail(id: string) {
  return defHttp.get<IPatientDetail>({
    url: `/chronicDiseaseManagement/queryPatientDetail?id=${id}`,
  });
}

/**
 * @description 管理端-慢病管理统计-当前年度疾病统计
 * /chronicDiseaseManagement/queryDiseaseTopStatistics
 */
export function queryDiseaseTopStatistics(data?: any) {
  return defHttp.get({
    url: '/chronicDiseaseManagement/queryDiseaseTopStatistics',
    data,
  });
}

/**
 * @description 慢病干预计划-患者慢病干预记录-分页查询
 * /chronicDiseaseIntervenePlan/queryPatientInterveneRecordPageList
 */

export interface IPatientInterveneRecordPageList {
  age: string;
  id: string;
  idCardNo: string;
  leaveDate: string;
  outInId: string;
  patientName: string;
  planId: string;
  planName: string;
  sex: string;
  telephone: string;
  visitDiagnosisName: string;
  visitHospitalName: string;
  visitType: string;
}
export function queryPatientInterveneRecordPageList(data: any) {
  return defHttp.post<IPatientInterveneRecordPageList[]>({
    url: '/chronicDiseaseIntervenePlan/queryPatientInterveneRecordPageList',
    data,
  });
}

/**
 * @description 慢病干预计划-患者慢病干预详情- 查询评价记录信息
 * /evaluateRecord/queryList
 */

export interface IEvaluateRecordList {
  createTime: string;
  createUser: string;
  deleteFlag: number;
  evaluateContent: string;
  evaluateTemplateId: string;
  evaluateTime: string;
  id: string;
  patientId: string;
  updateTime: string;
  updateUser: string;
}
export function getEvaluateRecordList(patientId: string) {
  return defHttp.get<IEvaluateRecordList[]>({
    url: `/evaluateRecord/queryList?patientId=${patientId}`,
  });
}

/**
 * @description 慢病干预计划-患者慢病干预详情- 根据身份证号获取电子处方列表
 * /electronicPrescription/queryListByIdCard
 */

export interface IElectronicPrescriptionListByIdCard {
  days: string;
  dosage: string;
  frequency: string;
  id: string;
  itemName: string;
  specifications: string;
  total: string;
  usage: string;
}
export function getElectronicPrescriptionListByIdCard(idCardNo: string) {
  return defHttp.get<IElectronicPrescriptionListByIdCard[]>({
    url: `/electronicPrescription/queryListByIdCard?idCardNo=${idCardNo}`,
  });
}

/**
 * @description 慢病干预计划-患者慢病干预详情- 查询患者接收到的宣教内容-分页列表
 * /educationContent/queryPageList
 */

export interface IEducationContentQueryPageList {
  columnId: string;
  columnName: string;
  content: string;
  createTime: string;
  createUser: string;
  fileName: string;
  fileUrl: string;
  id: string;
  name: string;
  orgId: string;
  orgName: string;
  pushTime: string;
  pushUser: string;
  readStatus: number;
  status: number;
  target: number;
}

export function getEducationContentQueryPageList(data: any) {
  return defHttp.post({
    url: '/educationContent/queryPageList',
    data,
  });
}

/**
 * 医护端-工作台-慢病复诊提醒
 * /chronicDiseaseManagement/chronicDiseaseDoctorRediagnosisReminder
 */
export function chronicDiseaseDoctorRediagnosisReminder(data: any) {
  return defHttp.post({
    url: '/chronicDiseaseManagement/chronicDiseaseDoctorRediagnosisReminder',
    data,
  });
}
