import { defHttp } from '@ft/request';

/**
 * @description 慢病评价模板-分页查询
 * /evaluateTemplate/queryPage
 */
export interface IPageList {
  createTime: string;
  createUser: string;
  deleteFlag: number;
  id: string;
  templateContent: string;
  templateName: string;
  templateStatus: number;
  updateTime: string;
  updateUser: string;
}
export function getEvaluateTemplatePage(data?: any) {
  return defHttp.post<IPageList[]>({ url: '/evaluateTemplate/queryPage', data });
}

/**
 * @description 慢病评价模板-新增
 * /evaluateTemplate/save
 */
export function addEvaluateTemplate(data?: any) {
  return defHttp.post({ url: '/evaluateTemplate/save', data });
}

/**
 * @description 慢病评价模板-修改
 * /evaluateTemplate/update
 */
export function updateEvaluateTemplate(data?: any) {
  return defHttp.post({ url: '/evaluateTemplate/update', data });
}

/**
 * @description 慢病评价模板-删除
 * /evaluateTemplate/delete
 */

export const deleteEvaluateTemplate = (id: string) => {
  return defHttp.post({
    url: '/evaluateTemplate/delete?id=' + id,
  });
};

/**
 * @description 慢病评价模板-详情
 * /evaluateTemplate/detail
 */
export function getEvaluateTemplateDetail(id: string) {
  return defHttp.post({ url: '/evaluateTemplate/detail?id=' + id });
}
