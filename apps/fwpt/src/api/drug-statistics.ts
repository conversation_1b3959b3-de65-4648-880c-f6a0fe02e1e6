import { defHttp } from '@ft/request';
/**
 * @description 发药统计
 */
export interface IEducationColumn {
  chartVOList: ChartVolist[];
  prescCreateHospital: string;
}

export interface ChartVolist {
  drugCode: string;
  drugName: string;
  unit: string;
  x: string;
  xvalue: string;
  yvalue: number;
}
/**
 * @description 药品发放数量分布
 */
export const countDistribution = (data: any) => {
  return defHttp.post<IEducationColumn[]>({
    url: '/dispensingStatistics/countDistribution',
    data,
  });
};
export interface ICountStatistics {
  drugCode: string;
  drugName: string;
  total: number;
  unit: string;
  xValue: string;
}
/**
 * @description 药品发放数量统计
 */
export const countStatistics = (data: any) => {
  return defHttp.post<ICountStatistics[]>({
    url: '/dispensingStatistics/countStatistics',
    data,
  });
};
export interface IStatisticsList {
  dispenser: string;
  dispensingTime: string;
  drugCode: string;
  drugName: string;
  id: string;
  patientAge: string;
  patientDeptCode: string;
  patientDeptName: string;
  patientIdCard: string;
  patientName: string;
  patientPhone: string;
  patientSexValue: string;
  patientVisitNum: string;
  prescCreateDoc: string;
  prescCreateDocId: string;
  prescCreateHospital: string;
  prescCreateHospitalCode: string;
  prescCreateTime: string;
  prescriptionStatus: number;
  prescriptionStatusDesc: string;
  quantity: number;
}
/**
 * @description 发布记录详情(分页)
 */
export const getStatisticsList = (data: any) => {
  return defHttp.post<IStatisticsList[]>({
    url: '/dispensingStatistics/statisticsList',
    data,
  });
};
/**
 * @description 各处方开具机构药品发药数量统计表(分页)
 */
export const getQuantityOfOrgList = (data: any) => {
  return defHttp.post<{ list: IEducationColumn[] }>({
    url: '/dispensingStatistics/quantityOfOrg',
    data,
  });
};

/**
 * @description 发药记录列表统计导出
 * /expert/export
 */
export function statisticsListExport(data?: any) {
  return defHttp.post(
    {
      url: '/dispensingStatistics/statisticsListexport',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );
}
