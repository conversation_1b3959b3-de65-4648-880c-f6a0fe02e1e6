import { useGlobSetting } from '@ft/internal/hooks/setting';
import { defHttp } from '@ft/request';
/**
 * @description 分页查询宣教内容
 * /educationContent/page
 */
export interface IEducationContentItem {
  /** 审核状态(1-待审核，2-初审通过待审核 3-已发布) */
  auditStatus?: number;
  /** 栏目ID */
  columnId?: string;
  /** 栏目名称 */
  columnName?: string;
  /** 宣教内容 */
  content?: string;
  /** 创建时间 */
  createTime?: string;
  /** 创建人 */
  createUser?: string;
  /** 文件名称 */
  fileName?: string;
  /** 文件链接 */
  fileUrl?: string;
  /** 主键ID */
  id?: string;
  /** 宣教名称 */
  name?: string;
  /** 机构ID */
  orgId?: string;
  /** 机构名称 */
  orgName?: string;
  /** 推送时间 */
  pushTime?: string;
  /** 推送人 */
  pushUser?: string;
  /** 阅读状态(0-未读，1-已读) */
  readStatus?: number;
  /** 发布状态(0-启用，1-未启用) */
  status?: number;
  /** 宣教对象(0-患者，1-本机构医务人员，2-全市医务人员) */
  target?: number;
  /** 是否置顶(0-否，1-是) */
  topFlag?: number;
  /** 更新时间 */
  updateTime?: string;
}
export interface IEducationContentPageRes {
  total: number;
  list: IEducationContentItem[];
  pageNum: number;
  pageSize: number;
  size: number;
  startRow: number;
  endRow: number;
  pages: number;
  prePage: number;
  nextPage: number;
  isFirstPage: boolean;
  isLastPage: boolean;
  hasPreviousPage: boolean;
  hasNextPage: boolean;
  navigatePages: number;
  navigatepageNums: number[];
  navigateFirstPage: number;
  navigateLastPage: number;
}
export const queryEducationContentPage = (data: any) => {
  return defHttp.post<IEducationContentPageRes>({
    url: '/educationContent/page',
    data,
  });
};

/**
 * @description 条件查询宣教内容列表
 * /educationContent/queryList
 */
export const queryEducationContentList = (data: any) => {
  return defHttp.post<IEducationContentItem[]>({
    url: '/educationContent/queryList',
    data,
  });
};
/**
 * @description 新增宣教内容
 * /educationContent/add
 */
export const addEducationContent = (data: any) => {
  return defHttp.post({
    url: '/educationContent/add',
    data,
  });
};

/**
 * @description 修改宣教审核状态
 * /educationContent/update
 */
export const updateEducationAuditStatus = (params: any) => {
  return defHttp.get({
    url: '/educationContent/auditStatusChange',
    params,
  });
};

/**
 * @description 修改宣教内容
 * /educationContent/update
 */
export const updateEducationContent = (data: any) => {
  return defHttp.post({
    url: '/educationContent/update',
    data,
  });
};
/**
 * @description 删除宣教内容
 * /educationContent/delete
 */
export const deleteEducationContent = (id: string) => {
  return defHttp.post(
    {
      url: '/educationContent/delete',
      params: {
        id,
      },
    },
    {
      joinParamsToUrl: true,
    },
  );
};

/**
 * @description 置顶宣教内容
 * /educationContent/updateTop
 */
export const updateEducationContentTop = (params: { id: string; topFlag: number }) => {
  return defHttp.post(
    {
      url: '/educationContent/updateTop',
      params,
    },
    {
      notParmasToData: true,
    },
  );
};
/**
 * @description 查询宣教内容详情
 * /educationContent/detail
 */
export const queryEducationContentDetail = (id: string) => {
  return defHttp.get({
    url: `/educationContent/detail?id=${id}`,
  });
};
/**
 * @description 查询推送本科室患者用户列表
 * /educationPushRecord/queryPushUser
 */
export const queryPushUserList = (params) => {
  return defHttp.get(
    {
      url: '/educationPushRecord/queryPushUser',
      params,
    },
    {
      errorMessageMode: 'none',
    },
  );
};

/**
 * 根据条件搜索专病用户
 * POST /educationPushRecord/querySpecificDiseasesUser
 */
export const querySpecificDiseasesUser = (data?: any) => {
  return defHttp.post({
    url: '/educationPushRecord/querySpecificDiseasesUser',
    data,
  });
};
/**
 * @description 健康宣教定点推送
 * /educationPushRecord/push
 */
export const pushEducationContent = (data: any) => {
  return defHttp.post({
    url: '/educationPushRecord/push',
    data,
  });
};

const { apiUrl, urlPrefix } = useGlobSetting();

export const EDU_DOWNLOAD_URL = `${apiUrl}${urlPrefix}/educationContent/upload`;
