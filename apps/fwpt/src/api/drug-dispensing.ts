import { defHttp } from '@ft/request';

export interface IPrescription {
  dispenser: string;
  dispensingTime: string;
  id: string;
  mainDiagCode: string;
  mainDiagName: string;
  patientAge: string;
  patientAreaCode: string;
  patientAreaName: string;
  patientDeptCode: string;
  patientDeptName: string;
  patientIdCard: string;
  patientName: string;
  patientPhone: string;
  patientSexValue: string;
  patientVisitNum: string;
  prescCreateDoc: string;
  prescCreateDocId: string;
  prescCreateHospital: string;
  prescCreateHospitalCode: string;
  prescCreateTime: string;
  prescriptionNo: string;
  prescriptionStatuDesc: string;
  /**	处方状态 0 待发药 1 已发药 */
  prescriptionStatus: number;
}
export interface IStorage {
  drugCode: string;
  drugName: string;
  manufacturer: string;
  quantity: number;
  responsiblePerson: string;
  specs: string;
  storageTime: string;
}
export interface IByPrescriptionId {
  days: number;
  dispenser: string;
  dispensingStatus: number;
  dispensingTime: string;
  dosage: string;
  dosageUnit: string;
  drugCode: string;
  drugName: string;
  frequency: string;
  id: string;
  pkPrescription: string;
  specs: string;
  total: number;
  usage: string;
}

export interface IPrescriptionPost {
  doctorSignImg: string;
  drugDetailList: Partial<DrugDetailList>[];
  id?: string;
  mainDiagCode: string;
  mainDiagName: string;
  patientAge: string;
  patientAreaCode?: string;
  patientAreaName?: string;
  patientDeptCode: string;
  patientDeptName: string;
  patientIdCard: string;
  patientName: string;
  patientPhone: string;
  patientSexValue?: string;
  patientVisitNum?: string;
  prescCreateDoc: string;
  prescCreateDocId: string;
  prescCreateHospital: string;
  prescCreateHospitalCode: string;
  prescCreateTime: string;
  prescriptionNo?: string;
  /**	处方状态 0 待发药 1 已发药 */
  prescriptionStatus: 0 | 1;
  signValue: string;
}

export interface DrugDetailList {
  dayNum: number;
  dosage: string;
  dosageForm: string;
  dosageUnit: string;
  drugCode: string;
  drugName?: string;
  drugUsage?: string;
  drugUsageName: string;
  frequency: number;
  mpqUnit: string;
  id?: string;
  prescriptionId: string;
  specs: string;
  total: number;
  note: string;
}

/*
 * @description: 处方明细信息表-分页查询
 */
export const getByPrescriptionIdPage = (data?: any) =>
  defHttp.post<IByPrescriptionId[]>({ url: `/prescriptionDetail/pageByPrescriptionId`, data });
/*
 * @description: 处方明细信息表-列表查询
 */
export const getByPrescriptionIdList = (data?: any) =>
  defHttp.post<IByPrescriptionId[]>({ url: `/prescriptionDetail/listByPrescriptionId`, data });
/*
 * @description: 处方信息表-分页查询
 */
export const getPrescriptionPage = (data?: any) =>
  defHttp.post<IPrescription[]>({ url: '/prescription/page', data });
/*
 * @description: 处方信息表-列表查询
 */
export const getPrescriptionList = (data?: any) =>
  defHttp.post<IPrescription[]>({ url: '/prescription/list', data });
/*
 * @description: 处方信息表-更新处方信息表
 */
export const editDrugCatalogue = (data?: any) => defHttp.post({ url: '/drugCatalogue/edit', data });
/*
 * @description: 处方信息表-详情
 */

export const detailPrescription = (id: string) =>
  defHttp.get<IPrescriptionPost>({ url: `/prescription/detail/${id}` });
/*
 * @description: 处方信息表-新增处方信息
 */
export const addPrescription = (data?: Partial<IPrescriptionPost>) =>
  defHttp.post({ url: '/prescription/add', data });
/**
 * 处方信息表 前端控制器-编辑处方信息
 */
export function updatePrescription(data: IPrescriptionPost) {
  return defHttp.post({ url: `/prescription/update`, data });
}

/**
 * 处方信息表 前端控制器-删除处方
 */
export function removePrescription(id: string) {
  return defHttp.post({ url: `/prescription/delete?id=${id}` });
}

export interface DetailPrescriptionInfo {
  dispenser: string;
  dispensingTime: string;
  id: string;
  mainDiagCode: string;
  mainDiagName: string;
  patientAge: string;
  patientAreaCode: string;
  patientAreaName: string;
  patientDeptCode: string;
  patientDeptName: string;
  patientIdCard: string;
  patientName: string;
  patientPhone: string;
  patientSexValue: string;
  patientVisitNum: string;
  prescCreateDoc: string;
  prescCreateDocId: string;
  prescCreateHospital: string;
  prescCreateHospitalCode: string;
  prescCreateTime: string;
  prescriptionNo: string;
  prescriptionStatuDesc: string;
  /**	处方状态 0 待发药 1 已发药 */
  prescriptionStatus: 0 | 1;
}

/**
 * @description 处方记录列表统计导出
 * /expert/export
 */
export function prescriptionExport(data?: any) {
  return defHttp.post(
    {
      url: '/prescription/export',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );
}

/**
 * 处方信息表 前端控制器-医生端——分页查询处方列表
 */
export function getPrescriptionPageByDoctor(data = {}) {
  return defHttp.post({ url: `/prescription/pageDoctor`, data });
}

export function verifySignData(data: any) {
  return defHttp.post({ url: '/prescription/verifySignData', data });
}

/**
 * 驳回发药
 * /outbound/backDrug
 */
export function backDrug(data = {}) {
  return defHttp.post({ url: `/outbound/backDrug`, data });
}

interface IVerifySignData {
  orgData: string;
  signValue: string;
}
/**
 * 验证签名数据
 * /prescription/verifySignData
 */
export function prescriptionVerifySignData(data: IVerifySignData) {
  return defHttp.post({ url: `/prescription/verifySignData`, data });
}
