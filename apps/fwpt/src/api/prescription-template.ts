import { defHttp } from '@ft/request';

export interface IPresTemplateDrugInfo {
  dosage: string;
  drugName: string;
  drugUsage: string;
  frequency: string;
  id: string;
  quantity: number;
  tid: string;
  usageId: string;
}

export interface ITemplateDrugPage {
  applyDiagnosticCode: string;
  applyDiagnosticName: string;
  id: string;
  ownerType: number;
  ownerTypeCode: string;
  tname: string;
}

/**
 * @description 处方模板-批量新增模板处方药品
 * /presTemplate/batchAddTemplateDrug
 */
export const getBatchAddTemplateDrug = (data?: any) => {
  return defHttp.post({
    url: '/presTemplate/batchAddTemplateDrug',
    data,
  });
};

/**
 * @description 处方模板-删除
 * /presTemplate/delete/{id}
 */
export const deletePresTemplate = (id: string) => {
  return defHttp.delete({
    url: `/presTemplate/delete/${id}`,
  });
};

/**
 * @description 处方模板-删除处方模板药品
 * /presTemplate/deleteTemplateDrug/{id}
 */
export const deleteTemplateDrug = (id: string) => {
  return defHttp.delete({
    url: `/presTemplate/deleteTemplateDrug/${id}`,
  });
};

/**
 * @description 处方模板药品信息-编辑
 * /presTemplate/editTemplateDrug
 */
export const editTemplateDrug = (data?: any) => {
  return defHttp.post({
    url: '/presTemplate/editTemplateDrug',
    data,
  });
};

/**
 * @description 处方模板-分页查询
 * /presTemplate/queryPage
 */
export const getPresTemplatePage = (data?: any) => {
  return defHttp.post<ITemplateDrugPage[]>({
    url: '/presTemplate/queryPage',
    data,
  });
};

/**
 * @description 处方模板-查询药品详情
 * /presTemplate/queryTemplateDrugInfo
 */
export const getPresTemplateDrugInfo = (data?: any) => {
  return defHttp.post<IPresTemplateDrugInfo[]>({
    url: '/presTemplate/queryTemplateDrugInfo',
    data,
  });
};

/**
 * @description 处方模板-新增-编辑
 * /presTemplate/saveOrUpdate
 */
export const saveOrUpdatePresTemplate = (data?: any) => {
  return defHttp.post({
    url: '/presTemplate/saveOrUpdate',
    data,
  });
};
