import { defHttp } from '@ft/request';

/**
 * @description 查询机构列表
 * /organization/queryByCondition
 *
 */
export const queryOrganizationList = (data?: any) => {
  return defHttp.post(
    {
      url: '/infection-sysmgt/organization/queryByCondition',
      data,
    },
    {
      joinPrefix: false,
    },
  );
};

export const getDeptList = (data?: any) => {
  return defHttp.post(
    { url: '/infection-sysmgt/sysDept/list', data },
    {
      joinPrefix: false,
    },
  );
};
