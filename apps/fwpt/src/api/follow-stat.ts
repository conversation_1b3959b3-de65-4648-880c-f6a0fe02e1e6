import { defHttp } from '@ft/request';
import type { BasicFetchResult } from './follow-template';

export interface IFollowStatRow {
  /** 传染病病种类型 字典:【INFECTIOUS_DISEASE】 1:病毒性肝炎 2:艾滋病 3:结核病 */
  infectionDisease: number;
  /** 随访病种名称 */
  infectionDiseaseName: string;
  /** 随访科室名称 */
  visitDeptName: string;
  /** 随访人数 */
  visitNum: number;
  /** 随访率 */
  visitRate: string;
  /** 随访次数 */
  visitTimes: number;
}

/**
 * 随访统计服务-分页查询随访统计信息列表
 */
export function getFollowVisitStatistics(data: Record<string, any> = {}) {
  return defHttp.post<BasicFetchResult<IFollowStatRow[]>>({
    url: `/followVisitStatistics/queryPage`,
    data,
  });
}

/**
 * 随访统计服务-导出随访统计记录
 */
export function exportFollowVisitStatistics(data: Record<string, any> = {}) {
  return defHttp.post(
    { url: `/followVisitStatistics/export`, data, responseType: 'blob' },
    { isReturnNativeResponse: true },
  );
}

export interface IDeptStatRow {
  /** 传染病病种类型 字典:【INFECTIOUS_DISEASE】 1:病毒性肝炎 2:艾滋病 3:结核病 */
  infectionDisease: number;
  /** 随访病种名称 */
  infectionDiseaseName: string;
  /** 随访科室名称 */
  visitDeptName: string;
  /** 随访人数 */
  visitNum: number;
  /** 随访率 */
  visitRate: string;
  /** 随访次数 */
  visitTimes: number;
}

/**
 * 随访统计服务-分页查询科室随访统计信息列表
 */
export function getFollowVisitDeptPage(data: Record<string, any> = {}) {
  return defHttp.post<BasicFetchResult<IDeptStatRow[]>>({
    url: `/followVisitStatistics/queryDeptPage`,
    data,
  });
}

/**
 * 随访统计服务-导出科室随访统计记录
 */
export function exportFollowVisitDeptPage(data: Record<string, any> = {}) {
  return defHttp.post(
    { url: `/followVisitStatistics/exportDept`, data, responseType: 'blob' },
    { isReturnNativeResponse: true },
  );
}

export interface IDiseaseStatRow {
  /** 现住址 */
  address: string;
  /** 年龄 */
  age: number;
  /** 联系人1电话 */
  contactOnePhone: string;
  /** 联系人2电话 */
  contactTwoPhone: string;
  /** 创建时间 */
  createTime: string;
  /** 创建人 */
  createUser: string;
  /** 删除标识(0-未删除，1-删除) */
  deleteFlag: number;
  /** 诊断结果名称(艾滋病等) */
  diagnosticResults: string;
  /** 出院日期 */
  dischargeDate: string;
  /** 主键ID */
  id: string;
  /** 身份证号 */
  idCardNo: string;
  /** 住院号 */
  inId: string;
  /** 传染病病种类型 字典:【INFECTIOUS_DISEASE】 1:病毒性肝炎 2:艾滋病 3:结核病 */
  infectionDisease: number;
  /** 下次随访时间 */
  lastVisitTime: string;
  /** 门诊/急诊住院号; */
  outInId: string;
  /** 患者编号ID */
  patientId: string;
  /** 患者姓名 */
  patientName: string;
  /** 个案登记号 */
  recordNo: string;
  /** 性别 */
  sexName: string;
  /** 更新时间 */
  updateTime: string;
  /** 更新人 */
  updateUser: string;
  /** 就诊科室名称 */
  visitDeptName: string;
  /** 就诊机构名称 */
  visitOrgName: string;
  /** 随访关闭原因 */
  visitReason: string;
  /** 随访状态：0 待随访，1 已随访，2 随访关闭 */
  visitStatus: number;
  /** 随访任务ID */
  visitTaskId: string;
  /** 随访任务名称 */
  visitTaskName: string;
  /** 随访模板ID */
  visitTemplateId: string;
  /** 随访模板名称 */
  visitTemplateName: string;
  /** 随访日期 */
  visitTime: string;
  /** 随访人 */
  visitUser: string;
  /** 随访方式：1 电话，2 家庭，3 门诊 */
  visitWay: number;
}

/**
 * 随访统计服务-分页查询随访病种随访详细记录列表
 */
export function getDiseaseDetailPage(data: Record<string, any> = {}) {
  return defHttp.post<BasicFetchResult<IDiseaseStatRow[]>>({
    url: `/followVisitStatistics/queryDetailPage`,
    data,
  });
}

/**
 * 随访统计服务-导出随访病种随访记录
 * */
export function exportDiseaseDetailPage(data: Record<string, any> = {}) {
  return defHttp.post(
    { url: `/followVisitStatistics/exportDetail`, data, responseType: 'blob' },
    { isReturnNativeResponse: true },
  );
}

export interface IFollowHistory {
  /** 现住址 */
  address: string;
  /** 年龄 */
  age: number;
  /** 联系人1电话 */
  contactOnePhone: string;
  /** 联系人2电话 */
  contactTwoPhone: string;
  /** 创建时间 */
  createTime: any;
  /** 创建人 */
  createUser: string;
  /** 删除标识(0-未删除，1-删除) */
  deleteFlag: number;
  /** 诊断结果名称(艾滋病等) */
  diagnosticResults: string;
  /** 出院日期 */
  dischargeDate: any;
  /** 主键ID */
  id: string;
  /** 身份证号 */
  idCardNo: string;
  /** 住院号 */
  inId: string;
  /** 传染病病种类型 字典:【INFECTIOUS_DISEASE】 1:病毒性肝炎 2:艾滋病 3:结核病 */
  infectionDisease: number;
  /** 下次随访时间 */
  lastVisitTime: any;
  /** 门诊/急诊住院号; */
  outInId: string;
  /** 患者编号ID */
  patientId: string;
  /** 患者姓名 */
  patientName: string;
  /** 个案登记号 */
  recordNo: string;
  /** 性别 */
  sexName: string;
  /** 更新时间 */
  updateTime: any;
  /** 更新人 */
  updateUser: string;
  /** 就诊科室名称 */
  visitDeptName: string;
  /** 就诊机构名称 */
  visitOrgName: string;
  /** 随访关闭原因 */
  visitReason: string;
  /** 随访状态：0 待随访，1 已随访，2 随访关闭 */
  visitStatus: number;
  /** 随访任务ID */
  visitTaskId: string;
  /** 随访任务名称 */
  visitTaskName: string;
  /** 随访模板ID */
  visitTemplateId: string;
  /** 随访模板名称 */
  visitTemplateName: string;
  /** 随访日期 */
  visitTime: any;
  /** 随访人 */
  visitUser: string;
  /** 随访方式：1 电话，2 家庭，3 门诊 */
  visitWay: number;
}
/**
 * 随访记录服务-查询患者既往随访记录列表
 */
export function getFollowHistoryList(patientId) {
  return defHttp.get<IFollowHistory[]>({
    url: `/followVisitRecord/queryHistoryList`,
    params: { patientId, visitType: 1 },
  });
}

export interface IDoctorStatRow {
  infectionDisease: number;
  infectionDiseaseName: string;
  visitDoctorName: string;
  visitNum: number;
  visitTimes: number;
  visitDeptName: string;
  totalNumber: number;
}

/**
 * 随访统计服务-分页查询医生-护士随访统计信息列表
 * /followVisitStatistics/queryDoctorPage
 * */
export function getDoctorPage(data: Record<string, any> = {}) {
  return defHttp.post<BasicFetchResult<IDoctorStatRow[]>>({
    url: `/followVisitStatistics/queryDoctorPage`,
    data,
  });
}

/**
 * 随访统计服务-导出医生-护士统计记录
 * /followVisitStatistics/exportDoctor
 * */
export function exportDoctor(data: Record<string, any> = {}) {
  return defHttp.post(
    { url: `/followVisitStatistics/exportDoctor`, data, responseType: 'blob' },
    { isReturnNativeResponse: true },
  );
}
