import { defHttp } from '@ft/request';
import type {
  TemplateTag,
  TemplateType,
} from '../views/tracking-manage/follow-task/components/TemplateEdit/types';

export interface TemplateListReq {
  templateName?: string;
  templateType?: TemplateType;
  visitTypeId: string;
  deptId?: string;
  visitDisease?: string;
  templateTag?: number;
}

export interface BasicFetchResult<T> {
  list: T[];
  total: number;
}

/**
 * 分页查询模板列表
 * /template/queryPage
 */
export function getTemplatePage(data?: Partial<TemplateListReq>) {
  return defHttp.post<BasicFetchResult<IFollowTemplate>>({
    url: '/template/queryPage',
    data,
  });
}

/**
 * 模板列表
 * /template/queryList
 *
 */
export function getTemplateList(data?: Partial<TemplateListReq>) {
  return defHttp.post<IFollowTemplate[]>({
    url: '/template/queryList',
    data,
  });
}

export interface IFollowTemplate {
  deptId: string;
  deptName: string;
  id?: string;
  templateName: string;

  /** 模板类型：1 随访模板，2 随访问卷模板 */
  templateType: TemplateType;
  /**	模板标签：0 自定义模板，1 内置模板 */
  templateTag: TemplateTag;
  visitContentList: VisitContentList[];
  visitDisease: string;
  visitDiseaseName: string;
  visitObjectList: VisitObjectList[];
  visitTypeId: string;
  visitTypeName: string;
  updateTime?: string;
}

export interface VisitContentList {
  dataItemList: DataItemList[] | null;
  /**	数据模块：1 随访对象，2 随访内容 */
  dataModel: number;
  dataName: string;
  dataType: string;
  dataTypeName?: string;
  id?: string;
  isRequired?: number;
  isShow?: number;
  manualInput?: number;
  sortNum?: number;
  visitTemplateId?: string;
}

export interface DataItemList {
  dataItem: string;
  id?: string;
  templateDataId?: string;
  visitTemplateId?: string;
}

export interface VisitObjectList {
  dataItemList: DataItemList[];
  dataKey: string;
  dataModel: number;
  dataName: string;
  dataType: string;
  dataTypeName: string;
  id: string;
  isRequired: number;
  isShow: number;
  manualInput: number;
  sortNum: number;
  visitTemplateId: string;
}

/**
 * 保存模板
 * /template/save
 */
export function saveTemplate(data: Omit<IFollowTemplate, 'id'>) {
  return defHttp.post({
    url: '/template/save',
    data,
  });
}

/**
 * 模板详情
 * /template/detail
 */
export function getTemplateDetail(id: string) {
  return defHttp.get<IFollowTemplate>({
    url: '/template/detail?visitTemplateId=' + id,
  });
}

/**
 * 更新模板
 * /template/update
 */
export function updateTemplate(data: IFollowTemplate) {
  return defHttp.post({
    url: '/template/update',
    data,
  });
}

/**
 * 删除模板
 * /template/remove
 */

export function removeTemplate(id: string) {
  return defHttp.post({
    url: '/template/remove?visitTemplateId=' + id,
  });
}

/**
 * 查询模板随访对象默认数据项信息
 * /template/default_data
 */

export function getTemplateDefaultData() {
  return defHttp.get<VisitObjectList[]>({
    url: '/template/default_data',
  });
}
