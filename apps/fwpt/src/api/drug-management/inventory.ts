import { defHttp } from '@ft/request';

export interface IDrugCatalogue {
  catalogName: string;
  drugLevelCode: string;
  drugLevelDesc: string;
  drugTypeCode: string;
  drugTypeDesc: string;
  /** 启用标志  1 启用 0 禁用 */
  enableFlag: number;
  id: string;
}
export interface IStorage {
  drugCode: string;
  drugName: string;
  manufacturer: string;
  quantity: number;
  responsiblePerson: string;
  specs: string;
  storageTime: string;
}
/*
 * @description: 药品目录信息-分页查询
 */
export const getDrugCataloguePage = (data?: any) =>
  defHttp.post<IDrugCatalogue[]>({ url: '/drugCatalogue/page', data });
/*
 * @description: 药品目录信息-列表查询
 */
export const getDrugCatalogueList = (data?: any) =>
  defHttp.post<IDrugCatalogue[]>({ url: '/drugCatalogue/list', data });
/*
 * @description: 药品目录信息-更新药品目录信息
 */
export const editDrugCatalogue = (data?: any) => defHttp.post({ url: '/drugCatalogue/edit', data });
/*
 * @description: 药品目录信息-详情
 */
export const detailDrugCatalogue = (id: string) =>
  defHttp.post({ url: `/drugCatalogue/detail/${id}` });
/*
 * @description: 药品目录信息-删除药品目录信息
 */
export const delDrugCatalogue = (id: string) => defHttp.delete({ url: `/drugCatalogue/del/${id}` });
/*
 * @description: 药品目录信息-新增药品目录信息
 */
export const addDrugCatalogue = (data?: any) => defHttp.post({ url: '/drugCatalogue/add', data });

/*
 * @description: 药品目录信息-启用/禁用
 */
export const changeEnableFlag = (data: { id: string; enableFlag: number }) =>
  defHttp.post({ url: '/drugCatalogue/enable', data });
