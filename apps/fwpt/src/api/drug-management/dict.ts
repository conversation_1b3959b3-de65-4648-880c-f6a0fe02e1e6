import { defHttp } from '@ft/request';

export interface IDrugInfo {
  approvalNumber: string;
  dosageForm: string;
  drugCode: string;
  drugInventory: number;
  drugName: string;
  enableFlag: number;
  id: string;
  manufacturer: string;
  mpq: string;
  mpqUnit: string;
  note: string;
  pkCatalogue: string;
  price: string;
  specs: string;
  unit: string;
  usage: string;
}
export interface IStorage {
  drugStorageList?: IDrugStorageList[];
  drugOutboundList?: IDrugStorageList[];
  total: number;
}
export interface IDrugStorageList {
  dispenser: string;
  dispensingTime: string;
  drugCode: string;
  id: string;
  medicalInstitutionCode: string;
  medicalInstitutionNae: string;
  patientName: string;
  quantity: number;
}
export interface IOutboundStatistics {
  dispenser: string;
  dispensingTime: string;
  drugCode: string;
  drugName: string;
  id: string;
  patientAge: string;
  patientDeptCode: string;
  patientDeptName: string;
  patientIdCard: string;
  patientName: string;
  patientPhone: string;
  patientSexValue: string;
  patientVisitNum: string;
  prescCreateDoc: string;
  prescCreateDocId: string;
  prescCreateHospital: string;
  prescCreateHospitalCode: string;
  prescCreateTime: string;
  prescriptionStatus: number;
  prescriptionStatusDesc: string;
  quantity: number;
}
/*
 * @description: 药品出库记录-发药记录列表统计(分页)
 */
export const getOutboundStatisticsList = (data) =>
  defHttp.post<IOutboundStatistics[]>({ url: '/outbound/statisticsList', data });
/*
 * @description: 药品出库记录-自提发药
 */
export const getOutboundDispensing = (data) => defHttp.post({ url: '/outbound/dispensing', data });

/*
 * @description: 药品信息字典表-出库列表查询
 */
export const getOutboundList = (data) =>
  defHttp.post<IStorage>({ url: '/outbound/listAndCount', data });

/*
 * @description: 药品信息字典表-药品入库
 */
export const addStorage = (data) => defHttp.post({ url: '/storage/add', data });
/*
 * @description: 药品出库记录-发药记录列表统计(分页)
 */
/*
 * @description: 药品信息字典表-更新入库信息
 */
export const editStorage = (data) => defHttp.post({ url: '/storage/edit', data });
/*
 * @description: 药品信息字典表-入库详情
 */
export const detailStorage = (id: string) => defHttp.get({ url: `/storage/detail/${id}` });
/*
 * @description: 药品信息字典表-入库删除
 */
export const delStorage = (id: string) => defHttp.delete({ url: `/storage/del/${id}` });
/*
 * @description: 药品信息字典表-入库列表查询
 */
export const getStorageList = (data) =>
  defHttp.post<IStorage>({ url: '/storage/listAndCount', data });

/*
 * @description: 药品信息字典表-分页查询
 */
export const getDrugInfoPage = (data) => defHttp.post<IDrugInfo[]>({ url: '/drugInfo/page', data });
/*
 * @description: 药品信息字典表-列表查询
 */
export const getDrugInfoList = (data?: any) =>
  defHttp.post<IDrugInfo[]>({ url: '/drugInfo/list', data });
/*
 * @description: 药品信息字典表-更新药品信息
 */
export const editDrugInfo = (data) => defHttp.post({ url: '/drugInfo/edit', data });

/*
 * @description: 药品信息字典表-药品字典列表查询
 */
export const getDrugInfoDictList = (data?: any) =>
  defHttp.post({ url: '/drugInfo/dictList', data });
/*
 * @description: 药品信息字典表-详情
 */
export const detailDrugInfo = (id: string) => defHttp.get({ url: `/drugInfo/detail/${id}` });
/*
 * @description: 药品信息字典表-删除药品信息
 */
export const delDrugInfo = (id: string) => defHttp.delete({ url: `/drugInfo/del/${id}` });
/*
 * @description: 药品信息字典表-新增药品信息
 */
export const addDrugInfo = (data) => defHttp.post({ url: '/drugInfo/add', data });

/**
 * @description 发药记录列表统计导出
 * /expert/export
 */
export function statisticsListexport(data?: any) {
  return defHttp.post(
    {
      url: '/outbound/statisticsListexport',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );
}

/**
 * @description 药品库存管理-导出
 * /expert/export
 */
export function inventoryExport(data?: any) {
  return defHttp.post(
    {
      url: '/drugInfo/inventoryExport',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );
}
