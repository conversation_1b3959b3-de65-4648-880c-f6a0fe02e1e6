import { defHttp } from '@ft/request';

/**
 * @description 咨询问诊 - 排班管理 - 班别管理分页
 * /consultationClassManage/getPage
 *
 */
export interface IClassPage {
  className: string;
  classTime: string;
  createTime: string;
  createUser: string;
  deleteFlag: number;
  id: string;
  orgId: string;
  orgName: string;
  status: number;
  updateTime: string;
  updateUser: string;
}
export const getClassPage = (data) =>
  defHttp.post<IClassPage[]>({
    url: '/consultationClassManage/getPage',
    data,
  });
/**
 * @description 咨询问诊 - 排班管理 - 班别管理不分页
 * /consultationClassManage/getList/{orgId}
 */
export const getClassList = (orgId?: string) =>
  defHttp.post<IClassPage[]>({
    url: `/consultationClassManage/getList/${orgId}`,
  });

/**
 * @description 咨询问诊 - 排班管理 - 新增班别
 * /consultationClassManage/save
 */
export const addConsultationClassManage = (data) =>
  defHttp.post({
    url: '/consultationClassManage/save',
    data,
  });

/**
 * @description 咨询问诊 - 排班管理 - 编辑班别
 * /consultationClassManage/edit
 */
export const editConsultationClassManage = (data) =>
  defHttp.post({
    url: '/consultationClassManage/edit',
    data,
  });

/**
 * @description 咨询问诊 - 排班管理 - 编辑班别状态
 * /consultationClassManage/editStatus/{id}
 */
export const editClassStatus = (id: string) =>
  defHttp.post({
    url: `/consultationClassManage/editStatus/${id}`,
  });
/**
 * @description 咨询问诊 - 排班管理 - 删除班别
 * /consultationClassManage/delete/{id}
 */
export const deleteConsultationClassManage = (id: string) =>
  defHttp.post({
    url: `/consultationClassManage/delete/${id}`,
  });
