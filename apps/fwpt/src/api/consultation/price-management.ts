import { defHttp } from '@ft/request';

/**
 * @description 分页查询定价列表
 * /consultationPrice/queryPage
 */
export const getConsultationPricePage = (data?: any) => {
  return defHttp.post({ url: '/consultationPrice/queryPage', data });
};

/**
 * @description 新增定价
 * /consultationPrice/add
 */
export const addConsultationPrice = (data?: any) => {
  return defHttp.post({ url: '/consultationPrice/add', data });
};

/**
 * @description 修改定价
 * /consultationPrice/update
 * */
export const updateConsultationPrice = (data?: any) => {
  return defHttp.post({ url: '/consultationPrice/update', data });
};

/**
 * @description 删除定价
 * /consultationPrice/delete
 * */
export const deleteConsultationPrice = (id: any) => {
  return defHttp.post({ url: '/consultationPrice/delete?id=' + id });
};

/**
 * @description 查询专家分组定价
 * /consultationPrice/queryByCondition
 * */
export const queryByCondition = (data?: any) => {
  return defHttp.post({ url: '/consultationPrice/queryByCondition', data });
};

/**
 * @description 查询所有专家分组列表(category应用分类不传查所有)
 * /consultationPrice/queryExpertGroup
 * */
export const queryExpertGroup = (category?: string) => {
  return defHttp.get({ url: '/consultationPrice/queryExpertGroup', params: { category } });
};
