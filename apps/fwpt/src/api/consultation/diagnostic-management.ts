import { defHttp } from '@ft/request';
/**
 * @description 传染病诊断-分页查询
 * /diagnosis/queryPage
 */
export const queryDiagnosisPage = (data: any) => {
  return defHttp.post({
    url: '/diagnosis/queryPage',
    data,
  });
};
/**
 * @description 传染病诊断-新增
 * /diagnosis/add
 */
export const addDiagnosis = (data: any) => {
  return defHttp.post({
    url: '/diagnosis/add',
    data,
  });
};
/**
 * @description 传染病诊断-编辑
 * /diagnosis/edit
 */
export const editDiagnosis = (data: any) => {
  return defHttp.post({
    url: '/diagnosis/edit',
    data,
  });
};
/**
 * @description 传染病诊断-同步
 * /diagnosis/syn
 */
export const synDiagnosis = () => {
  return defHttp.get({
    url: '/diagnosis/syn',
  });
};
