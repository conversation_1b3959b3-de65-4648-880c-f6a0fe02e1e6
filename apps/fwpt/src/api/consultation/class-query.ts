import { defHttp } from '@ft/request';

/**
 * @description 咨询问诊 - 排班查询 - 获取专家组排班
 * /consultationExpertGroupClass/getList
 *
 */
export interface IConsultationExpertGroupClass {
  expertGroupId: string;
  expertGroupName: string;
  groupTypeId: string;
  groupTypeName: string;
  weekClassList: IWeekClassList[];
}
export interface IWeekClassList {
  classTime: string;
  className: string;
  classType: string;
  classTypeName: string;
  week: number;
  weekName: string;
}
export const getConsultationExpertGroupClassList = (data) =>
  defHttp.post({
    url: '/consultationExpertGroupClass/getList',
    data,
  });

/**
 * @description 咨询问诊 - 专家排班 - 清除排班 专家组排班集合不传
 * /consultationExpertGroupClass/clearClass
 */
export const clearClass = (data) =>
  defHttp.post({ url: '/consultationExpertGroupClass/clearClass', data });

/**
 * @description 咨询问诊 - 专家排班 - 新增-修改排班
 * /consultationExpertGroupClass/saveOrUpdate
 */
export const saveOrUpdateClass = (data) =>
  defHttp.post({ url: '/consultationExpertGroupClass/saveOrUpdate', data });
