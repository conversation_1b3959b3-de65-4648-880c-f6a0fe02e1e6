import { defHttp } from '@ft/request';

/**
 * @description 获取统计年度下拉列表
 * /consultationInfo/dropDown/getYearList
 * */
export const getYearList = () => {
  return defHttp.get({
    url: '/consultationInfo/dropDown/getYearList',
  });
};

/**
 * @description 获取统计季度下拉列表
 * /consultationInfo/dropDown/getQuarterList
 * */
export const getQuarterList = (year) => {
  return defHttp.get({
    url: '/consultationInfo/dropDown/getQuarterList',
    params: {
      year,
    },
  });
};

/**
 * @description 获取统计月度下拉列表
 * /consultationInfo/dropDown/getMonthList
 * */
export const getMonthList = (year, quarter) => {
  return defHttp.get({
    url: '/consultationInfo/dropDown/getMonthList',
    params: {
      year,
      quarter,
    },
  });
};

/**
 * @description 获取咨询机构下拉列表
 * /consultationInfo/dropDown/getConsultOrgList
 * */
export const getConsultOrgList = () => {
  return defHttp.get({
    url: '/consultationInfo/dropDown/getConsultOrgList',
  });
};
/**
 * @description 分页查询
 * /consultationInfo/page
 */
export const getConsultationInfoPage = (data?: any) => {
  return defHttp.post({
    url: '/consultationInfo/page',
    data,
  });
};

/**
 * @description 批量导出
 * /consultationInfo/exportExcel
 * */
export const consultationInfoExport = (data) =>
  defHttp.post(
    {
      url: '/consultationInfo/exportExcel',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );

/**
 * @description 订单基本信息
 * /consultationOrder/queryDetailByConsultId
 * */
export const getConsultationOrderDetail = (consultId: string) => {
  return defHttp.get({
    url: '/consultationOrder/queryDetailByConsultId',
    params: {
      consultId,
    },
  });
};

/**
 * @description 患者咨询信息详情
 * /consultationInfo/detail
 */
export const getConsultationInfoDetail = (id: string) => {
  return defHttp.get({
    url: '/consultationInfo/detail',
    params: {
      id,
    },
  });
};

/**
 * @description 根据咨询问诊ID获取电子处方列表
 * /electronicPrescription/queryListByConsultationId
 * */
export const getElectronicPrescriptionList = (consultationId: string) => {
  return defHttp.get({
    url: '/electronicPrescription/queryListByConsultationId',
    params: {
      consultationId,
    },
  });
};

/**
 * @description 根据咨询问诊ID获取聊天记录列表
 * /consultationChatRecord/queryListByConsultationId
 * */
export const getConsultationChatRecordList = (consultationId: string) => {
  return defHttp.get({
    url: '/consultationChatRecord/queryListByConsultationId',
    params: {
      consultationId,
    },
  });
};
