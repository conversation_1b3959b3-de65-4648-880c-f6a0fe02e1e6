import { defHttp } from '@ft/request';

/**
 * @description 咨询问诊统计
 * /consultationStatistics/count
 */
export const getConsultationStatisticsCount = (data?: any) => {
  return defHttp.post({ url: '/consultationStatistics/count', data });
};

/**
 * @description 咨询问诊-专家组详情
 * /consultationStatistics/detail
 * */
export const getConsultationStatisticsDetail = (data?: any) => {
  return defHttp.post({ url: '/consultationStatistics/detail', data });
};

/**
 * @description 导出咨询问诊统计
 * /consultationStatistics/export
 */
export const consultationStatisticsExport = (data?: any) => {
  return defHttp.post(
    {
      url: '/consultationStatistics/export',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * @description 分页查询咨询问诊接诊列表
 * /consultationStatistics/queryPage
 */
export const getConsultationStatisticsPage = (data?: any) => {
  return defHttp.post({ url: '/consultationStatistics/queryPage', data });
};

/**
 * @description 分页查询咨询问诊接诊列表(医生维度)
 * /consultationStatistics/consultationPageOfDoctor
 */
export const getConsultationStatisticsConsultationPageOfDoctor = (data?: any) => {
  return defHttp.post({ url: '/consultationStatistics/consultationPageOfDoctor', data });
};

/**
 * @description 满意度评分统计
 * /consultationStatistics/markCount
 */
export const getConsultationStatisticsMarkCount = (data?: any) => {
  return defHttp.post({ url: '/consultationStatistics/markCount', data });
};

/**
 * @description 导出满意度评分统计
 * /consultationStatistics/markExport
 */
export const consultationStatisticsMarkExport = (data?: any) => {
  return defHttp.post(
    {
      url: '/consultationStatistics/markExport',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * @description 满意度评分统计-专家组详情
 * /consultationStatistics/markDetail
 * */
export const getConsultationStatisticsMarkDetail = (data?: any) => {
  return defHttp.post({ url: '/consultationStatistics/markDetail', data });
};
