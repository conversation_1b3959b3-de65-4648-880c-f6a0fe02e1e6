import { defHttp } from '@ft/request';

/**
 * @description 分页查询
 * /consultationEvaluateTemplate/page
 * */
export const getConsultationEvaluateTemplatePage = (data?: any) => {
  return defHttp.post({
    url: '/consultationEvaluateTemplate/page',
    data,
  });
};

/**
 * @description 查看详情
 * /consultationEvaluateTemplate/detail
 * */
export const getConsultationEvaluateTemplateDetail = (id: string) => {
  return defHttp.get({
    url: `/consultationEvaluateTemplate/detail?id=${id}`,
  });
};

/**
 * @description 新增
 * /consultationEvaluateTemplate/save
 * */
export const addConsultationEvaluateTemplate = (data?: any) => {
  return defHttp.post({
    url: '/consultationEvaluateTemplate/save',
    data,
  });
};

/**
 * @description 更新
 * /consultationEvaluateTemplate/update
 * */
export const updateConsultationEvaluateTemplate = (data?: any) => {
  return defHttp.post({
    url: '/consultationEvaluateTemplate/update',
    data,
  });
};
/**
 * @description 删除
 * /consultationEvaluateTemplate/delete
 * */
export const deleteConsultationEvaluateTemplate = (id: string) => {
  return defHttp.delete({
    url: `/consultationEvaluateTemplate/delete?id=${id}`,
  });
};
