import { defHttp } from '@ft/request';

export interface ICommonExpressionPage {
  id: string;
  ownerType: number;
  ownerTypeCode: string;
  replyContent: string;
  sort: number;
}

/**
 * @description 常用语模板-分页查询
 * /commonExpression/queryPage
 */
export const getCommonExpressionPage = (data?: any) => {
  return defHttp.post<ICommonExpressionPage[]>({
    url: '/commonExpression/queryPage',
    data,
  });
};

/**
 * @description 常用语模板-删除
 * /commonExpression/delete/{id}
 */
export const deleteCommonExpression = (id: string) => {
  return defHttp.delete({
    url: `/commonExpression/delete/${id}`,
  });
};

/**
 * @description 常用语模板-新增-编辑
 * /commonExpression/saveOrUpdate
 */
export const saveOrUpdateCommonExpression = (data?: any) => {
  return defHttp.post({
    url: '/commonExpression/saveOrUpdate',
    data,
  });
};
