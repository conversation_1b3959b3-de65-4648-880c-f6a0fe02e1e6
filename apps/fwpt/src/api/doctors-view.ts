export * from '../../../sjyy/src/api/doctors-view';

import { defHttp } from '@ft/request';

export interface IPatientRecord {
  /**入院时间*/
  admitHospitalTime: string;
  /**诊断疾病名称*/
  diagnoseDiseaseName: string;
  /**诊断科室名称*/
  diagnosisDept: string;
  /**诊断医生名称*/
  diagnosisDoctor: string;
  /**医疗卫生机构名称*/
  hospitalOrg: string;
  /**医疗卫生机构编码*/
  hospitalOrgCode: string;
  /**就诊id*/
  id: string;
  /**住院号*/
  inpatientNo: string;
  /**出院时间*/
  leaveTime: string;
  /**门急诊号*/
  outpatientNo: string;
  /**病人id*/
  patientId: string;
  /**患者姓名*/
  patientName: string;
  /**患者表主键，机构代码_系统代码_患者ID*/
  pkPatientInfo: string;
  /**治疗方案*/
  treatmentAdvice: string;
  /**门诊就诊时间*/
  visitDate: string;
  /**住院次数*/
  visitTimes: string;
  /**就诊类型名称*/
  visitType: string;
  /**就诊类型代码*/
  visitTypeCode: string;
  /**年龄*/
  age: string;
  /**性别*/
  sex: string;
}

/** 获取医生接诊门诊患者就诊记录 */
export const getPatientVisitRecordList = (data) =>
  defHttp.post<IPatientRecord[]>({
    url: '/generat/patientPageList',
    data,
  });

/**
 * 判断是否存在证明
 * /relieveIsolateProveRecord/exist/{outpatientNo}
 */
export const existProve = (outpatientNo: string) =>
  defHttp.post({
    url: `/relieveIsolateProveRecord/exist/${outpatientNo}`,
  });
