<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { computed, ref } from 'vue';
  import { useRequest } from '@ft/request';
  import type { IFollowTypeEditModalDT } from './data';
  import { editFormSchema } from './data';
  import { saveVisitType, updateVisitType } from '/@/api/follow-type';

  const emit = defineEmits(['register', 'sussess']);

  const mode = ref('add');
  const getTitle = computed(() => (mode.value === 'add' ? '新增随访类型' : '编辑随访类型'));

  const [registerForm, formAction] = useForm({
    labelWidth: 100,
    schemas: editFormSchema,
    showActionButtonGroup: false,
  });

  const [register, { closeModal }] = useModalInner<IFollowTypeEditModalDT>((data) => {
    mode.value = data.mode;
    if (!data.record) return;
    formAction.setFieldsValue(data.record);
  });

  const { loading, run: saveVisitTypeRun } = useRequest(
    (params) => (mode.value === 'add' ? saveVisitType(params) : updateVisitType(params)),
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess: () => {
        formAction.resetFields();
        closeModal();
        emit('sussess');
      },
    },
  );

  function onOk() {
    formAction.validate().then((values) => {
      saveVisitTypeRun(values);
    });
  }
</script>

<template>
  <BasicModal
    v-bind="$attrs"
    @register="register"
    :width="450"
    :title="getTitle"
    :ok-button-props="{ loading }"
    @ok="onOk"
    destroyOnClose
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<style scoped></style>
