import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import type { IVisitType } from '/@/api/follow-type';
import type { ModalEditDT } from '/@/types';

/**
 * 随访类型
 * 状态
 */
export const searchFormSchema: FormSchema[] = [
  {
    field: 'visitTypeName',
    label: '随访类型名称',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 0 },
        { label: '停用', value: 1 },
      ],
    },
    colProps: { span: 6 },
  },
];

/**
 *createTime	创建时间	string	
  createUser	创建人	string	
  deleteFlag	删除标识(0-未删除，1-删除)	integer	
  id	主键ID	string	
  remark	备注	string	
  scopeCode	随访执行范围	string	
  scopeName	随访执行范围名称	string	
  status	状态: 0 启用，1禁用	integer	
  updateTime	更新时间	string	
  updateUser	更新人	string	
  visitTypeName	随访类型名称	string
 */
export const columns: BasicColumn[] = [
  {
    title: '随访类型名称',
    dataIndex: 'visitTypeName',
    width: 200,
    align: 'left',
  },
  {
    title: '随访执行范围',
    dataIndex: 'scopeName',
    width: 200,
    align: 'left',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
    align: 'left',
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 200,
    align: 'left',
  },
  {
    title: '维护人',
    dataIndex: 'updateUser',
    width: 200,
    align: 'left',
  },
  {
    title: '维护时间',
    dataIndex: 'updateTime',
    width: 200,
    align: 'left',
  },
];

export type IFollowTypeEditModalDT = ModalEditDT<IVisitType>;

/**
  createTime	创建时间		false	
  string(date-time)
  createUser	创建人		false	
  string
  deleteFlag	删除标识(0-未删除，1-删除)		false	
  integer(int32)
  id	主键ID		false	
  string
  remark	备注		false	
  string
  scopeCode	随访执行范围		false	
  string
  scopeName	随访执行范围名称		false	
  string
  status	状态: 0 启用，1禁用		false	
  integer(int32)
  updateTime	更新时间		false	
  string(date-time)
  updateUser	更新人		false	
  string
  visitTypeName	随访类型名称		false	
  string
 */
export const editFormSchema: FormSchema[] = [
  {
    field: 'id',
    label: '主键ID',
    component: 'Input',
    show: false,
  },
  {
    label: '随访类型名称',
    field: 'visitTypeName',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '随访执行范围',
    field: 'scopeCode',
    component: 'Select',
    componentProps: ({ formModel }) => {
      return {
        options: [
          { label: '门诊', value: '1' },
          { label: '住院', value: '2' },
        ],
        onChange: (_, opt) => {
          // @ts-ignore
          formModel.scopeName = opt?.label;
        },
      };
    },
    required: true,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    field: 'scopeName',
    label: '随访执行范围名称',
    component: 'Input',
    show: false,
  },
  {
    label: '状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 0 },
        { label: '停用', value: 1 },
      ],
      allowClear: false,
    },
    defaultValue: 1,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
];
