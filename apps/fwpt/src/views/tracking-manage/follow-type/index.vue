<script setup lang="ts">
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { Switch } from 'ant-design-vue';
  import { useModal } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { columns, searchFormSchema } from './data';
  import EditModal from './EditModal.vue';
  import type { IVisitType } from '/@/api/follow-type';
  import { deleteVisitType, getVisitTypePage, updateVisitType } from '/@/api/follow-type';
  import type { RecordWithExtra } from '/@/types';

  const [registerTable, tableIns] = useTable({
    api: getVisitTypePage,
    columns,
    dataSource: [],
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas: searchFormSchema,
    },
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
    resizeHeightOffset: 10,
    afterFetch: (data) => {
      return data.map((item) => {
        return {
          ...item,
          loading: false,
        };
      });
    },
  });

  const [register, { openModal }] = useModal();

  function onAdd() {
    console.log('onAdd');
    openModal(true, {
      mode: 'add',
    });
  }

  function onEdit(record: IVisitType) {
    openModal(true, {
      mode: 'edit',
      record,
    });
  }

  const { run } = useRequest(deleteVisitType, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      tableIns.reload();
    },
  });

  function onRemove(record: IVisitType) {
    run(record.id);
  }

  function createActions(record: Recordable, _column): ActionItem[] {
    return [
      {
        label: '编辑',
        type: 'link',
        onClick: onEdit.bind(null, record),
      },
      {
        label: '删除',
        type: 'link',
        danger: true,
        popConfirm: {
          title: '确定删除吗？',
          placement: 'topRight',
          okButtonProps: { danger: true },
          confirm: onRemove.bind(null, record),
        },
      },
    ];
  }

  const { runAsync: updateVisitTypeRun } = useRequest(updateVisitType, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      tableIns.reload();
    },
  });
  function onSwitchStatus(checked, record: RecordWithExtra<IVisitType>) {
    record.loading = true;
    updateVisitTypeRun({
      ...record,
      status: checked,
    }).finally(() => {
      record.loading = false;
    });
  }
</script>

<template>
  <div class="h-full pr-4 pb-4">
    <div class="h-full">
      <BasicTable @register="registerTable">
        <template #headerTop>
          <span class="text-base font-bold">随访类型列表</span>
        </template>
        <template #tableTitle>
          <Button class="mt-4" type="primary" @click="onAdd">新增</Button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'status'">
            <Switch
              :loading="record.loading"
              :checkedValue="0"
              :unCheckedValue="1"
              v-model:checked="record[column.dataIndex]"
              @change="onSwitchStatus($event, record)"
            />
          </template>
          <template v-if="column.dataIndex === 'action'">
            <TableAction :divider="false" :actions="createActions(record, column)" />
          </template>
        </template>
      </BasicTable>
    </div>
    <EditModal @register="register" @sussess="tableIns.reload" />
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }
  }
</style>
