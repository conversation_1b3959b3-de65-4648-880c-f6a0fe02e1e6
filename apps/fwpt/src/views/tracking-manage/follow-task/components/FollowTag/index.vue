<script setup lang="ts">
  defineOptions({
    name: 'FollowTag',
  });

  withDefaults(
    defineProps<{
      color?: 'orange' | 'purple';
    }>(),
    {
      color: 'orange',
    },
  );
</script>

<template>
  <i
    :class="{
      'border-auxiliary-purple text-auxiliary-purple bg-[#FFECFF]': color === 'purple',
      'border-auxiliary-orange text-auxiliary-orange bg-[#FFF3EC]': color === 'orange',
    }"
    class="text-xs not-italic border-0.5 px-1.5 py-2px rounded-sm"
  >
    <slot></slot>
  </i>
</template>
