<script setup lang="ts">
  import { BasicTitle } from '@ft/internal/components/Basic';
  import { Button } from '@ft/internal/components/Button';
  import type { FormInstance } from 'ant-design-vue';
  import { Col, Form, FormItemRest, Input, Radio, RadioGroup, Row } from 'ant-design-vue';
  import { onMounted, ref } from 'vue';
  import { Icon } from '@ft/internal/components/Icon';
  import { useModal } from '@ft/internal/components/Modal';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { useRoutePageMode } from '@ft/internal/hooks/web/useRoutePageMode';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { useRequest } from '@ft/request';
  import ApiSelect from '@ft/internal/components/Form/src/components/ApiSelect.vue';
  import { DictEnum, getDictItemList } from '@ft/internal/api';
  import ObjectTable from '../../components/ObjectTable/index.vue';
  import TemplatePreviewModal from './TemplatePreviewModal.vue';
  import { TemplateTag, TemplateType } from './types';
  import type { FormStateType, TemplatePreViewDT } from './types';
  import { ComponentList, optionComponents } from './data';
  import {
    getTemplateDefaultData,
    getTemplateDetail,
    saveTemplate,
    updateTemplate,
  } from '/@/api/follow-template';

  const props = withDefaults(
    defineProps<{
      category?: TemplateType;
    }>(),
    {
      category: TemplateType.Template,
    },
  );

  const { isCreateMode, isEditMode } = useRoutePageMode();

  const id = useRouteQuery('templateId', '', { transform: String });
  const visitTypeId = useRouteQuery('visitTypeId', '', { transform: String });
  const visitTypeName = useRouteQuery('visitTypeName', '', { transform: String });

  const FormItem = Form.Item;

  const formState = ref<FormStateType>({
    id: '',
    templateTag: TemplateTag.Custom,
    templateType: props.category,
    visitTypeId: '',
    visitTypeName: '',
    templateName: '',
    deptId: '',
    deptName: '',
    visitDisease: '',
    visitObjectList: [],
    visitDiseaseName: '',
    visitContentList: [
      {
        id: '',
        dataModel: 2,
        dataName: '',
        dataType: 'Input',
        dataTypeName: '输入框',
        dataItemList: [],
      },
    ],
  });

  const { run: getTemplateDetailRun } = useRequest(getTemplateDetail, {
    manual: true,
    onSuccess: (data) => {
      formState.value = data;
    },
  });

  const { runAsync: getTemplateDefaultDataRun } = useRequest(getTemplateDefaultData, {
    manual: true,
  });

  onMounted(() => {
    if (isCreateMode) {
      formState.value.visitTypeId = visitTypeId.value;
      formState.value.visitTypeName = visitTypeName.value;
      getTemplateDefaultDataRun().then((data) => {
        formState.value.visitObjectList = data;
      });
    }

    if (isEditMode) {
      getTemplateDetailRun(id.value);
    }
  });

  const formRef = ref<FormInstance>();

  function onComponentChange(e, idx: number) {
    if (optionComponents.includes(formState.value.visitContentList?.[idx].dataType)) {
      formState.value.visitContentList[idx].dataItemList = [{ dataItem: '' }];
    } else {
      formState.value.visitContentList[idx].dataItemList = [];
    }
    const val = e.target.value;
    const comp = ComponentList.find((i) => i.component === val);
    formState.value.visitContentList[idx].dataTypeName = comp?.name;
  }

  function onAddFormItem() {
    formState.value.visitContentList.push({
      dataModel: 2,
      dataName: '',
      dataType: 'Input',
      dataTypeName: '输入框',
      dataItemList: [],
    });
  }

  const [registerPreview, { openModal }] = useModal();

  const { createMessage } = useMessage();
  function onPreview() {
    formRef.value
      ?.validate()
      .then(() => {
        openModal<TemplatePreViewDT>(true, {
          formState: formState.value,
          templateTag: TemplateTag.Custom,
          templateName: formState.value.templateName,
        });
      })
      .catch(() => {
        createMessage.error('请填写完整信息');
      });
  }

  const go = useGo();
  function onCancel() {
    go({
      name:
        props.category === TemplateType.Template ? 'FollowTaskTemplate' : 'FollowTaskQuestionnaire',
    });
  }

  const { loading, run: saveTemplateRun } = useRequest(
    (parmas) => (isCreateMode ? saveTemplate(parmas) : updateTemplate(parmas)),
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess: () => {
        onCancel();
      },
    },
  );
  function onSave() {
    formRef.value
      ?.validate()
      .then(() => {
        console.log(`formState.value`, formState.value);
        saveTemplateRun(formState.value);
      })
      .catch(() => {
        createMessage.error('请检查表单信息');
      });
  }

  function getDiseaseList() {
    return getDictItemList(DictEnum.INFECTIOUS_DISEASE);
  }
</script>

<template>
  <div class="pr-4 h-full">
    <div class="bg-white rounded-t flex flex-col h-full">
      <div class="flex p-4 items-center justify-between border-b border-[#EDEEF0]">
        <span class="text-base font-bold">
          {{ isEditMode ? '编辑' : '新增'
          }}{{ category === TemplateType.Questionnaire ? '问卷' : '' }}模板
        </span>
        <div class="actions-group flex gap-2">
          <Button @click="onCancel">取消</Button>
          <Button @click="onPreview">预览</Button>
          <Button :loading="loading" @click="onSave" type="primary">保存</Button>
        </div>
      </div>
      <div class="page-body p-4 flex-1 basis-0 min-h-0 of-y-auto pb-8">
        <Form ref="formRef" :model="formState" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
          <BasicTitle class="mb-3" span normal>模板基本信息</BasicTitle>
          <FormItem v-show="false" label="id" name="id">
            <Input disabled placeholder="请输入" v-model:value="formState.id" />
          </FormItem>
          <Row>
            <Col :span="6">
              <FormItem label="随访类型" name="visitTypeName">
                <Input disabled placeholder="请输入" v-model:value="formState.visitTypeName" />
              </FormItem>
            </Col>
            <Col :span="6">
              <FormItem
                :label="`${category === TemplateType.Template ? '模板' : '问卷'}名称`"
                name="templateName"
                required
              >
                <Input placeholder="请输入" v-model:value="formState.templateName" />
              </FormItem>
            </Col>

            <Col :span="6">
              <FormItem label="随访病种" name="visitDisease">
                <ApiSelect
                  :api="getDiseaseList"
                  labelField="dictItemName"
                  valueField="dictItemCode"
                  placeholder="请选择"
                  v-model:value="formState.visitDisease"
                />
              </FormItem>
            </Col>
          </Row>

          <BasicTitle class="mb-3" span normal>
            {{ category === TemplateType.Template ? '随访' : '问卷' }}对象信息
          </BasicTitle>
          <Row>
            <Col :span="24">
              <FormItem :wrapper-col="{ span: 24 }" label="" name="visitObjectList">
                <FormItemRest>
                  <ObjectTable v-model:value="formState.visitObjectList" />
                </FormItemRest>
              </FormItem>
            </Col>
          </Row>
          <BasicTitle class="mb-3" span normal
            >{{ category === TemplateType.Template ? '随访' : '问卷' }}内容
          </BasicTitle>
          <Row>
            <Col :span="24">
              <div
                class="bg-[#F5F7FA] p-4 mb-3"
                v-for="(item, idx) in formState.visitContentList"
                :key="idx"
              >
                <FormItem v-show="false" label="id" :name="['visitContentList', idx, 'id']">
                  <Input disabled placeholder="请输入" v-model:value="item.id" />
                </FormItem>
                <div class="field mb-4">
                  <FormItem
                    :label-col="{ span: 2 }"
                    label="数据名称"
                    required
                    :name="['visitContentList', idx, 'dataName']"
                  >
                    <Input class="!w-50" placeholder="请输入" v-model:value="item.dataName" />
                    <span
                      v-if="idx !== 0"
                      class="ml-2 text-info-text-color hover:text-auxiliary-red cursor-pointer"
                      @click="formState.visitContentList.splice(idx, 1)"
                    >
                      <Icon :size="18" icon="ant-design:delete-outlined" />
                    </span>
                  </FormItem>
                </div>
                <div class="field">
                  <FormItem
                    :label-col="{ span: 2 }"
                    label="数据类型"
                    required
                    :name="['visitContentList', idx, 'dataType']"
                  >
                    <RadioGroup
                      v-model:value="item.dataType"
                      @change="onComponentChange($event, idx)"
                    >
                      <Radio value="Input">输入框</Radio>
                      <Radio value="Select">下拉选择框</Radio>
                      <Radio value="RadioGroup">单选</Radio>
                      <Radio value="CheckboxGroup">多选</Radio>
                      <Radio value="DatePicker">日期</Radio>
                    </RadioGroup>
                  </FormItem>
                </div>
                <div
                  class="option-wrap p-4 mt-2 bg-white"
                  v-if="optionComponents.includes(item.dataType)"
                >
                  <div
                    class="mb-3"
                    v-for="(option, optionIndex) in item.dataItemList"
                    :key="optionIndex"
                  >
                    <FormItem
                      :label-col="{ span: 2 }"
                      :label="`数据选项${optionIndex + 1}`"
                      required
                      :name="['visitContentList', idx, 'dataItemList', optionIndex, 'dataItem']"
                    >
                      <Input class="!w-50" placeholder="请输入" v-model:value="option.dataItem" />
                      <span
                        v-if="optionIndex !== 0"
                        class="ml-2 hover:text-auxiliary-red cursor-pointer"
                        @click="item.dataItemList?.splice(optionIndex, 1)"
                      >
                        <Icon :size="18" icon="ant-design:delete-outlined" />
                      </span>
                    </FormItem>
                  </div>
                  <div>
                    <Button
                      class="!px-0"
                      type="link"
                      pre-icon="ant-design:plus-circle-outlined"
                      @click="item.dataItemList?.push({ dataItem: '' })"
                    >
                      添加选项
                    </Button>
                  </div>
                </div>
              </div>
              <div>
                <Button
                  class="!px-0 !text-primary-color !border-primary-color"
                  type="dashed"
                  pre-icon="ant-design:plus-circle-outlined"
                  @click="onAddFormItem"
                  block
                >
                  添加数据项
                </Button>
              </div>
            </Col>
          </Row>
        </Form>
      </div>
    </div>
    <TemplatePreviewModal :category="category" @register="registerPreview" />
  </div>
</template>
<style scoped></style>
