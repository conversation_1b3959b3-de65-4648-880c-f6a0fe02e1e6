import type { IFollowTemplate } from '/@/api/follow-template';

export interface DataItem {
  field: string;
  label: string;
  component: 'Input' | 'Select' | 'RadioGroup' | 'CheckboxGroup' | 'DatePicker';
  options: { item: string }[];
}

export type FormStateType = IFollowTemplate;

export enum TemplateTag {
  /** 自定义模板 */
  Custom,
  /** 系统模板 */
  BuiltIn,
}

// 模板类型：1 随访模板，2 随访问卷模板
export enum TemplateType {
  /** 随访模板 */
  Template = 1,
  /** 问卷模板 */
  Questionnaire,
}

export type TemplatePreViewDT = {
  formState?: FormStateType;
  templateTag: TemplateTag;
  id?: string;
  templateName: string;
};

interface ModuleItem {
  moduleName: string;
  moduleContent: ModuleContentItem[];
}

interface ModuleContentItem {
  isInput: boolean;
  isSelect: boolean;
  isInputTextArea: boolean;
  label: string;
  options: { item: string }[];
}

export interface ExportWordDT {
  templateName: string;
  moduleList: ModuleItem[];
}
