<script setup lang="ts">
  import type { FormSchema } from '@ft/internal/components/Form';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { computed, ref } from 'vue';
  import { BasicTitle } from '@ft/internal/components/Basic';
  import { useRequest } from '@ft/request';
  import { useToggle } from '@vueuse/core';
  import { Button } from '@ft/internal/components/Button';
  import { exportWordFromTemplate } from '@ft/internal/utils/export/exportWord';
  import type { ExportWordDT, TemplatePreViewDT } from './types';
  import { TemplateTag, TemplateType } from './types';
  import { genSchemas, schemasFlatToLevel } from './utils';
  import { BuiltInTemplateMap } from './data';
  import { getTemplateDetail } from '/@/api/follow-template';

  const props = withDefaults(
    defineProps<{
      category?: TemplateType;
      exportWord?: boolean;
    }>(),
    {
      category: TemplateType.Template,
      exportWord: false,
    },
  );

  defineEmits(['register']);

  const templateName = ref('');
  const templateTag = ref<TemplateTag>(TemplateTag.Custom);

  const getModalTitle = computed(() => {
    return templateTag.value === TemplateTag.Custom ? '自定义模板预览' : '模板预览';
  });
  const getModalWidth = computed(() => {
    return templateTag.value === TemplateTag.Custom ? '800px' : '85%';
  });

  const [registerForm, formAction] = useForm({
    showActionButtonGroup: false,
    labelWidth: 150,
  });

  const { runAsync: getTemplateDetailRun } = useRequest(getTemplateDetail, {
    manual: true,
  });

  const templateSchemas = ref<FormSchema[]>();
  const [register] = useModalInner<TemplatePreViewDT>((data) => {
    templateTag.value = data.templateTag;
    if (data.templateTag === TemplateTag.BuiltIn) {
      const schemas = BuiltInTemplateMap[data.templateName];
      schemas && formAction.resetSchema(schemas);
      templateName.value = data.templateName;
      templateSchemas.value = schemas;

      return;
    }

    if (data.templateTag === TemplateTag.Custom && data.id) {
      templateName.value = data.templateName;
      getTemplateDetailRun(data.id).then((result) => {
        const schemas = genSchemas({
          category: props.category,
          object: result.visitObjectList,
          content: result.visitContentList,
        });
        formAction.resetSchema(schemas);
        templateSchemas.value = schemas;
      });
      return;
    }

    if (!data.formState) return;
    templateName.value = data.formState.templateName;
    const schemas = genSchemas({
      category: props.category,
      object: data.formState.visitObjectList,
      content: data.formState.visitContentList,
    });
    formAction.resetSchema(schemas);
  });

  function handleOk() {
    console.log('ok');
  }

  const [exportWordLoading, setExportWordLoading] = useToggle(false);
  async function handleExportWord() {
    try {
      setExportWordLoading(true);

      // 1. 获取扁平化的schema结构
      const flatSchemas = schemasFlatToLevel(templateSchemas.value);

      // 2. 获取选项映射
      const builtInTemplateOptionsMap = await collectOptionsFromApis();

      // 3. 构建模板数据
      const templateData = buildExportTemplateData(flatSchemas, builtInTemplateOptionsMap);

      // 4. 执行导出
      await exportWordFromTemplate(
        `${import.meta.env.VITE_PUBLIC_PATH}/templates/template.docx`,
        templateData,
        `${templateName.value}.docx`,
      );
    } catch (error) {
      console.error('导出Word文档失败:', error);
    } finally {
      setExportWordLoading(false);
    }
  }

  /**
   * 从API组件中收集选项数据
   */
  async function collectOptionsFromApis(): Promise<Record<string, any>> {
    if (!templateSchemas.value) return {};

    // 收集所有包含API的组件
    const apis = templateSchemas.value.reduce<Record<string, () => Promise<any>>>((acc, schema) => {
      // @ts-ignore
      if (schema.componentProps?.api) {
        // @ts-ignore
        acc[schema.field] = schema.componentProps.api;
      }
      return acc;
    }, {});

    // 调用API并构建选项映射
    const optionsMap = await Object.entries(apis).reduce(async (accPromise, [key, apiFunc]) => {
      const acc = await accPromise;
      if (!apiFunc) return acc;

      try {
        const result = await apiFunc();
        const options = result.map((item: any) => ({
          item: item.dictItemName,
        }));
        acc[key] = options;
      } catch (err) {
        console.error(`获取字段${key}的选项数据失败:`, err);
        acc[key] = [];
      }

      return acc;
    }, Promise.resolve({} as Record<string, any>));

    return optionsMap;
  }

  /**
   * 构建导出Word所需的模板数据
   */
  function buildExportTemplateData(
    flatSchemas: any[],
    optionsMap: Record<string, any>,
  ): ExportWordDT {
    return {
      templateName: templateName.value,
      moduleList: flatSchemas.map((schema) => ({
        moduleName: schema.label,
        moduleContent: schema.children.map((child: any) => {
          // 判断组件类型
          const isInput = ['Input', 'DatePicker'].includes(child.component);
          const isSelect = [
            'ApiSelect',
            'Select',
            'RadioGroup',
            'ApiRadioGroup',
            'CheckboxGroup',
            'ApiCheckboxGroup',
          ].includes(child.component);
          const isInputTextArea = child.component === 'InputTextArea';

          // 处理选项数据
          let options = [];
          if (optionsMap[child.field]) {
            options = optionsMap[child.field];
          } else if (child.componentProps?.options) {
            options = child.componentProps.options.map((item: any) => ({
              item: item.label,
            }));
          }

          return {
            label: child.label,
            isInput,
            isSelect,
            isInputTextArea,
            options,
          };
        }),
      })),
    };
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="getModalTitle"
    :can-fullscreen="false"
    :width="getModalWidth"
    :min-height="650"
    :height="650"
    @register="register"
    @ok="handleOk"
    ok-text="保存"
    :footer="null"
  >
    <div class="text-center text-2xl py-3 mb-3 relative">
      <div v-if="exportWord" class="absolute top-0 right-0">
        <Button :loading="exportWordLoading" ghost type="primary" @click="handleExportWord"
          >导出</Button
        >
      </div>
      {{ templateName }}
    </div>
    <BasicForm class="template-preview-form" @register="registerForm">
      <template #moduleName="{ schema }">
        <BasicTitle span>{{ schema?.label }}</BasicTitle>
      </template>
    </BasicForm>
  </BasicModal>
</template>
<style lang="less" scoped></style>
