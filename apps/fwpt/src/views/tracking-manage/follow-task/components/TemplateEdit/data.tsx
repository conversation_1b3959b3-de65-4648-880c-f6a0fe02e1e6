import { DictEnum, getDictItemList } from '@ft/internal/api';
import type { FormSchema } from '@ft/internal/components/Form';
import { useUserStoreWithOut } from '@ft/internal/store/modules/user';
import dayjs from 'dayjs';

const userStore = useUserStoreWithOut();

export const ComponentList = [
  {
    component: 'Input',
    name: '输入框',
  },
  {
    component: 'Select',
    name: '下拉选择框',
    optional: true,
  },
  {
    component: 'RadioGroup',
    name: '单选',
    optional: true,
    group: true,
  },
  {
    component: 'CheckboxGroup',
    name: '多选',
    optional: true,
    group: true,
  },
  {
    component: 'DatePicker',
    name: '日期',
  },
];

export const optionComponents = ComponentList.filter((i) => i.optional).map((i) => i.component);
export const groupComponents = ComponentList.filter((i) => i.group).map((i) => i.component);
/**
 * @description 肺结核患者随访服务记录表模板
 */
export const FollowTuberculosisTemplate: FormSchema[] = [
  {
    field: 'isEdit',
    component: 'Input',
    label: '',
    ifShow: false,
  },
  {
    field: 'moduleName1',
    component: 'Input',
    label: '患者基本信息',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  {
    field: 'patientName',
    component: 'Input',
    label: '患者姓名',
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
    required: true,
  },
  // {
  //   field: 'recordNo',
  //   component: 'Input',
  //   label: '登记号',
  //   colProps: { span: 6 },
  //   itemProps: { wrapperCol: { span: 14 } },
  // },
  {
    field: 'inId',
    component: 'Input',
    label: '患者住院号',
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
    required: true,
  },
  {
    field: 'dischargeDate',
    label: '出院日期',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      style: { width: '100%' },
    },
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
    required: true,
  },
  {
    field: 'contactOnePhone',
    component: 'Input',
    label: '联系电话1',
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
    required: true,
    rules: [
      {
        pattern: /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
        message: '请输入正确的联系电话',
      },
    ],
  },
  {
    field: 'contactTwoPhone',
    component: 'Input',
    label: '联系电话2',
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
    rules: [
      {
        pattern: /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
        message: '请输入正确的联系电话',
      },
    ],
  },
  {
    field: 'address',
    component: 'Input',
    label: '地址',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'moduleName1',
    component: 'Input',
    label: '随访内容',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  {
    field: 'visitTime',
    label: '随访日期',
    componentProps: {
      style: { width: '100%' },
      valueFormat: 'YYYY-MM-DD',
    },
    component: 'DatePicker',
    defaultValue: dayjs().format('YYYY-MM-DD'),
    isHandleDateDefaultValue: false,
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'visitWay',
    label: '随访方式',
    component: 'RadioGroup',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
    componentProps: {
      options: [
        { label: '电话', value: 1 },
        { label: '家庭', value: 2 },
        { label: '门诊', value: 3 },
      ],
    },
    defaultValue: 1,
  },
  {
    field: 'moduleName1',
    label: '症状',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  {
    field: 'symptoms',
    label: '症状',
    component: 'RadioGroup',
    componentProps: {
      // 无症状 咳嗽 咳痰 胸痛 呼吸困难 其他
      options: [
        { label: '无症状', value: '无症状' },
        { label: '咳嗽', value: '咳嗽' },
        { label: '咳痰', value: '咳痰' },
        { label: '胸痛', value: '胸痛' },
        { label: '呼吸困难', value: '呼吸困难' },
        { label: '其他', value: '其他' },
      ],
    },
    colProps: { span: 12 },
  },
  {
    field: 'symptomsOther',
    label: '其他症状',
    disabledLabelWidth: true,
    component: 'Input',
    colProps: { span: 8 },
    ifShow: ({ model }) => model.symptoms === '其他',
  },
  // 查体
  {
    field: 'moduleName2',
    label: '查体',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  /**
   * 血压(mmHg)：
   * 体重(kg)：
   * 体质指数(BMI)(kg/m²)：
   * 心率(次/分钟)：
   * 其他：
   */
  {
    field: 'bloodPressure',
    label: '血压(mmHg)',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'weight',
    label: '体重(kg)',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'bmi',
    label: '体质指数(BMI)(kg/m²)',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'heartRate',
    label: '脉搏(次/分钟)',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'other',
    label: '其他',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },

  // 生活方式指导
  {
    field: 'moduleName3',
    label: '生活方式指导',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  /**
   * 饮食：
   * 戒烟：
   * 戒酒：
   * 遵守咳嗽礼仪：
   * 休息与活动：
   * 心理调整：
   * 遵医行为：
   */
  {
    field: 'diet',
    label: '饮食',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'quitSmoking',
    label: '戒烟',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.QUIT_SMOKING),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
    defaultValue: '无吸烟',
  },
  {
    field: 'quitDrinking',
    label: '戒酒',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.QUIT_DRINKING),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
    defaultValue: '无饮酒史',
  },
  {
    field: 'noDrugAbuse',
    label: '遵守咳嗽礼仪',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.COUGH_ETIQUETTE),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
    defaultValue: '是',
  },
  {
    field: 'restAndActivity',
    label: '休息与活动',
    component: 'Input',
    colProps: { span: 16 },
    itemProps: { wrapperCol: { span: 7 } },
  },
  {
    field: 'psychologicalAdjustment',
    label: '心理调整',
    component: 'RadioGroup',
    componentProps: {
      // 良好 一般  差
      options: [
        { label: '良好', value: '良好' },
        { label: '一般', value: '一般' },
        { label: '差', value: '差' },
      ],
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
    defaultValue: '一般',
  },
  {
    field: 'complianceBehavior',
    label: '遵医行为',
    component: 'RadioGroup',
    componentProps: {
      // 良好 一般  差
      options: [
        { label: '良好', value: '良好' },
        { label: '一般', value: '一般' },
        { label: '差', value: '差' },
      ],
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
    defaultValue: '一般',
  },
  // 近期异常辅助检查结果
  {
    field: 'moduleName4',
    label: '近期异常辅助检查结果',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  // 近期异常辅助检查结果
  {
    field: 'recentAbnormalResults',
    label: '近期异常辅助检查结果',
    labelWidth: 160,
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    itemProps: { wrapperCol: { span: 14 } },
    colProps: { span: 14 },
    // suffix: ({ model }) =>
    //   model.isEdit ? (
    //     <Button class="relative -top-8" type="primary">
    //       同步His
    //     </Button>
    //   ) : (
    //     ''
    //   ),
  },
  // 服药依从性
  {
    field: 'moduleName5',
    label: '服药依从性',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  // 服药依从性
  {
    field: 'medicationCompliance',
    label: '服药依从性',
    component: 'RadioGroup',
    componentProps: {
      // 规律 间断 不服药
      options: [
        { label: '规律', value: '规律' },
        { label: '间断', value: '间断' },
        { label: '不服药', value: '不服药' },
      ],
    },
    colProps: { span: 24 },
    defaultValue: '规律',
  },
  // 药物不良反应
  {
    field: 'moduleName6',
    label: '药物不良反应',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  // 药物不良反应
  {
    field: 'adverseDrugReactions',
    label: '药物不良反应',
    component: 'RadioGroup',
    componentProps: {
      // 无 有
      options: [
        { label: '无', value: '无' },
        { label: '有', value: '有' },
      ],
    },
    colProps: { span: 24 },
    defaultValue: '无',
  },
  // 用药情况
  {
    field: 'moduleName7',
    label: '用药情况',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  {
    field: 'drugDetail',
    label: '用药明细',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 5, maxRows: 5 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 8 } },
  },
  {
    field: 'dosage5',
    label: '',
    labelWidth: 70,
    component: 'InputNumber',
    show: false,
  },
  // VTE风险指导
  {
    field: 'moduleName8',
    label: 'VTE风险指导',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  // VTE患者风险：
  {
    field: 'vteRisk',
    label: 'VTE患者风险',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.VTE_RISK),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 6 } },
  },
  // 是否停用抗凝药物
  {
    field: 'isStopAnticoagulation',
    label: '是否停用抗凝药物',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.KNYW_FLAG),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  // 是否出血（使用抗凝药物者填写）
  {
    field: 'isUseAnticoagulation',
    label: '是否出血（使用抗凝药物者填写）',
    labelWidth: 300,
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.CX_FLAG),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 12 },
  },
  // PTE症状
  {
    field: 'pteSymptoms',
    label: 'PTE症状',
    component: 'ApiCheckboxGroup',
    componentProps: {
      api: () => getDictItemList(DictEnum.PTE_ZZ),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 24 },
  },
  // DVT症状
  {
    field: 'dvtSymptoms',
    label: 'DVT症状',
    component: 'ApiCheckboxGroup',
    componentProps: {
      api: () => getDictItemList(DictEnum.DVT_ZZ),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 24 },
  },
  // 近期凝血检查及下肢彩超等结果
  {
    field: 'bloodTestResult-label',
    label: '近期凝血检查及下肢彩超等结果',
    labelWidth: 200,
    component: 'InputTextArea',
    slot: 'bloodTestResult-slot',
    colProps: { span: 24 },
  },
  {
    field: 'bloodTestResult',
    label: '',
    component: 'InputTextArea',
    disabledLabelWidth: true,
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 }, colon: false },
  },
  // 转归
  {
    field: 'transferTo',
    label: '转归',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.ZG_FLAG),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 8 },
  },
  // 健康指导：
  {
    field: 'healthGuidance',
    label: '健康指导',
    component: 'ApiCheckboxGroup',
    componentProps: {
      api: () => getDictItemList(DictEnum.FJH_JKZD),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
    },
    colProps: { span: 24 },
  },
  // 其他
  {
    field: 'moduleName9',
    label: '其他',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  //是否电话随访
  {
    field: 'isPhoneFollow',
    label: '是否电话随访',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '是', value: '是' },
        { label: '否', value: '否' },
      ],
    },
    defaultValue: '否',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  // * 预约复查：
  {
    field: 'lastVisitTime',
    label: '预约复查',
    component: 'DatePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      style: { width: '100%' },
    },
    required: ({ model }) => model.isPhoneFollow === '是',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 6 } },
  },
  // 此次随访评价：
  // @ts-ignore
  {
    field: 'evaluation',
    label: '此次随访评价',
    component: 'RadioGroup',
    componentProps: {
      // 控制满意  控制不满意 不良反应 并发症
      options: [
        { label: '控制满意', value: '控制满意' },
        { label: '控制不满意', value: '控制不满意' },
        { label: '不良反应', value: '不良反应' },
        { label: '并发症', value: '并发症' },
      ],
    },
    colProps: { span: 24 },
  },
  // 备注：
  {
    field: 'remark',
    label: '备注',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.TEMPLATE_REMARKS),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 6 } },
  },
  // 随访人：
  {
    field: 'visitUser',
    label: '随访人',
    component: 'Input',
    colProps: { span: 24 },
    defaultValue: userStore.getUserInfo.employeeName,
    itemProps: { wrapperCol: { span: 6 } },
  },
];

/**
 * @description 病毒性肝炎患者随访服务记录表模板
 */
export const FollowHepatitisTemplate: FormSchema[] = [
  {
    field: 'isEdit',
    component: 'Input',
    label: '',
    ifShow: false,
  },
  {
    field: 'moduleName1',
    component: 'Input',
    label: '患者基本信息',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  {
    field: 'patientName',
    component: 'Input',
    label: '患者姓名',
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
    required: true,
  },
  // {
  //   field: 'recordNo',
  //   component: 'Input',
  //   label: '登记号',
  //   colProps: { span: 6 },
  //   itemProps: { wrapperCol: { span: 14 } },
  // },
  {
    field: 'inId',
    component: 'Input',
    label: '患者住院号',
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
    required: true,
  },
  {
    field: 'dischargeDate',
    label: '出院日期',
    component: 'DatePicker',
    componentProps: {
      style: { width: '100%' },
      valueFormat: 'YYYY-MM-DD',
    },
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
    required: true,
  },
  {
    field: 'moduleName1',
    component: 'Input',
    label: '随访内容',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  {
    field: 'visitTime',
    label: '随访日期',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      style: { width: '100%' },
    },
    defaultValue: dayjs().format('YYYY-MM-DD'),
    isHandleDateDefaultValue: false,
    colProps: { span: 8 },
  },
  {
    field: 'visitWay',
    label: '随访方式',
    component: 'RadioGroup',
    colProps: { span: 8 },
    componentProps: {
      options: [
        { label: '电话', value: 1 },
        { label: '家庭', value: 2 },
        { label: '门诊', value: 3 },
      ],
    },
  },
  {
    field: 'moduleName1',
    label: '症状',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  {
    field: 'symptoms',
    label: '症状',
    component: 'RadioGroup',
    componentProps: {
      // 乏力  食欲缺乏 厌油 肝区疼痛  恶心 呕吐  腹胀 腹泻巩膜和或皮肤黄染 尿色加深 皮肤瘙痒 粪便颜色变浅
      options: [
        { label: '乏力', value: '乏力' },
        { label: '食欲缺乏', value: '食欲缺乏' },
        { label: '厌油', value: '厌油' },
        { label: '肝区疼痛', value: '肝区疼痛' },
        { label: '恶心', value: '恶心' },
        { label: '呕吐', value: '呕吐' },
        { label: '腹胀', value: '腹胀' },
        { label: '腹泻', value: '腹泻' },
        { label: '巩膜和或皮肤黄染', value: '巩膜和或皮肤黄染' },
        { label: '尿色加深', value: '尿色加深' },
        { label: '皮肤瘙痒', value: '皮肤瘙痒' },
        { label: '粪便颜色变浅', value: '粪便颜色变浅' },
        { label: '其他', value: '其他' },
      ],
    },
    colProps: { span: 16 },
  },
  {
    field: 'symptomsOther',
    label: '其他症状',
    disabledLabelWidth: true,
    component: 'Input',
    colProps: { span: 8 },
    ifShow: ({ model }) => model.symptoms === '其他',
  },
  // 查体
  {
    field: 'moduleName2',
    label: '查体',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  /**
   * 血压(mmHg)：
   * 体重(kg)：
   * 体质指数(BMI)(kg/m²)：
   * 心率(次/分钟)：
   * 腹水：
   * 蜘蛛痣：
   * 其他：
   */
  {
    field: 'bloodPressure',
    label: '血压(mmHg)',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'weight',
    label: '体重(kg)',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'bmi',
    label: '体质指数(BMI)(kg/m²)',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'heartRate',
    label: '心率(次/分钟)',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'ascites',
    label: '腹水',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'spiderNevus',
    label: '蜘蛛痣',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'peOther',
    label: '其他',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  // 生活方式指导
  {
    field: 'moduleName3',
    label: '生活方式指导',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  /**
   * 饮食：
   * 戒烟：
   * 戒酒：
   * 不滥用药物
   * 休息与活动：
   * 心理调整：
   * 遵医行为：
   */
  {
    field: 'diet',
    label: '饮食',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'quitSmoking',
    label: '戒烟',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.QUIT_SMOKING),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
    defaultValue: '无吸烟',
  },
  {
    field: 'quitDrinking',
    label: '戒酒',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.QUIT_DRINKING),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
    defaultValue: '无饮酒史',
  },
  {
    field: 'noDrugAbuse',
    label: '不滥用药物',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.NO_SUBSTANCE_ABUSE),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
    defaultValue: '是',
  },
  {
    field: 'restAndActivity',
    label: '休息与活动',
    component: 'Input',
    colProps: { span: 16 },
    itemProps: { wrapperCol: { span: 7 } },
  },
  {
    field: 'psychologicalAdjustment',
    label: '心理调整',
    component: 'RadioGroup',
    componentProps: {
      // 良好 一般  差
      options: [
        { label: '良好', value: '良好' },
        { label: '一般', value: '一般' },
        { label: '差', value: '差' },
      ],
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
    defaultValue: '一般',
  },
  {
    field: 'complianceBehavior',
    label: '遵医行为',
    component: 'RadioGroup',
    componentProps: {
      // 良好 一般  差
      options: [
        { label: '良好', value: '良好' },
        { label: '一般', value: '一般' },
        { label: '差', value: '差' },
      ],
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
    defaultValue: '一般',
  },
  // 近期异常辅助检查结果
  {
    field: 'moduleName4',
    label: '近期异常辅助检查结果',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  // 近期异常辅助检查结果
  {
    field: 'recentAbnormalResults',
    label: '近期异常辅助检查结果',
    labelWidth: 160,
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    itemProps: { wrapperCol: { span: 14 } },
    colProps: { span: 14 },
    // suffix: ({ model }) =>
    //   model.isEdit ? (
    //     <Button class="relative -top-8" type="primary">
    //       同步His
    //     </Button>
    //   ) : (
    //     ''
    //   ),
  },
  // 服药依从性
  {
    field: 'moduleName5',
    label: '服药依从性',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  // 服药依从性
  {
    field: 'medicationCompliance',
    label: '服药依从性',
    component: 'RadioGroup',
    componentProps: {
      // 规律 间断 不服药
      options: [
        { label: '规律', value: '规律' },
        { label: '间断', value: '不规律' },
        { label: '不服药', value: '不服药' },
      ],
    },
    colProps: { span: 24 },
    defaultValue: '规律',
  },
  // 药物不良反应
  {
    field: 'moduleName6',
    label: '药物不良反应',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  // 药物不良反应
  {
    field: 'adverseDrugReactions',
    label: '药物不良反应',
    component: 'RadioGroup',
    componentProps: {
      // 无 有
      options: [
        { label: '无', value: '无' },
        { label: '有', value: '有' },
      ],
    },
    colProps: { span: 24 },
    defaultValue: '有',
  },
  // 用药情况
  {
    field: 'moduleName7',
    label: '用药情况',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  // 用药明细
  {
    field: 'drugDetail',
    label: '用药明细',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 5, maxRows: 5 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 8 } },
  },

  // VTE风险指导
  {
    field: 'moduleName8',
    label: 'VTE风险指导',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  // VTE患者风险：
  {
    field: 'vteRisk',
    label: 'VTE患者风险',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.VTE_RISK),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 6 } },
  },
  // 是否停用抗凝药物
  {
    field: 'isStopAnticoagulation',
    label: '是否停用抗凝药物',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.KNYW_FLAG),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  // 是否出血（使用抗凝药物者填写）
  {
    field: 'isUseAnticoagulation',
    label: '是否出血（使用抗凝药物者填写）',
    labelWidth: 300,
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.CX_FLAG),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 12 },
  },
  // PTE症状
  {
    field: 'pteSymptoms',
    label: 'PTE症状',
    component: 'ApiCheckboxGroup',
    componentProps: {
      api: () => getDictItemList(DictEnum.PTE_ZZ),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 24 },
  },
  // DVT症状
  {
    field: 'dvtSymptoms',
    label: 'DVT症状',
    component: 'ApiCheckboxGroup',
    componentProps: {
      api: () => getDictItemList(DictEnum.DVT_ZZ),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 24 },
  },
  // 近期凝血检查及下肢彩超等结果
  {
    field: 'bloodTestResult-label',
    label: '近期凝血检查及下肢彩超等结果',
    labelWidth: 200,
    component: 'InputTextArea',
    slot: 'bloodTestResult-slot',
    colProps: { span: 24 },
  },
  {
    field: 'bloodTestResult',
    label: '',
    component: 'InputTextArea',
    disabledLabelWidth: true,
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 }, colon: false },
  },
  // 转归
  {
    field: 'transferTo',
    label: '转归',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.ZG_FLAG),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 8 },
  },
  // 健康指导：
  {
    field: 'healthGuidance',
    label: '健康指导',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 8 } },
  },
  // 其他
  {
    field: 'moduleName9',
    label: '其他',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  //是否电话随访
  {
    field: 'isPhoneFollow',
    label: '是否电话随访',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '是', value: '是' },
        { label: '否', value: '否' },
      ],
    },
    defaultValue: '否',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  // * 预约复查：
  {
    field: 'lastVisitTime',
    label: '预约复查',
    component: 'DatePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      style: { width: '100%' },
    },
    required: ({ model }) => model.isPhoneFollow === '是',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 6 } },
  },
  // 此次随访评价：
  // @ts-ignore
  {
    field: 'evaluation',
    label: '此次随访评价',
    component: 'RadioGroup',
    componentProps: {
      // 控制满意  控制不满意 不良反应 并发症
      options: [
        { label: '控制满意', value: '控制满意' },
        { label: '控制不满意', value: '控制不满意' },
        { label: '不良反应', value: '不良反应' },
        { label: '并发症', value: '并发症' },
      ],
    },
    colProps: { span: 24 },
  },
  // 备注：
  {
    field: 'remark',
    label: '备注',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.TEMPLATE_REMARKS),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 6 } },
  },
  // 随访人：
  {
    field: 'visitUser',
    label: '随访人',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 6 } },
    defaultValue: userStore.getUserInfo.employeeName,
  },
];

export const FollowAIDSTemplate: FormSchema[] = [
  {
    field: 'isEdit',
    component: 'Input',
    label: '',
    ifShow: false,
  },
  {
    field: 'moduleName1',
    component: 'Input',
    label: '患者基本信息',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  {
    field: 'patientName',
    component: 'Input',
    label: '患者姓名',
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
    required: true,
  },
  // {
  //   field: 'recordNo',
  //   component: 'Input',
  //   label: '登记号',
  //   colProps: { span: 6 },
  //   itemProps: { wrapperCol: { span: 14 } },
  // },
  {
    field: 'inId',
    component: 'Input',
    label: '患者住院号',
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
    required: true,
  },
  {
    field: 'dischargeDate',
    label: '出院日期',
    component: 'DatePicker',
    componentProps: {
      style: { width: '100%' },
      valueFormat: 'YYYY-MM-DD',
    },
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
    required: true,
  },
  {
    field: 'moduleName1',
    component: 'Input',
    label: '随访内容',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  {
    field: 'visitTime',
    label: '随访日期',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      style: { width: '100%' },
    },
    defaultValue: dayjs().format('YYYY-MM-DD'),
    isHandleDateDefaultValue: false,
    colProps: { span: 8 },
  },
  {
    field: 'visitWay',
    label: '随访方式',
    component: 'RadioGroup',
    colProps: { span: 8 },
    componentProps: {
      options: [
        { label: '电话', value: 1 },
        { label: '家庭', value: 2 },
        { label: '门诊', value: 3 },
      ],
    },
  },
  {
    field: 'moduleName1',
    label: '症状',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  {
    field: 'symptoms',
    label: '症状',
    component: 'RadioGroup',
    componentProps: {
      // 1发热
      // 2咽痛
      // 3盗汗
      // 3皮疹
      // 4恶心
      // 5呕吐
      // 6腹泻
      // 8关节疼痛
      // 9淋巴结肿大
      // 10口腔溃疡

      options: [
        { label: '发热', value: '发热' },
        { label: '咽痛', value: '咽痛' },
        { label: '盗汗', value: '盗汗' },
        { label: '皮疹', value: '皮疹' },
        { label: '恶心', value: '恶心' },
        { label: '呕吐', value: '呕吐' },
        { label: '腹泻', value: '腹泻' },
        { label: '关节疼痛', value: '关节疼痛' },
        { label: '淋巴结肿大', value: '淋巴结肿大' },
        { label: '口腔溃疡', value: '口腔溃疡' },
        { label: '其他', value: '其他' },
      ],
    },
    colProps: { span: 16 },
  },
  {
    field: 'symptomsOther',
    label: '其他症状',
    disabledLabelWidth: true,
    component: 'Input',
    colProps: { span: 8 },
    ifShow: ({ model }) => model.symptoms === '其他',
  },
  // 查体
  {
    field: 'moduleName2',
    label: '查体',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  /**
   * 血压(mmHg)：
   * 体重(kg)：
   * 体质指数(BMI)(kg/m²)：
   * 心率(次/分钟)：
   * 腹水：
   * 蜘蛛痣：
   * 其他：
   */
  {
    field: 'bloodPressure',
    label: '血压(mmHg)',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'weight',
    label: '体重(kg)',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'bmi',
    label: '体质指数(BMI)(kg/m²)',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'heartRate',
    label: '心率(次/分钟)',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'peOther',
    label: '其他',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  // 生活方式指导
  {
    field: 'moduleName3',
    label: '生活方式指导',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  /**
   * 饮食：
   * 戒烟：
   * 戒酒：
   * 不滥用药物
   * 休息与活动：
   * 心理调整：
   * 遵医行为：
   */
  {
    field: 'diet',
    label: '饮食',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'quitSmoking',
    label: '戒烟',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.QUIT_SMOKING),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
    defaultValue: '无吸烟',
  },
  {
    field: 'quitDrinking',
    label: '戒酒',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.QUIT_DRINKING),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
    defaultValue: '无饮酒史',
  },
  {
    field: 'noDrugAbuse',
    label: '不滥用药物',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.NO_SUBSTANCE_ABUSE),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
    defaultValue: '是',
  },
  {
    field: 'restAndActivity',
    label: '休息与活动',
    component: 'Input',
    colProps: { span: 16 },
    itemProps: { wrapperCol: { span: 7 } },
  },
  {
    field: 'psychologicalAdjustment',
    label: '心理调整',
    component: 'RadioGroup',
    componentProps: {
      // 良好 一般  差
      options: [
        { label: '良好', value: '良好' },
        { label: '一般', value: '一般' },
        { label: '差', value: '差' },
      ],
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
    defaultValue: '一般',
  },
  {
    field: 'complianceBehavior',
    label: '遵医行为',
    component: 'RadioGroup',
    componentProps: {
      // 良好 一般  差
      options: [
        { label: '良好', value: '良好' },
        { label: '一般', value: '一般' },
        { label: '差', value: '差' },
      ],
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
    defaultValue: '一般',
  },
  // 近期CD4+T淋巴细胞计数
  {
    field: 'moduleName4',
    label: '近期CD4+T淋巴细胞计数',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  // 近期CD4+T淋巴细胞计数
  {
    field: 'recentCD4',
    label: '近期CD4+T淋巴细胞计数',
    labelWidth: 160,
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    itemProps: { wrapperCol: { span: 14 } },
    colProps: { span: 14 },
    // suffix: ({ model }) =>
    //   model.isEdit ? (
    //     <Button class="relative -top-8" type="primary">
    //       同步His
    //     </Button>
    //   ) : (
    //     ''
    //   ),
  },
  // 服药依从性
  {
    field: 'moduleName5',
    label: '服药依从性',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  // 服药依从性
  {
    field: 'medicationCompliance',
    label: '服药依从性',
    component: 'RadioGroup',
    componentProps: {
      // 规律 间断 不服药
      options: [
        { label: '规律', value: '规律' },
        { label: '间断', value: '间断' },
        { label: '不服药', value: '不服药' },
      ],
    },
    colProps: { span: 24 },
    defaultValue: '规律',
  },
  // 药物不良反应
  {
    field: 'moduleName6',
    label: '药物不良反应',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  // 药物不良反应
  {
    field: 'adverseDrugReactions',
    label: '药物不良反应',
    component: 'RadioGroup',
    componentProps: {
      // 无 有
      options: [
        { label: '无', value: '无' },
        { label: '有', value: '有' },
      ],
    },
    colProps: { span: 24 },
    defaultValue: '有',
  },
  // 用药情况
  {
    field: 'moduleName7',
    label: '用药情况',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  // 用药明细
  {
    field: 'drugDetail',
    label: '用药明细',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 5, maxRows: 5 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 8 } },
  },

  // VTE风险指导
  {
    field: 'moduleName8',
    label: 'VTE风险指导',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  // VTE患者风险：
  {
    field: 'vteRisk',
    label: 'VTE患者风险',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.VTE_RISK),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 6 } },
  },
  // 是否停用抗凝药物
  {
    field: 'isStopAnticoagulation',
    label: '是否停用抗凝药物',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.KNYW_FLAG),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  // 是否出血（使用抗凝药物者填写）
  {
    field: 'isUseAnticoagulation',
    label: '是否出血（使用抗凝药物者填写）',
    labelWidth: 300,
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.CX_FLAG),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 12 },
  },
  // PTE症状
  {
    field: 'pteSymptoms',
    label: 'PTE症状',
    component: 'ApiCheckboxGroup',
    componentProps: {
      api: () => getDictItemList(DictEnum.PTE_ZZ),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 24 },
  },
  // DVT症状
  {
    field: 'dvtSymptoms',
    label: 'DVT症状',
    component: 'ApiCheckboxGroup',
    componentProps: {
      api: () => getDictItemList(DictEnum.DVT_ZZ),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 24 },
  },
  // 近期凝血检查及下肢彩超等结果
  {
    field: 'bloodTestResult-label',
    label: '近期凝血检查及下肢彩超等结果',
    labelWidth: 200,
    component: 'InputTextArea',
    slot: 'bloodTestResult-slot',
    colProps: { span: 24 },
  },
  {
    field: 'bloodTestResult',
    label: '',
    component: 'InputTextArea',
    disabledLabelWidth: true,
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 }, colon: false },
  },
  // 转归
  {
    field: 'transferTo',
    label: '转归',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.ZG_FLAG),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 8 },
  },
  // 健康指导：
  {
    field: 'healthGuidance',
    label: '健康指导',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 8 } },
  },
  // 其他
  {
    field: 'moduleName9',
    label: '其他',
    component: 'Input',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  //是否电话随访
  {
    field: 'isPhoneFollow',
    label: '是否电话随访',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '是', value: '是' },
        { label: '否', value: '否' },
      ],
    },
    defaultValue: '否',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  // * 预约复查：
  {
    field: 'lastVisitTime',
    label: '预约复查',
    component: 'DatePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      style: { width: '100%' },
    },
    required: ({ model }) => model.isPhoneFollow === '是',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 6 } },
  },
  // 此次随访评价：
  // @ts-ignore
  {
    field: 'evaluation',
    label: '此次随访评价',
    component: 'RadioGroup',
    componentProps: {
      // 控制满意  控制不满意 不良反应 并发症
      options: [
        { label: '控制满意', value: '控制满意' },
        { label: '控制不满意', value: '控制不满意' },
        { label: '不良反应', value: '不良反应' },
        { label: '并发症', value: '并发症' },
      ],
    },
    colProps: { span: 24 },
  },
  // 备注：
  {
    field: 'remark',
    label: '备注',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.TEMPLATE_REMARKS),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 6 } },
  },
  // 随访人：
  {
    field: 'visitUser',
    label: '随访人',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 6 } },
    defaultValue: userStore.getUserInfo.employeeName,
  },
];

export const BuiltInTemplateMap = {
  病毒性肝炎患者随访服务记录表: FollowHepatitisTemplate,
  肺结核患者随访记录表: FollowTuberculosisTemplate,
  'HIV/AIDS患者随访服务记录表': FollowAIDSTemplate,
};
