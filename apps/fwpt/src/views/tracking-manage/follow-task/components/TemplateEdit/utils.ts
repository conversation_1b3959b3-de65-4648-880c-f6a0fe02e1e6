import type { FormSchema } from '@ft/internal/components/Form';
import { buildShortUUID } from '@ft/internal/utils/uuid';
import { groupComponents, optionComponents } from './data';
import type { FormStateType } from './types';
import { TemplateType } from './types';

const ObjectComponentMap = {
  input: 'Input',
  date: 'DatePicker',
};

export function genSchemas({
  category,
  object,
  content,
}: {
  category: TemplateType;
  object: FormStateType['visitObjectList'];
  content: FormStateType['visitContentList'];
}) {
  const basicInfoSchemas: FormSchema[] =
    object
      ?.filter((i) => i.isShow === 0)
      .sort((a, b) => a.sortNum - b.sortNum)
      .map((item, idx) => {
        const props: Recordable = {
          disabled: item.manualInput === 1,
          style: { width: '100%' },
        };
        if (item.dataType === 'date') {
          props.valueFormat = 'YYYY-MM-DD';
        }
        return {
          label: `${idx + 1}.${item.dataName}`,
          field: item?.dataKey || 'basicInfo' + idx,
          component: ObjectComponentMap[item.dataType],
          componentProps: props,
          required: item.isRequired === 0,
          colProps: { span: 16 },
        };
      }) || [];

  // @ts-ignore
  const contentSchemas: FormSchema[] = content.map((item, idx) => ({
    label: `${idx + 1}.${item.dataName}`,
    field: item.id || buildShortUUID(),
    component: item.dataType,
    componentProps: optionComponents.includes(item.dataType)
      ? {
          options:
            item.dataItemList?.map((item) => {
              return { label: item.dataItem, value: item.dataItem };
            }) ?? [],
        }
      : {
          style: { width: '100%' },
          ...(item.dataType === 'DatePicker'
            ? {
                format: 'YYYY-MM-DD',
                valueFormat: 'YYYY-MM-DD',
              }
            : {}),
        },
    colProps: { span: 24 },
    itemProps: {
      labelCol: { span: 4 },
      wrapperCol: groupComponents.includes(item.dataType) ? { span: 20 } : { span: 6 },
    },
  }));

  const schemas: FormSchema[] = [
    {
      field: 'moduleName1',
      label: '患者基本信息',
      component: 'Input',
      colSlot: 'moduleName',
      colProps: { span: 24 },
    },
    ...basicInfoSchemas,
    {
      field: 'moduleName2',
      label: category === TemplateType.Template ? '随访内容' : '问卷内容',
      component: 'Input',
      colSlot: 'moduleName',
      colProps: { span: 24 },
    },
    ...contentSchemas,
  ];
  return schemas;
}

type FormSchemaWithChildren = FormSchema & { children: FormSchema[] };

export function schemasFlatToLevel(schemas?: FormSchema[]) {
  if (!schemas) return [];
  const result: FormSchemaWithChildren[] = [];
  let currentModule: FormSchemaWithChildren | null = null;

  for (const [_, schema] of schemas.entries()) {
    if (currentModule && currentModule.children.find((item) => item.field === schema.field)) {
      continue;
    }
    if (schema.colSlot === 'moduleName') {
      if (currentModule) {
        result.push(currentModule);
      }
      currentModule = { ...schema, children: [] }; // 创建新的模块
    } else if (currentModule) {
      if (!schema.label && schema.colProps?.span === 24) {
        continue;
      } else {
        currentModule.children.push(schema); // 将当前 schema 添加为子级
      }
    }
  }

  if (currentModule) {
    result.push(currentModule); // 添加最后一个模块
  }

  return result;
}
