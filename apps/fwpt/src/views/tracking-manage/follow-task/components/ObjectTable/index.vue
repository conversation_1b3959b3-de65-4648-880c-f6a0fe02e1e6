<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { InputNumber, Switch } from 'ant-design-vue';
  import { toRaw } from 'vue';
  import { useInjectFormItemContext } from 'ant-design-vue/lib/form';
  import { SwitchField, columns } from './data';

  defineOptions({
    name: 'ObjectTable',
  });

  withDefaults(
    defineProps<{
      value?: Recordable[];
    }>(),
    {
      value: () => [],
    },
  );

  const emit = defineEmits<{
    'update:value': [value: Recordable[]];
    change: [value: Recordable[]];
  }>();

  const { onFieldChange, clearValidate } = useInjectFormItemContext();

  const [register, tableIns] = useTable({
    columns,
    size: 'small',
    canResize: false,
    pagination: false,
  });

  function onChange() {
    const data = tableIns.getDataSource();
    emit('update:value', toRaw(data));
    emit('change', toRaw(data));
    clearValidate();
    onFieldChange();
  }
</script>

<template>
  <div class="object-table-wrapper">
    <BasicTable :data-source="value" @register="register">
      <template #bodyCell="{ record, column }">
        <template v-if="SwitchField.includes(column.dataIndex)">
          <Switch
            :checkedValue="0"
            :unCheckedValue="1"
            v-model:checked="record[column.dataIndex]"
            @change="onChange"
          />
        </template>
        <template v-if="column.dataIndex === 'sortNum'">
          <InputNumber
            :min="0"
            :precision="0"
            :formatter="(value) => String(value).replace(/[^0-9]/g, '')"
            :parser="(value) => String(value).replace(/[^0-9]/g, '')"
            v-model:value="record[column.dataIndex]"
            @change="onChange"
          />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<style scoped></style>
