<script setup lang="ts">
  import { StyledList } from '@ft/components';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { computed, ref, watch } from 'vue';
  import { Button } from '@ft/internal/components/Button';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { useRequest } from '@ft/request';
  import type { IVisitType } from '/@/api/follow-type';
  import { getVisitTypeList } from '/@/api/follow-type';
  import type { IFollowVisitPlan } from '/@/api/follow-task';
  import {
    deleteFollowVisitPlan,
    getFollowVisitPlanPage,
    updateFollowVisitPlan,
  } from '/@/api/follow-task';
  import { Switch } from 'ant-design-vue';
  import { columns, searchSchema } from './data';
  import type { RecordWithExtra } from '/@/types';

  const { data } = useRequest(getVisitTypeList, {
    onSuccess: (data) => {
      activeKey.value = data[0].id || '';
      activeItem.value = data[0];
    },
  });

  const items = computed(() => {
    return data.value || [];
  });

  const activeKey = ref();
  const activeItem = ref<IVisitType>();

  const [register, tableIns] = useTable({
    api: getFollowVisitPlanPage,
    columns,
    useSearchForm: true,
    formConfig: {
      labelWidth: 80,
      schemas: searchSchema,
    },
    inset: true,
    immediate: false,
    resizeHeightOffset: 10,
    beforeFetch: (params) => {
      return {
        ...params,
        visitTypeId: activeKey.value,
      };
    },
    afterFetch: (data) => {
      return data.map((item) => {
        return {
          ...item,
          loading: false,
        };
      });
    },
    actionColumn: {
      title: '操作',
      width: 120,
      dataIndex: 'action',
    },
  });

  watch(activeItem, () => {
    tableIns.reload();
  });

  function createActions(record, _column): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: onEdit.bind(null, record),
      },
      {
        label: '删除',
        danger: true,
        popConfirm: {
          placement: 'topRight',
          title: '确认删除吗？',
          okButtonProps: { danger: true },
          confirm: onDelete.bind(null, record),
        },
      },
    ];
  }

  const go = useGo();
  function onAdd() {
    go({
      name: 'FollowTaskScheduledTaskEdit',
      query: {
        visitTypeId: activeKey.value,
        visitTypeName: activeItem.value?.visitTypeName,
        scopeName: activeItem.value?.scopeName,
      },
    });
  }

  function onEdit(record: IFollowVisitPlan) {
    go({
      name: 'FollowTaskScheduledTaskEdit',
      query: {
        mode: 'edit',
        planId: record.id,
      },
    });
  }

  const { run: deleteFollowVisitPlanRun } = useRequest(deleteFollowVisitPlan, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      tableIns.reload();
    },
  });

  function onDelete(record: IFollowVisitPlan) {
    deleteFollowVisitPlanRun(record.id);
  }

  const { runAsync: updateFollowVisitPlanRun } = useRequest(updateFollowVisitPlan, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      tableIns.reload();
    },
  });
  function onSwitchStatus(checked, record: RecordWithExtra<IFollowVisitPlan>) {
    record.loading = true;
    updateFollowVisitPlanRun({
      id: record.id,
      status: checked,
    }).finally(() => {
      record.loading = false;
    });
  }
</script>

<template>
  <div class="h-full pr-4 pb-4">
    <div class="h-full bg-white rounded rounded-lt-0 flex">
      <div class="p-4 border-r-1 border-[#EDEEF0] flex flex-col">
        <span class="text-base font-bold">随访类型</span>
        <div class="mt-4 flex-1 basis-0 of-y-auto">
          <StyledList
            :items="items"
            v-model="activeKey"
            v-model:value="activeItem"
            valueField="id"
            labelField="visitTypeName"
          />
        </div>
      </div>
      <div class="content flex-1 py-3 of-hidden">
        <BasicTable @register="register">
          <template #tableTitle>
            <Button type="primary" @click="onAdd">新增计划</Button>
          </template>

          <template #bodyCell="{ record, column }">
            <template v-if="column.dataIndex === 'status'">
              <Switch
                :loading="record.loading"
                :checkedValue="0"
                :unCheckedValue="1"
                v-model:checked="record[column.dataIndex]"
                @change="onSwitchStatus($event, record)"
              />
            </template>
            <template v-if="column.dataIndex === 'action'">
              <TableAction :divider="false" :actions="createActions(record, column)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container .ant-form {
      padding: 16px !important;
    }

    .vben-basic-table--inset .ant-table-wrapper {
      padding: 0 16px;
    }
  }
</style>
