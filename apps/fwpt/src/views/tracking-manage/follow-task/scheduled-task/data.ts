import { DictEnum, getDictItemList } from '@ft/internal/api';
import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { getTemplateList } from '/@/api/follow-template';
import type { ModalEditDT } from '/@/types';
import type { IFollowVisitTask } from '/@/api/follow-task';

export interface TaskEditModalDT extends ModalEditDT<IFollowVisitTask> {
  visitPlanId: string;
}
/**
 * 计划名称：
 * 计划状态：
 */
export const searchSchema: FormSchema[] = [
  {
    field: 'planName',
    label: '计划名称',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'status',
    label: '计划状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 0 },
        { label: '禁用', value: 1 },
      ],
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
];

export const columns: BasicColumn[] = [
  {
    title: '计划名称',
    dataIndex: 'planName',
    width: 200,
  },
  {
    title: '随访执行范围',
    dataIndex: 'scopeName',
    width: 240,
  },
  {
    title: '科室范围',
    dataIndex: 'deptScopeName',
    width: 200,
  },
  {
    title: '随访任务',
    dataIndex: 'visitTypeName',
    width: 200,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
  },
  {
    title: '计划状态',
    dataIndex: 'status',
    width: 200,
  },
  {
    title: '创建者',
    dataIndex: 'createUser',
    width: 200,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 200,
  },
];

/**
 * 随访类型
 * 随访执行范围：
 * * 计划名称：
 * 科室范围：
 * 备注：
 * 计划状态：
 */
export const EditSchema: FormSchema[] = [
  {
    field: 'id',
    component: 'Input',
    label: 'id',
    show: false,
  },
  {
    field: 'visitTypeId',
    label: '随访类型Id',
    component: 'Input',
    show: false,
  },
  {
    field: 'visitTypeName',
    label: '随访类型',
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    colProps: { span: 6 },
  },
  {
    field: 'scopeName',
    label: '随访执行范围',
    componentProps: {
      disabled: true,
    },
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'planName',
    label: '计划名称',
    component: 'Input',
    required: true,
    colProps: { span: 6 },
  },
  {
    field: 'deptScopeName',
    label: '科室范围',
    component: 'Input',
    show: false,
  },
  {
    field: 'deptScope',
    label: '科室范围',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.DEPT_TYPE),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        showSearch: true,
        optionFilterProp: 'label',
        onChange: (_, opt) => {
          formModel['deptScopeName'] = opt?.label;
        },
      };
    },
    colProps: { span: 6 },
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 3, maxRows: 3 },
    },
    colProps: { span: 12 },
    defaultValue: '',
  },
  {
    field: 'status',
    label: '计划状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 0 },
        { label: '禁用', value: 1 },
      ],
    },
    defaultValue: 0,
    colProps: { span: 6 },
  },
];

/**
 * 任务名称
 * 随访模板/问卷
 * 任务触发时间
 * 随访人
 * 任务状态
 * 维护人
 * 维护时间
 */
export const TaskColumns: BasicColumn[] = [
  {
    title: 'id',
    dataIndex: 'id',
    defaultHidden: true,
  },
  {
    title: '任务名称',
    dataIndex: 'taskName',
    width: 200,
  },
  {
    title: '随访模板/问卷',
    dataIndex: 'templateName',
    width: 200,
  },
  {
    title: '任务触发时间',
    dataIndex: 'taskFrequency',
    width: 180,
  },
  {
    title: '任务对象',
    dataIndex: 'taskObjectName',
    width: 100,
  },
  {
    title: '任务状态',
    dataIndex: 'taskStatus',
    width: 100,
  },
  {
    title: '维护人',
    dataIndex: 'updateUser',
    width: 100,
  },
  {
    title: '维护时间',
    dataIndex: 'updateTime',
    width: 180,
  },
];

/**
 * createTime	创建时间	string(date-time)	
createUser	创建人	string	
deleteFlag	删除标识(0-未删除，1-删除)	integer(int32)	
id	主键ID	string	
lastDay	触发任务离院天数（单位天）	integer(int32)	
loopDay	任务循环天数	integer(int32)	
loopNum	任务循环次数	integer(int32)	
taskFrequency	任务频率：1 单次 ，2 循环	integer(int32)	
taskName	任务名称	string	
taskObject	用户类型：1 医护，2 患者	integer(int32)	
taskStatus	状态: 0 启用，1禁用	integer(int32)	
updateTime	更新时间	string(date-time)	
updateUser	更新人	string	
visitPlanId	随访计划ID	string	
visitTemplateId	随访模板/问卷ID	string
 */
/**
 * * 任务名称：
 * * 随访模板/问卷：
 *  任务对象
 * * 任务触发时间：
 * 状态：
 */
export const TaskEditSchemas: FormSchema[] = [
  {
    field: 'id',
    component: 'Input',
    label: 'id',
    show: false,
  },
  {
    field: 'taskName',
    label: '任务名称',
    component: 'Input',
    required: true,
    colProps: { span: 22 },
  },
  // visitPlanId
  {
    field: 'visitPlanId',
    label: ' ',
    component: 'Input',
    show: false,
  },
  {
    field: 'visitTypeId',
    label: ' ',
    component: 'Input',
    ifShow: false,
  },
  {
    // templateName
    field: 'templateName',
    label: ' ',
    component: 'Input',
    show: false,
  },
  {
    field: 'taskObject',
    label: '任务对象',
    component: 'Select',
    componentProps: ({ formModel }) => {
      return {
        options: [
          { label: '护士', value: 1 },
          { label: '患者', value: 2 },
        ],
        onChange: () => {
          formModel['visitTemplateId'] = undefined;
        },
      };
    },
    required: true,
    colProps: { span: 22 },
  },
  {
    field: 'visitTemplateId',
    label: '随访模板/问卷',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => {
          if (!formModel.visitTypeId || !formModel.taskObject) {
            return Promise.resolve([]);
          }

          return getTemplateList({
            visitTypeId: formModel.visitTypeId,
            templateType: formModel.taskObject, // 	模板类型：1 随访模板-护士 随访问卷模板-患者
          });
        },
        labelField: 'templateName',
        valueField: 'id',
        showSearch: true,
        optionFilterProp: 'label',
        onChange: (_, opt) => {
          formModel['templateName'] = opt?.label;
        },
      };
    },
    required: true,
    colProps: { span: 22 },
  },

  // 任务患者 taskPatient
  {
    field: 'taskPatient',
    label: '任务患者',
    component: 'RadioGroup',
    required: true,
    componentProps: {
      options: [
        { label: '全部患者', value: 1 },
        { label: '部分患者', value: 2 },
      ],
    },
    defaultValue: 1,
    colProps: { span: 22 },
    ifShow: ({ model }) => model.taskObject === 2,
  },
  {
    // taskPatientList
    field: 'taskPatientList',
    label: '选择患者',
    component: 'Input',
    required: true,
    ifShow: ({ model }) => model.taskPatient === 2,
    slot: 'PatientSelect',
    colProps: { span: 22 },
  },
  {
    field: 'taskFrequency',
    label: '任务触发时间',
    component: 'RadioGroup',
    required: true,
    componentProps: {
      options: [
        { label: '单次', value: 1 },
        // { label: '循环', value: 2 },
      ],
    },
    defaultValue: 1,
    colProps: { span: 22 },
  },
  // 离院天数
  {
    field: 'lastDay',
    label: ' ',
    component: 'Input',
    slot: 'timeSetting',
    colProps: { span: 22 },
    defaultValue: 1,
  },
  // 循环间隔
  {
    field: 'loopDay',
    label: ' ',
    component: 'Input',
    show: false,
    defaultValue: 1,
    ifShow: ({ model }) => model.taskFrequency === 2,
  },
  // 循环次数
  {
    field: 'loopNum',
    label: ' ',
    component: 'Input',
    show: false,
    defaultValue: 1,
    ifShow: ({ model }) => model.taskFrequency === 2,
  },
  {
    field: 'taskStatus',
    label: '状态',
    component: 'Select',
    required: true,
    componentProps: {
      options: [
        { label: '启用', value: 0 },
        { label: '禁用', value: 1 },
      ],
      allowClear: false,
    },
    defaultValue: 0,
    colProps: { span: 22 },
  },
  // 维护人	维护时间
  {
    field: 'updateUser',
    label: '维护人',
    component: 'Input',
    show: false,
  },
  {
    field: 'updateTime',
    label: '维护时间',
    component: 'Input',
    show: false,
  },
];
