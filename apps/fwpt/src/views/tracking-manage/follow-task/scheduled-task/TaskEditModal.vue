<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import type { RegisterFn } from '@ft/internal/components/Modal';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { computed, ref } from 'vue';
  import { FormItemRest, InputNumber } from 'ant-design-vue';
  import { useRequest } from '@ft/request';
  import type { TaskEditModalDT } from './data';
  import { TaskEditSchemas } from './data';
  import { saveFollowVisitTask, updateFollowVisitTask } from '/@/api/follow-task';
  import PatientSelect from './PatientSelect.vue';

  const props = defineProps<{
    visitTypeId?: string;
    visitPlanId: string;
  }>();

  const emit = defineEmits<{
    register: Parameters<RegisterFn>;
    success: [];
  }>();

  const mode = ref<'add' | 'edit' | 'view'>('add');

  const getTitle = computed(() => {
    return mode.value === 'add' ? '添加任务' : '编辑任务';
  });

  const [registerForm, formAction] = useForm({
    labelWidth: 120,
    showActionButtonGroup: false,
    schemas: TaskEditSchemas,
  });

  const [register, { closeModal }] = useModalInner<TaskEditModalDT>((data) => {
    mode.value = data.mode;
    formAction.setFieldsValue({
      ...(data.record || {}),
      visitTypeId: props.visitTypeId,
      visitPlanId: props.visitPlanId,
    });
  });

  // saveFollowVisitTask updateFollowVisitTask
  const { run: saveFollowVisitTaskRun } = useRequest(
    (params) =>
      mode.value === 'add' ? saveFollowVisitTask(params) : updateFollowVisitTask(params),
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess: () => {
        emit('success');
        closeModal();
      },
    },
  );

  function onOk() {
    formAction.validate().then((values) => {
      saveFollowVisitTaskRun(values);
    });
  }
</script>

<template>
  <BasicModal width="600px" @register="register" destroy-on-close :title="getTitle" @ok="onOk">
    <BasicForm @register="registerForm">
      <template #PatientSelect="{ model, field }">
        <PatientSelect v-model:value="model[field]" />
      </template>
      <template #timeSetting="{ model, field }">
        <div class="w-full bg-[#F5F7FA] p-3 flex flex-col gap-3">
          <div class="flex gap-2 items-center">
            <span class="min-w-40px text-right">离院后</span>
            <span class="w-80px">
              <FormItemRest>
                <InputNumber :min="0" v-model:value="model[field]" />
              </FormItemRest>
            </span>
            <span>天</span>
          </div>
          <div v-if="model['taskFrequency'] === 2" class="flex gap-2 items-center">
            <span class="min-w-40px text-right">每</span>
            <span class="w-80px">
              <FormItemRest>
                <InputNumber :min="0" v-model:value="model['loopDay']" />
              </FormItemRest>
            </span>
            <span>天</span>
          </div>
          <div v-if="model['taskFrequency'] === 2" class="flex gap-2 items-center">
            <span class="min-w-40px text-right">循环</span>
            <span class="w-80px">
              <FormItemRest>
                <InputNumber :min="0" v-model:value="model['loopNum']" />
              </FormItemRest>
            </span>
            <span>次</span>
          </div>
        </div>
      </template>
    </BasicForm>
  </BasicModal>
</template>
