<script setup lang="ts">
  import { Button } from '@ft/internal/components/Button';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { BasicTitle } from '@ft/internal/components/Basic';
  import { Switch, Tooltip } from 'ant-design-vue';
  import { useModal } from '@ft/internal/components/Modal';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { onMounted, ref } from 'vue';
  import { useRoutePageMode } from '@ft/internal/hooks/web/useRoutePageMode';
  import { useRequest } from '@ft/request';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import type { TaskEditModalDT } from './data';
  import { EditSchema, TaskColumns } from './data';
  import TaskEditModal from './TaskEditModal.vue';
  import {
    type IFollowVisitTask,
    deleteFollowVisitTask,
    getFollowVisitPlanDetail,
    getFollowVisitTaskList,
    saveFollowVisitPlan,
    updateFollowVisitPlan,
    updateFollowVisitTask,
  } from '/@/api/follow-task';
  import type { RecordWithExtra } from '/@/types';

  defineOptions({
    disabledReloadOnQueryChanged: true,
  });

  const visitTypeId = useRouteQuery('visitTypeId', '', { transform: String });
  const visitTypeName = useRouteQuery('visitTypeName', '', { transform: String });
  const scopeName = useRouteQuery('scopeName', '', { transform: String });
  const planId = useRouteQuery('planId', '', { transform: String });

  const { isEditMode, isCreateMode } = useRoutePageMode();
  const [register, formAction] = useForm({
    showActionButtonGroup: false,
    labelWidth: 100,
    schemas: EditSchema,
  });

  const taskData = ref<IFollowVisitTask[]>([]);
  const [registerTable, tableIns] = useTable({
    api: getFollowVisitTaskList,
    columns: TaskColumns,
    actionColumn: {
      title: '操作',
      width: 120,
      dataIndex: 'action',
    },
    immediate: false,
    pagination: false,
    resizeHeightOffset: 20,
    beforeFetch: () => {
      console.log(`planId.value`, planId.value);
      return {
        visitPlanId: planId.value,
      };
    },
    afterFetch: (data) => {
      return data.map((item) => {
        return {
          ...item,
          loading: false,
        };
      });
    },
  });

  onMounted(() => {
    formAction.setFieldsValue({
      visitTypeId,
      visitTypeName,
      scopeName,
    });
    if (isEditMode) {
      getPlanDetail(planId.value);
    }
    if (planId.value) tableIns.reload();
  });

  const [registerEditModal, { openModal }] = useModal();

  const { runAsync: getFollowVisitPlanDetailRun } = useRequest(getFollowVisitPlanDetail, {
    manual: true,
  });

  function getPlanDetail(id: string) {
    getFollowVisitPlanDetailRun(id).then((data) => {
      formAction.setFieldsValue(data);
    });
  }

  function onAddTask() {
    openModal<TaskEditModalDT>(true, {
      mode: 'add',
      visitPlanId: planId.value,
    });
  }

  const go = useGo();
  function onCancel() {
    go({
      name: 'FollowTaskScheduledTask',
    });
  }

  function onEdit(record: IFollowVisitTask) {
    openModal<TaskEditModalDT>(true, {
      mode: 'edit',
      record,
      visitPlanId: planId.value,
    });
  }

  // deleteFollowVisitTask
  const { runAsync: deleteFollowVisitTaskRun } = useRequest(deleteFollowVisitTask, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      tableIns.reload();
    },
  });

  function onDelete(record: IFollowVisitTask) {
    deleteFollowVisitTaskRun(record.id);
  }

  function createActions(record: IFollowVisitTask, _column): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: onEdit.bind(null, record),
      },
      {
        label: '删除',
        danger: true,
        popConfirm: {
          title: '确认删除吗？',
          placement: 'topRight',
          okButtonProps: { danger: true },
          confirm: onDelete.bind(null, record),
        },
      },
    ];
  }

  function onSuccess() {
    tableIns.reload();
  }

  const { loading, runAsync: saveFollowVisitPlanRun } = useRequest(
    (params) => (isEditMode ? updateFollowVisitPlan(params) : saveFollowVisitPlan(params)),
    {
      manual: true,
      showSuccessMessage: () => '操作成功',
      onSuccess: (id) => {
        if (isCreateMode) {
          planId.value = id;
        }
        const pId = isCreateMode ? id : planId.value;
        getPlanDetail(pId);
      },
    },
  );
  function onSave() {
    formAction.validate().then((values) => {
      saveFollowVisitPlanRun(values);
    });
  }

  // updateFollowVisitTask
  const { runAsync: updateFollowVisitTaskRun } = useRequest(updateFollowVisitTask, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      tableIns.reload();
    },
  });
  function onSwitchStatus(checked, record: RecordWithExtra<IFollowVisitTask>) {
    record.loading = true;
    updateFollowVisitTaskRun({
      taskStatus: checked,
      id: record.id,
    });
  }
</script>

<template>
  <div class="pr-4 h-full">
    <div class="bg-white rounded-t flex flex-col h-full">
      <div class="flex p-4 items-center justify-between border-b border-[#EDEEF0]">
        <span class="text-base font-bold"> 新增随访计划 </span>
        <div class="actions-group flex gap-2">
          <Button @click="onCancel">取消</Button>
          <Button :loading="loading" @click="onSave" type="primary">保存</Button>
        </div>
      </div>
      <div class="page-body p-4 flex-1 basis-0 min-h-0 of-y-auto pb-8 flex flex-col">
        <div class="scheduled-form">
          <BasicTitle class="mb-3" span normal>计划基础信息配置</BasicTitle>
          <BasicForm @register="register" />
        </div>
        <div class="scheduled-table">
          <BasicTable :data-source="taskData" @register="registerTable">
            <template #headerTop>
              <BasicTitle class="mb-3" span normal>计划随访任务配置</BasicTitle>
            </template>
            <template #tableTitle>
              <Tooltip
                :visible="!planId ? undefined : false"
                placement="right"
                title="要添加任务，请先保存随访计划"
              >
                <Button :disabled="!planId" type="primary" @click="onAddTask">添加</Button>
              </Tooltip>
            </template>
            <template #bodyCell="{ record, column }">
              <template v-if="column.dataIndex === 'taskFrequency'">
                <span v-if="record['lastDay']">离院后{{ record['lastDay'] }}天</span>
                <span v-if="record[column.dataIndex] === 2">每{{ record['loopDay'] }}天</span>
                <span v-if="record[column.dataIndex] === 2">循环{{ record['loopNum'] }}次</span>
              </template>
              <template v-if="column.dataIndex === 'taskStatus'">
                <Switch
                  :loading="record.loading"
                  :checkedValue="0"
                  :unCheckedValue="1"
                  v-model:checked="record[column.dataIndex]"
                  @change="onSwitchStatus($event, record)"
                />
              </template>
              <template v-if="column.dataIndex === 'action'">
                <TableAction :divider="false" :actions="createActions(record, column)" />
              </template>
            </template>
          </BasicTable>
        </div>
      </div>
    </div>
    <TaskEditModal
      :visitTypeId="visitTypeId"
      :visitPlanId="planId"
      @register="registerEditModal"
      @success="onSuccess"
    />
  </div>
</template>
<style scoped></style>
