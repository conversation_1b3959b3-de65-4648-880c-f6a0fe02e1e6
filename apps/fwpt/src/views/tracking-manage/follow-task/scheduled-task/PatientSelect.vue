<script setup lang="ts">
  import { Select, Tag } from 'ant-design-vue';
  import { computed } from 'vue';
  import type { IFollowVisitTaskPatient } from '/@/api/follow-task';
  import { getFollowVisitTaskPatientList } from '/@/api/follow-task';
  import { useRequest } from '@ft/request';
  import { omit } from 'lodash-es';

  const props = defineProps<{
    value?: IFollowVisitTaskPatient[];
  }>();

  const emit = defineEmits(['update:value']);
  const state = computed({
    get: () => props.value?.map((item) => item.userId),
    set: (newVal) => {
      emit(
        'update:value',
        options.value
          ?.filter((item) => newVal?.includes(item.value))
          .map((i) => omit(i, ['value', 'label'])),
      );
    },
  });

  const { data } = useRequest(getFollowVisitTaskPatientList);

  const options = computed(() => {
    return data.value?.map((item) => ({
      ...item,
      label: item.patientName,
      value: item.userId,
    }));
  });
</script>

<template>
  <Select
    class="w-full"
    v-bind="$attrs"
    v-model:value="state"
    mode="multiple"
    :maxTagCount="4"
    :options="options"
    showSearch
    optionFilterProp="label"
  >
    <template #tagRender="{ label, closable, onClose, option }">
      <Tag :closable="closable" style="margin-right: 3px" @close="onClose">
        <span>{{ option?.phone }}</span>
        &nbsp;&nbsp;{{ label }}
      </Tag>
    </template>
    <template #option="{ label, phone }">
      <div class="flex gap-4 justify-start">
        <span class="w-100px">{{ phone }}</span>
        <span>{{ label }}</span>
      </div>
    </template>
  </Select>
</template>
