<script setup lang="ts">
  import { Icon } from '@ft/internal/components/Icon';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { Divider, Input, Pagination } from 'ant-design-vue';
  import { computed, ref, watch } from 'vue';
  import { useModal } from '@ft/internal/components/Modal';
  import { get } from 'lodash-es';
  import { useRequest } from '@ft/request';
  import { SearchOutlined } from '@ant-design/icons-vue';
  import { useLoading } from '@ft/internal/components/Loading';
  import type { TemplatePreViewDT } from '../components/TemplateEdit/types';
  import { TemplateTag, TemplateType } from '../components/TemplateEdit/types';
  import TemplatePreviewModal from '../components/TemplateEdit/TemplatePreviewModal.vue';
  import FollowTag from '../components/FollowTag/index.vue';
  import type { IVisitType } from '/@/api/follow-type';
  import type { IFollowTemplate, TemplateListReq } from '/@/api/follow-template';
  import { getTemplatePage, removeTemplate } from '/@/api/follow-template';
  const props = withDefaults(
    defineProps<{
      activeItem?: IVisitType;
      titleField?: string;
    }>(),
    {
      titleField: 'title',
    },
  );

  const pageNum = ref(1);
  const pageSize = ref(20);
  const searchValue = ref('');

  const {
    data,
    loading,
    run: getTemplatePageRun,
    refresh: refreshTemplatePage,
  } = useRequest(getTemplatePage, {
    manual: true,
  });

  const listRef = ref<HTMLElement | null>(null);

  useLoading({
    target: listRef.value,
    props: {
      tip: '加载中...',
      absolute: true,
      loading: loading.value,
    },
  });
  const total = computed(() => get(data.value, 'total', 0));

  const params = computed(
    () =>
      ({
        pageNum: pageNum.value,
        pageSize: pageSize.value,
        visitTypeId: props.activeItem?.id,
        templateType: TemplateType.Template,
      } as TemplateListReq),
  );

  watch(params, (params) => {
    if (!params.visitTypeId) return;
    getTemplatePageRun(params);
  });

  function onSeaarch() {
    getTemplatePageRun({ ...params.value, templateName: searchValue.value });
  }

  const list = computed(() => data.value?.list || []);

  const { createConfirm } = useMessage();

  const go = useGo();

  function onEdit(e, item: IFollowTemplate) {
    e.stopPropagation();
    go({ name: 'FollowTaskTemplateEdit', query: { templateId: item.id, mode: 'edit' } });
  }

  const { run: removeTemplateRun } = useRequest(removeTemplate, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      refreshTemplatePage();
    },
  });

  function onRemove(e, item: IFollowTemplate) {
    e.stopPropagation();
    createConfirm({
      title: '删除模板',
      content: '确定删除该模板吗？',
      iconType: 'warning',
      onOk: () => {
        item.id && removeTemplateRun(item.id);
      },
    });
  }

  function onAddTemplate() {
    go({
      name: 'FollowTaskTemplateEdit',
      query: {
        visitTypeId: props.activeItem?.id,
        visitTypeName: props.activeItem?.visitTypeName,
      },
    });
  }

  const [registerPreviewModal, { openModal }] = useModal();

  function onPreview(item: IFollowTemplate) {
    openModal<TemplatePreViewDT>(true, {
      templateTag: item.templateTag,
      id: item.id,
      templateName: item.templateName,
    });
  }
</script>

<template>
  <div class="h-full flex flex-col">
    <div class="page-heder border-b border-[#EDEEF0] p-4">
      <span class="text-base font-bold">{{ get(activeItem, titleField) }}</span>
      <Divider type="vertical" />
      <span class="text-info-text-color">随访执行范围：</span>
      <FollowTag :color="activeItem?.scopeCode === '1' ? 'orange' : 'purple'">
        {{ activeItem?.scopeName }}
      </FollowTag>
    </div>

    <div class="flex-1 flex flex-col">
      <div class="flex justify-between px-4 pt-4">
        <span>随访模板：</span>
        <Input
          v-model:value="searchValue"
          placeholder="请输入模板名称"
          style="width: 200px"
          @press-enter="onSeaarch"
        >
          <template #suffix>
            <SearchOutlined
              @click="onSeaarch"
              class="cursor-pointer"
              style="color: rgb(0 0 0 / 45%)"
            />
          </template>
        </Input>
      </div>
      <div
        ref="listRef"
        class="page-body grid sm:grid-cols-2 md:grid-cols-3 2xl:grid-cols-5 gap-4 auto-rows-min flex-1 basis-0 of-y-auto p-4"
      >
        <div
          class="min-h-108px flex items-center justify-center gap-2 text-primary-color bg-[#F0FFF7] p-4 border rounded border-primary-color min-w-266px transition hover:shadow-[0px_4px_16px_0px_rgba(0,0,0,0.08)] cursor-pointer hover:translate-y-[-4px]"
          @click="onAddTemplate"
        >
          <Icon :size="22" icon="ant-design:plus-outlined" />
          <span>新增模板</span>
        </div>
        <div
          class="relative p-4 border rounded border-[#DCDFE6] min-w-266px transition hover:shadow-[0px_4px_16px_0px_rgba(0,0,0,0.08)] cursor-pointer group"
          v-for="item in list"
          :key="item.id"
          @click="onPreview(item)"
        >
          <div class="font-bold mb-2">{{ item.templateName }}</div>
          <div>
            <span v-if="item.templateTag === TemplateTag.Custom" class="text-info-text-color">
              随访科室：
            </span>
            <span>{{ item.deptName }}</span>
          </div>
          <div>
            <span class="text-info-text-color">随访病种：</span>
            <span>{{ item.visitDiseaseName }}</span>
          </div>
          <div
            v-if="item.templateTag === TemplateTag.Custom"
            class="absolute right-4 bottom-4 z-2 flex justify-between gap-2"
          >
            <span
              class="w-24px h-24px flex items-center justify-center rounded-sm hover:bg-[#F5F5F5]"
              @click="onEdit($event, item)"
            >
              <Icon icon="follow-template-edit|svg" />
            </span>

            <span
              class="w-24px h-24px flex items-center justify-center rounded-sm hover:bg-[#F5F5F5] hover:text-auxiliary-red"
              @click="onRemove($event, item)"
            >
              <Icon icon="follow-template-delete|svg" />
            </span>
          </div>
        </div>
      </div>
      <div class="footer flex justify-end py-3 px-4">
        <Pagination
          :show-total="(total) => `共${total}条`"
          show-quick-jumper
          v-model:current="pageNum"
          show-less-items
          :page-size="pageSize"
          :total="total"
        />
      </div>
    </div>

    <TemplatePreviewModal exportWord @register="registerPreviewModal" />
  </div>
</template>
<style scoped></style>
