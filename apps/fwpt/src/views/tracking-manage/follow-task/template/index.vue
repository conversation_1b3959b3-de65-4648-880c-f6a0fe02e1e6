<script setup lang="ts">
  import { StyledList } from '@ft/components';
  import { computed, ref } from 'vue';
  import { useRequest } from '@ft/request';
  import Right from './Right.vue';
  import type { IVisitType } from '/@/api/follow-type';
  import { getVisitTypeList } from '/@/api/follow-type';

  const { data } = useRequest(getVisitTypeList, {
    onSuccess: (data) => {
      activeKey.value = data[0].id || '';
      activeItem.value = data[0];
    },
  });

  const items = computed(() => {
    return data.value || [];
  });

  const activeKey = ref();
  const activeItem = ref<IVisitType>();
</script>

<template>
  <div class="h-full pr-4 pb-4">
    <div class="h-full bg-white rounded rounded-lt-0 flex">
      <div class="p-4 border-r-1 border-[#EDEEF0] flex flex-col">
        <span class="text-base font-bold">随访类型</span>
        <div class="mt-4 flex-1 basis-0 of-y-auto">
          <StyledList
            :items="items"
            v-model="activeKey"
            v-model:value="activeItem"
            valueField="id"
            labelField="visitTypeName"
          />
        </div>
      </div>
      <div class="content flex-1">
        <Right :activeItem="activeItem" titleField="visitTypeName" />
      </div>
    </div>
  </div>
</template>
<style scoped></style>
