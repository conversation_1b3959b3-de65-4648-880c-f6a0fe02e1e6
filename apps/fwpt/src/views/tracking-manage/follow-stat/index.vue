<script setup lang="ts">
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { columns, searchFormSchemas } from './data';
  import type { IFollowStatRow } from '/@/api/follow-stat';
  import { exportFollowVisitStatistics, getFollowVisitStatistics } from '/@/api/follow-stat';

  const props = defineProps({
    /**
     infectionDisease	传染病病种类型 字典:【INFECTIOUS_DISEASE】 1:病毒性肝炎 2:艾滋病 3:结核病
     */
    infectionDisease: {
      type: Number,
      default: 1,
    },
    diseaseName: {
      type: String,
      default: '',
    },
  });
  const [registerTable, tableFns] = useTable({
    api: getFollowVisitStatistics,
    columns,
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas: searchFormSchemas,
      fieldMapToTime: [['followTime', ['visitStartTime', 'visitEndTime'], 'YYYY-MM-DD']],
    },
    beforeFetch: (query) => {
      return {
        ...query,
        infectionDisease: props.infectionDisease,
      };
    },
    resizeHeightOffset: 16,
    actionColumn: {
      title: '操作',
      dataIndex: 'action',
      width: 120,
    },
  });

  const go = useGo();
  function onDeptStatistics(record: IFollowStatRow) {
    console.log(record);
    const query = tableFns.getForm().getFieldsValue();
    go({
      name: 'FollowStatDept',
      query: {
        infectionDisease: props.infectionDisease,
        diseaseName: props.diseaseName,
        ...(query || {}),
      },
    });
  }

  function createActions(record: IFollowStatRow, _column) {
    return [
      {
        label: '科室随访统计',
        onClick: onDeptStatistics.bind(null, record),
      },
    ];
  }

  const { runAsync: exportRun, loading: exportLoading } = useRequest(exportFollowVisitStatistics, {
    manual: true,
    showSuccessMessage: true,
  });

  function onExport() {
    exportUtil(
      exportRun({
        ...tableFns.getForm().getFieldsValue(),
        infectionDisease: props.infectionDisease,
      }),
    );
  }
</script>

<template>
  <div class="h-full h-full pr-4 pb-4">
    <div class="h-full bg-white rounded rounded-lt-0">
      <BasicTable @register="registerTable">
        <template #tableTitle> {{ diseaseName }}随访统计表 </template>
        <template #toolbar>
          <Button :loading="exportLoading" type="primary" @click="onExport"> 导出 </Button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :actions="createActions(record, column)" />
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>

<style scoped lang="less"></style>
