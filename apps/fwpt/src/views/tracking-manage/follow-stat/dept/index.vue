<script setup lang="ts">
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import { useRoute } from 'vue-router';
  import { onMounted } from 'vue';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { columns, searchFormSchemas } from './data';
  import type { IDeptStatRow } from '/@/api/follow-stat';
  import { exportFollowVisitDeptPage, getFollowVisitDeptPage } from '/@/api/follow-stat';

  const { query } = useRoute();
  const [registerTable, tableFns] = useTable({
    api: getFollowVisitDeptPage,
    columns,
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas: searchFormSchemas,
      fieldMapToTime: [['followTime', ['visitStartTime', 'visitEndTime'], 'YYYY-MM-DD']],
      submitOnReset: false,
      resetFunc: onTableDefaultReload,
    },
    beforeFetch: (params) => {
      return {
        ...params,
        infectionDisease: query.infectionDisease,
      };
    },
    resizeHeightOffset: 16,
    actionColumn: {
      title: '操作',
      dataIndex: 'action',
      width: 200,
    },
    immediate: false,
  });

  async function onTableDefaultReload() {
    await tableFns.getForm().setFieldsValue({
      ...query,
      followTime: [query.visitStartTime, query.visitEndTime],
    });
    tableFns.reload();
  }

  onMounted(() => {
    onTableDefaultReload();
  });

  const go = useGo();
  function onFollowDetail(record: IDeptStatRow) {
    const query = tableFns.getForm().getFieldsValue();
    go({
      name: 'FollowStatDeptDetail',
      query: {
        ...(query || {}),
        infectionDisease: record.infectionDisease || query.infectionDisease,
        visitDeptName: record.visitDeptName === '汇总' ? '' : record.visitDeptName,
      },
    });
  }

  function onPatientStatistics(record: IDeptStatRow) {
    const query = tableFns.getForm().getFieldsValue();
    go({
      name: 'FollowStatDeptPatient',
      query: {
        ...(query || {}),
        infectionDisease: record.infectionDisease || query.infectionDisease,
        visitDeptName: record.visitDeptName === '汇总' ? '' : record.visitDeptName,
      },
    });
  }

  function createActions(record: IDeptStatRow, _column) {
    return [
      {
        label: '医生/护士统计',
        onClick: onPatientStatistics.bind(null, record),
      },
      {
        label: '随访详情',
        onClick: onFollowDetail.bind(null, record),
      },
    ];
  }

  const { runAsync: exportRun, loading: exportLoading } = useRequest(exportFollowVisitDeptPage, {
    manual: true,
    showSuccessMessage: true,
  });

  function onExport() {
    exportUtil(exportRun(tableFns.getForm().getFieldsValue()));
  }
</script>

<template>
  <div class="h-full h-full pr-4 pb-4">
    <div class="h-full bg-white rounded rounded-lt-0">
      <BasicTable @register="registerTable">
        <template #tableTitle> 科室随访统计 </template>
        <template #toolbar>
          <Button :loading="exportLoading" type="primary" @click="onExport"> 导出 </Button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :divider="false" :actions="createActions(record, column)" />
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>

<style scoped lang="less"></style>
