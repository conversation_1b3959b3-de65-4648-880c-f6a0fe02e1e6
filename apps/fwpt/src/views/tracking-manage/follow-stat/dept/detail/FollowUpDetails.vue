<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import { StyledList } from '@ft/components';
  import { useRequest } from '@ft/request';
  import type { IFollowVisitRecordPost } from '/@/api/workbench';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { BasicTitle } from '@ft/internal/components/Basic';
  import { Descriptions, DescriptionsItem } from 'ant-design-vue';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { useToggle } from '@vueuse/core';
  import { Button } from '@ft/internal/components/Button';
  import PatientInfoCard from '../../../workbench/components/PatientInfoCard.vue';
  import FollowTable from '../../../workbench/components/FollowTable/index.vue';
  import type { IFollowHistory } from '/@/api/follow-stat';
  import { getFollowHistoryList } from '/@/api/follow-stat';
  import { type IFollowTemplate, getTemplateDetail } from '/@/api/follow-template';
  import { useFollowRecordFormData } from '../../../workbench/follow-up-registration.data';

  const patientId = useRouteQuery('patientId', '', { transform: String });
  const patientName = useRouteQuery('patientName', '', { transform: String });
  const patientBirthday = useRouteQuery('patientBirthday', '', { transform: String });
  const patientAge = useRouteQuery('patientAge', '', { transform: String });
  const patientSex = useRouteQuery('patientSex', '', { transform: Number });
  const patientPhone = useRouteQuery('patientPhone', '', { transform: String });
  const patientIdCard = useRouteQuery('patientIdCard', '', { transform: String });

  const activeTemplate = ref<IFollowTemplate>();

  const activeHistory = ref<IFollowHistory>();
  const activeHistoryId = ref<string>();

  const { run: getTemplateDetailRun } = useRequest(getTemplateDetail, {
    manual: true,
    onSuccess(data) {
      activeTemplate.value = data;
    },
  });

  const { data: items = ref([]) } = useRequest(getFollowHistoryList, {
    defaultParams: [patientId.value],
    ready: patientId.value,
    onSuccess(data) {
      data.length && getTemplateDetailRun(data[0].visitTemplateId);
      data.length && (activeHistoryId.value = data[0].id);
    },
  });

  watch(activeHistory, (history, oldHistory) => {
    if (!history) return;
    if (oldHistory?.id === history?.id) return;
    getTemplateDetailRun(history.visitTemplateId);
  });

  const patientInfo = computed(() => ({
    name: patientName.value,
    birthday: patientBirthday.value,
    age: patientAge.value,
    sex: patientSex.value,
    phone: patientPhone.value,
    idCardNo: patientIdCard.value,
  }));

  const visitRecord = ref<IFollowVisitRecordPost>();

  function onFetchSuccess(data: IFollowVisitRecordPost) {
    visitRecord.value = data;
  }

  const [showTable, setShowTable] = useToggle(true);

  const [registerFrom, formAction] = useForm({
    labelWidth: 140,
    colon: true,
    showActionButtonGroup: false,
    disabled: true,
  });

  const { exec } = useFollowRecordFormData({
    isAdd: ref(false),
    formAction,
  });
  async function onTdClick(record: IFollowVisitRecordPost) {
    setShowTable(false);
    await formAction.resetSchema([]);
    exec(record.visitTemplateId, record.id);
  }

  function onBack() {
    setShowTable(true);
  }
</script>
<template>
  <div class="w-full h-full pr-4 flex justify-between gap-10px pb-10px">
    <div class="flex flex-col gap-2.5">
      <PatientInfoCard :patientInfo="patientInfo" />
      <div class="flex-1 bg-#fff rounded-2 p-3 flex flex-col gap-2">
        <BasicTitle span normal>随访信息</BasicTitle>
        <div class="mt-1">
          <StyledList
            label-field="templateName"
            value-field="id"
            :items="items"
            v-model:value="activeHistory"
            v-model="activeHistoryId"
            :width="204"
          >
            <template #default="item">
              <div>
                <div>{{ item.visitTime }}</div>
                <div>{{ item.visitTemplateName }}</div>
              </div>
            </template>
          </StyledList>
        </div>
      </div>
    </div>
    <div class="flex-1 flex gap-2.5 flex-col bg-#fff rounded-lg px-6 py-4">
      <div class="text-#333 text-24px text-center relative">
        {{ activeTemplate?.templateName }}
        <Button
          class="!absolute top-0 right-0"
          ghost
          type="primary"
          pre-icon="ant-design:arrow-left-outlined"
          @click="onBack"
          v-if="!showTable"
        >
          返回
        </Button>
      </div>
      <div class="flex flex-col gap-2 flex-1 of-hidden basis-0 h-full">
        <Descriptions v-show="showTable" :label-style="{ color: '#999' }" :column="4" size="small">
          <!-- 患者姓名 -->
          <DescriptionsItem label="患者姓名">{{ patientName }}</DescriptionsItem>
          <!-- 患者编号： -->
          <DescriptionsItem label="患者编号">{{ patientId }}</DescriptionsItem>
          <!-- 患者住院号： -->
          <DescriptionsItem label="患者住院号">{{ visitRecord?.inId }}</DescriptionsItem>
          <!-- 出院日期： -->
          <DescriptionsItem label="出院日期">{{ visitRecord?.dischargeDate }}</DescriptionsItem>
        </Descriptions>
        <div class="power-data flex-1 overflow-auto">
          <FollowTable
            v-show="showTable"
            :patientId="patientId"
            :template="activeTemplate"
            @col-click="onTdClick"
            @fetch-success="onFetchSuccess"
          />
          <BasicForm v-show="!showTable" @register="registerFrom">
            <template #moduleName="{ schema }">
              <div class="flex items-center gap-2 mb-3">
                <span v-if="schema.label" class="inline-block bg-primary-color w-3px h-1em"></span>
                <span>{{ schema.label }}</span>
              </div>
            </template>
          </BasicForm>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
  :deep {
    .vben-basic-table .ant-table-wrapper {
      padding: 0;
    }

    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table-form-container .ant-form,
    .vben-basic-table .ant-table-wrapper {
      border-radius: 4px;
    }
  }

  .doctors-view-styled-list {
    :deep {
      .ant-timeline-item {
        padding-bottom: 0;
      }

      .ant-timeline-item-head {
        background-color: transparent;
      }
    }
  }
</style>
