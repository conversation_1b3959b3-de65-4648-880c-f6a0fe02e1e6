<script setup lang="ts">
  import { Tabs } from 'ant-design-vue';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import { useRoute } from 'vue-router';
  import { onMounted, ref, watch } from 'vue';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { columns, searchFormSchemas } from './data';
  import type { IDiseaseStatRow } from '/@/api/follow-stat';
  import { exportDiseaseDetailPage, getDiseaseDetailPage } from '/@/api/follow-stat';

  const activeTab = ref('0');
  const { query } = useRoute();
  const [registerTable, tableFns] = useTable({
    api: getDiseaseDetailPage,
    columns,
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas: searchFormSchemas,
      fieldMapToTime: [['followTime', ['visitStartTime', 'visitEndTime'], 'YYYY-MM-DD']],
      submitOnReset: false,
      resetFunc: onTableDefaultReload,
    },
    resizeHeightOffset: 16,
    actionColumn: {
      title: '操作',
      dataIndex: 'action',
      width: 100,
    },
    immediate: false,
    beforeFetch: (params) => {
      return {
        ...params,
        visitStatus: activeTab.value,
      };
    },
  });
  watch(activeTab, () => {
    tableFns.reload();
  });
  async function onTableDefaultReload() {
    await tableFns.getForm().setFieldsValue({
      ...query,
      followTime: [query.visitStartTime, query.visitEndTime],
    });
    tableFns.reload();
  }

  onMounted(() => {
    onTableDefaultReload();
  });

  const go = useGo();
  function onPatientFollowDetail(record: IDiseaseStatRow) {
    console.log(`record`, record);
    go({
      name: 'FollowStatDeptDetailPatient',
      query: {
        patientId: record.patientId,
        patientName: record.patientName,
        patientAge: record.age,
        patientSex: record.sexName,
        patientIdCard: record.idCardNo,
      },
    });
  }

  function createActions(record: IDiseaseStatRow, _column) {
    return [
      {
        label: '患者随访详情',
        onClick: onPatientFollowDetail.bind(null, record),
      },
    ];
  }

  const { runAsync: exportRun, loading: exportLoading } = useRequest(exportDiseaseDetailPage, {
    manual: true,
    showSuccessMessage: true,
  });

  function onExport() {
    exportUtil(exportRun({ ...tableFns.getForm().getFieldsValue(), visitStatus: activeTab.value }));
  }
</script>

<template>
  <div class="h-full h-full pr-4 pb-4">
    <div class="h-full bg-white rounded rounded-lt-0">
      <BasicTable @register="registerTable">
        <template #headerTop>
          <div class="text-16px font-bold">随访统计表</div>
          <Tabs v-model:activeKey="activeTab">
            <Tabs.TabPane key="0" tab="待随访" />
            <Tabs.TabPane key="1" tab="已随访" />
            <Tabs.TabPane key="2" tab="随访关闭" />
          </Tabs>
        </template>
        <template #toolbar>
          <Button :loading="exportLoading" type="primary" @click="onExport"> 导出 </Button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :actions="createActions(record, column)" />
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>

<style scoped lang="less"></style>
