import { getDeptList, queryOrgPermission } from '@ft/internal/api';
import { getVisitTypeList } from '/@/api/follow-type';
import type { FormSchema } from '@ft/internal/components/Form';
import type { BasicColumn } from '@ft/internal/components/Table';
import dayjs from 'dayjs';
import { useUserStoreWithOut } from '@ft/internal/store/modules/user';

const userStore = useUserStoreWithOut();

const visitStatusMap = {
  0: '待随访',
  1: '已随访',
  2: '随访关闭',
};
/**
 * 随访时间
 * 随访类型
 * 医疗机构
 */
export const searchFormSchemas: FormSchema[] = [
  {
    field: 'infectionDisease',
    label: '病种',
    component: 'Input',
    show: false,
  },
  {
    field: 'followTime',
    label: '随访时间',
    component: 'RangePicker',
    componentProps: {
      style: { width: '100%' },
    },
    colProps: { span: 6 },
    defaultValue: [
      dayjs().startOf('M').format('YYYY-MM-DD'),
      dayjs().endOf('M').format('YYYY-MM-DD'),
    ],
  },
  {
    field: 'visitTypeId',
    label: '随访类型',
    component: 'ApiSelect',
    componentProps: {
      api: getVisitTypeList,
      labelField: 'visitTypeName',
      valueField: 'id',
      showSearch: true,
      optionFilterProp: 'label',
    },
    colProps: { span: 6 },
  },
  {
    field: 'visitOrgId',
    label: '医疗机构',
    component: 'Input',
    show: false,
    defaultValue: userStore.getUserInfo.orgId,
  },
  {
    field: 'visitOrgName',
    label: '医疗机构',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: queryOrgPermission,
        labelField: 'orgName',
        valueField: 'orgName',
        showSearch: true,
        optionFilterProp: 'label',
        onChange: (_, opt) => {
          opt && (formModel['visitOrgId'] = opt?.id);
        },
      };
    },
    defaultValue: userStore.getUserInfo.orgName,
    colProps: { span: 6 },
  },
  {
    field: 'visitDeptName',
    label: '随访科室',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => {
          if (!formModel.visitOrgId) {
            return Promise.resolve([]);
          }
          return getDeptList({ orgId: formModel.visitOrgId });
        },
        labelField: 'deptName',
        valueField: 'deptName',
        showSearch: true,
        optionFilterProp: 'label',
      };
    },
    colProps: { span: 6 },
  },
  // 患者姓名
  {
    field: 'patientName',
    label: '患者姓名',
    component: 'Input',
    colProps: { span: 6 },
  },
  // 随访状态
  // {
  //   field: 'visitStatus',
  //   label: '随访状态',
  //   component: 'Select',
  //   componentProps: {
  //     options: Object.entries(visitStatusMap).map(([key, value]) => {
  //       return { label: value, value: key };
  //     }),
  //   },
  //   colProps: { span: 6 },
  // },
];

/**
 随访日期 visitTime
 随访患者 patientName
 就诊号 recordNo
 住院号 inId
 身份证号 idCardNo
 性别 sexName
 年龄 age
 就诊科室 visitDeptName
 就诊诊断 diagnosticResults
 随访状态 visitStatus
 随访任务名称 visitTaskName
 随访内容 visitTemplateName
 */
export const columns: BasicColumn[] = [
  {
    title: '随访日期',
    dataIndex: 'visitTime',
  },
  {
    title: '随访患者',
    dataIndex: 'patientName',
  },
  {
    title: '住院号',
    dataIndex: 'inId',
  },
  {
    title: '身份证号',
    dataIndex: 'idCardNo',
    width: 180,
  },
  {
    title: '性别',
    dataIndex: 'sexName',
  },
  {
    title: '年龄',
    dataIndex: 'age',
  },
  {
    title: '就诊科室',
    dataIndex: 'visitDeptName',
  },
  {
    title: '就诊诊断',
    dataIndex: 'diagnosticResults',
  },
  {
    title: '随访状态',
    dataIndex: 'visitStatus',
    customRender({ text }) {
      // 	随访状态：0 待随访，1 已随访，2 随访关闭

      return visitStatusMap[text];
    },
  },
  {
    title: '随访任务名称',
    dataIndex: 'visitTaskName',
  },
  {
    title: '随访内容',
    dataIndex: 'visitTemplateName',
    width: 180,
  },
];
