import { getDeptList, queryOrgPermission } from '@ft/internal/api';
import { getVisitTypeList } from '/@/api/follow-type';
import type { FormSchema } from '@ft/internal/components/Form';
import type { BasicColumn } from '@ft/internal/components/Table';
import dayjs from 'dayjs';
import { useUserStoreWithOut } from '@ft/internal/store/modules/user';

const userStore = useUserStoreWithOut();
/**
 * 随访时间
 * 随访类型
 * 医疗机构
 */
export const searchFormSchemas: FormSchema[] = [
  {
    field: 'infectionDisease',
    label: '病种',
    component: 'Input',
    show: false,
  },
  {
    field: 'followTime',
    label: '随访时间',
    component: 'RangePicker',
    componentProps: {
      style: { width: '100%' },
    },
    colProps: { span: 6 },
    defaultValue: [
      dayjs().startOf('M').format('YYYY-MM-DD'),
      dayjs().endOf('M').format('YYYY-MM-DD'),
    ],
  },
  {
    field: 'visitTypeId',
    label: '随访类型',
    component: 'ApiSelect',
    componentProps: {
      api: getVisitTypeList,
      labelField: 'visitTypeName',
      valueField: 'id',
      showSearch: true,
      optionFilterProp: 'label',
    },
    colProps: { span: 6 },
  },
  {
    field: 'visitOrgId',
    label: '医疗机构',
    component: 'Input',
    show: false,
    defaultValue: userStore.getUserInfo.orgId,
  },
  {
    field: 'visitOrgName',
    label: '医疗机构',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: queryOrgPermission,
        labelField: 'orgName',
        valueField: 'orgName',
        showSearch: true,
        optionFilterProp: 'label',
        onChange: (_, opt) => {
          opt && (formModel['visitOrgId'] = opt?.id);
        },
      };
    },
    defaultValue: userStore.getUserInfo.orgName,
    colProps: { span: 6 },
  },
  {
    field: 'visitDeptName',
    label: '随访科室',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => {
          if (!formModel.visitOrgId) {
            return Promise.resolve([]);
          }
          return getDeptList({ orgId: formModel.visitOrgId });
        },
        labelField: 'deptName',
        valueField: 'deptName',
        showSearch: true,
        optionFilterProp: 'label',
      };
    },
    colProps: { span: 6 },
  },
];

/**
 * 随访科室
 * 随访人数
 * 随访次数
 * 随访率
 */
export const columns: BasicColumn[] = [
  {
    title: '随访科室',
    dataIndex: 'visitDeptName',
  },
  {
    title: '已随访人数',
    dataIndex: 'visitNum',
  },
  {
    title: '已随访数',
    dataIndex: 'visitTimes',
  },
  {
    title: '未随访数',
    dataIndex: 'notVisitNumber',
  },
  {
    title: '随访率',
    dataIndex: 'visitRate',
  },
];

/**
 * 随访医生或护士
 * 已随访人数
 * 已随访数
 */
export const doctorColumns: BasicColumn[] = [
  {
    title: '随访医生或护士',
    dataIndex: 'visitDoctorName',
  },
  {
    title: '已随访人数',
    dataIndex: 'visitNum',
  },
  {
    title: '已随访数',
    dataIndex: 'visitTimes',
  },
];
