<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import { useRoute } from 'vue-router';
  import { onMounted } from 'vue';
  import { doctorColumns, searchFormSchemas } from '../data';
  import { exportDoctor, getDoctorPage } from '/@/api/follow-stat';

  const { query } = useRoute();
  const [registerTable, tableFns] = useTable({
    api: getDoctorPage,
    columns: doctorColumns,
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas: searchFormSchemas,
      fieldMapToTime: [['followTime', ['visitStartTime', 'visitEndTime'], 'YYYY-MM-DD']],
      submitOnReset: false,
      resetFunc: onTableDefaultReload,
    },
    beforeFetch: (params) => {
      return {
        ...params,
        infectionDisease: query.infectionDisease,
      };
    },
    resizeHeightOffset: 16,
    immediate: false,
  });

  async function onTableDefaultReload() {
    await tableFns.getForm().setFieldsValue({
      ...query,
      followTime: [query.visitStartTime, query.visitEndTime],
    });
    tableFns.reload();
  }

  onMounted(() => {
    onTableDefaultReload();
  });
  const { runAsync: exportRun, loading: exportLoading } = useRequest(exportDoctor, {
    manual: true,
    showSuccessMessage: true,
  });

  function onExport() {
    exportUtil(exportRun(tableFns.getForm().getFieldsValue()));
  }
</script>

<template>
  <div class="h-full h-full pr-4 pb-4">
    <div class="h-full bg-white rounded rounded-lt-0">
      <BasicTable @register="registerTable">
        <template #tableTitle> 医生/护士随访统计 </template>
        <template #toolbar>
          <Button :loading="exportLoading" type="primary" @click="onExport"> 导出 </Button>
        </template>
      </BasicTable>
    </div>
  </div>
</template>

<style scoped lang="less"></style>
