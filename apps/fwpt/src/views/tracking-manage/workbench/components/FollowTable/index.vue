<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import { useRequest } from '@ft/request';
  import { isEmpty, omit } from 'lodash-es';
  import { TemplateTag } from '../../../follow-task/components/TemplateEdit/types';
  import type { FollowDetailTableColumns } from './types';
  import { BuiltInTemplateColumnsMap, templateColumns } from './data';
  import type { IFollowTemplate } from '/@/api/follow-template';
  import { getTemplateDetail } from '/@/api/follow-template';
  import type { IFollowVisitRecordPost } from '/@/api/workbench';
  import { getFollowVisitRecordExistList } from '/@/api/workbench';

  const VisitWayMap = {
    1: '电话',
    2: '家庭',
    3: '门诊',
  };
  const props = withDefaults(
    defineProps<{
      patientId?: string;
      template?: IFollowTemplate;
      visitType?: number;
    }>(),
    {
      visitType: 1,
    },
  );

  const emit = defineEmits<{
    (e: 'col-click', colData: Recordable): void;
    (
      e: 'fetch-success',
      data: Omit<IFollowVisitRecordPost, 'visitContentDataList' | 'visitObjectDataList'>,
    ): void;
  }>();

  const columns = ref<FollowDetailTableColumns[]>(templateColumns);

  const { run: getTemplateDetailRun } = useRequest(getTemplateDetail, {
    manual: true,
    onSuccess(_template) {
      let _columns: FollowDetailTableColumns[] = [];

      _template.visitContentList.forEach((i) => {
        _columns.push({
          title: i.dataName,
          dataIndex: i.id,
          attr: {},
        });
      });
      columns.value = _columns;
    },
  });

  const { data: existList = ref([]), run } = useRequest(getFollowVisitRecordExistList, {
    manual: true,
    onSuccess(data) {
      data?.length &&
        emit('fetch-success', omit(data[0], ['visitObjectDataList', 'visitObjectDataList']));
    },
  });

  watch([() => props.patientId, () => props.template], ([patientId, template]) => {
    if (!template || !patientId) return;
    run(patientId, template.id!, props.visitType);
    if (template.templateTag === TemplateTag.BuiltIn) {
      columns.value = BuiltInTemplateColumnsMap[template.templateName];
    } else {
      getTemplateDetailRun(template.id!);
    }
  });

  const tableData = computed(() => {
    if (!existList.value) return [];
    return existList.value.reduce((rows, cur) => {
      const item = cur?.visitContentDataList?.reduce((subAcc, subCur) => {
        subAcc[subCur.dataKey] = subCur.dataValue;
        return subAcc;
      }, {} as Recordable);
      if (!isEmpty(item)) {
        item.id = cur.id;
        item.visitTemplateId = cur.visitTemplateId;
        rows.push(item);
      }
      return rows;
    }, [] as Recordable[]);
  });
</script>

<template>
  <table class="table2 h-full table-all" cellpadding="0" cellspacing="0">
    <tr align="center" v-for="(col, idx) in columns" :key="idx">
      <td class="thBack tdLine" v-bind="col.attr">{{ col.title }}</td>
      <template v-if="!col?.isParent">
        <td
          v-for="(item, idx) in tableData"
          :key="idx"
          class="flex-1 w-[280px] hover:bg-[#F0FFF7] cursor-pointer"
          @click="$emit('col-click', item)"
        >
          {{
            col.dataIndex === 'visitWay' ? VisitWayMap[item[col.dataIndex!]] : item[col.dataIndex!]
          }}
        </td>
      </template>
    </tr>
  </table>
</template>

<style lang="less" scoped>
  .table-all {
    color: black;
    border: 1px solid #ebeef5;
    border-radius: 2px 0 0;

    .thBack {
      background: #f5f7fa;
      border: 1px solid #ebeef5;
    }

    .tdLine {
      width: 140px;
      flex: 1;
    }

    .tdBtn {
      width: 99px;
    }

    tr {
      text-align: left;
      font-size: 14px;
      color: #666;
    }

    th,
    td {
      border: 1px solid #ebeef5;
      border-radius: 2px 0 0;
      padding: 9px 12px;
      text-align: left;
      font-size: 14px;
      color: #666;
    }
  }
</style>
