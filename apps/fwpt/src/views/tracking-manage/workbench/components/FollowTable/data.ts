import type { FollowDetailTableColumns } from './types';

export const templateColumns: FollowDetailTableColumns[] = [
  {
    title: '随访日期',
    attr: {
      colspan: 2,
    },
    dataIndex: 'visitTime',
  },
  {
    title: '随访方式',
    attr: {
      colspan: 2,
    },
    dataIndex: 'visitWay',
  },
  {
    title: '症状',
    attr: {
      colspan: 2,
    },
    dataIndex: 'symptoms',
  },
  {
    title: '查体',
    attr: {
      rowspan: 6,
    },
    isParent: true,
  },

  {
    title: '血压（mmHg）',
    attr: {},
    dataIndex: 'bloodPressure',
  },
  {
    title: '体重(kg)',
    attr: {},
    dataIndex: 'weight',
  },
  {
    title: '体质指数(BMI）（kg/m²）',
    attr: {},
    dataIndex: 'bmi',
  },
  {
    title: '心率（次/分钟）',
    attr: {},
    dataIndex: 'heartRate',
  },
  {
    title: '其他',
    attr: {},
    dataIndex: 'other',
  },
  {
    title: '生 活 方 式 指 导',
    attr: {
      rowspan: 8,
    },
    isParent: true,
  },
  {
    title: '饮食',
    attr: {},
    dataIndex: 'diet',
  },
  {
    title: '戒烟',
    attr: {},
    dataIndex: 'quitSmoking',
  },
  {
    title: '戒酒',
    attr: {},
    dataIndex: 'quitDrinking',
  },
  {
    title: '遵守咳嗽礼仪',
    attr: {},
    dataIndex: 'noDrugAbuse',
  },
  {
    title: '休息与活动',
    attr: {},
    dataIndex: 'restAndActivity',
  },
  // 心理调整
  {
    title: '心理调整',
    attr: {},
    dataIndex: 'psychologicalAdjustment',
  },
  // 遵医行为
  {
    title: '遵医行为',
    attr: {},
    dataIndex: 'complianceBehavior',
  },
  // 近期异常辅助检查结果
  {
    title: '近期异常辅助检查结果',
    attr: {
      colspan: 2,
    },
    dataIndex: 'recentAbnormalResults',
  },
  // 服药依从性
  {
    title: '服药依从性',
    attr: {
      colspan: 2,
    },
    dataIndex: 'medicationCompliance',
  },
  // 药物不良反应
  {
    title: '药物不良反应',
    attr: {
      colspan: 2,
    },
    dataIndex: 'adverseDrugReactions',
  },
  // 用 药
  {
    title: '用 药',
    attr: {
      colspan: 2,
    },
    dataIndex: 'drugDetail',
  },
  // VTE 风险指导
  {
    title: 'VTE',
    attr: {
      rowspan: 9,
    },
    isParent: true,
  },
  {
    title: 'VTE 风险指导',
    attr: {},
    dataIndex: 'vteRisk',
  },
  // 是否停用抗凝药物
  {
    title: '是否停用抗凝药物',
    attr: {},
    dataIndex: 'isStopAnticoagulation',
  },

  // 是否出血(使用抗凝药物者填写）
  {
    title: '是否出血',
    attr: {},
    dataIndex: 'isUseAnticoagulation',
  },

  // PTE症状
  {
    title: 'PTE症状',
    attr: {},
    dataIndex: 'pteSymptoms',
  },

  // DVT症状
  {
    title: 'DVT症状',
    attr: {},
    dataIndex: 'dvtSymptoms',
  },

  // 近期凝血检查及下肢彩超等结果
  {
    title: '近期凝血检查及下肢彩超等结果',
    attr: {},
    dataIndex: 'bloodTestResult',
  },
  // 转归
  {
    title: '转归',
    attr: {},
    dataIndex: 'transferTo',
  },
  // 健康指导
  {
    title: '健康指导',
    attr: {},
    dataIndex: 'healthGuidance',
  },
  // 是否电话随访
  {
    title: '是否电话随访',
    attr: {
      colspan: 2,
    },
    dataIndex: 'isPhoneFollow',
  },
  // 预约复查
  {
    title: '预约复查',
    attr: {
      colspan: 2,
    },
    dataIndex: 'lastVisitTime',
  },
  // 此次随访评价
  {
    title: '此次随访评价',
    attr: {
      colspan: 2,
    },
    dataIndex: 'evaluation',
  },
  // 备注
  {
    title: '备注',
    attr: {
      colspan: 2,
    },
    dataIndex: 'remark',
  },
  // 随访人
  {
    title: '随访人',
    attr: {
      colspan: 2,
    },
    dataIndex: 'visitUser',
  },
];

/**
 * 系统内置模板-病毒性肝炎患者随访服务记录表字段
 *
 */
export const templateColumnsVirus: FollowDetailTableColumns[] = [
  {
    title: '随访日期',
    attr: {
      colspan: 2,
    },
    dataIndex: 'visitTime',
  },
  {
    title: '随访方式',
    attr: {
      colspan: 2,
    },
    dataIndex: 'visitWay',
  },
  {
    title: '症状',
    attr: {
      colspan: 2,
    },
    dataIndex: 'symptoms',
  },
  {
    title: '查体',
    attr: {
      rowspan: 8,
    },
    isParent: true,
  },
  {
    title: '血压（mmHg）',
    attr: {},
    dataIndex: 'bloodPressure',
  },
  {
    title: '体重(kg)',
    attr: {},
    dataIndex: 'weight',
  },
  {
    title: '体质指数(BMI）（kg/m²）',
    attr: {},
    dataIndex: 'bmi',
  },
  {
    title: '心率（次/分钟）',
    attr: {},
    dataIndex: 'heartRate',
  },
  // 腹水
  {
    title: '腹水',
    attr: {},
    dataIndex: 'ascites',
  },
  // 蜘蛛痣
  {
    title: '蜘蛛痣',
    attr: {},
    dataIndex: 'spiderNevus',
  },
  {
    title: '其他',
    attr: {},
    dataIndex: 'other',
  },
  {
    title: '生 活 方 式 指 导',
    attr: {
      rowspan: 8,
    },
    isParent: true,
  },
  {
    title: '饮食',
    attr: {},
    dataIndex: 'diet',
  },
  {
    title: '戒烟',
    attr: {},
    dataIndex: 'quitSmoking',
  },
  {
    title: '戒酒',
    attr: {},
    dataIndex: 'quitDrinking',
  },
  {
    title: '遵守咳嗽礼仪',
    attr: {},
    dataIndex: 'noDrugAbuse',
  },
  {
    title: '休息与活动',
    attr: {},
    dataIndex: 'restAndActivity',
  },
  // 心理调整
  {
    title: '心理调整',
    attr: {},
    dataIndex: 'psychologicalAdjustment',
  },
  // 遵医行为
  {
    title: '遵医行为',
    attr: {},
    dataIndex: 'complianceBehavior',
  },
  // 近期异常辅助检查结果
  {
    title: '近期异常辅助检查结果',
    attr: {
      colspan: 2,
    },
    dataIndex: 'recentAbnormalResults',
  },
  // 服药依从性
  {
    title: '服药依从性',
    attr: {
      colspan: 2,
    },
    dataIndex: 'medicationCompliance',
  },
  // 药物不良反应
  {
    title: '药物不良反应',
    attr: {
      colspan: 2,
    },
    dataIndex: 'adverseDrugReactions',
  },
  // 用 药
  {
    title: '用 药',
    attr: {
      colspan: 2,
    },
    dataIndex: 'drugDetail',
  },
  // VTE 风险指导
  {
    title: 'VTE',
    attr: {
      rowspan: 9,
    },
    isParent: true,
  },
  {
    title: 'VTE 风险指导',
    attr: {},
    dataIndex: 'vteRisk',
  },
  // 是否停用抗凝药物
  {
    title: '是否停用抗凝药物',
    attr: {},
    dataIndex: 'isStopAnticoagulation',
  },

  // 是否出血(使用抗凝药物者填写）
  {
    title: '是否出血',
    attr: {},
    dataIndex: 'isUseAnticoagulation',
  },

  // PTE症状
  {
    title: 'PTE症状',
    attr: {},
    dataIndex: 'pteSymptoms',
  },

  // DVT症状
  {
    title: 'DVT症状',
    attr: {},
    dataIndex: 'dvtSymptoms',
  },

  // 近期凝血检查及下肢彩超等结果
  {
    title: '近期凝血检查及下肢彩超等结果',
    attr: {},
    dataIndex: 'bloodTestResult',
  },
  // 转归
  {
    title: '转归',
    attr: {},
    dataIndex: 'transferTo',
  },
  // 健康指导
  {
    title: '健康指导',
    attr: {},
    dataIndex: 'healthGuidance',
  },
  // 是否电话随访
  {
    title: '是否电话随访',
    attr: {
      colspan: 2,
    },
    dataIndex: 'isPhoneFollow',
  },
  // 预约复查
  {
    title: '预约复查',
    attr: {
      colspan: 2,
    },
    dataIndex: 'lastVisitTime',
  },
  // 此次随访评价
  {
    title: '此次随访评价',
    attr: {
      colspan: 2,
    },
    dataIndex: 'evaluation',
  },
  // 备注
  {
    title: '备注',
    attr: {
      colspan: 2,
    },
    dataIndex: 'remark',
  },
  // 随访人
  {
    title: '随访人',
    attr: {
      colspan: 2,
    },
    dataIndex: 'visitUser',
  },
];

export const templateColumnsAIDS: FollowDetailTableColumns[] = [
  {
    title: '随访日期',
    attr: {
      colspan: 2,
    },
    dataIndex: 'visitTime',
  },
  {
    title: '随访方式',
    attr: {
      colspan: 2,
    },
    dataIndex: 'visitWay',
  },
  {
    title: '症状',
    attr: {
      colspan: 2,
    },
    dataIndex: 'symptoms',
  },
  {
    title: '查体',
    attr: {
      rowspan: 6,
    },
    isParent: true,
  },

  {
    title: '血压（mmHg）',
    attr: {},
    dataIndex: 'bloodPressure',
  },
  {
    title: '体重(kg)',
    attr: {},
    dataIndex: 'weight',
  },
  {
    title: '体质指数(BMI）（kg/m²）',
    attr: {},
    dataIndex: 'bmi',
  },
  {
    title: '心率（次/分钟）',
    attr: {},
    dataIndex: 'heartRate',
  },
  {
    title: '其他',
    attr: {},
    dataIndex: 'other',
  },
  {
    title: '生 活 方 式 指 导',
    attr: {
      rowspan: 8,
    },
    isParent: true,
  },
  {
    title: '饮食',
    attr: {},
    dataIndex: 'diet',
  },
  {
    title: '戒烟',
    attr: {},
    dataIndex: 'quitSmoking',
  },
  {
    title: '戒酒',
    attr: {},
    dataIndex: 'quitDrinking',
  },
  {
    title: '遵守咳嗽礼仪',
    attr: {},
    dataIndex: 'noDrugAbuse',
  },
  {
    title: '休息与活动',
    attr: {},
    dataIndex: 'restAndActivity',
  },
  // 心理调整
  {
    title: '心理调整',
    attr: {},
    dataIndex: 'psychologicalAdjustment',
  },
  // 遵医行为
  {
    title: '遵医行为',
    attr: {},
    dataIndex: 'complianceBehavior',
  },
  // 近期CD4+T淋巴细胞计数
  {
    title: '近期CD4+T淋巴细胞计数',
    attr: {
      colspan: 2,
    },
    dataIndex: 'recentCD4',
  },
  // 服药依从性
  {
    title: '服药依从性',
    attr: {
      colspan: 2,
    },
    dataIndex: 'medicationCompliance',
  },
  // 药物不良反应
  {
    title: '药物不良反应',
    attr: {
      colspan: 2,
    },
    dataIndex: 'adverseDrugReactions',
  },
  // 用 药
  {
    title: '用 药',
    attr: {
      colspan: 2,
    },
    dataIndex: 'drugDetail',
  },
  // VTE 风险指导
  {
    title: 'VTE',
    attr: {
      rowspan: 9,
    },
    isParent: true,
  },
  {
    title: 'VTE 风险指导',
    attr: {},
    dataIndex: 'vteRisk',
  },
  // 是否停用抗凝药物
  {
    title: '是否停用抗凝药物',
    attr: {},
    dataIndex: 'isStopAnticoagulation',
  },

  // 是否出血(使用抗凝药物者填写）
  {
    title: '是否出血',
    attr: {},
    dataIndex: 'isUseAnticoagulation',
  },

  // PTE症状
  {
    title: 'PTE症状',
    attr: {},
    dataIndex: 'pteSymptoms',
  },

  // DVT症状
  {
    title: 'DVT症状',
    attr: {},
    dataIndex: 'dvtSymptoms',
  },

  // 近期凝血检查及下肢彩超等结果
  {
    title: '近期凝血检查及下肢彩超等结果',
    attr: {},
    dataIndex: 'bloodTestResult',
  },
  // 转归
  {
    title: '转归',
    attr: {},
    dataIndex: 'transferTo',
  },
  // 健康指导
  {
    title: '健康指导',
    attr: {},
    dataIndex: 'healthGuidance',
  },
  // 是否电话随访
  {
    title: '是否电话随访',
    attr: {
      colspan: 2,
    },
    dataIndex: 'isPhoneFollow',
  },
  // 预约复查
  {
    title: '预约复查',
    attr: {
      colspan: 2,
    },
    dataIndex: 'lastVisitTime',
  },
  // 此次随访评价
  {
    title: '此次随访评价',
    attr: {
      colspan: 2,
    },
    dataIndex: 'evaluation',
  },
  // 备注
  {
    title: '备注',
    attr: {
      colspan: 2,
    },
    dataIndex: 'remark',
  },
  // 随访人
  {
    title: '随访人',
    attr: {
      colspan: 2,
    },
    dataIndex: 'visitUser',
  },
];

export const BuiltInTemplateColumnsMap = {
  肺结核患者随访记录表: templateColumns,
  病毒性肝炎患者随访服务记录表: templateColumnsVirus,
  'HIV/AIDS患者随访服务记录表': templateColumnsAIDS,
};
