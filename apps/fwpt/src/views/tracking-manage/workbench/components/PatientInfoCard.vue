<script setup lang="ts">
  import { Icon } from '@ft/internal/components/Icon';

  interface PatientInfo {
    name: string;
    birthday: string;
    age: string | number;
    sex: string | number;
    phone: string;
    idCardNo: string;
  }
  defineProps<{
    patientInfo: Partial<PatientInfo>;
  }>();

  function formatName(name) {
    if (name.length >= 3) {
      const firstChar = name[0];
      const lastChar = name[name.length - 1];
      return firstChar + '*' + lastChar;
    }
    return name;
  }
</script>

<template>
  <div class="bg-#fff rounded-[0_8px_8px_8px] py-3 px-4">
    <div class="relative flex justify-between gap-3 mb-3">
      <div
        class="rounded-4 flex items-center text-20px justify-center fw-bold text-#fff bg-gradient-to-br bg-left-top from-#FF6161 to-#FF9494 from-4% to-99% h-56px w-56px"
      >
        {{ formatName(patientInfo?.name) }}
      </div>
      <div class="flex-1 flex flex-col justify-center text-14px gap-6px">
        <div class="flex gap-2 items-center">
          <span class="text-#333333 fw-bold">{{ patientInfo?.name }}</span>
          <Icon v-if="patientInfo?.sex === 2" icon="woman|svg" :size="16" />
          <Icon v-else icon="man|svg" :size="16" />
        </div>
        <div class="text-#999999">
          {{ patientInfo?.birthday || '-' }} ({{ patientInfo?.age || '-' }})
        </div>
      </div>
    </div>
    <div class="flex flex-col gap-2 text-#666666">
      <div> {{ patientInfo?.phone }} </div>
      <div> {{ patientInfo?.idCardNo }} </div>
    </div>
  </div>
</template>
