export interface WorkbenchConfig {
  todayFollow: {
    show: boolean;
    todayTask: boolean;
    todayFollowUp: boolean;
    todayFollowPadding: boolean;
    historicalTasks: boolean;
    historicalFollowedUp: boolean;
    unSuccessFollowUp: boolean;
  };
  deptFollow: {
    show: boolean;
    todayTask: boolean;
    todayFollowUp: boolean;
    todayFollowPadding: boolean;
    historicalTasks: boolean;
    historicalFollowedUp: boolean;
    unSuccessFollowUp: boolean;
  };
  entireHospitalFollow: {
    show: boolean;
    historicalTasks: boolean;
    followedUpTotal: boolean;
    pendingFollowUpTotal: boolean;
    recallFollowUpTotal: boolean;
    unSuccessFollowUpTotal: boolean;
  };
}

export const DefaultWorkbenchConfig: WorkbenchConfig = {
  // 今日个人随访工作量
  todayFollow: {
    show: true,
    // 今日任务
    todayTask: true,
    // 今日已随访
    todayFollowUp: true,
    // 今日待随访
    todayFollowPadding: true,
    // 历史总任务
    historicalTasks: true,
    // 历史已随访
    historicalFollowedUp: true,
    // 随访未成功数
    unSuccessFollowUp: true,
  },
  // 科室随访工作量
  deptFollow: {
    show: true,
    // 今日任务
    todayTask: true,
    // 今日已随访
    todayFollowUp: true,
    // 今日待随访
    todayFollowPadding: true,
    // 历史总任务
    historicalTasks: true,
    // 历史已随访
    historicalFollowedUp: true,
    // 随访未成功数
    unSuccessFollowUp: true,
  },
  // 全院随访任务汇总
  entireHospitalFollow: {
    show: true,
    // 历史总任务
    historicalTasks: true,
    // 已随访总人数
    followedUpTotal: true,
    // 待随访总人数
    pendingFollowUpTotal: true,
    // 随访召回总人数
    recallFollowUpTotal: true,
    // 随访未成功总人数
    unSuccessFollowUpTotal: true,
  },
};
