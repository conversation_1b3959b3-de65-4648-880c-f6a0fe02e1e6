<script setup lang="ts">
  import type { IFollowVisitRecordPost, VisitDataList } from '/@/api/workbench';
  import { saveFollowVisitRecord, updateFollowVisitRecord } from '/@/api/workbench';
  import { FixedAction } from '@ft/components';
  import { Button } from '@ft/internal/components/Button';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { useRequest } from '@ft/request';
  import { pick } from 'lodash-es';
  import { computed, onMounted, ref } from 'vue';
  import { extractField } from './data';
  import { useFollowRecordFormData } from './follow-up-registration.data';

  defineOptions({
    disabledReloadOnQueryChanged: true,
  });

  const go = useGo();

  const mode = useRouteQuery('mode', 'add', { transform: String });
  const templateId = useRouteQuery('templateId', '', { transform: String });
  const visitRecordId = useRouteQuery('visitRecordId', '', { transform: String });
  const visitType = useRouteQuery('visitType', 1, { transform: Number });

  const isView = computed(() => mode.value === 'view');
  const isEdit = computed(() => mode.value === 'edit');
  const isAdd = computed(() => mode.value === 'add');

  const [registerFrom, formAction] = useForm({
    labelWidth: 140,
    colon: true,
    showActionButtonGroup: false,
    disabled: isView,
  });

  const { exec, visitTemplate, objectFields, fieldTemplateDataMap, listFieldKey } =
    useFollowRecordFormData({
      isAdd,
      formAction,
    });
  onMounted(async () => {
    exec(templateId.value, visitRecordId.value);
  });

  const divRef = ref<HTMLDivElement | null>(null);

  const { run: saveFollowVisitRecordRun } = useRequest(
    (params) => (isAdd.value ? saveFollowVisitRecord(params) : updateFollowVisitRecord(params)),
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess() {
        go(-1);
      },
    },
  );

  async function onSave(isSubmit = true) {
    const values = await formAction.validate();

    const visitContentDataList: Partial<VisitDataList>[] = [];
    const visitObjectDataList: Partial<VisitDataList>[] = [];

    for (const key in values) {
      if (objectFields.value.includes(key)) {
        visitObjectDataList.push({
          dataKey: key,
          dataValue: values[key],
          dataModel: 1,
          visitTemplateId: templateId.value,
          templateDataId: fieldTemplateDataMap.value.get(key) || undefined,
        });
      } else {
        const dataValue = listFieldKey.value.includes(key)
          ? values[key]?.join(',')
          : values[key]
          ? values[key]
          : '';
        if (key === 'id') continue;
        visitContentDataList.push({
          dataKey: key,
          dataValue,
          dataModel: 2,
          visitTemplateId: templateId.value,
          templateDataId: fieldTemplateDataMap.value.get(key) || undefined,
        });
      }
    }
    const params: DeepPartial<IFollowVisitRecordPost> = pick(values, [...extractField, 'id']);
    params.visitContentDataList = visitContentDataList;
    params.visitObjectDataList = visitObjectDataList;
    params.visitTemplateId = templateId.value;
    params.visitTemplateName = visitTemplate.value?.templateName;
    params.visitType = visitType.value;
    if (isSubmit) {
      // 提交
      params.visitStatus = 1;
    } else {
      // 保存
      params.visitStatus = 0;
    }

    saveFollowVisitRecordRun(params);
  }

  function onCancel() {
    go({
      name: 'Workbench',
    });
  }

  function onEdit() {
    mode.value = 'edit';
    formAction.setFieldsValue({ isEdit: true });
  }
  function onEditCancel() {
    mode.value = 'view';
    formAction.setFieldsValue({ isEdit: false });
  }
</script>

<template>
  <div class="h-full pr-4">
    <div class="bg-white h-full p-6" ref="divRef">
      <div class="page-content flex flex-col h-full">
        <div class="page-title relative">
          <h2 class="text-center text-2xl font-normal">{{ visitTemplate?.templateName }}</h2>
          <div class="edit-actions absolute right-0 top-0 flex gap-3">
            <Button v-if="isView" type="primary" @click="onEdit">编辑</Button>
            <template v-if="isEdit">
              <Button @click="onEditCancel">取消</Button>
              <Button @click="onSave(false)">保存</Button>
              <Button type="primary" @click="onSave">提交</Button>
            </template>
          </div>
        </div>
        <div class="page-body p-4 !h-[calc(100vh-194px)] of-y-auto pb-8">
          <BasicForm @register="registerFrom">
            <template #moduleName="{ schema }">
              <div class="flex items-center gap-2 mb-3">
                <span v-if="schema.label" class="inline-block bg-primary-color w-3px h-1em"></span>
                <span>{{ schema.label }}</span>
              </div>
            </template>
          </BasicForm>
        </div>
      </div>
    </div>
    <FixedAction v-if="isAdd" class="justify-end pr-6" :referenceEl="divRef">
      <Button @click="onCancel"> 取消 </Button>
      <Button @click="onSave(false)"> 保存 </Button>
      <Button type="primary" @click="onSave"> 提交 </Button>
    </FixedAction>
  </div>
</template>
