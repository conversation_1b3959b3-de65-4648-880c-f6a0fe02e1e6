<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { computed, ref } from 'vue';
  import { useRequest } from '@ft/request';
  import { editFormSchema } from './data';
  import { backUpFollowVisit } from '/@/api/workbench';

  const emit = defineEmits(['register', 'success']);

  const mode = ref('add');
  const getTitle = computed(() => '随访召回');

  const [registerForm, formAction] = useForm({
    labelWidth: 80,
    schemas: editFormSchema,
    showActionButtonGroup: false,
  });

  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data.mode;
    formAction.setFieldsValue(data.record);
  });
  const { loading, run: saveRun } = useRequest(backUpFollowVisit, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      emit('success');
      closeModal();
    },
  });

  function onOk() {
    formAction.validate().then((values) => {
      saveRun(values);
    });
  }
</script>

<template>
  <BasicModal
    destroy-on-close
    @register="register"
    :width="500"
    :min-height="100"
    centered
    :title="getTitle"
    @ok="onOk"
    :ok-button-props="{
      loading,
    }"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<style scoped></style>
