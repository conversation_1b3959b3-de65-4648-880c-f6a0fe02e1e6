<script setup lang="ts">
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { saveWorkbenchConfig } from '/@/api/workbench';
  import type { FormInstance } from 'ant-design-vue';
  import { Checkbox, Col, Form, FormItem, Row } from 'ant-design-vue';
  import { ref, watch } from 'vue';
  import { useUserStore } from '@ft/internal/store/modules/user';
  import { cloneDeep } from 'lodash-es';
  import type { WorkbenchConfig } from './ConfigureWorkspace';
  import { DefaultWorkbenchConfig } from './ConfigureWorkspace';
  const defaultConfig = cloneDeep(DefaultWorkbenchConfig);

  const props = defineProps<{
    config?: WorkbenchConfig;
  }>();
  const emit = defineEmits(['register', 'success']);

  const [register, { closeModal }] = useModalInner();

  const formRef = ref<FormInstance>();

  const { userInfo } = useUserStore();
  const { loading, run: saveRun } = useRequest(saveWorkbenchConfig, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      emit('success');
      closeModal();
    },
  });
  const formModel = ref(defaultConfig);

  watch(
    () => props.config,
    (config) => {
      if (config) {
        formModel.value = cloneDeep(config);
      }
    },
  );

  function onOk() {
    formRef.value?.validate().then(() => {
      saveRun({
        userId: userInfo!.id,
        configContent: JSON.stringify(formModel.value),
      });
    });
  }
</script>

<template>
  <BasicModal
    v-bind="$attrs"
    @register="register"
    :width="740"
    :min-height="120"
    title="配置工作台"
    centered
    @ok="onOk"
    :ok-button-props="{
      loading,
    }"
    ok-text="保存"
    :destroy-on-close="true"
  >
    <Form
      ref="formRef"
      :model="formModel"
      :label-col="{ style: { width: '140px' } }"
      :wrapper-col="{ span: 18 }"
    >
      <FormItem label="今日个人随访工作量" class="config-item">
        <div class="module-container">
          <Checkbox v-model:checked="formModel.todayFollow.show">显示模块</Checkbox>
          <div class="checkbox-group">
            <Row :gutter="32">
              <Col :span="8">
                <Checkbox
                  v-model:checked="formModel.todayFollow.todayTask"
                  :disabled="!formModel.todayFollow.show"
                  >今日任务</Checkbox
                >
              </Col>
              <Col :span="8">
                <Checkbox
                  v-model:checked="formModel.todayFollow.todayFollowUp"
                  :disabled="!formModel.todayFollow.show"
                  >今日已随访</Checkbox
                >
              </Col>
              <Col :span="8">
                <Checkbox
                  v-model:checked="formModel.todayFollow.todayFollowPadding"
                  :disabled="!formModel.todayFollow.show"
                  >今日待随访</Checkbox
                >
              </Col>
            </Row>
            <Row :gutter="32">
              <Col :span="8">
                <Checkbox
                  v-model:checked="formModel.todayFollow.historicalTasks"
                  :disabled="!formModel.todayFollow.show"
                  >历史总任务</Checkbox
                >
              </Col>
              <Col :span="8">
                <Checkbox
                  v-model:checked="formModel.todayFollow.historicalFollowedUp"
                  :disabled="!formModel.todayFollow.show"
                  >历史已随访</Checkbox
                >
              </Col>
              <Col :span="8">
                <Checkbox
                  v-model:checked="formModel.todayFollow.unSuccessFollowUp"
                  :disabled="!formModel.todayFollow.show"
                  >随访未成功数</Checkbox
                >
              </Col>
            </Row>
          </div>
        </div>
      </FormItem>

      <FormItem label="科室随访工作量" class="config-item">
        <div class="module-container">
          <Checkbox v-model:checked="formModel.deptFollow.show">显示模块</Checkbox>
          <div class="checkbox-group">
            <Row :gutter="32">
              <Col :span="8">
                <Checkbox
                  v-model:checked="formModel.deptFollow.todayTask"
                  :disabled="!formModel.deptFollow.show"
                  >今日任务</Checkbox
                >
              </Col>
              <Col :span="8">
                <Checkbox
                  v-model:checked="formModel.deptFollow.todayFollowUp"
                  :disabled="!formModel.deptFollow.show"
                  >今日已随访</Checkbox
                >
              </Col>
              <Col :span="8">
                <Checkbox
                  v-model:checked="formModel.deptFollow.todayFollowPadding"
                  :disabled="!formModel.deptFollow.show"
                  >今日待随访</Checkbox
                >
              </Col>
            </Row>
            <Row :gutter="32">
              <Col :span="8">
                <Checkbox
                  v-model:checked="formModel.deptFollow.historicalTasks"
                  :disabled="!formModel.deptFollow.show"
                  >历史总任务</Checkbox
                >
              </Col>
              <Col :span="8">
                <Checkbox
                  v-model:checked="formModel.deptFollow.historicalFollowedUp"
                  :disabled="!formModel.deptFollow.show"
                  >历史已随访</Checkbox
                >
              </Col>
              <Col :span="8">
                <Checkbox
                  v-model:checked="formModel.deptFollow.unSuccessFollowUp"
                  :disabled="!formModel.deptFollow.show"
                  >随访未成功数</Checkbox
                >
              </Col>
            </Row>
          </div>
        </div>
      </FormItem>

      <FormItem label="全院随访任务汇总" class="config-item">
        <div class="module-container">
          <Checkbox v-model:checked="formModel.entireHospitalFollow.show">显示模块</Checkbox>
          <div class="checkbox-group">
            <Row :gutter="32">
              <Col :span="8">
                <Checkbox
                  v-model:checked="formModel.entireHospitalFollow.historicalTasks"
                  :disabled="!formModel.entireHospitalFollow.show"
                  >历史总任务</Checkbox
                >
              </Col>
              <Col :span="8">
                <Checkbox
                  v-model:checked="formModel.entireHospitalFollow.followedUpTotal"
                  :disabled="!formModel.entireHospitalFollow.show"
                  >已随访总人数</Checkbox
                >
              </Col>
              <Col :span="8">
                <Checkbox
                  v-model:checked="formModel.entireHospitalFollow.pendingFollowUpTotal"
                  :disabled="!formModel.entireHospitalFollow.show"
                  >待随访总人数</Checkbox
                >
              </Col>
            </Row>
            <Row :gutter="32">
              <Col :span="8">
                <Checkbox
                  v-model:checked="formModel.entireHospitalFollow.recallFollowUpTotal"
                  :disabled="!formModel.entireHospitalFollow.show"
                  >随访召回总人数</Checkbox
                >
              </Col>
              <Col :span="8">
                <Checkbox
                  v-model:checked="formModel.entireHospitalFollow.unSuccessFollowUpTotal"
                  :disabled="!formModel.entireHospitalFollow.show"
                  >随访未成功总人数</Checkbox
                >
              </Col>
            </Row>
          </div>
        </div>
      </FormItem>
    </Form>
  </BasicModal>
</template>

<style scoped>
  .config-item {
    margin-bottom: 24px;
  }

  .module-container {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding-top: 6px;
  }

  .checkbox-group {
    margin-left: 0;
  }

  .checkbox-group :deep(.ant-row) {
    margin-bottom: 12px;
  }

  .checkbox-group :deep(.ant-row:last-child) {
    margin-bottom: 0;
  }

  :deep(.ant-checkbox-wrapper) {
    margin-left: 0;
  }
</style>
