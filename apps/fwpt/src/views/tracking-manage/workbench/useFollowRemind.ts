import { h } from 'vue';
import { getFollowReminders } from '/@/api/workbench';
import { useMessage } from '@ft/internal/hooks/web/useMessage';
import { Button } from '@ft/internal/components/Button';
import { tryOnMounted, tryOnUnmounted } from '@vueuse/core';

export function useFollowRemind() {
  const { notification } = useMessage();
  const key = `open${Date.now()}`;
  tryOnMounted(() => {
    getFollowReminders().then((msg) => {
      if (!msg) {
        return;
      }
      notification.info({
        message: '随访提醒',
        description: msg,
        placement: 'bottomRight',
        duration: 0,
        key,
        btn: () =>
          h(
            Button,
            {
              type: 'primary',
              size: 'small',
              onClick: () => notification.close(key),
            },
            { default: () => '我知道了' },
          ),
      });
    });
  });

  tryOnUnmounted(() => {
    notification.close(key);
  });
}
