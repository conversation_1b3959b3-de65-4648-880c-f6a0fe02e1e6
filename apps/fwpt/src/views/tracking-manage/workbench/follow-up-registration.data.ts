import type { FormSchema } from '@ft/internal/components/Table';
import type { Ref } from 'vue';
import { computed, ref, unref } from 'vue';
import { omit } from 'lodash-es';
import type { FormActionType } from '@ft/internal/components/Form';
import {
  BuiltInTemplateMap,
  FollowTuberculosisTemplate,
} from '../follow-task/components/TemplateEdit/data';
import {
  getFollowVisitRecordDetail,
  queryInspectionIndex,
  queryMedicationRecordList,
} from '/@/api/workbench';
import type { IFollowTemplate } from '/@/api/follow-template';
import { getTemplateDetail } from '/@/api/follow-template';
import { TemplateTag } from '../follow-task/components/TemplateEdit/types';
import { genSchemas } from '../follow-task/components/TemplateEdit/utils';
import { autoFillFields } from './data';

/**
 * @description: 肺结核患者随访服务记录表
 * 患者姓名
 * 患者编码
 * 患者住院号
 * 出院日期
 * 随访日期
 * 随访方式
 * 症状
 * 其他症状
 * 血压（mmHg）
 * 体重（kg）
 * 体质指数（BMI）（kg/m²）
 * 心率（次/分钟）
 * 其他
 * 饮食
 * 戒烟
 * 戒酒
 * 遵守咳嗽礼仪
 * 休息与活动
 * 心里调整
 * 遵医行为
 * 近期异常辅助检查结果
 * 服药依从性
 * 药物不良反应
 * 药物名称1
 * 用法用量
 * VTE患者风险
 * 健康指导
 * 预约复查
 * 随访评价
 * 备注
 * 随访人
 *
 */

export const addFormSchema: FormSchema[] = FollowTuberculosisTemplate;

interface Opt {
  formAction: FormActionType;
  isAdd: Ref<boolean>;
}

export function useFollowRecordFormData({ formAction, isAdd }: Opt) {
  const schemas = ref<any[]>([]);
  const listFieldKey = computed(() =>
    schemas.value
      .filter((i) => ['CheckboxGroup', 'ApiCheckboxGroup'].includes(i.component))
      .map((i) => i.field),
  );

  const visitTemplate = ref<IFollowTemplate>();

  const fieldTemplateDataMap = ref(new Map<string, string>());
  const objectFields = ref<string[]>([]);

  async function exec(templateId: string, visitRecordId?: string) {
    try {
      const template = await getTemplateDetail(templateId);
      visitTemplate.value = template;
      const isBuiltIn = template.templateTag === TemplateTag.BuiltIn;

      if (template.templateTag === TemplateTag.BuiltIn) {
        schemas.value = BuiltInTemplateMap[template.templateName];
      } else {
        schemas.value = genSchemas({
          category: template.templateType,
          object: template.visitObjectList,
          content: template.visitContentList,
        });

        objectFields.value = template.visitObjectList.map((i) => i.dataKey);
        template.visitContentList.forEach((i) => {
          fieldTemplateDataMap.value.set(i.id!, i.id!);
        });
        template.visitObjectList.forEach((i) => {
          fieldTemplateDataMap.value.set(i.dataKey!, i.id!);
        });
      }
      schemas.value.unshift({
        field: 'id',
        component: 'Input',
        label: 'ID',
        show: false,
      });
      await formAction.resetSchema(schemas.value);

      if (visitRecordId) {
        const data = await getFollowVisitRecordDetail(visitRecordId);
        let values: Recordable = {};
        if (!unref(isAdd)) {
          data?.visitObjectDataList?.forEach((i) => {
            values[i.dataKey] = i.dataValue;
          });
          data?.visitContentDataList?.forEach((i) => {
            const key = template.templateTag === TemplateTag.BuiltIn ? i.dataKey : i.templateDataId;
            values[key] = i.dataValue;
          });

          for (const key in values) {
            listFieldKey.value.includes(key) && (values[key] = values[key]?.split(','));
          }
          values.id = data.id;
          values.isEdit = true;
          values = {
            ...values,
            ...omit(values, ['visitObjectDataList', 'visitContentDataList']),
          };
        }
        // 填充基本信息和药物信息
        autoFillFields.forEach((key) => {
          values[key] = data[key];
        });
        if (isBuiltIn && data.inId) {
          const medicationList = await queryMedicationRecordList(data.inId);
          // 药品名称+用量+用量单位+用法+频次
          values.drugDetail = medicationList
            .map(
              (i) =>
                `${i.drugName || ''} ${i.drugUsed || ''}${i.douseUnit || ''}  ${
                  i.drugUsage || ''
                } ${i.drugUseDrequency || ''}`,
            )
            .join('\n');
          const inspectionList = await queryInspectionIndex(data.inId);
          //项目名称: 项目指标1 +项目结果2，项目指标2+项目结果2
          values.recentAbnormalResults = inspectionList
            ?.map((item) => {
              const exceptionIndexList = item.exceptionIndexList
                .map((index) => {
                  return `${index.indexName}+${index.exceptionFlag}`;
                })
                .join(',');
              return `${item.itemName}:${exceptionIndexList}`;
            })
            .join('\n');
        }
        formAction.setFieldsValue(values);
      } else {
        formAction.setFieldsValue({ isEdit: true });
      }
    } catch (error) {
      console.error(error);
    }
  }

  return {
    visitTemplate,
    fieldTemplateDataMap,
    objectFields,
    exec,
    listFieldKey,
  };
}
