<script setup lang="ts">
  import { useGo } from '/@@/hooks/web/usePage';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Empty, Switch, Tabs } from 'ant-design-vue';
  import { useModal } from '@ft/internal/components/Modal';
  import { Icon } from '@ft/internal/components/Icon';
  import { computed, onMounted, ref } from 'vue';
  import {
    columnsClose,
    columnsHas,
    columnsToBe,
    searchFormSchemaHas,
    searchFormSchemaToBe,
  } from './data';
  import { useRoute } from 'vue-router';
  import FollowUpRecallModal from './FollowUpRecallModal.vue';
  import CloseFollowModal from './CloseFollowModal.vue';
  import SelectTemplateModal from './SelectTemplateModal.vue';
  import type { IFollowVisitRecord, IVisitCount } from '/@/api/workbench';
  import {
    autoGenerate,
    followVisitRecordExportExcel,
    getFollowVisitPage,
    getMyWorkbenchConfig,
    queryVisitCount,
  } from '/@/api/workbench';
  import { useToggle } from '@vueuse/core';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import { getToken } from '@ft/internal/utils/auth';
  import { useWorkbenchStore } from '/@/store/modules/workbench';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import { useFollowRemind } from './useFollowRemind';
  import printJS from 'print-js';
  import ConfigureWorkspace from './ConfigureWorkspace.vue';
  import { Button } from '@ft/internal/components/Button';
  import type { WorkbenchConfig } from './ConfigureWorkspace';
  import { DefaultWorkbenchConfig } from './ConfigureWorkspace';
  import { cloneDeep } from 'lodash-es';

  const defaultConfig = cloneDeep(DefaultWorkbenchConfig);

  const go = useGo();
  const activeKey = ref(0);
  useFollowRemind();

  const workbenchConfig = ref<WorkbenchConfig>();

  const getAllShow = computed(() => {
    return (
      workbenchConfig.value?.todayFollow.show ||
      workbenchConfig.value?.deptFollow.show ||
      workbenchConfig.value?.entireHospitalFollow.show
    );
  });

  function getWorkbenchConfig() {
    getMyWorkbenchConfig().then((data) => {
      workbenchConfig.value = data?.configContent ? JSON.parse(data.configContent) : defaultConfig;
    });
  }
  onMounted(() => {
    getWorkbenchConfig();
  });

  const formScheamMap = {
    0: searchFormSchemaToBe,
    1: searchFormSchemaHas,
    2: searchFormSchemaHas,
  };

  const columnsMap = {
    0: columnsToBe,
    1: columnsHas,
    2: columnsClose,
  };
  const searchFormSchema = computed(() => {
    return formScheamMap[activeKey.value] || [];
  });
  const columns = computed(() => {
    return columnsMap[activeKey.value] || [];
  });

  const isExpanded = ref(true);
  const formConfigSpan = computed(() => {
    return (
      {
        0: isExpanded.value ? 8 : 24,
        1: isExpanded.value ? 24 : 24,
        2: isExpanded.value ? 24 : 24,
      }[activeKey.value] || 8
    );
  });

  const [registerTable, tableIns] = useTable({
    // dataSource: [{}],
    api: getFollowVisitPage,
    columns: columns,
    useSearchForm: true,
    formConfig: {
      labelWidth: 130,
      actionColOptions: {
        // @ts-ignore
        span: formConfigSpan,
        style: {
          textAlign: 'right',
        },
      },
      schemas: searchFormSchema,
      fieldMapToTime: [
        ['dischargeDate', ['dischargeDateStartTime', 'dischargeDateEndTime'], 'YYYY-MM-DD'],
        ['followUpDate', ['visitStartTime', 'visitEndTime'], 'YYYY-MM-DD'],
      ],
      showAdvancedButton: false,
    },
    beforeFetch(params) {
      params.visitStatus = activeKey.value;
      params.visitType = 1;
      return params;
    },
    actionColumn: {
      width: 130,
      title: '操作',
      dataIndex: 'action',
    },
    isCanResizeParent: true,
    // resizeHeightOffset: 10,
    // canResize: false,
    // scroll: {
    //   y: 480,
    // },
  });

  const [register, { openModal }] = useModal();
  const visitCount = ref<IVisitCount>();

  const dataList = computed(() => [
    {
      icon: 'personal-follow-up|svg',
      name: '今日个人随访工作量',
      show: workbenchConfig.value?.todayFollow.show,
      taskList: [
        {
          show: workbenchConfig.value?.todayFollow.todayTask,
          label: '今日任务',
          value: visitCount.value?.oneselfCount?.todayTaskNum || 0,
        },
        {
          show: workbenchConfig.value?.todayFollow.historicalTasks,
          label: '历史总任务',
          value: visitCount.value?.oneselfCount?.totalTaskNum || 0,
        },
      ],
      followUpList: [
        {
          show: workbenchConfig.value?.todayFollow.todayFollowUp,
          label: '今日已随访',
          value: visitCount.value?.oneselfCount?.existVisitNum || 0,
        },
        {
          show: workbenchConfig.value?.todayFollow.todayFollowPadding,
          label: '今日待随访',
          value: visitCount.value?.oneselfCount?.waitVisitNum || 0,
        },
        {
          show: workbenchConfig.value?.todayFollow.historicalFollowedUp,
          label: '历史已随访',
          value: visitCount.value?.oneselfCount?.historyVisitNum || 0,
        },
        {
          show: workbenchConfig.value?.todayFollow.unSuccessFollowUp,
          label: '随访未成功数',
          value: visitCount.value?.oneselfCount?.visitFailNum || 0,
        },
      ],
    },
    {
      icon: 'department-follow-up|svg',
      name: '科室随访工作量',
      show: workbenchConfig.value?.deptFollow.show,
      taskList: [
        {
          show: workbenchConfig.value?.deptFollow.todayTask,
          label: '今日任务',
          value: visitCount.value?.deptCount?.todayTaskNum || 0,
        },
        {
          show: workbenchConfig.value?.deptFollow.historicalTasks,
          label: '历史总任务',
          value: visitCount.value?.deptCount?.totalTaskNum || 0,
        },
      ],
      followUpList: [
        {
          show: workbenchConfig.value?.deptFollow.todayFollowUp,
          label: '今日已随访',
          value: visitCount.value?.deptCount?.existVisitNum || 0,
        },
        {
          show: workbenchConfig.value?.deptFollow.todayFollowPadding,
          label: '今日待随访',
          value: visitCount.value?.deptCount?.waitVisitNum || 0,
        },
        {
          show: workbenchConfig.value?.deptFollow.historicalFollowedUp,
          label: '历史已随访',
          value: visitCount.value?.deptCount?.historyVisitNum || 0,
        },
        {
          show: workbenchConfig.value?.deptFollow.unSuccessFollowUp,
          label: '随访未成功数',
          value: visitCount.value?.deptCount?.visitFailNum || 0,
        },
      ],
    },
    {
      icon: 'hospital-follow-up|svg',
      name: '全院随访任务汇总',
      show: workbenchConfig.value?.entireHospitalFollow.show,
      taskList: [
        {
          show: workbenchConfig.value?.entireHospitalFollow.historicalTasks,
          label: '历史总任务',
          value: visitCount.value?.hospitalCount?.totalTaskNum || 0,
        },
      ],
      followUpList: [
        {
          show: workbenchConfig.value?.entireHospitalFollow.followedUpTotal,
          label: '已随访总人数',
          value: visitCount.value?.hospitalCount?.existVisitNum || 0,
        },
        {
          show: workbenchConfig.value?.entireHospitalFollow.pendingFollowUpTotal,
          label: '待随访总人数',
          value: visitCount.value?.hospitalCount?.waitVisitNum || 0,
        },
        {
          show: workbenchConfig.value?.entireHospitalFollow.recallFollowUpTotal,
          label: '随访召回总人数',
          value: visitCount.value?.hospitalCount?.visitBackNum || 0,
        },
        {
          show: workbenchConfig.value?.entireHospitalFollow.unSuccessFollowUpTotal,
          label: '随访未成功总人数',
          value: visitCount.value?.hospitalCount?.visitFailNum || 0,
        },
      ],
    },
  ]);
  const query = useRoute().query;
  const store = useWorkbenchStore();
  onMounted(() => {
    queryVisitCount().then((data) => {
      visitCount.value = data;
    });
    if (query.visitRecordId && store.isFromExternal) {
      openSelectModal(true, {
        visitRecordId: query.visitRecordId,
        mode: 'edit',
        record: {
          infectionDisease: query.infectionDisease,
        },
      });
      store.setFromExternal(false);
    }
    if (query.patientName) {
      activeKey.value = 1;
      tableIns.getForm().resetSchema(formScheamMap[activeKey.value]);
      tableIns.getForm().setFieldsValue({
        patientName: query.patientName,
      });
      tableIns.reload();
    }
  });

  function getClass(icon: string) {
    const iconName = icon.replace('|svg', '');
    return `workbench-${iconName}`;
  }

  const [registerCloseModal, { openModal: openCloseModal }] = useModal();

  function handleFollowUpClose(record) {
    openCloseModal(true, {
      record,
    });
  }

  function createActions(record: Recordable, _column): ActionItem[] {
    const actions: ActionItem[] = [];
    if (activeKey.value === 0) {
      actions.push({
        label: '随访登记',
        type: 'link',
        onClick: handleRegistration.bind(null, record),
      });
    } else {
      // actions.push({
      //   label: '随访详情',
      //   type: 'link',
      //   onClick: handlePreviousDetails.bind(null, record),
      // });
      actions.push({
        label: '继续随访',
        type: 'link',
        onClick: handleContinuedFollowUp.bind(null, record),
      });
    }
    return actions;
  }

  function createDropDownActions(record): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '就诊详情',
        type: 'link',
        ifShow: () => !!record.patientProfileRecordId,
        onClick: handleVisitDetails.bind(null, record),
      },
      {
        label: '患者档案',
        type: 'link',
        onClick: handlePatientFile.bind(null, record),
      },
    ];
    if (activeKey.value == 0) {
      // actions.unshift({
      //   label: '既往随访详情',
      //   type: 'link',
      //   onClick: handlePreviousDetails.bind(null, record),
      // });
      actions.push({
        label: '随访未成功标记',
        type: 'link',
        onClick: handleFollowUpClose.bind(null, record),
      });
    }
    if (activeKey.value == 1) {
      actions.unshift({
        label: '随访召回',
        type: 'link',
        onClick: handleFollowUpRecall.bind(null, record),
      });
      actions.unshift({
        label: '随访详情',
        type: 'link',
        onClick: handlePreviousDetails.bind(null, record),
      });
    }
    if (activeKey.value == 2) {
      actions.unshift({
        label: '随访详情',
        type: 'link',
        onClick: handlePreviousDetails.bind(null, record),
      });
    }
    return actions;
  }
  function handleFollowUpRecall(record) {
    openModal(true, {
      mode: 'edit',
      record,
    });
  }
  const [registerSelectTemplate, { openModal: openSelectModal }] = useModal();

  function handleRegistration(record: IFollowVisitRecord) {
    openSelectModal(true, {
      visitRecordId: record.id,
      mode: 'edit',
      record,
    });
  }

  function handleContinuedFollowUp(record: IFollowVisitRecord) {
    openSelectModal(true, {
      visitRecordId: record.id,
      record,
    });
  }
  function handlePreviousDetails(record: IFollowVisitRecord) {
    go({
      name: 'WorkbenchPreviousDetails',
      query: {
        patientId: record.patientId,
        patientName: record.patientName,
        patientAge: record.age,
        patientSex: record.sex,
        patientIdCard: record.idCardNo,
        patientBirthday: record.birthDay,
      },
    });
  }
  const route = useRoute();
  const menuMap = {
    1: 'hepatitis',
    2: 'hiv',
    3: 'tub',
  };
  function handleVisitDetails(record) {
    const path = import.meta.env.DEV
      ? `http://localhost:5511/case-management/${menuMap[record.infectionDisease]}`
      : `/crb-ylyw/case-management/${menuMap[record.infectionDisease]}`;
    const remark = route.meta.remark as Recordable;
    window.open(
      `${path}?id=${remark.appId1}&menuId=${remark.menuId1}&infectionDisease=${
        record.infectionDisease
      }&patientProfileRecordId=${record.patientProfileRecordId}&patientId=${
        record.patientId
      }&diseaseSubCode=${record.infectionSubDisease}&caseStatusCode=${
        record.caseStatusCode
      }&token=${getToken()}`,
    );
  }
  function handlePatientFile(record) {
    const path = import.meta.env.DEV ? 'http://localhost:5120' : '/crb-sjyy/sjyy-select';
    const remark = route.meta.remark as Recordable;
    window.open(
      // eslint-disable-next-line prettier/prettier
      `${path}?id=${remark.appId}&menuId=${remark.menuId}&patientId=${
        record.patientId
      }&token=${getToken()}`,
    );
  }

  const reminderList = ref<{ title: string; content: string }[]>([]);

  function onAddFollowUpPatient() {
    openSelectModal(true, {
      mode: 'add',
    });
  }

  function onSelectedTemplate(templateInfo) {
    go({
      name: 'WorkbenchFollowUpRegistration',
      query: templateInfo,
    });
  }

  function onCloseFollow() {
    tableIns.reload();
  }

  const [syncLoading, setSyncLoading] = useToggle(false);
  const { createMessage } = useMessage();
  function onSyncPatient() {
    setSyncLoading(true);
    autoGenerate()
      .then((msg) => {
        createMessage.success(msg);
        tableIns.reload();
      })
      .finally(() => {
        setSyncLoading(false);
      });
  }

  function onTabChange() {
    isExpanded.value = true;
    tableIns.getForm().resetSchema(formScheamMap[activeKey.value]);
    tableIns.reload();
  }
  function onToggleAdvanced() {
    isExpanded.value = !isExpanded.value;
    tableIns.getForm().resetSchema(
      searchFormSchema.value.map((item, idx) => {
        return {
          ...item,
          show: idx > 2 ? isExpanded.value : true,
        };
      }),
    );
  }
  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(
    followVisitRecordExportExcel,
    {
      manual: true,
      showSuccessMessage: true,
    },
  );
  function onExportExpert() {
    exportUtil(
      exportExpertRunAsync({
        ...tableIns.getForm().getFieldsValue(),
        visitStatus: activeKey.value,
      }),
    );
  }
  async function onPrint() {
    const data = await getFollowVisitPage({
      ...tableIns.getForm().getFieldsValue(),
      visitStatus: activeKey.value,
      visitType: 1,
      pageSize: 9999,
    });
    const properties = columns.value.map((item) => {
      return { field: item.dataIndex, displayName: item.title };
    });

    printJS({
      printable: JSON.parse(JSON.stringify(data.list)?.replace(/:null/g, ':""')) || [],
      properties,
      type: 'json',
      style: ' @media print {html, body{ margin: 0;padding: 0; }}',
    });
  }
  const [registerConfigureWorkspace, { openModal: openConfigureWorkspace }] = useModal();
  function onConfiguration() {
    openConfigureWorkspace(true);
  }
  function handleConfigureSuccess() {
    getWorkbenchConfig();
  }
</script>

<template>
  <div class="workbench !h-[calc(100vh-102px)] of-y-auto pb-10px pr-8px">
    <Teleport to="body">
      <Button
        class="!fixed top-[320px] right-0 z-10"
        type="primary"
        @click="onConfiguration"
        pre-icon="ant-design:setting-twotone"
        :icon-size="18"
        title="配置工作台"
      >
        <i></i>
      </Button>
    </Teleport>
    <ConfigureWorkspace
      @register="registerConfigureWorkspace"
      @success="handleConfigureSuccess"
      :config="workbenchConfig"
    />
    <div class="h-220px grid grid-cols-3 gap-10px mb-10px" v-if="getAllShow">
      <template v-for="item in dataList" :key="item.name">
        <div
          v-if="item.show"
          class="flex-1 p-4 flex flex-col gap-4 rounded-1"
          :class="[getClass(item.icon)]"
        >
          <div class="h-full flex items-center justify-start gap-2">
            <Icon :icon="item.icon" :size="32" />
            <div class="fw-bold text-16px text-#333">{{ item.name }}</div>
          </div>
          <div class="h-full flex justify-start">
            <template v-for="i in item.taskList" :key="i.label">
              <div class="flex flex-col flex-1" v-if="i.show">
                <div class="text-#999999">{{ i.label }}</div>
                <div class="text-#333 text-24px fw-bold">{{ i.value }}</div>
              </div>
            </template>
          </div>
          <div class="h-full flex justify-start b-t-1px b-t-#E7E7E7 pt-4 -mt-2">
            <template v-for="a in item.followUpList" :key="a.label">
              <div class="flex flex-col flex-1 of-hidden" v-if="a.show">
                <div class="text-#999999 truncate" :title="a.label">{{ a.label }}</div>
                <div class="text-#333 text-24px fw-bold">{{ a.value }}</div>
              </div>
            </template>
          </div>
        </div>
      </template>
    </div>
    <div class="flex justify-between gap-10px h-960px of-hidden">
      <div class="flex-1 of-hidden bg-#fff p-4 rounded-1 flex flex-col">
        <span class="text-base font-bold">随访患者列表</span>
        <Tabs v-model:activeKey="activeKey" @change="onTabChange">
          <Tabs.TabPane
            :key="index"
            :tab="b"
            v-for="(b, index) in ['未随访', '已随访', '随访未成功']"
          />
        </Tabs>
        <div class="flex-1 of-hidden">
          <BasicTable @register="registerTable">
            <template #form-resetBefore>
              <span class="text-#999 text-14px cursor-pointer mr-2" @click="onToggleAdvanced">
                <Icon
                  class="transition-all duration-300"
                  :class="isExpanded ? 'rotate-180' : ''"
                  icon="ant-design:down-outlined"
                  :size="14"
                />
                <span>{{ isExpanded ? '收起' : '展开' }}</span>
              </span>
            </template>
            <template v-if="activeKey == 0" #tableTitle>
              <div class="flex gap-2">
                <Button type="primary" @click="onAddFollowUpPatient">新增</Button>
                <Button :loading="syncLoading" type="primary" @click="onSyncPatient">
                  同步患者
                </Button>
              </div>
            </template>
            <template #toolbar>
              <Button :loading="exportLoading" @click="onExportExpert"> 批量导出 </Button>
              <Button :loading="exportLoading" @click="onPrint"> 打印 </Button>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'status'">
                <Switch v-model:checked="record[column.dataIndex]" />
              </template>
              <template v-if="column.dataIndex === 'action'">
                <TableAction
                  :divider="false"
                  :actions="createActions(record, column)"
                  :drop-down-actions="createDropDownActions(record)"
                >
                  <template #more>
                    <Button size="small" type="link"> 更多 </Button>
                  </template>
                </TableAction>
              </template>
            </template>
          </BasicTable>
        </div>
      </div>
      <div class="w-320px bg-#fff rounded-1 p-4 flex flex-col gap-2">
        <span class="text-base font-bold">随访提醒</span>
        <div class="flex-1 flex flex-col gap-2 min-h-0 basis-0 overflow-y-auto">
          <Empty v-if="!reminderList.length" />
          <div
            v-else
            class="flex flex-col gap-2 p-3 bg-#F5F7FA rounded-1"
            v-for="(item, index) in reminderList"
            :key="index"
          >
            <div class="text-#252931">{{ item.title }}</div>
            <div class="text-#999">{{ item.content }}</div>
          </div>
        </div>
      </div>
    </div>
    <FollowUpRecallModal @register="register" @success="tableIns.reload" />
    <CloseFollowModal @register="registerCloseModal" @success="onCloseFollow" />
    <SelectTemplateModal @register="registerSelectTemplate" @success="onSelectedTemplate" />
  </div>
</template>
<style lang="less" scoped>
  .workbench {
    .workbench-personal-follow-up {
      background: url('/@/assets/images/personal-follow-up.png') no-repeat 100% 0%/20% 50%,
        linear-gradient(180deg, #f0fee8 0%, #fff 100%);
    }

    .workbench-department-follow-up {
      background: url('/@/assets/images/department-follow-up.png') no-repeat 100% 0%/20% 50%,
        linear-gradient(180deg, #fffdf7 0%, #fff 100%);
    }

    .workbench-hospital-follow-up {
      background: url('/@/assets/images/hospital-follow-up.png') no-repeat 100% 0%/20% 50%,
        linear-gradient(180deg, #f5fbff 0%, #fff 100%);
    }

    :deep {
      // .vben-basic-table-form-container {
      //   padding: 0;
      // }

      // .vben-basic-table .ant-table-wrapper {
      //   padding: 0;
      // }

      .vben-basic-table-form-container .ant-form {
        background-color: #f5f7fa;
        padding: 16px;
        border-radius: 0;
      }
    }
  }
</style>
