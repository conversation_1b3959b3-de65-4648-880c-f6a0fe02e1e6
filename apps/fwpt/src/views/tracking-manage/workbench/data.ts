import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { queryFollowDeptList } from '/@/api/workbench';
/**
admissionNumber	住院号	string	
age	年龄	integer	
createTime	创建时间	string	
createUser	创建人	string	
deleteFlag	删除标识(0-未删除，1-删除)	integer	
diagnosticResults	诊断结果名称(艾滋病等)	string	
dischargeDate	出院日期	string	
id	主键ID	string	
idCardNo	身份证号	string	
lastVisitTime	下次随访时间	string	
patientId	患者编号ID	string	
patientName	患者姓名	string	
sex	性别编码	string	
updateTime	更新时间	string	
updateUser	更新人	string	
visitDeptName	就诊科室名称	string	
visitOrgName	就诊机构名称	string	
visitReason	随访关闭原因	string	
visitSerialNo	就诊流水号;	string	
visitStatus	随访状态：0 待随访，1 已随访，2 随访关闭	integer	
visitTaskId	随访任务ID	string	
visitTaskName	随访任务名称	string	
visitTemplateId	随访模板ID	string	
visitTemplateName	随访模板名称	string	
visitTime	随访日期	string	
visitUser	随访人	string	
visitWay	随访方式：1 电话，2 家庭，3 门诊	integer	
 */
export const searchFormSchemaToBe: FormSchema[] = [
  {
    field: 'dischargeDate',
    label: '出院日期/挂号日期',
    component: 'RangePicker',
    componentProps: {
      options: [],
      style: 'width: 100%',
      // format: '',
      getPopupContainer() {
        return document.body;
      },
      placeholder: ['开始时间', '结束时间'],
    },
    colProps: { span: 8 },
  },
  {
    field: 'patientName',
    label: '患者姓名',
    component: 'Input',
    colProps: { span: 8 },
  },
  // {
  //   field: 'visitSerialNo',
  //   label: '就诊号',
  //   component: 'Input',
  //   colProps: { span: 8 },
  // },
  {
    field: 'inId',
    label: '住院号/门诊号',
    component: 'Input',
    colProps: { span: 8 },
  },
  // {
  //   field: 'visitTaskName',
  //   label: '随访任务',
  //   component: 'Input',
  //   colProps: { span: 8 },
  // },
  {
    field: 'visitSource',
    label: '就诊来源',
    component: 'Select',
    colProps: { span: 8 },
    componentProps: {
      options: [
        { label: '门诊', value: 1 },
        { label: '住院', value: 2 },
      ],
    },
  },
  {
    label: '就诊科室',
    field: 'visitDeptNameList',
    component: 'ApiSelect',
    componentProps: {
      mode: 'multiple',
      api: queryFollowDeptList,
      labelField: 'name',
      valueField: 'name',
      maxTagCount: 2,
      getPopupContainer: () => document.body,
      showSearch: true,
      optionFilterProp: 'label',
    },
    colProps: { span: 8 },
  },
];

export const columnsToBe: BasicColumn[] = [
  {
    title: '出院日期/挂号日期',
    dataIndex: 'dischargeDate',
    width: 180,
    align: 'left',
  },
  {
    title: '随访患耆',
    dataIndex: 'patientName',
    width: 100,
    align: 'left',
  },
  // {
  //   title: '就诊号',
  //   dataIndex: 'visitSerialNo',
  //   width: 140,
  //   align: 'left',
  // },
  {
    title: '就诊来源',
    dataIndex: 'visitSourceName',
    width: 100,
    align: 'left',
  },
  {
    title: '住院号/门诊号',
    dataIndex: 'inId',
    width: 140,
    align: 'left',
  },
  {
    title: '身份证号',
    dataIndex: 'idCardNo',
    width: 200,
    align: 'left',
  },
  {
    title: '性别',
    dataIndex: 'sexName',
    width: 70,
    align: 'left',
  },
  {
    title: '年龄',
    dataIndex: 'age',
    width: 70,
    align: 'left',
  },
  {
    title: '随访机构',
    dataIndex: 'visitOrgName',
    width: 180,
    align: 'left',
  },
  {
    title: '就诊科室',
    dataIndex: 'visitDeptName',
    width: 100,
    align: 'left',
  },
  {
    title: '就诊诊断',
    dataIndex: 'diagnosticResults',
    width: 100,
    align: 'left',
  },
  {
    title: '联系电话1',
    dataIndex: 'contactOnePhone',
    width: 100,
    align: 'left',
  },
  {
    title: '联系电话2',
    dataIndex: 'contactOnePhone',
    width: 100,
    align: 'left',
  },
  {
    title: '地址',
    dataIndex: 'address',
    width: 100,
    align: 'left',
  },
  {
    title: '随访任务名称',
    dataIndex: 'visitTaskName',
    width: 100,
    align: 'left',
  },
  {
    title: '随访内容',
    dataIndex: 'visitTemplateName',
    width: 200,
    align: 'left',
  },
];
/**
 * 随访日期
 * 随访患者
 * 就诊号
 * 住院号
 * 随访任务
 */

export const searchFormSchemaHas: FormSchema[] = [
  {
    field: 'followUpDate',
    label: '随访日期',
    component: 'RangePicker',
    componentProps: {
      options: [],
      style: 'width: 100%',
      // format: '',
      getPopupContainer() {
        return document.body;
      },
      placeholder: ['开始时间', '结束时间'],
    },
    colProps: { span: 8 },
  },
  {
    field: 'dischargeDate',
    label: '出院日期/挂号日期',
    component: 'RangePicker',
    componentProps: {
      options: [],
      style: 'width: 100%',
      // format: '',
      getPopupContainer() {
        return document.body;
      },
      placeholder: ['开始时间', '结束时间'],
    },
    colProps: { span: 8 },
  },

  {
    field: 'patientName',
    label: '随访患者',
    component: 'Input',
    colProps: { span: 8 },
  },
  // {
  //   field: 'visitSerialNo',
  //   label: '就诊号',
  //   component: 'Input',
  //   colProps: { span: 8 },
  // },
  {
    field: 'inId',
    label: '住院号/门诊号',
    component: 'Input',
    colProps: { span: 8 },
  },
  // {
  //   field: 'visitTaskName',
  //   label: '随访任务',
  //   component: 'Input',
  //   colProps: { span: 8 },
  // },

  {
    field: 'visitSource',
    label: '就诊来源',
    component: 'Select',
    colProps: { span: 8 },
    componentProps: {
      options: [
        { label: '门诊', value: 1 },
        { label: '住院', value: 2 },
      ],
    },
  },
  {
    label: '就诊科室',
    field: 'visitDeptNameList',
    component: 'ApiSelect',
    componentProps: {
      mode: 'multiple',
      api: queryFollowDeptList,
      labelField: 'name',
      valueField: 'name',
      maxTagCount: 2,
      getPopupContainer: () => document.body,
      showSearch: true,
      optionFilterProp: 'label',
    },
    colProps: { span: 8 },
  },
];

export const columnsHas: BasicColumn[] = [
  {
    title: '随访日期',
    dataIndex: 'visitTime',
    width: 180,
    align: 'left',
  },
  {
    title: '随访患者',
    dataIndex: 'patientName',
    width: 100,
    align: 'left',
  },
  // {
  //   title: '就诊号',
  //   dataIndex: 'visitSerialNo',
  //   width: 100,
  //   align: 'left',
  // },
  {
    title: '就诊来源',
    dataIndex: 'visitSourceName',
    width: 100,
    align: 'left',
  },
  {
    title: '出院日期/挂号日期',
    dataIndex: 'dischargeDate',
    width: 180,
    align: 'left',
  },
  {
    title: '住院号/门诊号',
    dataIndex: 'inId',
    width: 160,
    align: 'left',
  },
  {
    title: '身份证号',
    dataIndex: 'idCardNo',
    width: 200,
    align: 'left',
  },
  {
    title: '性别',
    dataIndex: 'sexName',
    width: 70,
    align: 'left',
  },
  {
    title: '年龄',
    dataIndex: 'age',
    width: 70,
    align: 'left',
  },
  {
    title: '随访机构',
    dataIndex: 'visitOrgName',
    width: 180,
    align: 'left',
  },
  {
    title: '就诊科室',
    dataIndex: 'visitDeptName',
    width: 100,
    align: 'left',
  },
  {
    title: '就诊诊断',
    dataIndex: 'diagnosticResults',
    width: 100,
    align: 'left',
  },
  {
    title: '联系电话1',
    dataIndex: 'contactOnePhone',
    width: 100,
    align: 'left',
  },
  {
    title: '联系电话2',
    dataIndex: 'contactTwoPhone',
    width: 100,
    align: 'left',
  },
  {
    title: '地址',
    dataIndex: 'address',
    width: 100,
    align: 'left',
  },
  {
    title: '随访任务名称',
    dataIndex: 'visitTaskName',
    width: 100,
    align: 'left',
  },
  {
    title: '随访内客',
    dataIndex: 'visitTemplateName',
    width: 200,
    align: 'left',
  },
  {
    title: '随访人',
    dataIndex: 'visitUser',
    width: 100,
    align: 'left',
  },
  {
    title: '下次随访时间',
    dataIndex: 'lastVisitTime',
    width: 180,
    align: 'left',
  },
];

export const columnsClose: BasicColumn[] = [
  {
    title: '随访日期',
    dataIndex: 'visitTime',
    width: 180,
    align: 'left',
  },
  {
    title: '随访患者',
    dataIndex: 'patientName',
    width: 100,
    align: 'left',
  },
  {
    title: '就诊来源',
    dataIndex: 'visitSourceName',
    width: 100,
    align: 'left',
  },
  {
    title: '出院日期/挂号日期',
    dataIndex: 'dischargeDate',
    width: 180,
    align: 'left',
  },
  // {
  //   title: '就诊号',
  //   dataIndex: 'visitSerialNo',
  //   width: 100,
  //   align: 'left',
  // },
  {
    title: '住院号/门诊号',
    dataIndex: 'inId',
    width: 140,
    align: 'left',
  },
  {
    title: '身份证号',
    dataIndex: 'idCardNo',
    width: 200,
    align: 'left',
  },
  {
    title: '性别',
    dataIndex: 'sexName',
    width: 70,
    align: 'left',
  },
  {
    title: '年龄',
    dataIndex: 'age',
    width: 70,
    align: 'left',
  },
  {
    title: '随访机构',
    dataIndex: 'visitOrgName',
    width: 180,
    align: 'left',
  },
  {
    title: '就诊科室',
    dataIndex: 'visitDeptName',
    width: 100,
    align: 'left',
  },
  {
    title: '就诊诊断',
    dataIndex: 'diagnosticResults',
    width: 100,
    align: 'left',
  },
  {
    title: '联系电话1',
    dataIndex: 'contactOnePhone',
    width: 100,
    align: 'left',
  },
  {
    title: '联系电话2',
    dataIndex: 'contactTwoPhone',
    width: 100,
    align: 'left',
  },
  {
    title: '地址',
    dataIndex: 'address',
    width: 100,
    align: 'left',
  },
  {
    title: '随访任务名称',
    dataIndex: 'visitTaskName',
    width: 100,
    align: 'left',
  },
  {
    title: '随访内容',
    dataIndex: 'visitTemplateName',
    width: 200,
    align: 'left',
  },
  {
    title: '随访人',
    dataIndex: 'visitUser',
    width: 100,
    align: 'left',
  },
  {
    title: '末正常随访原因',
    dataIndex: 'visitReason',
    width: 160,
    align: 'left',
  },
  {
    title: '下次随访时间',
    dataIndex: 'lastVisitTime',
    width: 180,
    align: 'left',
  },
];

/**
 * 短信通知：
 */
export const editFormSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '短信通知',
    field: 'content',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
];

export type FollowCloseModalDT = {
  record: Recordable;
};
/**
 * 关闭随访
 *
 */
export const FollowCloseFormSchema: FormSchema[] = [];

export const extractField = [
  'recordNo',
  'inId',
  'dischargeDate',
  'lastVisitTime',
  'patientId',
  'patientName',
  'visitTime',
  'visitUser',
  'visitWay',
  'visitSerialNo',
  'contactOnePhone',
  'contactTwoPhone',
];

export const autoFillFields = [
  'patientId',
  'patientName',
  'inId',
  'dischargeDate',
  'contactOnePhone',
  'contactTwoPhone',
  'address',
];
