<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { omit } from 'lodash-es';
  import type { FollowCloseModalDT } from './data';
  import { closeFollowVisit } from '/@/api/workbench';

  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm({
    layout: 'vertical',
    schemas: [
      // id
      {
        field: 'id',
        label: '随访ID',
        component: 'Input',
        show: false,
      },
      // 关闭随访原因
      {
        field: 'visitReason',
        label: '请输入未成功随访的原因',
        component: 'InputTextArea',
        componentProps: {
          autoSize: { minRows: 4, maxRows: 4 },
        },
        defaultValue: '电话未接听',
        required: true,
        colProps: { span: 24 },
      },
      {
        field: 'visitStatus',
        label: '随访状态',
        component: 'InputNumber',
        show: false,
        defaultValue: 2,
      },
      {
        field: 'visitType',
        label: 'visitType',
        component: 'InputNumber',
        show: false,
      },
    ],
    showActionButtonGroup: false,
  });

  const [register, { closeModal }] = useModalInner<FollowCloseModalDT>((data) => {
    formAction.setFieldsValue(omit(data.record, ['visitStatus', 'visitReason'])).then(() => {
      formAction.clearValidate();
    });
  });

  const { loading, run: saveRun } = useRequest(closeFollowVisit, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      emit('success');
      closeModal();
    },
  });

  function onOk() {
    formAction.validate().then((values) => {
      saveRun(values);
    });
  }
</script>

<template>
  <BasicModal
    v-bind="$attrs"
    @register="register"
    :width="600"
    :min-height="100"
    title="随访未成功-备注"
    centered
    @ok="onOk"
    :ok-button-props="{
      loading,
    }"
    :destroy-on-close="true"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<style scoped></style>
