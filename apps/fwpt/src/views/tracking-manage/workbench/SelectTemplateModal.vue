<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { TemplateType } from '../follow-task/components/TemplateEdit/types';
  import { getTemplateList } from '/@/api/follow-template';

  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm({
    labelWidth: 120,
    schemas: [
      {
        field: 'mode',
        label: '模式',
        component: 'Input',
        show: false,
      },
      {
        field: 'visitRecordId',
        label: '患者ID',
        component: 'Input',
        show: false,
      },
      {
        field: 'infectionDisease',
        label: '传染病',
        component: 'InputNumber',
        show: false,
      },
      {
        field: 'templateId',
        label: '模板名称',
        component: 'ApiRadioGroup',
        componentProps: ({ formModel }) => {
          return {
            api: async () => {
              if (formModel.mode === 'add' || formModel.infectionDisease) {
                const params: Parameters<typeof getTemplateList>[0] = {
                  templateType: TemplateType.Template,
                };
                if (formModel.infectionDisease) {
                  params.visitDisease = formModel.infectionDisease;
                }

                const templateList = await getTemplateList(params);
                formModel.templateId = templateList[0].id;
                return templateList;
              } else {
                return Promise.resolve([]);
              }
            },
            labelField: 'templateName',
            valueField: 'id',
            required: true,
            colProps: { span: 24 },
            itemProps: { wrapperCol: { span: 16 } },
          };
        },
      },
    ],
    showActionButtonGroup: false,
  });

  const [register, { closeModal }] = useModalInner((data) => {
    console.log(`data`, data);
    formAction.setFieldsValue({
      visitRecordId: data?.visitRecordId,
      mode: data?.mode,
      infectionDisease: data.record?.infectionDisease,
    });
  });

  function onOk() {
    formAction.validate().then((values) => {
      emit('success', values);
      closeModal();
    });
  }
</script>

<template>
  <BasicModal
    destroy-on-close
    v-bind="$attrs"
    @register="register"
    :width="600"
    :min-height="100"
    title="选择模板"
    centered
    @ok="onOk"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<style scoped></style>
