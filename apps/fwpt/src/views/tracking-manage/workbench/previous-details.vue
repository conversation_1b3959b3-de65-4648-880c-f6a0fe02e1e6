<script setup lang="ts">
  import { But<PERSON>, DatePicker, Select } from 'ant-design-vue';
  import { computed, ref } from 'vue';
  import { StyledList } from '@ft/components';
  import { useRequest } from '@ft/request';
  import type { IFollowVisitRecordPost } from '/@/api/workbench';
  import { getFollowVisitTemplateList } from '/@/api/workbench';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import type { IFollowTemplate } from '/@/api/follow-template';
  import PatientInfoCard from './components/PatientInfoCard.vue';
  import FollowTable from './components/FollowTable/index.vue';

  const patientId = useRouteQuery('patientId', '', { transform: String });
  const patientName = useRouteQuery('patientName', '', { transform: String });
  const patientBirthday = useRouteQuery('patientBirthday', '', { transform: String });
  const patientAge = useRouteQuery('patientAge', '', { transform: String });
  const patientSex = useRouteQuery('patientSex', '', { transform: Number });
  const patientPhone = useRouteQuery('patientPhone', '', { transform: String });
  const patientIdCard = useRouteQuery('patientIdCard', '', { transform: String });
  const visitType = useRouteQuery('visitType', 1, { transform: Number });
  const activeTemplate = ref<IFollowTemplate>();
  const activeTemplateId = ref();

  const { data: items = ref([]) } = useRequest(getFollowVisitTemplateList, {
    defaultParams: [patientId.value],
    ready: patientId.value,
    onSuccess(data) {
      activeTemplateId.value = data[0].id;
      activeTemplate.value = data[0];
    },
  });

  const patientInfo = computed(() => ({
    name: patientName.value,
    birthday: patientBirthday.value,
    age: patientAge.value,
    sex: patientSex.value,
    phone: patientPhone.value,
    idCardNo: patientIdCard.value,
  }));

  const searchInfo = ref({
    year: undefined,
    org: undefined,
    type: undefined,
    ks: undefined,
    zd: undefined,
  });

  const go = useGo();

  const visitRecord = ref<IFollowVisitRecordPost>();

  function onFetchSuccess(data: IFollowVisitRecordPost) {
    visitRecord.value = data;
  }

  function onTdClick(item: Recordable) {
    if (visitType.value === 1) {
      go({
        name: 'WorkbenchFollowUpRegistration',
        query: {
          templateId: item.visitTemplateId,
          visitRecordId: item.id,
          mode: 'view',
        },
      });
    } else if (visitType.value === 2) {
      go({
        name: 'CommunityFollowUpRegistration',
        query: {
          templateId: item.visitTemplateId,
          visitRecordId: item.id,
          mode: 'view',
        },
      });
    }
  }
</script>
<template>
  <div class="w-full h-full pr-4 flex justify-between gap-10px pb-10px">
    <div class="flex flex-col gap-10px">
      <PatientInfoCard :patientInfo="patientInfo" />
      <div class="flex-1 bg-#fff rounded-2 p-3 flex flex-col gap-2">
        <div>患者就诊记录</div>
        <div class="rounded-2 bg-#FAFAFA p-10px flex flex-col gap-2">
          <DatePicker v-model:value="searchInfo.year" picker="year" />
          <Select v-model:value="searchInfo.org" style="width: 100%" placeholder="请选择机构" />
          <Select v-model:value="searchInfo.type" style="width: 100%" placeholder="请选择类别" />
          <Select v-model:value="searchInfo.ks" style="width: 100%" placeholder="请选择科室" />
          <Select v-model:value="searchInfo.zd" style="width: 100%" placeholder="请输入诊断" />
          <div class="flex justify-end">
            <Button type="primary">查询</Button>
          </div>
        </div>
        <div class="mt-1">
          <div class="flex items-center gap-2 mb-2">
            <span class="inline-block bg-primary-color w-3px h-1em"></span>
            <span>既往随访记录</span>
          </div>
          <StyledList
            label-field="templateName"
            value-field="id"
            :items="items"
            v-model:value="activeTemplate"
            v-model="activeTemplateId"
            :width="204"
          />
        </div>
      </div>
    </div>
    <div class="flex-1 flex gap-6 flex-col bg-#fff rounded-2 p-6 h-[calc(100vh-113px)]">
      <div class="text-#333 text-24px text-center">{{ activeTemplate?.templateName }}</div>
      <div class="flex justify-start">
        <div class="flex justify-start flex-1">
          <div class="text-#999999">患者姓名：</div>
          <div class="text-#333">{{ patientName }}</div>
        </div>
        <div class="flex justify-start flex-1">
          <div class="text-#999999">患者编号：</div>
          <div class="text-#333">{{ patientId }}</div>
        </div>
        <div class="flex justify-start flex-1">
          <div class="text-#999999">患者住院号：</div>
          <div class="text-#333">{{ visitRecord?.inId }}</div>
        </div>
        <div class="flex justify-start flex-1">
          <div class="text-#999999">出院日期：</div>
          <div class="text-#333">{{ visitRecord?.dischargeDate }}</div>
        </div>
      </div>
      <div class="power-data flex-1 overflow-auto">
        <FollowTable
          :patientId="patientId"
          :template="activeTemplate"
          :visitType="visitType"
          @col-click="onTdClick"
          @fetch-success="onFetchSuccess"
        />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
  :deep {
    .vben-basic-table .ant-table-wrapper {
      padding: 0;
    }

    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table-form-container .ant-form,
    .vben-basic-table .ant-table-wrapper {
      border-radius: 4px;
    }
  }

  .doctors-view-styled-list {
    :deep {
      .ant-timeline-item {
        padding-bottom: 0;
      }

      .ant-timeline-item-head {
        background-color: transparent;
      }
    }
  }
</style>
