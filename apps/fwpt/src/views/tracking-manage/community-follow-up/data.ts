import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { getTemplateList } from '/@/api/follow-template';

/**
 * 离院日期
 * 随访模板
 * 随访日期
 * 随访患者
 */
export const searchFormSchema: FormSchema[] = [
  {
    field: 'dischargeDate',
    label: '离院日期',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      style: 'width: 100%',
      getPopupContainer() {
        return document.body;
      },
    },
    colProps: { span: 6 },
  },
  {
    field: 'visitTemplateId',
    label: '随访模板',
    component: 'ApiSelect',
    componentProps: {
      api: () =>
        getTemplateList({
          templateType: 1,
        }),
      labelField: 'templateName',
      valueField: 'id',
      showSearch: true,
      optionFilterProp: 'label',
    },
    colProps: { span: 6 },
  },
  {
    field: 'followUpDate',
    label: '随访日期',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      style: 'width: 100%',
      getPopupContainer() {
        return document.body;
      },
    },
    colProps: { span: 6 },
  },
  {
    field: 'patientName',
    label: '随访患者',
    component: 'Input',
    colProps: { span: 6 },
  },
];

/**
 * 离院日期
 * 随访日期
 * 随访患者
 * 身份证号
 * 性别
 * 年龄
 * 就诊科室
 * 就诊诊断
 * 联系电话1
 * 联系电话2
 * 地址
 * 随访任务
 * 随访模板
 */
export const columns: BasicColumn[] = [
  {
    title: '离院日期',
    dataIndex: 'dischargeDate',
    width: 200,
    align: 'left',
  },
  {
    title: '随访日期',
    dataIndex: 'visitTime',
    width: 200,
    align: 'left',
  },
  {
    title: '随访患者',
    dataIndex: 'patientName',
    width: 200,
    align: 'left',
  },
  {
    title: '身份证号',
    dataIndex: 'idCardNo',
    width: 200,
    align: 'left',
  },
  {
    title: '性别',
    dataIndex: 'sexName',
    width: 100,
    align: 'left',
  },
  {
    title: '年龄',
    dataIndex: 'age',
    width: 100,
    align: 'left',
  },
  {
    title: '就诊科室',
    dataIndex: 'visitDeptName',
    width: 200,
    align: 'left',
  },
  {
    title: '就诊诊断',
    dataIndex: 'diagnosticResults',
    width: 200,
    align: 'left',
  },
  {
    title: '联系电话1',
    dataIndex: 'contactOnePhone',
    width: 200,
    align: 'left',
  },
  {
    title: '联系电话2',
    dataIndex: 'contactTwoPhone',
    width: 200,
    align: 'left',
  },
  {
    title: '地址',
    dataIndex: 'address',
    width: 200,
    align: 'left',
  },
  {
    title: '随访任务',
    dataIndex: 'visitTaskName',
    width: 200,
    align: 'left',
  },
  {
    title: '随访模板',
    dataIndex: 'visitTemplateName',
    width: 200,
    align: 'left',
  },
];
