<script setup lang="ts">
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Tabs } from 'ant-design-vue';
  import { useModal } from '@ft/internal/components/Modal';
  import { ref } from 'vue';
  import SelectTemplateModal from '../workbench/SelectTemplateModal.vue';
  import { columns, searchFormSchema } from './data';
  import { useGo } from '/@@/hooks/web/usePage';
  import CloseFollowModal from '../workbench/CloseFollowModal.vue';
  import { getFollowVisitPage } from '/@/api/workbench';
  const activeKey = ref(0);
  const go = useGo();
  const [registerTable, tableIns] = useTable({
    api: getFollowVisitPage,
    columns,
    dataSource: [{}],
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas: searchFormSchema,
      fieldMapToTime: [
        ['dischargeDate', ['dischargeDateStartTime', 'dischargeDateEndTime'], 'YYYY-MM-DD'],
        ['followUpDate', ['visitStartTime', 'visitEndTime'], 'YYYY-MM-DD'],
      ],
    },
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
    },
    resizeHeightOffset: 10,
    beforeFetch: (params) => {
      params.visitStatus = activeKey.value;
      params.visitType = 2;
      return params;
    },
  });
  function onTabChange() {
    tableIns.reload();
  }
  function createActions(record: Recordable, _column): ActionItem[] {
    if (activeKey.value === 0) {
      return [
        {
          label: '随访登记',
          type: 'link',
          onClick: handleRegistration.bind(null, record),
        },
        {
          label: '随访未成功',
          type: 'link',
          onClick: handleFollowUpClose.bind(null, record),
        },
      ];
    } else {
      return [
        {
          label: '随访详情',
          type: 'link',
          onClick: handlePreviousDetails.bind(null, record),
        },
      ];
    }
  }
  const [registerSelectTemplate, { openModal: openSelectModal }] = useModal();
  function handleRegistration(record) {
    openSelectModal(true, {
      visitRecordId: record.id,
      mode: 'edit',
      record,
    });
  }
  const [registerCloseModal, { openModal: openCloseModal }] = useModal();
  function handleFollowUpClose(record) {
    openCloseModal(true, {
      record,
    });
  }
  function handlePreviousDetails(record) {
    go({
      name: 'CommunityPreviousDetails',
      query: {
        patientId: record.patientId,
        patientName: record.patientName,
        patientAge: record.age,
        patientSex: record.sex,
        patientIdCard: record.idCardNo,
        patientBirthday: record.birthDay,
        visitType: 2,
      },
    });
  }
  function onSelectedTemplate(templateInfo) {
    go({
      name: 'CommunityFollowUpRegistration',
      query: { ...templateInfo, visitType: 2 },
    });
  }
</script>

<template>
  <div class="h-full pr-4 pb-4">
    <div class="h-full">
      <BasicTable @register="registerTable">
        <template #headerTop>
          <span class="text-base font-bold">随访患者列表</span>
          <Tabs v-model:activeKey="activeKey" @change="onTabChange">
            <Tabs.TabPane
              :key="index"
              :tab="b"
              v-for="(b, index) in ['未随访', '已随访', '随访未成功']"
            />
          </Tabs>
        </template>
        <template #tableTitle> </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :divider="false" :actions="createActions(record, column)" />
          </template>
        </template>
      </BasicTable>
    </div>
    <SelectTemplateModal @register="registerSelectTemplate" @success="onSelectedTemplate" />
    <CloseFollowModal @register="registerCloseModal" @success="tableIns.reload()" />
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }
  }
</style>
