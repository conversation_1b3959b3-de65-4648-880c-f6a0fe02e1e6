<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { Divider, Empty, Spin } from 'ant-design-vue';
  import { computed, ref } from 'vue';
  import { Icon } from '@ft/internal/components/Icon';
  import { Button } from '@ft/internal/components/Button';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { useRequest } from '@ft/request';
  import { debounce } from 'lodash-es';
  import dayjs from 'dayjs';
  import VisitDetails from '../../../../sjyy/src/views/doctors-view/VisitDetails.vue';
  import { SearchSchemas } from './data';
  import type { IPatientRecord } from '/@/api/doctors-view';
  import { existProve, getPatientVisitRecordList } from '/@/api/doctors-view';
  const [register, { getFieldsValue }] = useForm({
    showActionButtonGroup: false,
    labelWidth: 0,
    schemas: SearchSchemas,
    fieldMapToTime: [['visitTime', ['visitStartDate', 'visitEndDate'], 'YYYY-MM-DD']],
  });

  const {
    loading,
    data,
    runAsync: getPatientVisitRecordListRunAsync,
  } = useRequest(() => getPatientVisitRecordList({ ...getFieldsValue() }), {
    onSuccess: (data) => {
      activePatient.value = data.length > 0 ? data[0].id : '';
      activeItem.value = data[0];
    },
  });

  const patientVisitRecordList = computed(() => {
    return data.value || ([] as IPatientRecord[]);
  });

  const onFieldChange = debounce(() => {
    getPatientVisitRecordListRunAsync();
  }, 1000);

  const activePatient = ref('');
  const activeItem = ref<IPatientRecord>();

  const { data: recordId } = useRequest(
    async () => {
      if (activePatient.value) {
        return existProve(activeItem.value!.outpatientNo!);
      } else {
        return '-1';
      }
    },
    {
      refreshDeps: [activePatient],
    },
  );

  const go = useGo();
  async function onGenCart() {
    if (recordId.value) {
      go({
        name: 'GenerateCertificateCert',
        query: {
          recordId: recordId.value,
        },
      });
    } else {
      const activeItemValue = activeItem.value || ({} as IPatientRecord);
      const sexCode = parseInt(activeItemValue.sex);
      go({
        name: 'IssuingRecordListAdd',
        query: {
          detail: JSON.stringify({
            name: activeItemValue.patientName,
            outpatientNo: activeItemValue.outpatientNo,
            sex: sexCode === 1 ? '男' : '女',
            sexCode: sexCode,
            age: activeItemValue.age,
            diagnoseDate: activeItemValue.visitDate,
          }),
        },
      });
    }
  }
  function handleClick(patient) {
    activePatient.value = patient.id;
    activeItem.value = patient;
  }
  const patientInfo = computed(() => ({
    idCardNo: '',
  }));

  function getVisitDate(visitDate?: string) {
    return visitDate && dayjs(visitDate).format('YYYY-MM-DD');
  }
</script>

<template>
  <div class="h-[calc(100%-16px)] w-[calc(100%-16px)] bg-white rounded rounded-lt-none flex">
    <div class="border-r-1 border-[#EDEEF0] w-272px flex flex-col">
      <div class="flex flex-col gap-3 p-4 pb-0">
        <div class="text-base font-bold">患者列表</div>
        <BasicForm @register="register" @field-value-change="onFieldChange" />
      </div>
      <div class="patient-list flex-1 min-h-0 basis-0 of-y-auto p-4">
        <Spin class="!py-5" :spinning="loading" tip="加载中">
          <div v-if="patientVisitRecordList && patientVisitRecordList.length > 0">
            <div
              class="patient-item relative p-4 border-1 border-[#DCDFE6] rounded cursor-pointer mb-2 hover:border-primary-color transition-all"
              :class="{
                'border-primary-color shadow-[0_0_0_2px_rgba(51,188,113,0.2)]':
                  activePatient === patient.id,
              }"
              v-for="patient in patientVisitRecordList"
              :key="patient.id"
              @click="handleClick(patient)"
            >
              <div class="flex items-center mb-2">
                <span class="font-bold">{{ patient.patientName }}</span>
                <Icon icon="male|svg" class="ml-2" v-if="patient?.sex === '1'" />
                <Icon icon="female|svg" class="ml-2" v-else />
              </div>
              <div class="flex items-center">
                <span>{{ patient?.age }}岁</span>
                <Divider type="vertical" />
                <span>{{ patient.outpatientNo }}</span>
              </div>
              <div class="flex items-center">
                <span class="text-info-text-color">就诊时间：</span>
                <span>{{ getVisitDate(patient.visitDate) }}</span>
              </div>
              <div
                v-if="patient.diagnoseDiseaseName"
                class="diseases absolute inline-flex text-center right-0 top-4 py-2px px-10px rounded-l font-bold text-shadow-[0px_1px_2px_rgba(0_0_0_0.24)]"
              >
                <span
                  class="inline-flex max-w-5em"
                  :title="patient.diagnoseDiseaseName"
                  style="
                    background: linear-gradient(119deg, #7196fa 0%, #2841e4 99%);
                    background-clip: text;
                    -webkit-text-fill-color: transparent;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                  "
                >
                  {{ patient.diagnoseDiseaseName }}
                </span>
              </div>
            </div>
          </div>
          <Empty v-else :image="Empty.PRESENTED_IMAGE_SIMPLE" />
        </Spin>
      </div>
    </div>
    <div class="flex-1 of-hidden">
      <div class="p-4 flex flex-col gap-4 border-b-1 border-[#EDEEF0]">
        <div class="relative bg-[#F5F7FA] py-4 px-6 rounded flex flex-col gap-4">
          <div class="flex">
            <div class="flex-1">
              <span class="text-info-text-color">门诊号：</span>
              <span>{{ activeItem?.outpatientNo || '-' }}</span>
            </div>
            <div class="flex-1">
              <span class="text-info-text-color">患者编码：</span>
              <span>{{ activeItem?.patientId || '-' }}</span>
            </div>
            <div class="flex-1">
              <span class="text-info-text-color">就诊患者：</span>
              <span>{{ activeItem?.patientName || '-' }}</span>
            </div>
            <div class="flex-1">
              <span class="text-info-text-color">就诊时间：</span>
              <span>{{ activeItem?.visitDate || '-' }}</span>
            </div>
          </div>
          <div class="basic-info-row flex">
            <div class="flex-1">
              <span class="text-info-text-color">就诊机构：</span>
              <span>{{ activeItem?.hospitalOrg || '-' }}</span>
            </div>
            <div class="flex-1">
              <span class="text-info-text-color">科室：</span>
              <span>{{ activeItem?.diagnosisDept || '-' }}</span>
            </div>
            <div class="flex-1">
              <span class="text-info-text-color">医生：</span>
              <span>{{ activeItem?.diagnosisDoctor || '-' }}</span>
            </div>
            <div class="flex-1"> </div>
          </div>
          <img
            src="/@/assets/images/st-label.png"
            class="w-66px absolute -left-20px -top-20px"
            alt=""
            srcset=""
          />
        </div>
      </div>
      <div class="prescription-details p-4 flex flex-col gap-4 h-full">
        <div class="flex justify-between">
          <div class="text-base font-bold">就诊明细</div>
          <Button type="primary" @click="onGenCart" :disabled="recordId === '-1'">
            {{ recordId && recordId !== '-1' ? '查看详情' : '生成证书' }}
          </Button>
        </div>
        <VisitDetails :patient-info="patientInfo" :active-item="activeItem" />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
  .patient-list {
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #dcdfe6;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f3f3f3;
    }
  }

  .diseases {
    background: linear-gradient(114deg, rgb(113 150 250 / 20%) 0%, rgb(40 65 228 / 20%) 99%);
  }
</style>
