import type { FormSchema } from '@ft/internal/components/Form';
import dayjs from 'dayjs';
/**
 * 就诊时间
 * 接诊病种
 * 患者姓名
 */
export const SearchSchemas: FormSchema[] = [
  {
    field: 'visitTime',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      placeholder: ['就诊开始时间', '就诊结束时间'],
      allowClear: false,
    },
    defaultValue: [dayjs().subtract(1, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
    label: '',
    labelWidth: 0,
    colProps: { span: 24 },
  },
  {
    field: 'diagnoseName',
    component: 'Input',
    label: '',
    labelWidth: 0,
    colProps: { span: 24 },
    componentProps: {
      placeholder: '就诊诊断',
    },
  },
  {
    field: 'patientName',
    component: 'Input',
    label: '',
    labelWidth: 0,
    componentProps: {
      placeholder: '患者姓名',
    },
    colProps: { span: 24 },
  },
];
