import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';

/**
 * 随访时间
 * 医疗机构
 * 随访类型
 * 随访科室
 * 患者姓名
 * 随访状态
 */
export const SearchSchemas: FormSchema[] = [
  {
    field: 'followDate',
    label: '随访时间',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      style: { width: '100%' },
    },
    colProps: { span: 6 },
  },
  {
    field: 'medicalInstitution',
    label: '医疗机构',
    component: 'ApiSelect',
    colProps: { span: 6 },
  },
  {
    field: 'followType',
    label: '随访类型',
    component: 'ApiSelect',
    componentProps: {},
    colProps: { span: 6 },
  },
  {
    field: 'followDepartment',
    label: '随访科室',
    component: 'ApiSelect',
    colProps: { span: 6 },
  },
  {
    field: 'patientName',
    label: '患者姓名',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'followStatus',
    label: '随访状态',
    component: 'ApiSelect',
    colProps: { span: 6 },
  },
];

/**
 * 随访日期
 * 随访患者
 * 就诊号
 * 住院号
 * 身份证号
 * 性别
 * 年龄
 * 就诊科室
 * 就诊诊断
 * 随访状态
 * 随访任务名称
 * 随访内容
 */
export const columns: BasicColumn[] = [
  {
    title: '随访日期',
    dataIndex: 'followDate',
    width: 120,
  },
  {
    title: '随访患者',
    dataIndex: 'patientName',
    width: 120,
  },
  {
    title: '就诊号',
    dataIndex: 'visitNumber',
    width: 120,
  },
  {
    title: '住院号',
    dataIndex: 'hospitalNumber',
    width: 120,
  },
  {
    title: '身份证号',
    dataIndex: 'idCard',
    width: 120,
  },
  {
    title: '性别',
    dataIndex: 'gender',
    width: 80,
  },
  {
    title: '年龄',
    dataIndex: 'age',
    width: 80,
  },
  {
    title: '就诊科室',
    dataIndex: 'department',
    width: 120,
  },
  {
    title: '就诊诊断',
    dataIndex: 'diagnosis',
    width: 120,
  },
  {
    title: '随访状态',
    dataIndex: 'followStatus',
    width: 120,
  },
  {
    title: '随访任务名称',
    dataIndex: 'followTaskName',
    width: 120,
  },
  {
    title: '随访内容',
    dataIndex: 'followContent',
    width: 120,
  },
];

/**
 * @description 肺结核患者随访服务记录表列
 *
 * 未在 BasicTable 中使用，只是借用这个类型， 此列是在 原生的 Table 中使用
 */

export const TuberculosisColumns: BasicColumn[] = [
  {
    title: '随访日期',
    dataIndex: 'followDate',
    width: 140,
    colSpan: 2,
  },
  {
    title: '随访方式',
    dataIndex: 'followType',
    width: 140,
    colSpan: 2,
  },
  {
    title: '查体',
    dataIndex: 'physicalExamination',
    children: [
      // 症状 血压（mmHg） 体重(kg) 体质指数(BMI）（kg/m²） 心率（次/分钟）其他
      {
        title: '症状',
        dataIndex: 'symptom',
        width: 140,
      },
      {
        title: '血压（mmHg）',
        dataIndex: 'bloodPressure',
        width: 140,
      },
      {
        title: '体重(kg)',
        dataIndex: 'weight',
        width: 140,
      },
      {
        title: '体质指数(BMI）（kg/m²）',
        dataIndex: 'bmi',
        width: 140,
      },
      {
        title: '心率（次/分钟）',
        dataIndex: 'heartRate',
        width: 140,
      },
      {
        title: '其他',
        dataIndex: 'other',
        width: 140,
      },
    ],
  },
  // 生活方式指导
  {
    title: '生活方式指导',
    dataIndex: 'lifeStyleGuidance',
    children: [
      // 饮食 戒烟 戒酒 遵守咳嗽礼仪 休息与活动 心理调整 遵医行为
      {
        title: '饮食',
        dataIndex: 'diet',
        width: 140,
      },
      {
        title: '戒烟',
        dataIndex: 'quitSmoking',
        width: 140,
      },
      {
        title: '戒酒',
        dataIndex: 'quitDrinking',
        width: 140,
      },
      {
        title: '遵守咳嗽礼仪',
        dataIndex: 'coughEtiquette',
        width: 140,
      },
      {
        title: '休息与活动',
        dataIndex: 'restAndActivity',
        width: 140,
      },
      {
        title: '心理调整',
        dataIndex: 'psychologicalAdjustment',
        width: 140,
      },
      {
        title: '遵医行为',
        dataIndex: 'complianceBehavior',
        width: 140,
      },
    ],
  },
  // 近期异常辅助检查结果
  {
    title: '近期异常辅助检查结果',
    dataIndex: 'recentAbnormalAuxiliaryExaminationResults',
    width: 140,
    colSpan: 2,
  },
  // 服药依从性
  {
    title: '服药依从性',
    dataIndex: 'medicationCompliance',
    width: 140,
    colSpan: 2,
  },
  // 药物不良反应
  {
    title: '药物不良反应',
    dataIndex: 'adverseDrugReactions',
    width: 140,
    colSpan: 2,
  },
  // 用药
  {
    title: '用药',
    dataIndex: 'medication',
    children: [
      // 药物名称1 用法用量 药物名称2 用法用量 药物名称3 用法用量
      {
        title: '药物名称1',
        dataIndex: 'drugName1',
        width: 140,
      },
      {
        title: '用法用量',
        dataIndex: 'usage1',
        width: 140,
      },
      {
        title: '药物名称2',
        dataIndex: 'drugName2',
        width: 140,
      },
      {
        title: '用法用量',
        dataIndex: 'usage2',
        width: 140,
      },
      {
        title: '药物名称3',
        dataIndex: 'drugName3',
        width: 140,
      },
      {
        title: '用法用量',
        dataIndex: 'usage3',
        width: 140,
      },
    ],
  },
  // VTE风险指导
  {
    title: 'VTE风险指导',
    dataIndex: 'vteRiskGuidance',
    children: [
      // VTE患者风险 健康指导
      {
        title: 'VTE患者风险',
        dataIndex: 'vteRisk',
        width: 140,
      },
      {
        title: '健康指导',
        dataIndex: 'healthGuidance',
        width: 140,
      },
    ],
  },
  // 预约复查
  {
    title: '预约复查',
    dataIndex: 'appointmentReview',
    width: 140,
    colSpan: 2,
  },
  // 此次随访评价
  {
    title: '此次随访评价',
    dataIndex: 'evaluation',
    width: 140,
    colSpan: 2,
  },
  // 备注
  {
    title: '备注',
    dataIndex: 'remark',
    width: 140,
    colSpan: 2,
  },
  // 随访人
  {
    title: '随访人',
    dataIndex: 'followPerson',
    width: 140,
    colSpan: 2,
  },
];

/**
 * @description 病毒性肝炎患者随访服务记录表列
 *
 * 未在 BasicTable 中使用，只是借用这个类型, 此列是在 原生的 Table 中使用
 */
export const HepatitisColumns: BasicColumn[] = [];
