<script setup lang="ts">
  import { ref } from 'vue';

  import { StyledList } from '@ft/components';
  import { Icon } from '@ft/internal/components/Icon';
  import { BasicTitle } from '@ft/internal/components/Basic';
  import { Timeline, TimelineItem } from 'ant-design-vue';
  import { TuberculosisColumns } from './data';

  const items = [
    {
      id: '10_1_1100_10399330',
      pkPatientInfo: '10_1100_**********',
      outpatientNo: '**********',
      inpatientNo: null,
      patientId: '**********',
      visitTypeCode: '1',
      visitType: '门诊',
      visitTimes: null,
      visitDate: '2022-04-16',
      hospitalOrgCode: '10',
      hospitalOrg: '***人民医院',
      diagnosisDoctor: null,
      diagnosisDept: '西区急诊科',
      diagnoseDiseaseName: '一般性医学检查',
      admitHospitalTime: null,
      leaveTime: null,
    },
    {
      id: '10_3_1100_ZY01**********',
      pkPatientInfo: '10_1100_**********',
      outpatientNo: '**********',
      inpatientNo: '**********',
      patientId: '**********',
      visitTypeCode: '3',
      visitType: '住院',
      visitTimes: '1',
      visitDate: '2022-02-05',
      hospitalOrgCode: '10',
      hospitalOrg: '***人民医院',
      diagnosisDoctor: '丁*洲',
      diagnosisDept: '呼吸与危重症医学科二区',
      diagnoseDiseaseName: '咳嗽',
      admitHospitalTime: '2022-02-05',
      leaveTime: '2022-02-10',
    },
  ];
  const activeItem = ref('10_1_1100_10399330');
  const patientInfo = {
    name: '张三',
    sex: 2,
    age: 20,
    birthday: '1999-01-01',
    phone: '13800000000',
    address: '上海市普陀区金沙江路 1518 弄',
    idCardNo: '330103199901010001',
    idCardFront: '身份证正面',
    telephone: '13800000000',
  };

  const activeItemName = ref<string>('');
  function handleChange(val) {
    activeItemName.value = val.label;
  }
</script>
<template>
  <div class="w-full h-full pr-4 flex justify-between gap-10px pb-10px">
    <div class="flex flex-col gap-10px">
      <div class="bg-#fff rounded-[0_8px_8px_8px] py-3 px-4">
        <div class="relative flex justify-between gap-3 mb-3">
          <div
            class="rounded-4 flex items-center text-20px justify-center fw-bold text-#fff bg-gradient-to-br bg-left-top from-#FF6161 to-#FF9494 from-4% to-99% h-56px w-56px"
          >
            {{ patientInfo?.name }}
          </div>
          <div class="flex-1 flex flex-col justify-center text-14px gap-6px">
            <div class="flex gap-2 items-center">
              <span class="fw-bold">{{ patientInfo?.name }}</span>
              <Icon v-if="patientInfo?.sex === 2" icon="woman|svg" :size="16" />
              <Icon v-else icon="man|svg" :size="16" />
            </div>
            <div class="text-info-text-color">
              {{ patientInfo?.birthday || '-' }} ({{ patientInfo?.age }})
            </div>
          </div>
        </div>
        <div class="flex flex-col gap-2 text-secondary-text-color">
          <div> {{ patientInfo?.telephone }} </div>
          <div> {{ patientInfo?.idCardNo }} </div>
        </div>
      </div>
      <div class="flex-1 bg-#fff rounded-2 p-3 flex flex-col gap-2">
        <div class="mt-1">
          <div class="flex items-center gap-2">
            <span class="inline-block bg-primary-color w-3px h-1em"></span>
            <span>随访信息</span>
          </div>
          <StyledList
            :items="items || []"
            v-model="activeItem"
            value-field="id"
            class="flex-1 doctors-view-styled-list"
            :width="204"
            @change="handleChange"
          >
            <template #default="item">
              <div class="flex gap-2 p-6px -ml-3">
                <div>
                  <div
                    v-if="item.visitTypeCode === '3'"
                    class="text-#FF7D34 rounded-50% bg-#FFF3EC px-1 text-12px b-1px b-color-#FF7D34"
                  >
                    住
                  </div>
                  <div
                    v-else
                    class="text-#4c81ff rounded-50% bg-#ecf1ff px-1 text-12px b-1px b-color-#4c81ff"
                  >
                    门
                  </div>
                </div>
                <Timeline>
                  <TimelineItem class="pb-0" color="#E7E7E7">
                    <template #dot>
                      <div class="w-6px h-6px rounded-50% !bg-#E7E7E7"></div>
                    </template>
                    <div
                      class="text-info-text-color text-12px line-height-24px flex justify-start gap-2"
                    >
                      <div v-if="item.visitTypeCode === '3'">{{
                        item?.admitHospitalTime || '-'
                      }}</div>
                      <div v-else>{{ item?.visitDate || '-' }}</div>
                    </div>
                    <div class="text-14px line-height-24px relative">
                      <div class="absolute top-0 -left-5.5 w-1px h-full bg-#E7E7E7"></div>
                      <div> 【{{ item?.diagnosisDept }}】{{ item?.hospitalOrg }} </div>
                    </div>
                  </TimelineItem>
                </Timeline>
              </div>
            </template>
          </StyledList>
        </div>
      </div>
    </div>
    <div class="flex-1 flex gap-6 flex-col bg-#fff rounded-2 p-6 h-[calc(100vh-113px)]">
      <BasicTitle span normal>患病信息</BasicTitle>
      <div class="relative bg-[#F5F7FA] py-4 px-6 rounded flex flex-col gap-4">
        <div class="flex">
          <div class="flex-1">
            <span class="text-info-text-color">门诊号：</span>
            <span>022061701</span>
          </div>
          <div class="flex-1">
            <span class="text-info-text-color">就诊时间：</span>
            <span>2023-12-03</span>
          </div>
          <div class="flex-1">
            <span class="text-info-text-color">就诊诊断：</span>
            <span>肺结核</span>
          </div>
          <div class="flex-1">
            <span class="text-info-text-color">科室：</span>
            <span>肝病科</span>
          </div>
        </div>
        <div class="basic-info-row flex">
          <div class="flex-1">
            <span class="text-info-text-color">医生：</span>
            <span>张三</span>
          </div>
          <div class="flex-1">
            <span class="text-info-text-color">就诊机构：</span>
            <span>宜昌市第三人民医院</span>
          </div>
          <div class="flex-1">
            <span class="text-info-text-color">治疗结果：</span>
            <span>治愈</span>
          </div>

          <div class="flex-1"> </div>
        </div>
        <!-- <img
          src="/@/assets/images/st-label.png"
          class="w-66px absolute -left-20px -top-20px"
          alt=""
          srcset=""
        /> -->
      </div>
      <div class="text-24px text-center">肺结核患者随访服务记录表</div>
      <div class="flex justify-start">
        <div class="flex justify-start flex-1">
          <div class="text-info-text-color">患者姓名：</div>
          <div>陈佳艺</div>
        </div>
        <div class="flex justify-start flex-1">
          <div class="text-info-text-color">患者编号：</div>
          <div>12977788</div>
        </div>
        <div class="flex justify-start flex-1">
          <div class="text-info-text-color">患者住院号：</div>
          <div>009029</div>
        </div>
        <div class="flex justify-start flex-1">
          <div class="text-info-text-color">出院日期：</div>
          <div>2023-09-21</div>
        </div>
      </div>
      <div class="power-data flex-1 overflow-y-auto">
        <VerticalTable :columns="TuberculosisColumns" />
        <table class="table2 h-full table-all" cellpadding="0" cellspacing="0">
          <tr align="center">
            <td class="thBack tdLine" colspan="2">随访日期</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine" colspan="2">随访方式</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>

          <tr align="center">
            <td class="thBack tdLine write-vertical-left !text-center tracking-0.5em" rowspan="6">
              查 体
            </td>
            <td class="thBack tdLine">症状</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine">血压（mmHg）</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine">体重(kg)）</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine">体质指数(BMI） （kg/m²）</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine">心率（次/分钟）</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine">其他</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>

          <tr align="center">
            <td class="thBack tdLine write-vertical-left !text-center tracking-0.5em" rowspan="7">
              生活方式指导
            </td>
            <td class="thBack tdLine">饮食</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine">戒烟</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine">戒酒</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine">遵守咳嗽礼仪</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine">休息与活动</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine">心理调整</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine">遵医行为</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine" colspan="2">近期异常辅助检查结果</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine" colspan="2">服药依从性</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine" colspan="2">药物不良反应</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine write-vertical-left !text-center tracking-0.5em" rowspan="6">
              用 药
            </td>
            <td class="thBack tdLine">药物名称1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine">用法用量</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine">药物名称2</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine">用法用量</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine">药物名称3</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine">用法用量</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>

          <tr align="center">
            <td class="thBack tdLine" rowspan="2">VTE 风险指导</td>
            <td class="thBack tdLine">VTE患者风险</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine">健康指导</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine" colspan="2">预约复查</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine" colspan="2">此次随访评价</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine" colspan="2">备注</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
          <tr align="center">
            <td class="thBack tdLine" colspan="2">随访人</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
            <td class="tdLine">1</td>
          </tr>
        </table>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
  :deep {
    .vben-basic-table .ant-table-wrapper {
      padding: 0;
    }

    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table-form-container .ant-form,
    .vben-basic-table .ant-table-wrapper {
      border-radius: 4px;
    }
  }

  .doctors-view-styled-list {
    :deep {
      .ant-timeline-item {
        padding-bottom: 0;
      }

      .ant-timeline-item-head {
        background-color: transparent;
      }
    }
  }

  .power-data {
    .table-all {
      color: black;
      border: 1px solid #ebeef5;
      border-radius: 2px 0 0;

      .thBack {
        background: #f5f7fa;
        border: 1px solid #ebeef5;
      }

      .tdLine {
        width: 140px;
      }

      .tdBtn {
        width: 99px;
      }

      tr {
        text-align: left;
        font-size: 14px;
        color: #666;
      }

      th,
      td {
        border: 1px solid #ebeef5;
        border-radius: 2px 0 0;
        padding: 9px 12px;
        text-align: left;
        font-size: 14px;
        color: #666;
      }
    }

    .table1 {
      margin-bottom: 10px;
    }

    .table3 {
      th,
      td {
        border-radius: 2px 0 0;
        padding: 4px;
        text-align: center;
        font-size: 14px;
        color: #666;
      }
    }
  }
</style>
