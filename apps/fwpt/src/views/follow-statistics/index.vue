<script setup lang="ts">
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { BasicTitle } from '@ft/internal/components/Basic';
  import { Button } from '@ft/internal/components/Button';
  import { SearchSchemas, columns } from './data';

  const [registerTable] = useTable({
    dataSource: [{}],
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas: SearchSchemas,
    },
    columns,
    actionColumn: {
      title: '操作',
      dataIndex: 'action',
      width: 100,
    },
  });

  const go = useGo();
  function onDetail(record) {
    console.log(record);
    go({ name: 'FollowStatisticsDetail' });
  }

  function createActions(record, _column): ActionItem[] {
    return [
      {
        label: '病种随访详情',
        onClick: onDetail.bind(null, record),
      },
    ];
  }
</script>

<template>
  <div class="h-[calc(100%-16px)] w-[calc(100%-16px)] rounded rounded-lt-none">
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <BasicTitle class="!pl-0">随访病种数据统计患者列表</BasicTitle>
      </template>
      <template #toolbar>
        <Button>导出</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }

    .vben-basic-table-form-container .ant-form,
    .vben-basic-table .ant-table-wrapper {
      border-radius: 4px;
    }

    .vben-basic-table-form-container .ant-form {
      border-top-left-radius: 0;
      margin-bottom: 10px;
    }
  }
</style>
