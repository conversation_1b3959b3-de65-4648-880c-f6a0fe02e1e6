import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';

/**
 * 随访时间
 * 医疗机构
 * 随访类型
 * 随访科室
 */
export const SearchSchemas: FormSchema[] = [
  {
    field: 'followDate',
    label: '随访时间',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      style: { width: '100%' },
    },
    colProps: { span: 6 },
  },
  {
    field: 'medicalInstitution',
    label: '医疗机构',
    component: 'ApiSelect',
    colProps: { span: 6 },
  },
  {
    field: 'followType',
    label: '随访类型',
    component: 'ApiSelect',
    componentProps: {},
    colProps: { span: 6 },
  },
  {
    field: 'followDepartment',
    label: '随访科室',
    component: 'ApiSelect',
    colProps: { span: 6 },
  },
];

/**
 * 随访病种
 * 随访人数
 * 随访次数
 * 随访率
 */
export const columns: BasicColumn[] = [
  {
    title: '随访病种',
    dataIndex: 'disease',
    width: 200,
  },
  {
    title: '随访人数',
    dataIndex: 'followNumber',
    width: 200,
  },
  {
    title: '随访次数',
    dataIndex: 'followTimes',
    width: 200,
  },
  {
    title: '随访率',
    dataIndex: 'followRate',
    width: 200,
  },
];
