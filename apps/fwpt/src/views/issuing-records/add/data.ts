import type { FormSchema } from '@ft/internal/components/Form/index';
import { DictEnum, getDictItemList } from '@ft/internal/api';
/**
 * @description: 新增开具证明
 * 患者基本信息
 * 门诊号
 * 学生（职工）
 * 性别
 * 年龄
 * 科别
 * 学校（单位）
 * 班（科/部门）
 * 联系电话
 * 患者诊疗信息
 * 确诊日期
 * 确诊疾病
 * 开始治疗
 * 就诊机构
 * 就诊科室
 * 后续治疗建议
 */

export const addFormSchema: FormSchema[] = [
  {
    field: 'patientInformation',
    component: 'Input',
    label: '',
    slot: 'patientInformation',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
    disabledLabelWidth: true,
  },
  {
    field: 'id',
    component: 'Input',
    label: 'id',
    show: false,
  },
  {
    field: 'signValue',
    component: 'Input',
    label: 'signValue',
    show: false,
  },
  {
    field: 'outpatientNo',
    component: 'Input',
    required: true,
    label: '门诊号',
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'name',
    component: 'Input',
    label: '学生（职工）姓名',
    required: true,
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'sex',
    component: 'Input',
    label: 'sex',
    show: false,
  },
  {
    field: 'sexCode',
    component: 'RadioGroup',
    componentProps: ({ formModel }) => {
      return {
        options: [
          { label: '男', value: 1 },
          { label: '女', value: 2 },
        ],
        onChange: (opt) => {
          if (opt?.target?.value) formModel.sex = opt?.target?.value === 1 ? '男' : '女';
        },
      };
    },
    label: '性别',
    required: true,
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'age',
    component: 'InputNumber',
    label: '年龄',
    required: true,
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
    componentProps: {
      min: 0,
    },
  },
  {
    field: 'deptId',
    component: 'Input',
    label: 'deptId',
    show: false,
    colProps: { span: 12 },
  },
  {
    field: 'deptName',
    component: 'Input',
    label: '科别',
    required: true,
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'orgName',
    component: 'Input',
    label: '学校（单位）',
    required: true,
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'departmentName',
    component: 'Input',
    label: '班（科/部门）',
    required: true,
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'contactPhone',
    component: 'Input',
    required: true,
    label: '联系电话',
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
    rules: [
      {
        pattern: /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
        message: '请输入正确的联系电话',
      },
    ],
  },
  {
    field: 'patientIdCard',
    component: 'Input',
    required: true,
    label: '身份证号',
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
    rules: [
      {
        pattern:
          /(^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}$)/,
        message: '请输入正确的身份证号',
      },
    ],
  },
  {
    field: 'treatmentInformation',
    component: 'Input',
    label: '',
    slot: 'treatmentInformation',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
    disabledLabelWidth: true,
  },
  {
    field: 'diagnoseDate',
    component: 'DatePicker',
    label: '确诊日期',
    required: true,
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      style: {
        width: '100%',
      },
    },
  },
  {
    field: 'diagnoseIllness',
    label: 'diagnoseIllness',
    component: 'Input',
    show: false,
    colProps: { span: 12 },
  },
  {
    field: 'diagnoseIllnessCode',
    label: '确诊疾病',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.DIAGNOSIS_DISEASE),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        onChange: (_, opt) => {
          if (opt?.label) formModel.diagnoseIllness = opt?.label;
        },
        getPopupContainer: () => document.body,
      };
    },
    required: true,
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'treatDate',
    component: 'DatePicker',
    label: '开始治疗',
    required: true,
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      style: {
        width: '100%',
      },
    },
  },
  {
    field: 'visitOrgName',
    component: 'Input',
    label: '就诊机构',
    required: true,
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
    // componentProps: {
    //   disabled: true,
    // },
  },
];
