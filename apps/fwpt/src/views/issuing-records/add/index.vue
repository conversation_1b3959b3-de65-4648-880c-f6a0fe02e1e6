<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { Button } from '@ft/internal/components/Button';
  import { useUserStore } from '@ft/internal/store/modules/user';
  import { onMounted, ref } from 'vue';
  import { defHttp, useRequest } from '@ft/request';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import {
    editProve,
    getDetail,
    issueProve,
    saveUserOperationLog,
    upload,
    verifySignData,
  } from '/@/api/issuing-records';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { useRoutePageMode } from '@ft/internal/hooks/web/useRoutePageMode';
  import { omit } from 'lodash-es';
  import { message } from 'ant-design-vue';
  import { useEventListener } from '@vueuse/core';
  import htmlToPdf from '@ft/internal/utils/htmlToPdf';
  import { useCheckSign } from '../shared';
  import RecoveryCertificate from '../../template-mg/RecoveryCertificate.vue';
  import { addFormSchema } from './data';
  import { getRandom } from '/@/api/cert';

  const random = ref('');
  const formState = ref({});
  const { getTemplate } = useCheckSign();
  const diagnoseDocSign = ref('');
  const deptDirectorSign = ref('');
  const diagnoseDocSignImg = ref('');
  const deptDirectorSignImg = ref('');
  const recordId = useRouteQuery('recordId', '', { transform: String });
  const recordDetail = useRouteQuery('detail', '', {
    transform: (val) => (val ? JSON.parse(val) : {}),
  });
  const { isEditMode } = useRoutePageMode();
  const { userInfo } = useUserStore();
  const { run: getProveDetail } = useRequest(getDetail, {
    manual: true,
    onSuccess: (data) => {
      setFieldsValue({ ...data, sexCode: data.sex == '男' ? 1 : 2, name: data.patientName });
    },
  });
  const [register, { getFieldsValue, setFieldsValue, validate }] = useForm({
    labelWidth: 140,
    colon: true,
    showActionButtonGroup: false,
    schemas: addFormSchema,
  });
  onMounted(() => {
    setFieldsValue({
      deptId: userInfo?.deptId,
      deptName: userInfo?.deptName,
      visitOrgName: userInfo?.orgName,
    });
    recordId.value && getProveDetail(recordId.value);
    if (Object.keys(recordDetail.value).length) {
      if (recordDetail.value.sex) setFieldsValue(recordDetail.value);
    }
  });

  function onCancel() {
    // resetFields();
    // setFieldsValue({
    //   deptId: userInfo?.deptId,
    //   deptName: userInfo?.deptName,
    //   visitOrgName: userInfo?.orgName,
    // });
    go({ name: 'IssuingRecordList' });
  }
  const go = useGo();
  const loading = ref(false);
  const signatureLoading = ref(false);
  const { runAsync: runAsyncIssueProve, loading: issueProveLoading } = useRequest(
    (data) =>
      isEditMode ? editProve(getFieldsValue()) : issueProve(Object.assign(data, getFieldsValue())),
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess(res) {
        go({
          name: 'IssuingRecordListDetail',
          query: {
            recordId: res.id || recordId.value,
          },
        });
      },
    },
  );

  const onClickBtn = async () => {
    if (isEditMode) {
      runAsyncIssueProve({});
    } else {
      const values = await validate();
      loading.value = true;
      const template = await getTemplate();
      formState.value = {
        ...values,
        orgUrl: template.orgIcon,
        signature: template.signature,
      };
      message.info('正在生成证明文件,大约需要5-10秒钟');
      setTimeout(async () => {
        try {
          const [pdfBlob, imgBlob] = await htmlToPdf('', '#cert-view', {
            noDownload: true,
          });
          const pdfRes = await upload(new File([pdfBlob], 'pdf.pdf', { type: 'application/pdf' }));
          const imgRes = await upload(new File([imgBlob!], 'img.jpg', { type: 'image/jpg' }));
          await runAsyncIssueProve({
            pdfUrl: pdfRes.url,
            imageUrl: imgRes.url,
            diagnoseDocSign: diagnoseDocSign.value,
            deptDirectorSign: deptDirectorSign.value,
            ...values,
          });
        } catch (e) {
          console.log(e);
          message.error('生成证明文件失败');
        } finally {
          loading.value = false;
        }
      }, 5000);
    }
  };

  const onSignBtn = async (type: 'diagnoseDocSign' | 'deptDirectorSign') => {
    if (!random.value) random.value = await getRandom();
    const width = 400;
    const height = 400;
    const screenLeft =
      window.screenLeft !== undefined ? window.screenLeft : (window.screen as any).left;
    const screenTop =
      window.screenTop !== undefined ? window.screenTop : (window.screen as any).top;
    const screenWidth = window.innerWidth
      ? window.innerWidth
      : document.documentElement.clientWidth;
    const screenHeight = window.innerHeight
      ? window.innerHeight
      : document.documentElement.clientHeight;

    // 计算居中位置的左、上坐标
    const left = screenLeft + (screenWidth - width) / 2;
    const top = screenTop + (screenHeight - height) / 2;
    const features = `width=${width},height=${height},top=${top},left=${left},scrollbars=no,menubar=no,toolbar=no,location=no,status=no,fullscreen=no`;
    const publicPath = import.meta.env.VITE_PUBLIC_PATH || '/';
    const pathPrefix = publicPath.endsWith('/') ? publicPath : publicPath + '/';
    window.open(`${pathPrefix}ukey/index.html#${random.value}@${type}`, 'ukey', features);
  };

  function base64ToFile(base64String: string, filename, mimeType = 'image/png') {
    // 解码 Base64 数据
    const byteCharacters = atob(base64String);
    const byteNumbers = new Uint8Array(byteCharacters.length);

    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }

    // 创建 Blob 对象
    const blob = new Blob([byteNumbers], { type: mimeType });

    // 创建 File 对象
    const file = new File([blob], filename, { type: mimeType });

    return file;
  }

  async function signatureListener(event) {
    const eventData = event.data;
    if (eventData.type === 'ukey') {
      signatureLoading.value = true;
      try {
        const data = eventData.data;
        if (!data.base64Cert) {
          message.warning('获取证书信息失败');
          return;
        }
        try {
          await verifySignData({
            oriData: data.orgData,
            signValue: data.signValue,
            certId: data.certId,
            base64Cert: data.base64Cert,
          });
        } catch (error) {
          message.warning('验签失败');
          return;
        }
        try {
          await saveUserOperationLog({
            operationContent: '获取签名图片',
            operationTime: data.time,
            signature: data.signValue,
            username: data.name,
          });
        } catch (error) {
          message.warning('保存操作日志失败');
          return;
        }
        if (data.random === random.value) {
          const file = base64ToFile(data.picture, 'img.png');
          console.log(data, file, 'file');
          const res = await defHttp.uploadFile(
            {
              url: '/api/infection-sysmgt/sysUser/uploadSignImg',
            },
            {
              file,
              filename: file.name,
            },
          );
          const url = res.data?.data?.url;
          if (!url) {
            message.error('上传签名失败');
          } else {
            if (data.type === 'diagnoseDocSign') {
              diagnoseDocSign.value = url;
              diagnoseDocSignImg.value = 'data:image/png;base64,' + data.picture;
            } else {
              deptDirectorSign.value = url;
              deptDirectorSignImg.value = 'data:image/png;base64,' + data.picture;
            }
            setFieldsValue({
              signValue: data.signValue,
            });
          }
        } else {
          message.warning('会话超时，请重新提交');
        }
      } catch (error: any) {
        console.log(error);
        message.warning(error.message);
      } finally {
        signatureLoading.value = false;
      }
    }
  }
  useEventListener('message', signatureListener);
</script>

<template>
  <div
    class="pr-4 h-full dark-loading"
    v-loading="signatureLoading || issueProveLoading || loading"
  >
    <div v-show="!loading" class="bg-white rounded-t flex flex-col h-full">
      <div class="page-body p-4 flex-1 basis-0 min-h-0 of-y-auto pb-8 flex flex-col">
        <div class="form">
          <BasicForm @register="register">
            <template #patientInformation>
              <div class="flex items-center gap-2">
                <span class="inline-block bg-primary-color w-3px h-1em"></span>
                <span>患者基本信息</span>
              </div>
            </template>
            <template #treatmentInformation>
              <div class="flex items-center gap-2">
                <span class="inline-block bg-primary-color w-3px h-1em"></span>
                <span>患者诊疗信息</span>
              </div>
            </template>
          </BasicForm>
        </div>
      </div>
      <div class="flex p-4 justify-end items-center">
        <div class="actions-group flex gap-2">
          <Button @click="onCancel">取消</Button>
          <Button @click="onSignBtn('deptDirectorSign')"> 科主任签章 </Button>
          <Button @click="onSignBtn('diagnoseDocSign')"> 医生签章 </Button>
          <Button
            @click="onClickBtn"
            type="primary"
            :disabled="!diagnoseDocSign || !deptDirectorSign"
          >
            生成证明
          </Button>
        </div>
      </div>
    </div>
    <RecoveryCertificate
      v-if="loading"
      v-bind="omit(formState, 'id')"
      :isPass="true"
      :doctorSign="diagnoseDocSignImg || diagnoseDocSign"
      :deptSign="deptDirectorSignImg || deptDirectorSign"
      ref="certRef"
    />
  </div>
</template>
<style lang="less">
  .dark-loading {
    .full-loading {
      background-color: rgb(255 255 255 / 96%);
    }
  }
</style>
