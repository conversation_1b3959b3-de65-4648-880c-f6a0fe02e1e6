<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { Button } from '@ft/internal/components/Button';
  import { useUserStore } from '@ft/internal/store/modules/user';
  import { onMounted, ref } from 'vue';
  import { useRequest } from '@ft/request';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { editProve, getDetail, issueProve, upload } from '/@/api/issuing-records';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { useRoutePageMode } from '@ft/internal/hooks/web/useRoutePageMode';
  import { omit } from 'lodash-es';
  import htmlToPdf from '@ft/internal/utils/htmlToPdf';
  import { message } from 'ant-design-vue';
  import { useCheckSign } from '../shared';
  import RecoveryCertificate from '../../template-mg/RecoveryCertificate.vue';
  import { addFormSchema } from './data';

  const formState = ref({});
  const { check, deptSign, userSignImg, getTemplate } = useCheckSign();
  check();
  const recordId = useRouteQuery('recordId', '', { transform: String });
  const recordDetail = useRouteQuery('detail', '', {
    transform: (val) => (val ? JSON.parse(val) : {}),
  });
  const { isEditMode } = useRoutePageMode();
  const { userInfo } = useUserStore();
  const { run: getProveDetail } = useRequest(getDetail, {
    manual: true,
    onSuccess: (data) => {
      setFieldsValue({ ...data, sexCode: data.sex == '男' ? 1 : 2, name: data.patientName });
    },
  });
  const [register, { getFieldsValue, setFieldsValue, validate }] = useForm({
    labelWidth: 140,
    colon: true,
    showActionButtonGroup: false,
    schemas: addFormSchema,
  });
  onMounted(() => {
    setFieldsValue({
      deptId: userInfo?.deptId,
      deptName: userInfo?.deptName,
      visitOrgName: userInfo?.orgName,
    });
    recordId.value && getProveDetail(recordId.value);
    if (Object.keys(recordDetail.value).length) {
      if (recordDetail.value.sex) setFieldsValue(recordDetail.value);
    }
  });

  function onCancel() {
    // resetFields();
    // setFieldsValue({
    //   deptId: userInfo?.deptId,
    //   deptName: userInfo?.deptName,
    //   visitOrgName: userInfo?.orgName,
    // });
    go({ name: 'IssuingRecordList' });
  }
  const go = useGo();
  const loading = ref(false);
  const { runAsync: runAsyncIssueProve, loading: issueProveLoading } = useRequest(
    (data) =>
      isEditMode ? editProve(getFieldsValue()) : issueProve(Object.assign(data, getFieldsValue())),
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess(res) {
        go({
          name: 'IssuingRecordListDetail',
          query: {
            recordId: res.id || recordId.value,
          },
        });
      },
    },
  );

  const onClickBtn = async () => {
    if (isEditMode) {
      runAsyncIssueProve({});
    } else {
      const values = await validate();
      loading.value = true;
      const template = await getTemplate();
      formState.value = {
        ...values,
        orgUrl: template.orgIcon,
        signature: template.signature,
      };
      message.info('正在生成证明文件,大约需要5-10秒钟');
      setTimeout(async () => {
        try {
          const [pdfBlob, imgBlob] = await htmlToPdf('', '#cert-view', {
            noDownload: true,
          });
          const pdfRes = await upload(new File([pdfBlob], 'pdf.pdf', { type: 'application/pdf' }));
          const imgRes = await upload(new File([imgBlob!], 'img.jpg', { type: 'image/jpg' }));
          await runAsyncIssueProve({
            pdfUrl: pdfRes.url,
            imageUrl: imgRes.url,
            diagnoseDocSign: userSignImg,
            deptDirectorSign: deptSign.value,
            ...values,
          });
        } catch (e) {
          message.error('生成证明文件失败');
        } finally {
          loading.value = false;
        }
      }, 5000);
    }
  };
</script>

<template>
  <div class="pr-4 h-full dark-loading" v-loading="issueProveLoading || loading">
    <div v-show="!loading" class="bg-white rounded-t flex flex-col h-full">
      <div class="page-body p-4 flex-1 basis-0 min-h-0 of-y-auto pb-8 flex flex-col">
        <div class="form">
          <BasicForm @register="register">
            <template #patientInformation>
              <div class="flex items-center gap-2">
                <span class="inline-block bg-primary-color w-3px h-1em"></span>
                <span>患者基本信息</span>
              </div>
            </template>
            <template #treatmentInformation>
              <div class="flex items-center gap-2">
                <span class="inline-block bg-primary-color w-3px h-1em"></span>
                <span>患者诊疗信息</span>
              </div>
            </template>
          </BasicForm>
        </div>
      </div>
      <div class="flex p-4 justify-end items-center">
        <div class="actions-group flex gap-2">
          <Button @click="onCancel">取消</Button>
          <Button @click="onClickBtn" type="primary"> 生成证明 </Button>
        </div>
      </div>
    </div>
    <RecoveryCertificate
      v-if="loading"
      v-bind="omit(formState, 'id')"
      :isPass="true"
      :doctorSign="userSignImg"
      :deptSign="deptSign"
      ref="certRef"
    />
  </div>
</template>
<style lang="less">
  .dark-loading {
    .full-loading {
      background-color: rgb(255 255 255 / 96%);
    }
  }
</style>
