import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import dayjs from 'dayjs';

export const formSchema: FormSchema[] = [
  {
    field: 'time',
    label: '申请日期',
    component: 'RangePicker',
    defaultValue: [
      dayjs().startOf('M').format('YYYY-MM-DD'),
      dayjs().endOf('M').format('YYYY-MM-DD'),
    ],
    componentProps: {
      style: 'width:100%',
      valueFormat: 'YYYY-MM-DD',
    },
    isHandleDateDefaultValue: false,
    colProps: { span: 6 },
  },
  {
    field: 'patientName',
    label: '患者姓名',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'outpatientNo',
    label: '门诊号',
    component: 'Input',
    colProps: { span: 6 },
  },
];

export const columns: BasicColumn[] = [
  {
    title: '申请日期',
    dataIndex: 'applyDate',
    width: 180,
    align: 'left',
  },
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    width: 100,
    align: 'left',
  },
  {
    title: '门诊号',
    dataIndex: 'outpatientNo',
    width: 120,
    align: 'left',
  },
  {
    title: '性别',
    dataIndex: 'sex',
    width: 100,
    align: 'left',
  },
  {
    title: '年龄',
    dataIndex: 'age',
    width: 100,
    align: 'left',
  },
  {
    title: '科室',
    dataIndex: 'deptName',
    width: 150,
    align: 'left',
  },
  {
    title: '学校(单位)',
    dataIndex: 'orgName',
    width: 150,
    align: 'left',
  },
  {
    title: '班级(科/部门)',
    dataIndex: 'departmentName',
    width: 150,
    align: 'left',
  },
  {
    title: '确诊日期',
    dataIndex: 'diagnoseDate',
    width: 180,
    align: 'left',
  },
  {
    title: '确诊病',
    dataIndex: 'diagnoseIllness',
    width: 150,
    align: 'left',
  },
  {
    title: '治疗日期',
    dataIndex: 'treatDate',
    width: 180,
    align: 'left',
  },
  {
    title: '诊断医生',
    dataIndex: 'diagnoseDocName',
    width: 100,
    align: 'left',
  },
  {
    title: '申请状态',
    dataIndex: 'applyStatus',
    width: 100,
    align: 'left',
  },
  {
    title: '科主任',
    dataIndex: 'deptDirectorName',
    width: 100,
    align: 'left',
  },
  {
    title: '诊断科室',
    dataIndex: 'diagnoseDeptName',
    width: 150,
    align: 'left',
  },
  {
    title: '患者类型',
    dataIndex: 'patientType',
    width: 100,
    align: 'left',
  },
  {
    title: '就诊机构',
    dataIndex: 'visitOrgName',
    width: 180,
    align: 'left',
  },
];
