<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { rejectApply } from '/@/api/issuing-records';
  import { computed, ref } from 'vue';

  const emit = defineEmits(['register', 'success']);
  const mode = ref<'add' | 'view'>('add');

  const disabled = computed(() => mode.value === 'view');

  const [registerForm, formAction] = useForm({
    labelWidth: 100,
    schemas: [
      {
        field: 'id',
        label: 'id',
        component: 'Input',
        show: false,
      },
      {
        field: 'rejectReason',
        label: '驳回原因',
        component: 'InputTextArea',
        componentProps: {
          autoSize: { minRows: 4, maxRows: 4 },
        },
        colProps: { span: 20 },
        required: true,
      },
    ],
    showActionButtonGroup: false,
    disabled,
  });

  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data.mode;
    formAction.setFieldsValue(data.record);
    formAction.clearValidate();
  });

  const { run: runRejectApply } = useRequest(rejectApply, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      emit('success');
      closeModal();
    },
  });

  function onOk() {
    formAction.validate().then((values) => {
      runRejectApply(values);
    });
  }
</script>

<template>
  <BasicModal
    destroy-on-close
    v-bind="$attrs"
    @register="register"
    width="50%"
    :min-height="100"
    title="驳回"
    centered
    :footer="disabled ? null : undefined"
    @ok="onOk"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<style scoped></style>
