<script setup lang="ts">
  import { Tabs } from 'ant-design-vue';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { ref, watch } from 'vue';
  import { useModal } from '@ft/internal/components/Modal';
  import { columns, formSchema } from './data';
  import { getApplyList } from '/@/api/issuing-records';
  import RejectModal from './RejectModal.vue';

  const activeTab = ref(0);
  watch(activeTab, () => {
    tableAction.reload();
  });
  const [registerTable, tableAction] = useTable({
    fetchSetting: {
      sizeField: 'pageSize',
      pageField: 'pageNum',
      listField: 'list',
    },
    columns,
    api: getApplyList,
    formConfig: {
      labelWidth: 80,
      showAdvancedButton: false,
      actionColOptions: {
        span: 6,
        style: {
          textAlign: 'right',
        },
      },
      schemas: formSchema,
      fieldMapToTime: [
        ['time', ['startTime', 'endTime'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ],
    },
    actionColumn: {
      width: 150,
      title: '操作',
      dataIndex: 'action',
    },
    beforeFetch(params) {
      return {
        ...params,
        applyStatus: activeTab.value === -1 ? undefined : activeTab.value,
      };
    },
    useSearchForm: true,
    showIndexColumn: true,
    bordered: false,
    size: 'small',
    resizeHeightOffset: 10,
  });
  const go = useGo();
  const createActions = (record, _column): ActionItem[] => {
    const actions: ActionItem[] = [
      {
        label: '生成证明',
        type: 'link',
        onClick: onAdd.bind(null, record),
        ifShow: () => {
          return record.applyStatusCode === 0;
        },
      },

      {
        label: '驳回',
        type: 'link',
        danger: true,
        onClick: onReject.bind(null, record),
        ifShow: () => {
          return record.applyStatusCode === 0;
        },
      },
      {
        label: '查看详情',
        type: 'link',
        onClick: onDetail.bind(null, record),
        ifShow: () => {
          return record.applyStatusCode === 1;
        },
      },
      {
        label: '驳回原因',
        type: 'link',
        onClick: onRejectReason.bind(null, record),
        ifShow: () => {
          return record.applyStatusCode === 2;
        },
      },
    ];
    return actions;
  };
  function onAdd(record) {
    go({
      name: 'IssuingRecordMobileApplyAdd',
      query: {
        applyId: record.id,
      },
    });
  }
  const [register, { openModal: openRejectModal }] = useModal();
  function onReject(record) {
    openRejectModal(true, { mode: 'add', record });
  }
  function onRejectReason(record) {
    openRejectModal(true, { mode: 'view', record });
  }
  function onDetail(record) {
    go({
      name: 'IssuingRecordMobileApplyDetail',
      query: {
        recordId: record.recordId,
      },
    });
  }
</script>

<template>
  <div class="w-full h-full pr-4">
    <BasicTable class="ft-main-table" @register="registerTable">
      <template #headerTop>
        <div class="text-16px font-bold">传染病解除隔离医学申请列表</div>
        <Tabs v-model:activeKey="activeTab">
          <Tabs.TabPane :key="0" tab="待开具" />
          <Tabs.TabPane :key="1" tab="已开具" />
          <Tabs.TabPane :key="2" tab="已驳回" />
          <Tabs.TabPane :key="-1" tab="全部" />
        </Tabs>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
    <RejectModal @register="register" @success="tableAction.reload" />
  </div>
</template>
