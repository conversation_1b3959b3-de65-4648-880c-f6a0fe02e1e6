<script setup lang="ts">
  import { <PERSON><PERSON>, Tabs } from 'ant-design-vue';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { exportUtil } from '@ft/internal/utils';
  import { Authority } from '@ft/internal/components/Authority';
  import { useRequest } from '@ft/request';
  import { ref, watch } from 'vue';
  import { usePermission } from '@ft/internal/hooks/web/usePermission';
  // import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { columns, formSchema } from './data';
  import { deleteProve, doExport, getPage } from '/@/api/issuing-records';
  import { RoleEnum } from '/@/enums/roleEnum';

  const { hasPermission } = usePermission();
  const go = useGo();
  // const activeTab = useRouteQuery('activeTab', '1', { transform: Number });
  const activeTab = ref(1);
  watch(activeTab, () => {
    tableAction.reload();
  });
  const [registerTable, tableAction] = useTable({
    fetchSetting: {
      sizeField: 'pageSize',
      pageField: 'pageNum',
      listField: 'list',
    },
    columns,
    api: getPage,
    formConfig: {
      labelWidth: 80,
      showAdvancedButton: false,
      actionColOptions: {
        span: hasPermission([RoleEnum.RECORDS_ADD]) ? 6 : 18,
        style: {
          textAlign: 'right',
        },
      },
      schemas: formSchema([RoleEnum.RECORDS_ADD]),
      fieldMapToTime: [
        [
          'time',
          ['issueStartDate', 'issueEndDate'],
          ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59'],
        ],
      ],
    },
    actionColumn: {
      width: hasPermission([RoleEnum.RECORDS_ADD]) ? 180 : 100,
      title: '操作',
      dataIndex: 'action',
    },
    beforeFetch(params) {
      return {
        ...params,
        examineType: activeTab.value === -1 ? undefined : activeTab.value,
      };
    },
    useSearchForm: true,
    showIndexColumn: true,
    bordered: false,
    size: 'small',
    resizeHeightOffset: 10,
  });

  function onPrint(record: any) {
    go({
      name: 'IssuingRecordListDetail',
      query: {
        // record: window.btoa(encodeURIComponent(JSON.stringify(record))),
        // JSON.parse(decodeURIComponent(window.atob(recordStr as any)));
        recordId: record.id,
      },
    });
  }
  const { runAsync: deleteProveRunAsync } = useRequest(deleteProve, {
    manual: true,
    showSuccessMessage: true,
    onSuccess() {
      tableAction.reload();
    },
  });
  function onRemove(record) {
    deleteProveRunAsync(record.id);
  }
  function createActions(record, _column): ActionItem[] {
    return [
      {
        label: '查看详情',
        type: 'link',
        onClick: onPrint.bind(null, record),
      },
      {
        label: '删除',
        type: 'link',
        auth: RoleEnum.RECORDS_ADD,
        ifShow: () => {
          return record.examineType === 1 || record.examineType === 3;
        },
        danger: true,
        popConfirm: {
          title: '确定删除吗？',
          placement: 'topRight',
          okButtonProps: { danger: true },
          confirm: onRemove.bind(null, record),
        },
      },
      {
        label: '编辑',
        type: 'link',
        auth: RoleEnum.RECORDS_ADD,
        ifShow: () => {
          return record.examineType === 1 || record.examineType === 3;
        },
        onClick: onEdit.bind(null, record),
      },
    ];
  }

  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(doExport, {
    manual: true,
    // showSuccessMessage: true,
  });
  function onExportExpert() {
    exportUtil(exportExpertRunAsync(tableAction.getForm().getFieldsValue()));
  }

  async function onAdd() {
    // await check();
    go({
      name: 'IssuingRecordListAdd',
    });
  }
  async function onEdit(record) {
    // await check();
    go({
      name: 'IssuingRecordListEdit',
      query: {
        mode: 'edit',
        recordId: record.id,
      },
    });
  }
</script>
<template>
  <div class="w-full h-full pr-4">
    <BasicTable class="ft-main-table" @register="registerTable">
      <template #headerTop>
        <div class="text-16px font-bold">传染病解除隔离医学证明列表</div>
        <Tabs v-model:activeKey="activeTab">
          <Tabs.TabPane :key="1" tab="待审核" />
          <Tabs.TabPane :key="2" tab="已审核" />
          <Tabs.TabPane :key="3" tab="已撤销" />
          <Tabs.TabPane :key="-1" tab="全部" />
        </Tabs>
      </template>
      <template #tableTitle>
        <Authority :value="RoleEnum.RECORDS_ADD">
          <Button type="primary" @click="onAdd"> 新增 </Button>
        </Authority>
      </template>
      <template #toolbar>
        <Button :loading="exportLoading" @click="onExportExpert"> 批量导出 </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
