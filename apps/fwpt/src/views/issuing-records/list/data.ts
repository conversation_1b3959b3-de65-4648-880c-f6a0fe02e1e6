import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import dayjs from 'dayjs';
import { usePermission } from '@ft/internal/hooks/web/usePermission';
const { hasPermission } = usePermission();
export const formSchema = (auth): FormSchema[] => {
  return [
    {
      field: 'time',
      label: '开具日期',
      component: 'RangePicker',
      defaultValue: [
        dayjs().startOf('M').format('YYYY-MM-DD'),
        dayjs().endOf('M').format('YYYY-MM-DD'),
      ],
      componentProps: {
        style: 'width:100%',
        valueFormat: 'YYYY-MM-DD',
      },
      isHandleDateDefaultValue: false,
      colProps: { span: 6 },
    },
    {
      field: 'issueOrgName',
      label: '开具机构',
      component: 'Input',
      colProps: { span: 6 },
      ifShow: !hasPermission(auth),
    },
    {
      field: 'diagnoseDeptName',
      label: '诊断科室',
      component: 'Input',
      colProps: { span: 6 },
      ifShow: !hasPermission(auth),
    },
    {
      field: 'diagnoseDocName',
      label: '诊断医生',
      component: 'Input',
      colProps: { span: 6 },
      ifShow: !hasPermission(auth),
    },
    {
      field: 'patientName',
      label: '患者姓名',
      component: 'Input',
      colProps: { span: 6 },
    },
    {
      field: 'outpatientNo',
      label: '门诊号',
      component: 'Input',
      colProps: { span: 6 },
      ifShow: hasPermission(auth),
    },
  ];
};

const examineTypeMap = new Map<number, string>([
  [1, '待审核'],
  [2, '已审核'],
  [3, '已撤销'],
]);
export const columns: BasicColumn[] = [
  {
    title: '流水号',
    dataIndex: 'serialNumber',
    width: 150,
    align: 'left',
  },
  {
    title: '开具日期',
    dataIndex: 'issueDate',
    width: 180,
    align: 'left',
  },
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    width: 100,
    align: 'left',
  },
  {
    title: '门诊号',
    dataIndex: 'outpatientNo',
    width: 120,
    align: 'left',
  },
  {
    title: '性别',
    dataIndex: 'sex',
    width: 100,
    align: 'left',
  },
  {
    title: '年龄',
    dataIndex: 'age',
    width: 100,
    align: 'left',
  },
  {
    title: '科室',
    dataIndex: 'deptName',
    width: 150,
    align: 'left',
  },
  {
    title: '学校(单位)',
    dataIndex: 'orgName',
    width: 100,
    align: 'left',
  },
  {
    title: '班级(科/部门)',
    dataIndex: 'departmentName',
    width: 150,
    align: 'left',
  },
  {
    title: '确诊日期',
    dataIndex: 'diagnoseDate',
    width: 180,
    align: 'left',
  },
  {
    title: '确诊病',
    dataIndex: 'diagnoseIllness',
    width: 150,
    align: 'left',
  },
  {
    title: '治疗日期',
    dataIndex: 'treatDate',
    width: 180,
    align: 'left',
  },
  // {
  //   title: '后续治疗建议',
  //   dataIndex: 'remarryTreatAdvise',
  //   width: 200,
  //   align: 'left',
  // },
  {
    title: '诊断医生',
    dataIndex: 'diagnoseDocName',
    width: 100,
    align: 'left',
  },
  {
    title: '科主任',
    dataIndex: 'deptDirectorName',
    width: 100,
    align: 'left',
  },
  {
    title: '审核状态',
    dataIndex: 'examineType',
    width: 100,
    align: 'left',
    format: examineTypeMap,
  },
  {
    title: '诊断科室',
    dataIndex: 'diagnoseDeptName',
    width: 150,
    align: 'left',
  },
  // {
  //   title: '患者类型',
  //   dataIndex: 'patientType',
  //   width: 100,
  //   align: 'left',
  // },
  {
    title: '就诊机构',
    dataIndex: 'visitOrgName',
    width: 180,
    align: 'left',
  },
  {
    title: '联系电话',
    dataIndex: 'contactPhone',
    width: 150,
    align: 'left',
  },
];
