<script setup lang="ts">
  import { Button } from '@ft/internal/components/Button';
  import { Authority } from '@ft/internal/components/Authority';
  import { useRequest } from '@ft/request';
  import { examinePass, getDetail, revokeRecord, upload } from '/@/api/issuing-records';
  import { useRoute } from 'vue-router';
  import { RoleEnum } from '/@/enums/roleEnum';
  import htmlToPdf from '@ft/internal/utils/htmlToPdf';
  import { ref } from 'vue';
  import { message } from 'ant-design-vue';
  import { omit } from 'lodash-es';
  import RecoveryCertificate from '../../template-mg/RecoveryCertificate.vue';
  const route = useRoute();
  const props = defineProps<{
    values: any;
  }>();

  const isPreview = !!props.values;
  const isPass = ref(false);
  const loading = ref(false);
  const certRef = ref();
  const { data: record, run } = useRequest<any, any>(
    () => {
      if (isPreview) {
        return props.values;
      }
      return getDetail(route.query.recordId as string);
    },
    {
      onSuccess() {
        isPass.value = isPreview || record.value?.examineType === 2;
      },
    },
  );
  const { run: runExamine } = useRequest(examinePass, {
    manual: true,
    showSuccessMessage: true,
    onSuccess() {
      run();
    },
    onFinally() {
      loading.value = false;
    },
  });

  const { run: runRevoke, loading: revokeLoading } = useRequest(
    () => revokeRecord(route.query.recordId as string),
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess() {
        run();
      },
    },
  );

  const onExamine = async () => {
    isPass.value = true;
    loading.value = true;
    message.info('正在生成证明文件,大约需要5-10秒钟');
    setTimeout(async () => {
      try {
        const [pdfBlob, imgBlob] = await htmlToPdf('', '#cert-view', {
          noDownload: true,
        });
        const pdfRes = await upload(new File([pdfBlob], 'pdf.pdf', { type: 'application/pdf' }));
        const imgRes = await upload(new File([imgBlob!], 'img.jpg', { type: 'image/jpg' }));
        runExamine({
          id: record.value?.id as string,
          pdfUrl: pdfRes.url,
          imageUrl: imgRes.url,
        });
      } catch (e) {
        message.error('生成证明文件失败');
        console.log(e);
        loading.value = false;
      }
    }, 5000);
  };

  const onPrint = () => {
    fetch(record.value?.pdfUrl as string)
      .then((res) => res.blob())
      .then((blob) => {
        const url = URL.createObjectURL(blob);
        const pdfView = document.getElementById('pdfView') as HTMLIFrameElement;
        pdfView.src = url;
        pdfView.onload = () => {
          pdfView.contentWindow?.window.print();
        };
      });
  };
</script>

<template>
  <div
    class="relative h-[calc(100%-16px)] w-[calc(100%-16px)] rounded rounded-lt-none bg-white flex justify-center"
    v-loading="loading"
  >
    <div class="actions absolute right-4 top-4 flex gap-3">
      <template v-if="!isPreview">
        <Authority :value="RoleEnum.RECORDS_DETAIL_EXAMINE">
          <Button
            type="primary"
            v-if="record?.examineType === 1 || record?.examineType === 3"
            @click="onExamine"
          >
            审核通过
          </Button>
          <Button
            type="primary"
            v-if="record?.examineType === 2"
            @click="runRevoke"
            :loading="revokeLoading"
          >
            撤销
          </Button>
        </Authority>
        <Button :disabled="record?.examineType !== 2" @click="onPrint">打印</Button>
      </template>
    </div>
    <div class="h-full w-full flex justify-center">
      <RecoveryCertificate
        v-bind="omit(record, 'id')"
        :isPass="isPass"
        :doctorSign="record?.diagnoseDocSign"
        :deptSign="record?.deptDirectorSign"
        ref="certRef"
      />
    </div>
    <iframe id="pdfView" style="display: none"></iframe>
  </div>
</template>
