<script setup lang="ts">
  import { Button } from 'ant-design-vue';
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { useToggle } from '@vueuse/core';
  import { computed } from 'vue';
  import { formSchema } from './data';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import { exportExcel, getStatisticData, getStatisticTableHeader } from '/@/api/issuing-records';

  const { loading, data: tableHeader } = useRequest(getStatisticTableHeader);
  const columns = computed(() => {
    const data =
      tableHeader.value?.map((item) => {
        return {
          title: item.fieldName,
          dataIndex: item.fieldCode,
          width: 100,
        };
      }) || [];
    return data;
  });

  const [registerTable, tableIns] = useTable({
    fetchSetting: {
      sizeField: 'pageSize',
      pageField: 'pageNum',
      listField: 'list',
    },
    columns,
    api: getStatisticData,
    formConfig: {
      labelWidth: 80,
      showAdvancedButton: false,
      actionColOptions: {
        span: 24,
        style: {
          textAlign: 'right',
        },
      },
      schemas: formSchema,
    },
    useSearchForm: true,
    showIndexColumn: false,
    bordered: false,
    size: 'small',
    resizeHeightOffset: 20,
    loading,
  });
  const { runAsync: exportRunAsync } = useRequest(exportExcel, {
    manual: true,
    showSuccessMessage: true,
  });
  const [exportLoading, setExportLoading] = useToggle(false);
  async function batchExport() {
    setExportLoading(true);
    try {
      await exportUtil(exportRunAsync(tableIns.getForm().getFieldsValue()));
    } catch (error) {
      console.error(error);
    } finally {
      setExportLoading(false);
    }
  }
</script>
<template>
  <div class="w-full h-full pr-4">
    <BasicTable class="ft-main-table" @register="registerTable">
      <template #headerTop>
        <div class="text-16px font-bold">传染病解除隔离医学证明统计</div>
      </template>
      <template #toolbar>
        <Button :loading="exportLoading" @click="batchExport"> 导出 </Button>
      </template>
    </BasicTable>
  </div>
</template>
