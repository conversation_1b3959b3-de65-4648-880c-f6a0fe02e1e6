import type { FormSchema } from '@ft/internal/components/Table';

const quarterOptions = [
  {
    label: '第一季度',
    value: '1',
  },
  {
    label: '第二季度',
    value: '2',
  },
  {
    label: '第三季度',
    value: '3',
  },
  {
    label: '第四季度',
    value: '4',
  },
];

const monthOptions = [
  {
    label: '1月',
    value: '1',
  },
  {
    label: '2月',
    value: '2',
  },
  {
    label: '3月',
    value: '3',
  },
  {
    label: '4月',
    value: '4',
  },
  {
    label: '5月',
    value: '5',
  },
  {
    label: '6月',
    value: '6',
  },
  {
    label: '7月',
    value: '7',
  },
  {
    label: '8月',
    value: '8',
  },
  {
    label: '9月',
    value: '9',
  },
  {
    label: '10月',
    value: '10',
  },
  {
    label: '11月',
    value: '11',
  },
  {
    label: '12月',
    value: '12',
  },
];

export const formSchema: FormSchema[] = [
  {
    label: '开具年度',
    field: 'year',
    component: 'DatePicker',
    defaultValue: new Date().getFullYear().toString(),
    componentProps: {
      style: { width: '100%' },
      picker: 'year',
      valueFormat: 'YYYY',
      format: 'YYYY' + '年',
    },
    isHandleDateDefaultValue: false,
    colProps: { span: 6 },
  },
  {
    label: '开具季度',
    field: 'quarter',
    component: 'Select',
    componentProps: {
      options: quarterOptions,
    },
    colProps: { span: 6 },
  },
  {
    label: '开具月度',
    field: 'month',
    component: 'Select',
    componentProps: {
      options: monthOptions,
    },
    colProps: { span: 6 },
  },
  {
    field: 'issueDoc',
    label: '开具医生',
    component: 'Input',
    colProps: { span: 6 },
  },
];
