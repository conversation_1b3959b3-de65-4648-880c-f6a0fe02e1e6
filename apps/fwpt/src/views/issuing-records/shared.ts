import { useRequest } from '@ft/request';
import { checkSign } from '/@/api/issuing-records';
import { templateApi } from '/@/api/template-mg';
import { useUserStore } from '@ft/internal/store/modules/user';
import { message } from 'ant-design-vue';

export const useCheckSign = () => {
  const userStore = useUserStore();
  const { userSignImg } = userStore.getUserInfo; // no reactive
  const {
    runAsync,
    data: deptSign,
    loading: checkLoading,
  } = useRequest(checkSign, {
    manual: true,
  });
  const check = () => {
    if (!userSignImg) {
      message.warning('登录用户未上传签名');
      return Promise.reject();
    }
    return runAsync();
  };
  const getTemplate = async () => {
    const template = await templateApi
      .list({ orgId: userStore.getUserInfo.orgId })
      .then((res) => res[0]);
    if (!template) {
      message.warning('未找到该机构模板信息');
    }
    return template;
  };
  return {
    deptSign,
    userSignImg,
    check,
    checkLoading,
    getTemplate,
  };
};
