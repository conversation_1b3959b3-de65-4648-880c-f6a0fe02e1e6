<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { computed, ref } from 'vue';
  import { useRequest } from '@ft/request';
  import { formSchema } from './data';
  import { templateApi } from '/@/api/template-mg';

  const emit = defineEmits(['register', 'success']);
  const mode = ref('add');
  const getTitle = computed(() => {
    return mode.value === 'add' ? '新增模板' : '编辑模板';
  });

  const [registerForm, formAction] = useForm({
    labelWidth: 80,
    schemas: formSchema,
    showActionButtonGroup: false,
  });
  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data.mode;
    if (mode.value === 'add') {
      formAction.resetFields();
    } else {
      formAction.setFieldsValue(data.record);
    }
  });
  const { loading, run: onOk } = useRequest(
    () =>
      mode.value === 'edit'
        ? templateApi.update(formAction.getFieldsValue() as any)
        : templateApi.add(formAction.getFieldsValue() as any),
    {
      manual: true,
      showSuccessMessage: true,
      async onBefore() {
        return formAction.validate();
      },
      onSuccess() {
        emit('success');
        closeModal();
      },
    },
  );
</script>

<template>
  <BasicModal
    @register="register"
    :width="600"
    :title="getTitle"
    @ok="onOk"
    :ok-button-props="{
      loading,
    }"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<style scoped></style>
