import type { FormSchema } from '@ft/internal/components/Table';
import { queryOrganizationList } from '/@/api/sys';
import { UPLOAD_URL } from '/@/api/template-mg';

export const formSchema: FormSchema[] = [
  {
    field: 'id',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'templateName',
    label: '模板名称',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    field: 'orgId',
    label: '所属机构',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => queryOrganizationList(),
        labelField: 'orgName',
        valueField: 'id',
        showSearch: true,
        optionFilterProp: 'label',
        onChange: (_, opt) => {
          formModel.orgName = opt?.label;
        },
        getPopupContainer: () => document.body,
      };
    },
    required: true,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    field: 'orgName',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'orgIcon',
    label: '机构图标',
    component: 'UploadAsset',
    required: true,
    componentProps: {
      uploadType: 'image',
      resultField: 'data.url',
      maxCount: 1,
      action: UPLOAD_URL,
    },
  },
  {
    field: 'signature',
    label: '签章',
    component: 'UploadAsset',
    required: true,
    componentProps: {
      uploadType: 'image',
      resultField: 'data.url',
      maxCount: 1,
      action: UPLOAD_URL,
    },
  },
];
