<script setup lang="ts">
  import { Icon } from '@ft/internal/components/Icon';
  import { EllipsisOutlined } from '@ant-design/icons-vue';
  import { Dropdown, Input, Menu } from 'ant-design-vue';
  import { ref } from 'vue';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import { useModal } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { templateApi } from '/@/api/template-mg';
  import TemplateModal from './Modal.vue';
  import RecoveryCertificate from './RecoveryCertificate.vue';
  const searchVal = ref('');
  const activeItem = ref<Recordable | null>(null);
  const { createConfirm } = useMessage();
  const [register, { openModal }] = useModal();
  const { data: items, run: refresh } = useRequest(
    () => templateApi.list({ templateName: searchVal.value }),
    {
      onSuccess(val) {
        if (!activeItem.value) {
          activeItem.value = val?.[0];
        } else if (val?.length === 0) {
          activeItem.value = null;
        }
      },
    },
  );
  const { run: onDelete } = useRequest(templateApi.delete, {
    onSuccess() {
      refresh();
    },
    manual: true,
    showSuccessMessage: true,
  });

  function onNew() {
    openModal(true, { mode: 'add' });
  }

  function onRemove(item) {
    createConfirm({
      title: '删除模板',
      content: '确定删除该模板吗？',
      iconType: 'warning',
      onOk: () => {
        onDelete(item.id);
      },
    });
  }

  function onEdit(record) {
    openModal(true, { mode: 'edit', record });
  }
</script>

<template>
  <div class="h-[calc(100%-16px)] w-[calc(100%-10px)] bg-white rounded-b-4px rounded-tr-4px flex">
    <div class="p-16px b-r-1px b-r-solid b-r-#EDEEF0">
      <div class="text-16px font-bold leading-24px"> 传染病解除隔离医学证明模板 </div>
      <div class="w-240px my-16px flex justify-between items-center gap-4">
        <Input
          placeholder="搜索"
          v-model:value="searchVal"
          @blur="refresh"
          @press-enter="refresh"
        />
        <Icon :size="32" icon="cert-template-add|svg" class="cursor-pointer" @click="onNew" />
      </div>
      <div
        v-for="item in items"
        :key="item.id"
        class="card relative transition-all"
        :class="{ 'card-active': activeItem?.id === item.id }"
        @click="activeItem = item"
      >
        <div class="title mb-8px">{{ item.templateName }}</div>
        <div class="leading-22px mb-4px">
          <span class="color-#999">机构：</span><span>{{ item.orgName }}</span>
        </div>
        <div class="leading-22px">
          <span class="color-#999">创建时间：</span><span>{{ item.createTime }}</span>
        </div>
        <div class="more-action">
          <Dropdown>
            <EllipsisOutlined
              style="
                color: var(--primary-color);
                font-size: 20px;
                box-shadow: 0 0 0 1px rgb(51 188 113 / 20%);
              "
            />
            <template #overlay>
              <Menu>
                <Menu.Item>
                  <div class="w-80px" @click="onEdit(item)">编辑</div>
                </Menu.Item>
                <Menu.Item>
                  <div class="w-80px color-#FF4864" @click="onRemove(item)">删除</div>
                </Menu.Item>
              </Menu>
            </template>
          </Dropdown>
        </div>
      </div>
    </div>
    <div class="flex-1 h-full of-hidden">
      <div class="h-56px text-16px font-bold leading-56px px-16px b-b-1px b-b-solid b-b-#EDEEF0">
        模板详情
      </div>
      <div class="h-[calc(100%-56px)] of-y-auto w-full bg-#FAFAFA px-16px pt-16px">
        <div class="h-full w-full flex justify-center">
          <RecoveryCertificate :orgUrl="activeItem?.orgIcon" />
        </div>
      </div>
    </div>
    <TemplateModal @register="register" @success="refresh" />
  </div>
</template>

<style lang="less" scoped>
  .card {
    width: 240px;
    padding: 16px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    cursor: pointer;
    margin-bottom: 8px;

    &:hover {
      border: 1px solid #33bc71;

      .more-action {
        display: block;
      }
    }

    &-active {
      border: 1px solid #33bc71;
      box-shadow: 0 0 0 2px rgb(51 188 113 / 20%);
    }

    .title {
      font-size: 14px;
      font-weight: bold;
      line-height: 22px;
    }

    .more-action {
      display: none;
      position: absolute;
      bottom: 13px;
      right: 16px;
    }
  }
</style>
