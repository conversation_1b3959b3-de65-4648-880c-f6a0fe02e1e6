<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { Input } from 'ant-design-vue';
  import { Icon } from '@ft/internal/components/Icon';

  import { StyledList } from '@ft/components';
  import { useRequest } from '@ft/request';
  import type { IDrugInfo } from '/@/api/drug-management/dict';
  import { getDrugInfoList } from '/@/api/drug-management/dict';
  import Edit from './comp/Edit.vue';

  const searchValue = ref('');

  const activeId = ref<string>();
  const activeItem = ref<IDrugInfo>();
  const {
    loading,
    data: drugInfoList,
    runAsync: getDrugInfoListAsync,
  } = useRequest(getDrugInfoList, {
    onSuccess: (data) => {
      activeId.value = data.length > 0 ? data[0].id : '';
      activeItem.value = data?.[0] || {};
    },
  });

  onMounted(() => {
    getDrugInfoListAsync();
  });
  const items = computed(() => {
    return drugInfoList.value?.filter((item) => {
      return item.drugName?.includes(searchValue.value);
    });
  });

  function handleChange(item) {
    activeItem.value = item;
  }
</script>

<template>
  <div class="flex gap-2 h-[calc(100%-10px)] w-[calc(100%-10px)] rounded rounded-lt-none">
    <div class="flex flex-col gap-3 w-228px bg-#fff rounded-[0px_8px_8px_8px] p-3">
      <span class="text-#333333 fw-bold">合理用药审核规则库</span>
      <div class="search flex items-center text-center gap-9px">
        <Input allow-clear placeholder="输入关键字" v-model:value="searchValue">
          <template #suffix>
            <Icon icon="ant-design:search-outlined" />
          </template>
        </Input>
      </div>
      <div class="pr-4 of-y-auto of-x-hidden min-w-0">
        <StyledList
          :items="items || []"
          v-model="activeId"
          v-model:value="activeItem"
          valueField="id"
          class="flex-1"
          :width="216"
          :loading="loading"
          @change="handleChange"
        >
          <template #default="item">
            <div class="flex justify-between items-center w-full relative">
              <div class="w-[calc(100%-20px)] overflow-hidden truncate">{{ item?.drugName }}</div>
            </div>
          </template>
        </StyledList>
      </div>
    </div>
    <div class="flex-1 h-[calc(100vh-113px)] of-y-auto rounded-2 bg-white p-4">
      <Edit :drug="activeItem" />
    </div>
  </div>
</template>

<style lang="less" scoped></style>
