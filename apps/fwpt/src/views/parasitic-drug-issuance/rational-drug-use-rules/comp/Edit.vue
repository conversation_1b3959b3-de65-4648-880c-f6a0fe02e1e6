<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicTitle } from '@ft/internal/components/Basic';
  import { Button } from '@ft/internal/components/Button';
  import { computed, nextTick, ref, watch } from 'vue';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import { useToggle } from '@vueuse/core';
  import { RuleFormSchemas } from './data';
  import { addOrEditMedicationRule, getMedicationRuleDetail } from '/@/api/rational-drug-use-rules';
  import type { IDrugInfo } from '/@/api/drug-management/dict';

  const props = defineProps<{
    drug?: IDrugInfo;
  }>();
  const wrapper = ref<HTMLDivElement | null>(null);
  const mode = ref<'view' | 'edit'>('view');
  const formDisabled = computed(() => mode.value === 'view');

  const [registerForm, formAction] = useForm({
    labelWidth: 100,
    schemas: RuleFormSchemas,
    showActionButtonGroup: false,
    disabled: formDisabled,
  });

  function getRuleDetail(drugId: string) {
    formAction.resetFields();
    getMedicationRuleDetail(drugId).then((res) => {
      formAction.setFieldsValue({
        ...(res || { drugId: props.drug?.id, drugCode: props.drug?.drugCode }),
      });
    });
  }
  watch(
    () => props.drug,
    (drug) => {
      if (drug) {
        nextTick(() => {
          getRuleDetail(drug.id);
        });
      }
    },
    { immediate: true, flush: 'post' },
  );

  function handleEdit() {
    mode.value = 'edit';
  }
  const { createMessage } = useMessage();
  const [saveLoading, setSaveLoading] = useToggle(false);
  function handleSave() {
    formAction.validate().then((values) => {
      setSaveLoading(true);
      addOrEditMedicationRule(values)
        .then((msg) => {
          createMessage.success(msg);
          mode.value = 'view';
          getRuleDetail(values.drugId as string);
        })
        .finally(() => {
          setSaveLoading(false);
        });
    });
  }

  function handleCancel() {
    mode.value = 'view';
  }
</script>

<template>
  <div class="h-full relative pr-15" ref="wrapper">
    <Button
      v-if="mode === 'view'"
      type="link"
      pre-icon="ant-design:edit-outlined"
      class="!absolute -top-2 right-0"
      @click="handleEdit"
    >
      编辑
    </Button>
    <span class="absolute z-10 -top-2 right-0">
      <Button
        v-if="mode === 'edit'"
        type="link"
        danger
        pre-icon="ant-design:close-outlined"
        @click="handleCancel"
        :disabled="saveLoading"
      >
        取消
      </Button>
      <Button
        v-if="mode === 'edit'"
        type="link"
        pre-icon="ant-design:save-outlined"
        :loading="saveLoading"
        @click="handleSave"
      >
        保存
      </Button>
    </span>

    <BasicForm @register="registerForm">
      <template #modelName="{ schema }">
        <BasicTitle normal span class="mb-2">{{ schema.componentProps?.label }}</BasicTitle>
      </template>
    </BasicForm>
  </div>
</template>
