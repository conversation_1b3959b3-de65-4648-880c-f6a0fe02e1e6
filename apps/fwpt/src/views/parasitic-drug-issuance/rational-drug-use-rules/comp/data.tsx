import { DictEnum, getDictItemList } from '@ft/internal/api';
import type { FormSchema } from '@ft/internal/components/Form';
import { InputNumber } from 'ant-design-vue';

/**
 * 基本信息
 *  药物类别 drugCategoryCode drugCategoryDesc
 *  主要成分 mainIngredients
 *  剂型 dosageFormCode dosageFormDesc
 * 适应症 indications
 * 剂量与用法
 * 成人 剂量 adultDosage 用法 adultUsageCode adultUsageDesc 频次 adultFrequency 用药时间 adultMaxDuration
 * 儿童 剂量 childDosage 用法 childUsageCode childUsageDesc 频次 childFrequency 用药时间 childMaxDuration
 * 副作用
 *  常见 sideEffectsCommon
 *  少见 sideEffectsUncommon
 *  罕见但严重 sideEffectsRareSerious
 */

function getDrugCategoryList() {
  return getDictItemList(DictEnum.DRUG_CATEGORY);
}

function getDosageFormList() {
  return getDictItemList(DictEnum.DOSAGE_FORM);
}

function getDrugUsageList() {
  return getDictItemList(DictEnum.DRUG_USAGE);
}

export const RuleFormSchemas: FormSchema[] = [
  {
    label: '药品编码',
    field: 'drugCode',
    component: 'Input',
    show: false,
  },
  {
    label: '药品名称',
    field: 'drugId',
    component: 'Input',
    show: false,
  },
  {
    label: '规则ID',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '',
    field: 'modelName1',
    component: 'Input',
    componentProps: {
      label: '基本信息',
    },
    colSlot: 'modelName',
    colProps: { span: 24 },
  },
  {
    label: '药物类别',
    field: 'drugCategoryCode',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: getDrugCategoryList,
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        onChange: (_, opt) => {
          opt && (formModel['drugCategoryDesc'] = opt?.label);
        },
      };
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '药物类别',
    field: 'drugCategoryDesc',
    component: 'Input',
    show: false,
  },
  {
    label: '主要成分',
    field: 'mainIngredients',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '剂型',
    field: 'dosageFormCode',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: getDosageFormList,
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        onChange: (_, opt) => {
          opt && (formModel['dosageFormDesc'] = opt?.label);
        },
      };
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '剂型',
    field: 'dosageFormDesc',
    component: 'Input',
    show: false,
  },
  {
    label: '',
    field: 'modelName2',
    component: 'Input',
    componentProps: {
      label: '适应症',
    },
    colSlot: 'modelName',
    colProps: { span: 24 },
  },
  {
    label: '',
    field: 'indications',
    labelWidth: 0,
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 2, maxRows: 2 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '',
    field: 'modelName3',
    component: 'Input',
    componentProps: {
      label: '剂量与用法',
    },
    colSlot: 'modelName',
    colProps: { span: 24 },
  },
  {
    label: '成人',
    field: 'adult',
    disabledLabelWidth: true,
    component: 'Input',
    colProps: { span: 24 },
    slot: 'blank',
  },
  {
    label: '剂量',
    labelWidth: 60,
    field: 'adultDosage',
    component: 'InputNumber',
    componentProps: {
      min: 0,
    },
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '用法',
    field: 'adultUsageCode',
    component: 'ApiSelect',
    componentProps: {
      api: getDrugUsageList,
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
    },
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '用法',
    field: 'adultUsageDesc',
    component: 'Input',
    show: false,
  },
  {
    label: '频次',
    field: 'adultFrequency',
    component: 'InputNumber',
    componentProps: {
      min: 0,
      precision: 0,
    },
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 16 } },
    suffix: '次/每日',
  },
  {
    label: '用药时间',
    field: 'adultMaxDuration',
    component: 'Input',
    render({ model, field }, { disabled }) {
      return (
        <div class="flex items-center gap-2">
          <span>不超过</span>
          <InputNumber
            class="flex-1"
            value={model[field]}
            onChange={(value) => {
              model[field] = value;
            }}
            precision={0}
            disabled={disabled}
          />
          <span>天</span>
        </div>
      );
    },
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '儿童',
    field: 'child',
    component: 'Input',
    colProps: { span: 24 },
    slot: 'blank',
    disabledLabelWidth: true,
  },
  {
    label: '剂量',
    labelWidth: 60,
    field: 'childDosage',
    component: 'InputNumber',
    componentProps: {
      min: 0,
    },
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '用法',
    field: 'childUsageCode',
    component: 'ApiSelect',
    componentProps: {
      api: getDrugUsageList,
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
    },
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '用法',
    field: 'childUsageDesc',
    component: 'Input',
    show: false,
  },
  {
    label: '频次',
    field: 'childFrequency',
    component: 'InputNumber',
    componentProps: {
      min: 0,
      precision: 0,
    },
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 16 } },
    suffix: '次/每日',
  },
  {
    label: '用药时间',
    field: 'childMaxDuration',
    component: 'Input',
    render({ model, field }, { disabled }) {
      return (
        <div class="flex items-center gap-2">
          <span>不超过</span>
          <InputNumber
            class="flex-1"
            value={model[field]}
            onChange={(value) => {
              model[field] = value;
            }}
            disabled={disabled}
            precision={0}
            min={0}
          />
          <span>天</span>
        </div>
      );
    },
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '',
    field: 'modelName4',
    component: 'Input',
    componentProps: {
      label: '副作用',
    },
    colSlot: 'modelName',
    colProps: { span: 24 },
  },
  {
    label: '常见',
    field: 'sideEffectsCommon',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 3, maxRows: 3 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '少见',
    field: 'sideEffectsUncommon',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 3, maxRows: 3 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '罕见但严重',
    field: 'sideEffectsRareSerious',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 3, maxRows: 3 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
];
