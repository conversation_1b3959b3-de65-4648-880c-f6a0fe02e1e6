<script setup lang="ts">
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { useRequest } from '@ft/request';
  import type { Ref } from 'vue';
  import { computed } from 'vue';
  import QuantityStatistics from './QuantityStatistics.vue';
  import QuantitativeDistribution from './QuantitativeDistribution.vue';
  import type { IEducationColumn } from '/@/api/drug-statistics';
  import { countDistribution, countStatistics, getQuantityOfOrgList } from '/@/api/drug-statistics';

  const { loading, data: quantityOfOrgList, runAsync } = useRequest(getQuantityOfOrgList);

  const dataSource = computed(() => {
    return quantityOfOrgList.value?.list?.map((item) => {
      const data = item?.chartVOList?.map((drug) => ({
        [drug.drugCode]: drug.yvalue,
      }));
      return {
        ...item,
        ...Object.assign({}, ...data),
      };
    });
  });
  const columns = computed(() => {
    const data =
      quantityOfOrgList.value?.list[0].chartVOList?.map((item) => {
        return {
          title: item.drugName,
          dataIndex: item.drugCode,
        };
      }) || [];
    return [
      {
        title: '开方机构',
        dataIndex: 'prescCreateHospital',
      },
      ...data,
    ];
  });

  const [register, ActionTable] = useTable({
    // api: getQuantityOfOrgList,
    columns: columns,
    dataSource,
    actionColumn: {
      title: '操作',
      dataIndex: 'action',
      width: 100,
    },
    resizeHeightOffset: 20,
    minHeight: 300,
    inset: true,
    immediate: false,
    useSearchForm: true,
    formConfig: {
      labelWidth: 80,
      schemas: [
        {
          field: 'queryTime',
          component: 'RangePicker',
          label: '发药时间',
          colProps: {
            span: 6,
          },
          componentProps: {
            valueFormat: 'YYYY-MM-DD',
            format: 'YYYY-MM-DD',
            showTime: false,
            style: {
              width: '100%',
            },
          },
        },
        {
          field: 'prescCreateHospital',
          component: 'Input',
          label: '开方机构',
          colProps: {
            span: 6,
          },
        },
        {
          field: 'dispenser',
          component: 'Input',
          label: '发药人',
          colProps: {
            span: 6,
          },
        },
      ],
      fieldMapToTime: [['queryTime', ['queryTimeStart', 'queryTimeEnd'], 'YYYY-MM-DD']],
      submitFunc: async () => {
        runAsync({ ...ActionTable.getForm().getFieldsValue() });
      },
    },
    loading,
  });

  const go = useGo();

  function onDrugDetail(record) {
    go({
      name: 'DrugStatisticsDetail',
      query: {
        ...ActionTable.getForm().getFieldsValue(),
        prescCreateHospital: record.prescCreateHospital,
      },
    });
  }

  function createActions(record, _column): ActionItem[] {
    const actions: ActionItem[] = [];
    if (record.prescCreateHospital != '合计') {
      actions.push({
        label: '发药详情',
        onClick: onDrugDetail.bind(null, record),
      });
    }
    return actions;
  }
  const { data: countStatisticsList } = useRequest(countStatistics);
  const { data: countDistributionList = [] as unknown as Ref<IEducationColumn[]> } =
    useRequest(countDistribution);
</script>

<template>
  <div class="h-[calc(100%-10px)] w-[calc(100%-16px)] flex flex-col gap-2.5">
    <div class="statistics h-294px flex gap-2.5">
      <div class="flex-1 of-hidden bg-white rounded rounded-lt-none p-4">
        <QuantityStatistics :data="countStatisticsList" />
      </div>
      <div class="flex-1 of-hidden bg-white rounded p-4">
        <QuantitativeDistribution :data="countDistributionList" />
      </div>
    </div>
    <div class="flex-1 of-hidden bg-white p-4 flex flex-col gap-2.5 rounded">
      <div class="text-base font-bold">各处方开具机构药品发药数量统计表</div>
      <div class="flex-1 of-hidden">
        <BasicTable @register="register">
          <template #bodyCell="{ record, column }">
            <template v-if="column.dataIndex === 'action'">
              <TableAction :divider="false" :actions="createActions(record, column)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table .ant-pagination {
      margin: 16px 0 !important;
    }
  }
</style>
