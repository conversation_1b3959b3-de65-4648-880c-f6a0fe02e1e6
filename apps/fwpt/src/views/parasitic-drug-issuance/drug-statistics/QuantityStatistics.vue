<script setup lang="ts">
  import { ref, watchEffect } from 'vue';
  import { useECharts } from '@ft/internal/hooks/web/useECharts';
  import { graphic } from 'echarts/core';
  /**
   * 药品发放数量统计
   */
  const props = defineProps({
    data: {
      type: Array,
      default: () => [],
    },
  });
  const chartRef = ref<HTMLDivElement>();
  const { setOptions } = useECharts(chartRef as any);

  watchEffect(() => {
    setOptions({
      tooltip: {
        trigger: 'axis',
        appendToBody: true,
      },
      grid: {
        left: '8%',
        right: '8%',
        bottom: '10%',
      },
      legend: {
        data: ['全市发放总量'],
      },
      xAxis: {
        data: props.data.map((item: any) => item.xValue),
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#B0B1B4',
        },
        axisLine: {
          lineStyle: {
            color: '#EDEEF0',
          },
        },
      },
      yAxis: {
        splitLine: {
          lineStyle: {
            color: '#EDEEF0',
          },
          interval: 30,
        },
        axisLabel: {
          color: '#B0B1B4',
        },
      },
      series: [
        {
          name: '全市发放总量',
          type: 'line',
          data: props.data.map((item: any) => item.total),
          smooth: true,
          itemStyle: {
            color: '#33BC71',
          },
          areaStyle: {
            color: new graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(86, 255, 168, 0.16)',
              },
              {
                offset: 1,
                color: 'rgba(148, 155, 253, 0)',
              },
            ]),
          },
        },
      ],
    });
  });
</script>

<template>
  <div class="flex flex-col h-full">
    <div class="title text-base font-bold">药品发放数量统计</div>
    <div ref="chartRef" class="flex-1 of-hidden"></div>
  </div>
</template>
