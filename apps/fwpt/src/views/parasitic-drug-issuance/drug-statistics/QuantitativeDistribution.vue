<script setup lang="ts">
  import { ref, watchEffect } from 'vue';
  import { useECharts } from '@ft/internal/hooks/web/useECharts';

  /**
   * 药品发放数量分布
   */
  const props = defineProps({
    data: {
      type: Array,
      default: () => [],
    },
  });

  const chartRef = ref<HTMLDivElement>();
  const { setOptions } = useECharts(chartRef as any);
  const getSeriesData = (input) => {
    const drugsMap = input.reduce((acc, hospital) => {
      hospital?.chartVOList.forEach(({ x, yvalue }) => {
        acc[x] ||= {
          name: x,
          data: [],
          type: 'bar',
          barWidth: 6,
          itemStyle: {
            borderRadius: [5, 5, 0, 0],
          },
        };
        acc[x].data.push(yvalue);
      });
      return acc;
    }, {});
    return Object.values(drugsMap);
  };

  watchEffect(() => {
    const xAxisData = props.data?.map((item: any) => item.prescCreateHospital);
    const seriesData: any = getSeriesData(props.data);
    const legendData = seriesData?.map((item: any) => item.name);
    setOptions({
      tooltip: {
        trigger: 'axis',
        appendToBody: true,
      },
      grid: {
        left: '8%',
        right: '8%',
        bottom: '10%',
      },
      legend: {
        data: legendData,
      },
      xAxis: {
        data: xAxisData,
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#B0B1B4',
        },
        axisLine: {
          lineStyle: {
            color: '#EDEEF0',
          },
        },
      },
      yAxis: {
        splitLine: {
          lineStyle: {
            color: '#EDEEF0',
          },
          interval: 30,
        },
        axisLabel: {
          color: '#B0B1B4',
        },
      },
      series: seriesData || [],
    });
  });
</script>

<template>
  <div class="flex flex-col h-full">
    <div class="title text-base font-bold">药品发放数量分布</div>
    <div ref="chartRef" class="flex-1 of-hidden"></div>
  </div>
</template>
