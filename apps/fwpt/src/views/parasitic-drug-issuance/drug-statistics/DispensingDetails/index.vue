<script setup lang="ts">
  import { Button } from '@ft/internal/components/Button';
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { nextTick, onMounted } from 'vue';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import { SearchSchemas, columns } from './data';
  import { getStatisticsList, statisticsListExport } from '/@/api/drug-statistics';

  const prescCreateHospital = useRouteQuery('prescCreateHospital', '', { transform: String });
  // const mainDiagName = useRouteQuery('mainDiagName', '', { transform: String });
  const queryTimeStart = useRouteQuery('queryTimeStart', '', { transform: String });
  const queryTimeEnd = useRouteQuery('queryTimeEnd', '', { transform: String });

  onMounted(() => {
    const values: Recordable = {
      prescCreateHospital: prescCreateHospital.value,
      // mainDiagName: mainDiagName.value,
    };
    if (queryTimeStart.value && queryTimeEnd.value) {
      values.queryTime = [queryTimeStart.value, queryTimeEnd.value];
    }
    ActionTable.getForm().setFieldsValue(values);
    nextTick(() => {
      ActionTable.getForm().submit();
    });
  });

  const [registerTable, ActionTable] = useTable({
    api: getStatisticsList,
    useSearchForm: true,
    formConfig: {
      labelWidth: 80,
      schemas: SearchSchemas,
      submitFunc: async () => {
        ActionTable.reload();
      },
      fieldMapToTime: [['queryTime', ['dispensingTimeStart', 'dispensingTimeEnd'], 'YYYY-MM-DD']],
    },
    beforeFetch: (params) => {
      params.prescCreateHospital = prescCreateHospital.value;
      // params.mainDiagName = mainDiagName.value;
      return params;
    },
    columns,
    inset: true,
    immediate: false,
  });
  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(
    statisticsListExport,
    {
      manual: true,
      showSuccessMessage: true,
    },
  );
  function onExportExpert() {
    exportUtil(
      exportExpertRunAsync({
        ...ActionTable.getForm().getFieldsValue(),
      }),
    );
  }
</script>

<template>
  <div class="DispensingDetails h-[calc(100%-16px)] w-[calc(100%-16px)] rounded rounded-lt-none">
    <BasicTable @register="registerTable">
      <template #headerTop>
        <div class="text-base font-bold">发药记录详情</div>
      </template>
      <template #toolbar>
        <Button :loading="exportLoading" @click="onExportExpert"> 批量导出 </Button>
      </template>
    </BasicTable>
  </div>
</template>
<style lang="less" scoped>
  .DispensingDetails {
    :deep {
      .vben-basic-table-form-container {
        padding: 0;
      }

      .vben-basic-table .ant-table-wrapper {
        padding: 16px;
      }

      .vben-basic-table-form-container .ant-form,
      .vben-basic-table .ant-table-wrapper {
        border-radius: 4px;
      }

      .vben-basic-table-form-container .ant-form {
        border-top-left-radius: 0;
        padding: 16px !important;
        margin-bottom: 10px !important;
      }
    }
  }
</style>
