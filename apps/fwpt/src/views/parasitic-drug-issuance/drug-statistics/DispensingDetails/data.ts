import type { FormSchema } from '@ft/internal/components/Form';
import type { BasicColumn } from '@ft/internal/components/Table';

/**
 * 发药时间：
 * 开方机构：
 * 主要诊断：
 * 患者姓名：
 */
export const SearchSchemas: FormSchema[] = [
  {
    field: 'queryTime',
    label: '发药时间',
    component: 'RangePicker',
    componentProps: {
      style: { width: '100%' },
    },
    colProps: { span: 6 },
  },
  {
    field: 'prescCreateHospital',
    label: '开方机构',
    component: 'Input',
    componentProps: {},
    colProps: { span: 6 },
  },
  {
    field: 'mainDiagName',
    label: '主要诊断',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'patientName',
    label: '患者姓名',
    component: 'Input',
    colProps: { span: 6 },
  },
];

/**
 * 发药时间
 * 药品编码
 * 药品名称
 * 发药数量
 * 患者姓名
 * 患者就诊号
 * 患者性别
 * 患者年龄
 * 患者身份证号
 * 患者联系方式
 * 患者科室
 * 发药人
 * 开单医生
 * 处方开具医院
 * 处方开具时间
 */
export const columns: BasicColumn[] = [
  {
    title: '发药时间',
    dataIndex: 'dispensingTime',
    width: 150,
  },
  {
    title: '药品编码',
    dataIndex: 'drugCode',
    width: 120,
  },
  {
    title: '药品名称',
    dataIndex: 'drugName',
    width: 120,
  },
  {
    title: '发药数量',
    dataIndex: 'quantity',
    width: 120,
  },
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    width: 120,
  },
  {
    title: '患者就诊号',
    dataIndex: 'patientVisitNum',
    width: 120,
  },
  {
    title: '患者性别',
    dataIndex: 'patientSexValue',
    width: 120,
  },
  {
    title: '患者年龄',
    dataIndex: 'patientAge',
    width: 120,
  },
  {
    title: '患者身份证号',
    dataIndex: 'patientIdCard',
    width: 120,
  },
  {
    title: '患者联系方式',
    dataIndex: 'patientPhone',
    width: 120,
  },
  {
    title: '患者科室',
    dataIndex: 'patientDeptName',
    width: 120,
  },
  {
    title: '发药人',
    dataIndex: 'dispenser',
    width: 120,
  },
  {
    title: '开单医生',
    dataIndex: 'prescCreateDoc',
    width: 120,
  },
  {
    title: '处方开具医院',
    dataIndex: 'prescCreateHospital',
    width: 120,
  },
  {
    title: '处方开具时间',
    dataIndex: 'prescCreateTime',
    width: 120,
  },
];
