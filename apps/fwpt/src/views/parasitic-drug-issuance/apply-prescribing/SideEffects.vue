<script setup lang="ts">
  import { reactive, watch } from 'vue';
  import { getSimpleDetail } from '/@/api/rational-drug-use-rules';
  import { Alert } from 'ant-design-vue';

  const props = defineProps<{
    drugId?: string;
  }>();

  async function getDrugEffect(drugId: string) {
    getSimpleDetail(drugId).then((res) => {
      sideEffects.sideEffectsCommon = res?.sideEffectsCommon;
      sideEffects.sideEffectsUncommon = res?.sideEffectsUncommon;
      sideEffects.sideEffectsRareSerious = res?.sideEffectsRareSerious;
    });
  }

  watch(
    () => props.drugId,
    (drugId) => {
      if (!drugId) {
        return;
      }
      getDrugEffect(drugId);
    },
    { immediate: true },
  );

  const sideEffects = reactive({
    sideEffectsCommon: '',
    sideEffectsUncommon: '',
    sideEffectsRareSerious: '',
  });
</script>
<template>
  <div>
    <Alert
      v-if="
        sideEffects?.sideEffectsCommon ||
        sideEffects?.sideEffectsRareSerious ||
        sideEffects?.sideEffectsUncommon
      "
      type="warning"
      message="副作用提醒"
      class="max-h-145px overflow-y-auto"
    >
      <template #description>
        <div v-if="sideEffects.sideEffectsCommon" class="flex">
          <label>常见：</label>
          <div class="flex-1 whitespace-pre-wrap">
            {{ sideEffects?.sideEffectsCommon }}
          </div>
        </div>
        <div v-if="sideEffects?.sideEffectsRareSerious" class="flex">
          <label>罕见但严重：</label>
          <div class="flex-1 whitespace-pre-wrap">
            {{ sideEffects?.sideEffectsRareSerious }}
          </div>
        </div>
        <div v-if="sideEffects?.sideEffectsUncommon" class="flex">
          <label>少见：</label>
          <div class="flex-1 whitespace-pre-wrap">
            {{ sideEffects?.sideEffectsUncommon }}
          </div>
        </div>
      </template>
    </Alert>
  </div>
</template>
