import { DictEnum, getDictItemList } from '@ft/internal/api';
import type { FormSchema } from '@ft/internal/components/Form';
import type { BasicColumn } from '@ft/internal/components/Table';

/**
 * 开具日期：
 * 开方医院：
 * 开方医生：
 * 主要诊断：
 * 患者姓名：
 */
export const SearchSchemas: FormSchema[] = [
  {
    field: 'prescriptionDate',
    label: '开具日期',
    component: 'RangePicker',
    componentProps: {
      style: { width: '100%' },
    },
    colProps: { span: 6 },
  },
  {
    field: 'mainDiagCode',
    label: '主要诊断',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.MAIN_DIAG_CODE),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
      optionFilterProp: 'label',
    },
    colProps: { span: 6 },
  },
  {
    field: 'patientName',
    label: '患者姓名',
    component: 'Input',
    colProps: { span: 6 },
  },
];

/**
 * 处方ID
 * 处方开具时间
 * 患者姓名
 * 患者性别
 * 患者年龄
 * 患者身份证号
 * 患者联系方式
 * 主要诊断
 * 发药状态
 * 发药人
 * 发药时间
 * 处方开具医生
 * 处方开具医院
 */
export const columns: BasicColumn[] = [
  {
    title: '处方编号',
    dataIndex: 'prescriptionNo',
    width: 190,
  },
  {
    title: '处方开具时间',
    dataIndex: 'prescCreateTime',
    width: 160,
  },
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    width: 120,
  },
  {
    title: '患者性别',
    dataIndex: 'patientSexValue',
    width: 80,
  },
  {
    title: '患者年龄',
    dataIndex: 'patientAge',
    width: 80,
  },
  {
    title: '患者身份证号',
    dataIndex: 'patientIdCard',
    width: 180,
  },
  {
    title: '患者联系方式',
    dataIndex: 'patientPhone',
    width: 140,
  },
  {
    title: '主要诊断',
    dataIndex: 'mainDiagName',
    width: 120,
  },
  {
    title: '发药状态',
    dataIndex: 'prescriptionStatuDesc',
    width: 80,
  },
  {
    title: '发药人',
    dataIndex: 'dispenser',
    width: 120,
  },
  {
    title: '发药时间',
    dataIndex: 'dispensingTime',
    width: 120,
  },
  {
    title: '处方开具医生',
    dataIndex: 'prescCreateDoc',
    width: 120,
  },
  {
    title: '处方开具医院',
    dataIndex: 'prescCreateHospital',
    width: 180,
  },
];

/**
 * 处方项目
 * 用量
 * 规格
 * 用法
 * 频次
 * 天数
 * 总量
 */
export const DetailColumns: BasicColumn[] = [
  {
    title: '处方项目',
    dataIndex: 'drugName',
    width: 120,
  },
  {
    title: '用量',
    dataIndex: 'dosage',
    width: 80,
  },
  {
    title: '规格',
    dataIndex: 'specs',
    width: 80,
  },
  {
    title: '用法',
    dataIndex: 'usage',
    width: 80,
  },
  {
    title: '频次',
    dataIndex: 'frequency',
    width: 80,
  },
  {
    title: '天数',
    dataIndex: 'days',
    width: 80,
  },
  {
    title: '总量',
    dataIndex: 'total',
    width: 80,
  },
];
