<script setup lang="ts">
  import { Button } from '@ft/internal/components/Button';
  import { onMounted, reactive, ref } from 'vue';
  import print from 'vue3-print-nb';
  import { type IPrescriptionPost, detailPrescription } from '/@/api/drug-dispensing';

  const props = defineProps<{
    prescriptionId: string;
  }>();
  defineOptions({
    directives: {
      print,
    },
  });

  const prescriptionDetail = ref<IPrescriptionPost>();

  onMounted(() => {
    detailPrescription(props.prescriptionId).then((res) => {
      prescriptionDetail.value = res;
    });
  });

  const printObj = reactive<any>({
    id: 'print-dom',
  });
</script>

<template>
  <div class="h-[calc(100%-16px)] w-[calc(100%-16px)] bg-white rounded p-10 flex justify-center">
    <Button
      v-print="printObj"
      class="!absolute !top-5 !right-20 w-45px"
      pre-icon="carbon:printer"
      :icon-size="18"
      type="link"
    >
      打印
    </Button>
    <div id="print-dom" class="max-w-210mm min-w-180mm text-base prescribing-print">
      <div class="header relative">
        <div class="top flex items-center justify-center">
          <img class="h-80px" src="/@/assets/images/hospital-logo.png" alt="" />
        </div>
        <div class="title text-center text-24px">处方笺</div>
        <div class="absolute top-3 right-0">
          <div class="text-2xl font-bold">普通</div>
          <div>处方/ID号:</div>
          <div>{{ prescriptionDetail?.prescriptionNo }}</div>
        </div>
      </div>
      <div class="body">
        <div class="basic-info border-b-1 flex justify-between pb-4 pt-4 mb-4 whitespace-nowrap">
          <div class="flex">
            <span>姓名：</span>
            <span class="inline-flex min-w-3em">{{ prescriptionDetail?.patientName }}</span>
          </div>
          <div class="flex">
            <span>性别：</span>
            <span class="inline-flex min-w-3em">
              {{ prescriptionDetail?.patientSexValue }}
            </span>
          </div>
          <div class="flex">
            <span>年龄：</span>
            <span class="inline-flex min-w-2em">
              {{ prescriptionDetail?.patientAge }}
            </span>
          </div>
          <div class="flex">
            <span>科室：</span>
            <span class="inline-flex min-w-6em">
              {{ prescriptionDetail?.patientDeptName }}
            </span>
          </div>
        </div>
        <div class="diagnosis border-b-1 flex gap-2">
          <div class="title min-w-2em whitespace-nowrap">诊断：</div>
          <div class="content min-h-3em">
            {{ prescriptionDetail?.mainDiagName }}
          </div>
        </div>
        <div class="drug py-4 min-h-3em flex">
          <div class="flex-1 of-hidden">
            <table
              v-for="(drug, idx) in prescriptionDetail?.drugDetailList || []"
              :key="idx"
              class="w-full inline-block"
            >
              <tr>
                <td>
                  <span>{{ drug?.drugName }}[{{ drug?.specs }}] </span>
                  <span>{{ drug?.total }}{{ drug?.dosageUnit }}</span>
                  <br />
                  <span class="mr-2">{{ drug?.dayNum }}天</span>
                  <span class="mr-2">每日{{ drug?.frequency }}次</span>
                  <span class="mr-2">每次{{ drug?.dosage }}{{ drug?.dosageUnit }}</span>
                  <span>{{ drug?.drugUsageName }}</span>
                  <br />
                  <span v-if="drug?.note">禁忌：{{ drug?.note || '-' }}</span>
                </td>
              </tr>
            </table>
          </div>
          <div class="text-xl flex flex-col gap-2 justify-center">
            <span>当</span>
            <span>日</span>
            <span>有</span>
            <span>效</span>
          </div>
        </div>
        <div class="footer flex justify-end">
          <div class="text-right flex items-center">
            <span>医师：</span>
            <span class="inline-flex min-w-4em">
              <img
                class="w-80px"
                v-if="prescriptionDetail?.doctorSignImg"
                :src="prescriptionDetail?.doctorSignImg"
              />
            </span>
            <span class="ml-4">处方日期：</span>
            <span class="inline-block w-142px">
              {{ prescriptionDetail?.prescCreateTime }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less">
  @media print {
    html,
    body {
      width: 100%;
      margin: 0;
      padding-top: 6mm;
      overflow: initial !important;
    }

    .prescribing-print {
      @page {
        size: auto;
        margin: 0;
      }

      font-size: 16px;
      margin: 0 auto;

      .header {
        margin-left: 10mm;
        margin-right: 10mm;
      }

      .body {
        padding-left: 10mm;
        padding-right: 10mm;
      }
    }
  }
</style>
