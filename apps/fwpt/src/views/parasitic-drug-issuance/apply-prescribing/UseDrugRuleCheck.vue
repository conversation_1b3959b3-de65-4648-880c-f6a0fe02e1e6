<script setup lang="ts">
  import type { ValueOf } from 'vxe-table';
  import { ref, watch } from 'vue';
  import { isEqual, isNil, isNumber } from 'lodash-es';
  import type { CheckType } from './types';
  import { checkByDrugId } from '/@/api/rational-drug-use-rules';

  const props = defineProps<{
    age: string | number;
    drugId?: string;
    checkType: ValueOf<typeof CheckType>;
    inputValue?: string | number;
  }>();

  const msg = ref('');

  watch(
    () => props,
    (val) => {
      if (!val.drugId) return;
      if (!isNumber(Number(val.age))) return;

      if (isNil(val.inputValue) || isEqual(val.inputValue, '')) {
        msg.value = '';
        return;
      }
      checkByDrugId({
        drugId: val.drugId,
        age: Number(val.age),
        checkType: val.checkType,
        inputValue: val.inputValue,
      }).then((data) => {
        msg.value = data;
      });
    },
    { immediate: true, deep: true },
  );
</script>

<template>
  <div class="text-yellow-500">
    {{ msg }}
  </div>
</template>
