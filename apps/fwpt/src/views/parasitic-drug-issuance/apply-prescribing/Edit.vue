<script setup lang="ts">
  import { FixedAction } from '@ft/components';
  import { BasicTitle } from '@ft/internal/components/Basic';
  import { Button } from '@ft/internal/components/Button';
  import ApiSelect from '@ft/internal/components/Form/src/components/ApiSelect.vue';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { useRoutePageMode } from '@ft/internal/hooks/web/useRoutePageMode';
  import {
    Col,
    Form,
    type FormInstance,
    FormItem,
    Input,
    InputNumber,
    Row,
    Select,
    message,
  } from 'ant-design-vue';
  import type { RuleObject } from 'ant-design-vue/lib/form';
  import { onMounted, ref } from 'vue';
  import {
    type IPrescriptionPost,
    addPrescription,
    detailPrescription,
    updatePrescription,
    verifySignData,
  } from '/@/api/drug-dispensing';
  import dayjs from 'dayjs';
  import { useUserStore } from '@ft/internal/store/modules/user';
  import type { IDrugInfo } from '/@/api/drug-management/dict';
  import { getDrugInfoList } from '/@/api/drug-management/dict';
  import { DictEnum, getDictItemList } from '@ft/internal/api';
  import { defHttp, useRequest } from '@ft/request';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { isNil } from 'lodash-es';
  import { useEventListener } from '@vueuse/core';
  import SideEffects from './SideEffects.vue';
  import UseDrugRuleCheck from './UseDrugRuleCheck.vue';
  import { getRandom } from '/@/api/cert';
  import { saveUserOperationLog } from '/@/api/issuing-records';

  const prescriptionId = useRouteQuery('prescriptionId', '', { transform: String });

  const { isEditMode } = useRoutePageMode();

  const userStore = useUserStore();
  const divRef = ref<HTMLDivElement | null>(null);
  const formRef = ref<FormInstance>();
  const random = ref('');

  /**
   * 患者姓名
   * 性别
   * 年龄
   * 科别
   * 诊断
   * 联系电话
   * 身份证号
   *
   * 药品信息
   *  药品名称
   *  剂量
   *  频次
   *  数量
   *  用法
   * 开方日期
   * 开方医师
   * 开方机构
   */

  const formState = ref<IPrescriptionPost>({
    id: undefined,
    patientName: '',
    patientSexValue: undefined,
    patientAge: '',
    patientDeptCode: '',
    patientDeptName: '',
    mainDiagCode: '',
    mainDiagName: '',
    patientPhone: '',
    patientIdCard: '',
    drugDetailList: [
      {
        id: undefined,
        drugCode: '',
        drugName: undefined,
        dosage: '',
        frequency: undefined,
        total: undefined,
        drugUsage: undefined,
        drugUsageName: '',
        specs: '',
        dosageUnit: '',
        dosageForm: '',
        note: '',
      },
    ],
    prescCreateTime: '',
    prescCreateDocId: '',
    prescCreateDoc: '',
    doctorSignImg: '',
    prescCreateHospitalCode: '',
    prescCreateHospital: '',
    prescriptionStatus: 0,
    signValue: '',
  });

  const formRules: { [k: string]: RuleObject[] } = {
    patientName: [{ required: true, message: '请输入患者姓名', trigger: 'change' }],
    patientSexValue: [{ required: true, message: '请选择患者性别', trigger: 'change' }],
    patientAge: [{ required: true, message: '请输入患者年龄', trigger: 'change' }],
    patientDeptName: [{ required: true, message: '请输入科别', trigger: 'change' }],
    mainDiagCode: [{ required: true, message: '请输入诊断', trigger: 'change' }],
  };

  function onAddDrug() {
    formState.value.drugDetailList &&
      formState.value.drugDetailList.push({
        id: undefined,
        drugCode: '',
        drugName: undefined,
        dosage: '',
        frequency: undefined,
        total: undefined,
        drugUsage: undefined,
        drugUsageName: '',
        specs: '',
        dosageUnit: '',
        dayNum: undefined,
        dosageForm: '',
        note: '',
      });
  }

  function setDefaultValues() {
    formState.value.patientDeptCode = userStore.getUserInfo.deptId;
    formState.value.patientDeptName = userStore.getUserInfo.deptName;
    formState.value.prescCreateTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
    formState.value.prescCreateDoc = userStore.getUserInfo.employeeName;
    formState.value.prescCreateDocId = userStore.getUserInfo.id;
    formState.value.prescCreateHospital = userStore.getUserInfo.orgName;
    formState.value.prescCreateHospitalCode = userStore.getUserInfo.orgId;
  }

  const { run: getPrescriptionDetail } = useRequest(detailPrescription, {
    manual: true,
    onSuccess: (data) => {
      formState.value = data;
    },
  });

  const go = useGo();
  function onBack() {
    go({
      name: 'ApplyPrescribing',
    });
  }

  // function onReset() {
  //   formRef.value?.resetFields();
  //   setDefaultValues();
  // }

  const { loading, run } = useRequest(
    isEditMode ? (p) => updatePrescription(p) : (p) => addPrescription(p),
    {
      manual: true,
      showSuccessMessage: true,

      onBefore() {
        return formRef?.value?.validate().then(async () => {
          if (formState.value.doctorSignImg) {
            return true;
          } else {
            if (!random.value) random.value = await getRandom();
            const width = 400;
            const height = 400;
            const screenLeft =
              window.screenLeft !== undefined ? window.screenLeft : (window.screen as any).left;
            const screenTop =
              window.screenTop !== undefined ? window.screenTop : (window.screen as any).top;
            const screenWidth = window.innerWidth
              ? window.innerWidth
              : document.documentElement.clientWidth;
            const screenHeight = window.innerHeight
              ? window.innerHeight
              : document.documentElement.clientHeight;

            // 计算居中位置的左、上坐标
            const left = screenLeft + (screenWidth - width) / 2;
            const top = screenTop + (screenHeight - height) / 2;
            const features = `width=${width},height=${height},top=${top},left=${left},scrollbars=no,menubar=no,toolbar=no,location=no,status=no,fullscreen=no`;
            const publicPath = import.meta.env.VITE_PUBLIC_PATH || '/';
            const pathPrefix = publicPath.endsWith('/') ? publicPath : publicPath + '/';
            window.open(
              `${pathPrefix}ukey/index.html#${random.value}@${userStore.getUserInfo.idCard}`,
              'ukey',
              features,
            );
            return false;
          }
        });
      },
      onSuccess: () => {
        onBack();
      },
    },
  );

  function onGenPrescription() {
    console.log('onGenPrescription');
    run(formState.value);
  }

  function onDrugChange(opt, item) {
    item.drugName = opt?.label;
    item.specs = opt?.specs;
    item.dosageUnit = opt?.mpqUnit;
    item.dosageForm = opt?.dosageForm;
    item.id = opt?.id;
    item.note = opt?.note;
  }

  function getUsedList() {
    return getDictItemList(DictEnum.DRUG_USAGE);
  }

  function getDiagOptions() {
    return getDictItemList(DictEnum.MAIN_DIAG_CODE);
  }

  async function _getDrugList() {
    const drugList = await getDrugInfoList({ enableFlag: 1 });
    return drugList.map((item) => ({ ...item, drugName: `${item.drugName}(${item.specs})` }));
  }

  function onCalcDrugTotal(item) {
    if (isNil(item.dosage) || isNil(item.frequency) || isNil(item.dayNum)) {
      item.total = undefined;
      return;
    }
    item.total = item.dosage * item.frequency * item.dayNum;
  }

  const drugOptions = ref<IDrugInfo[]>([]);

  async function getDrugOptions() {
    return drugOptions.value;
  }

  async function preloadDrugOptions() {
    const drugs = await _getDrugList();
    drugOptions.value = drugs;
  }

  onMounted(async () => {
    await preloadDrugOptions();
    setDefaultValues();
    isEditMode && getPrescriptionDetail(prescriptionId.value);
  });

  function getDrugId(drugCode?: string) {
    if (!drugCode) return undefined;
    return drugOptions.value?.find((item) => item.drugCode === drugCode)?.id;
  }
  function base64ToFile(base64String: string, filename, mimeType = 'image/png') {
    // 解码 Base64 数据
    const byteCharacters = atob(base64String);
    const byteNumbers = new Uint8Array(byteCharacters.length);

    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }

    // 创建 Blob 对象
    const blob = new Blob([byteNumbers], { type: mimeType });

    // 创建 File 对象
    const file = new File([blob], filename, { type: mimeType });

    return file;
  }

  async function signatureListener(event) {
    const eventData = event.data;
    if (eventData.type === 'ukey') {
      loading.value = true;
      try {
        const data = eventData.data;
        if (!data.base64Cert) {
          message.warning('获取证书信息失败');
          return;
        }
        try {
          await verifySignData({
            oriData: data.orgData,
            signValue: data.signValue,
            certId: data.certId,
            base64Cert: data.base64Cert,
          });
        } catch (error) {
          message.warning('验签失败');
          return;
        }
        try {
          await saveUserOperationLog({
            operationContent: '获取签名图片',
            operationTime: data.time,
            signature: data.signValue,
            username: data.name,
          });
        } catch (error) {
          message.warning('保存操作日志失败');
          return;
        }
        if (data.random === random.value) {
          const file = base64ToFile(data.picture, 'img.png');
          console.log(data, file, 'file');
          const res = await defHttp.uploadFile(
            {
              url: '/api/infection-sysmgt/sysUser/uploadSignImg',
            },
            {
              file,
              filename: file.name,
            },
          );
          const url = res.data?.data?.url;
          if (!url) {
            message.error('上传签名失败');
          } else {
            formState.value.doctorSignImg = url;
            formState.value.signValue = data.signValue;
            run(formState.value);
          }
        } else {
          message.warning('验证签名失败，请重试');
        }
      } catch (error: any) {
        console.log(error);
        message.warning(error.message);
      } finally {
        loading.value = false;
      }
    }
  }
  useEventListener('message', signatureListener);
</script>

<template>
  <div ref="divRef" class="h-[calc(100%-16px)] w-[calc(100%-16px)] bg-white rounded">
    <div class="h-full w-full flex flex-col">
      <div class="flex p-4 items-center justify-between border-b border-[#EDEEF0]">
        <span class="text-base font-bold"> {{ isEditMode ? '编辑' : '新增' }}处方 </span>
      </div>
      <div class="page-body p-4 flex-1 basis-0 min-h-0 of-y-auto pb-20">
        <Form
          ref="formRef"
          :model="formState"
          :rules="formRules"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
        >
          <BasicTitle class="mb-4" normal span>患者基本信息</BasicTitle>
          <FormItem no-style v-show="false" label="id" name="id" />
          <FormItem no-style v-show="false" label="doctorSignImg" name="doctorSignImg" />
          <Row>
            <Col span="6">
              <FormItem label="患者姓名" name="patientName">
                <Input v-model:value="formState.patientName" placeholder="请输入" />
              </FormItem>
            </Col>
            <Col span="6">
              <FormItem label="性别" name="patientSexValue">
                <Select v-model:value="formState.patientSexValue" placeholder="请选择">
                  <Select.Option value="男">男</Select.Option>
                  <Select.Option value="女">女</Select.Option>
                </Select>
              </FormItem>
            </Col>
            <Col span="6">
              <FormItem label="年龄" name="patientAge">
                <InputNumber v-model:value="formState.patientAge" placeholder="请输入" />
              </FormItem>
            </Col>
            <Col span="6">
              <FormItem no-style v-show="false" label="patientDeptCode" name="patientDeptCode" />
              <FormItem label="科别" name="patientDeptName">
                <Input disabled v-model:value="formState.patientDeptName" placeholder="请输入" />
              </FormItem>
            </Col>
            <Col span="6">
              <FormItem no-style v-show="false" label="mainDiagName" name="mainDiagName" />
              <FormItem label="诊断" name="mainDiagCode">
                <ApiSelect
                  :api="getDiagOptions"
                  label-field="dictItemName"
                  value-field="dictItemCode"
                  option-filter-prop="label"
                  show-search
                  v-model:value="formState.mainDiagCode"
                  placeholder="请输入"
                  @change="(_, opt) => (formState.mainDiagName = opt?.label)"
                />
              </FormItem>
            </Col>
            <Col span="6">
              <FormItem label="联系电话" name="patientPhone">
                <Input v-model:value="formState.patientPhone" placeholder="请输入" />
              </FormItem>
            </Col>
            <Col span="6">
              <FormItem label="身份证号" name="patientIdCard">
                <Input v-model:value="formState.patientIdCard" placeholder="请输入" />
              </FormItem>
            </Col>
          </Row>

          <BasicTitle class="mb-4" normal span>药品信息</BasicTitle>

          <Row
            class="border-1 border-dashed border-[#E7E7E7] pt-6 mb-4"
            v-for="(item, index) in formState.drugDetailList"
            :key="index"
          >
            <Col span="16" class="px-2">
              <Row>
                <Col span="12">
                  <FormItem no-style v-show="false" label="prescriptionId" name="prescriptionId" />
                  <FormItem no-style v-show="false" label="drugName" name="drugName" />
                  <FormItem no-style v-show="false" label="dosageForm" name="dosageForm" />
                  <FormItem label="药品名称" required :name="['drugDetailList', index, 'drugCode']">
                    <ApiSelect
                      :api="getDrugOptions"
                      v-model:value="item.drugCode"
                      labelField="drugName"
                      valueField="drugCode"
                      @change="(_, opt) => onDrugChange(opt, item)"
                      showSearch
                      optionFilterProp="label"
                      placeholder="请选择"
                    />
                  </FormItem>
                </Col>
                <Col span="12">
                  <FormItem label="剂量" required :name="['drugDetailList', index, 'dosage']">
                    <div class="flex gap-2 items-center">
                      <InputNumber
                        :min="0"
                        v-model:value="item.dosage"
                        placeholder="请输入"
                        @change="onCalcDrugTotal(item)"
                      />
                      <span class="whitespace-nowrap">{{ item.dosageUnit }}</span>
                    </div>
                    <UseDrugRuleCheck
                      :drug-id="getDrugId(item.drugCode)"
                      :age="formState.patientAge"
                      :check-type="1"
                      :input-value="item.dosage"
                    />
                  </FormItem>
                </Col>
                <Col span="12">
                  <FormItem label="频次" required :name="['drugDetailList', index, 'frequency']">
                    <div class="flex gap-2 items-center">
                      <InputNumber
                        :min="0"
                        v-model:value="item.frequency"
                        placeholder="请输入"
                        @change="onCalcDrugTotal(item)"
                      />
                      <span class="whitespace-nowrap">次/每日</span>
                    </div>
                    <UseDrugRuleCheck
                      :drug-id="getDrugId(item.drugCode)"
                      :age="formState.patientAge"
                      :check-type="2"
                      :input-value="item.frequency"
                    />
                  </FormItem>
                </Col>
                <Col span="12">
                  <FormItem label="天数" required :name="['drugDetailList', index, 'dayNum']">
                    <InputNumber
                      :min="0"
                      v-model:value="item.dayNum"
                      placeholder="请输入"
                      @change="onCalcDrugTotal(item)"
                    />
                    <UseDrugRuleCheck
                      :drug-id="getDrugId(item.drugCode)"
                      :age="formState.patientAge"
                      :check-type="3"
                      :input-value="item.dayNum"
                    />
                  </FormItem>
                </Col>
                <Col span="12">
                  <FormItem label="数量" :name="['drugDetailList', index, 'total']">
                    <div class="flex gap-2 items-center">
                      <InputNumber disabled :min="0" :value="item.total" placeholder="请输入" />
                      <span class="whitespace-nowrap">{{ item.dosageUnit }}</span>
                    </div>
                  </FormItem>
                </Col>

                <Col span="12">
                  <FormItem no-style v-show="false" label="drugUsageName" name="drugUsageName" />
                  <FormItem label="用法" required :name="['drugDetailList', index, 'drugUsage']">
                    <ApiSelect
                      :api="getUsedList"
                      label-field="dictItemName"
                      value-field="dictItemCode"
                      v-model:value="item.drugUsage"
                      placeholder="请选择"
                      @change="(_, opt) => (item.drugUsageName = opt?.label)"
                    />
                    <UseDrugRuleCheck
                      :drug-id="getDrugId(item.drugCode)"
                      :age="formState.patientAge"
                      :check-type="4"
                      :input-value="item.drugUsage"
                    />
                  </FormItem>
                </Col>
                <Col span="24">
                  <FormItem
                    :label-col="{ span: 3 }"
                    label="禁忌"
                    :name="['drugDetailList', index, 'note']"
                  >
                    <Input.TextArea
                      :autoSize="{ minRows: 3, maxRows: 3 }"
                      v-model:value="item.note"
                      placeholder="请输入"
                    />
                  </FormItem>
                </Col>
              </Row>
            </Col>

            <Col span="8" class="pr-4 pb-4">
              <SideEffects :drug-id="getDrugId(item.drugCode)" />
            </Col>
            <Col span="24">
              <div class="flex justify-start">
                <Button
                  v-if="index > 0"
                  pre-icon="ant-design:delete-outlined"
                  type="link"
                  danger
                  @click="() => formState.drugDetailList.splice(index, 1)"
                >
                  移除
                </Button>
              </div>
            </Col>
          </Row>
          <div class="mt-4">
            <Button pre-icon="ant-design:plus-circle-outlined" type="link" @click="onAddDrug">
              添加药品
            </Button>
          </div>
          <BasicTitle class="mb-4" normal span>处方信息</BasicTitle>
          <Row class="mt-4">
            <Col span="6">
              <FormItem label="开方日期" name="prescCreateTime">
                <Input disabled v-model:value="formState.prescCreateTime" placeholder="请输入" />
              </FormItem>
            </Col>
            <Col span="6">
              <FormItem no-style v-show="false" label="prescCreateDocId" name="prescCreateDocId" />
              <FormItem label="开方医师" name="prescCreateDoc">
                <Input disabled v-model:value="formState.prescCreateDoc" placeholder="请输入" />
              </FormItem>
            </Col>
            <Col span="6">
              <FormItem
                no-style
                v-show="false"
                label="prescCreateHospitalCode"
                name="prescCreateHospitalCode"
              />

              <FormItem label="开方机构" name="prescCreateHospital">
                <Input
                  disabled
                  v-model:value="formState.prescCreateHospital"
                  placeholder="请输入"
                />
              </FormItem>
            </Col>
          </Row>
        </Form>
      </div>
    </div>
    <FixedAction class="justify-end pr-4" :reference-el="divRef">
      <Button @click="onBack"> 返回 </Button>
      <!-- <Button type="primary" ghost @click="onReset" v-if="!isEditMode">重置</Button> -->
      <Button type="primary" :loading="loading" @click="onGenPrescription">
        {{ isEditMode ? '更新处方' : '生成处方' }}
      </Button>
    </FixedAction>
  </div>
</template>
