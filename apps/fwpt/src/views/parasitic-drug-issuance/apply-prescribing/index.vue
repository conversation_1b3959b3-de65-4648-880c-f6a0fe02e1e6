<script setup lang="ts">
  import { Button } from '@ft/internal/components/Button';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Tabs, Tooltip } from 'ant-design-vue';
  import { computed, ref } from 'vue';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { useUserStore } from '@ft/internal/store/modules/user';
  import { SearchSchemas, columns } from './data';
  import type { IPrescription } from '/@/api/drug-dispensing';
  import {
    getPrescriptionPageByDoctor,
    prescriptionExport,
    removePrescription,
  } from '/@/api/drug-dispensing';

  const activeKey = ref<any>(undefined);

  const userStore = useUserStore();
  const getAddDisabled = computed(() => !userStore.getUserInfo.userSignImg);

  const [registerTable, ActionTable] = useTable({
    api: getPrescriptionPageByDoctor,
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas: SearchSchemas,
      fieldMapToTime: [
        ['prescriptionDate', ['prescCreateTimeStart', 'prescCreateTimeEnd'], 'YYYY-MM-DD'],
      ],
    },
    columns,
    actionColumn: {
      title: '操作',
      dataIndex: 'action',
      width: 160,
    },
    beforeFetch: (params) => {
      params.prescriptionStatus = activeKey.value;
      return params;
    },
  });
  const go = useGo();

  function onPrescriptionDetail(record: IPrescription) {
    go({
      name: 'ApplyPrescribingDetail',
      query: {
        prescriptionId: record.id,
      },
    });
  }

  function onEdit(record: IPrescription) {
    go({
      name: 'ApplyPrescribingEdit',
      query: {
        prescriptionId: record.id,
        mode: 'edit',
      },
    });
  }

  const { run: removePrescriptionRun } = useRequest(removePrescription, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      ActionTable.reload();
    },
  });

  function createActions(record: IPrescription, _column): ActionItem[] {
    let actions: ActionItem[] = [
      {
        label: '处方详情',
        onClick: onPrescriptionDetail.bind(null, record),
      },
    ];
    if (record.prescriptionStatus === 0) {
      actions = actions.concat([
        {
          label: '编辑',
          onClick: onEdit.bind(null, record),
        },
        {
          label: '删除',
          danger: true,
          popConfirm: {
            title: '确定删除吗？',
            placement: 'topRight',
            okButtonProps: {
              danger: true,
            },
            confirm: () => removePrescriptionRun(record.id),
          },
        },
      ]);
    }

    return actions;
  }
  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(
    prescriptionExport,
    {
      manual: true,
      showSuccessMessage: true,
    },
  );
  function onExportExpert() {
    exportUtil(
      exportExpertRunAsync({
        ...ActionTable.getForm().getFieldsValue(),
        prescriptionStatus: activeKey.value,
      }),
    );
  }

  function onAdd() {
    go({
      name: 'ApplyPrescribingEdit',
    });
  }
</script>

<template>
  <div class="h-[calc(100%-10px)] w-[calc(100%-16px)] rounded rounded-lt-none">
    <BasicTable @register="registerTable">
      <template #headerTop>
        <div class="text-base font-bold">处方记录列表</div>
        <div>
          <Tabs v-model:activeKey="activeKey" @change="() => ActionTable.reload()">
            <Tabs.TabPane :key="undefined" tab="全部" />
            <Tabs.TabPane :key="0" tab="待发药" />
            <Tabs.TabPane :key="1" tab="已发药" />
          </Tabs>
        </div>
      </template>
      <template #tableTitle>
        <Tooltip :visible="getAddDisabled ? undefined : false">
          <template #title>未上传签名，无法开方</template>
          <Button type="primary" :loading="exportLoading" @click="onAdd"> 在线开方 </Button>
        </Tooltip>
      </template>
      <template #toolbar>
        <Button :loading="exportLoading" @click="onExportExpert"> 批量导出 </Button>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }

    .vben-basic-table-form-container .ant-form,
    .vben-basic-table .ant-table-wrapper {
      border-radius: 4px;
    }

    .vben-basic-table-form-container .ant-form {
      border-top-left-radius: 0;
      margin-bottom: 10px;
    }
  }
</style>
