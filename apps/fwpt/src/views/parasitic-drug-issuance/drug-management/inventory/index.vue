<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { Dropdown, Input } from 'ant-design-vue';
  import { Icon } from '@ft/internal/components/Icon';

  import { useModal } from '@ft/internal/components/Modal';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import { StyledList } from '@ft/components';
  import { useRequest } from '@ft/request';
  import { isNil } from 'lodash-es';
  import type { TreeDropMenuItem } from './comp/DropMenu.vue';
  import TreeDropMenu from './comp/DropMenu.vue';
  import DateList from './comp/DateList.vue';
  import type { IDrugCatalogue } from '/@/api/drug-management/inventory';
  import {
    changeEnableFlag,
    delDrugCatalogue,
    getDrugCatalogueList,
  } from '/@/api/drug-management/inventory';
  import AddTypeModal from './comp/AddTypeModal.vue';

  const { createConfirm } = useMessage();
  const searchValue = ref('');

  const activeId = ref<string>();
  const activeItem = ref();
  const {
    loading,
    data: drugCatalogueList,
    runAsync: getDrugCatalogueListAsync,
  } = useRequest(getDrugCatalogueList, {
    onSuccess: (data) => {
      if (data.length > 0) {
        // 如果列表中存在启用状态的目录，则默认选中第一个启用状态的目录
        const enableFlag = data.find((item) => item.enableFlag === 1);
        activeId.value = enableFlag?.id || data[0].id;
        activeItem.value = enableFlag || data?.[0] || {};
      }
    },
  });
  const items = computed(() => {
    // 如果enableFlag为 null，则默认设置为1,兼容以前数据
    return drugCatalogueList.value
      ?.map((item) => ({ ...item, enableFlag: isNil(item.enableFlag) ? 1 : item.enableFlag }))
      ?.filter((item) => {
        return item.catalogName?.includes(searchValue.value);
      });
  });
  const { runAsync: changeEnableFlagRunAsync } = useRequest(changeEnableFlag, {
    showSuccessMessage: true,
    manual: true,
    onSuccess: () => {
      getDrugCatalogueListAsync();
    },
  });

  function handleChangeEnableFlag(item: IDrugCatalogue) {
    console.log(item);
    createConfirm({
      iconType: 'warning',
      title: '状态切换',
      content: `确定${item.enableFlag === 0 ? '启用' : '禁用'}该目录吗？`,
      onOk: () => {
        changeEnableFlagRunAsync({ id: item.id, enableFlag: item.enableFlag === 0 ? 1 : 0 });
      },
    });
  }

  function createMenu(item: IDrugCatalogue): TreeDropMenuItem[] {
    return [
      {
        label: item.enableFlag === 0 ? '启用' : '禁用',
        icon:
          item.enableFlag === 0
            ? 'ant-design:check-circle-outlined'
            : 'ant-design:close-circle-outlined',
        onClick: handleChangeEnableFlag.bind(null, item),
      },
      {
        label: '编辑',
        icon: 'ant-design:edit-outlined',
        onClick: handleEdit.bind(null, item),
      },
      {
        label: '删除',
        icon: 'ant-design:delete-outlined',
        onClick: handleDel.bind(null, item),
      },
    ];
  }

  const [registerAddTypeModal, { openModal: openAddTypeModal }] = useModal();

  function handleAddType() {
    openAddTypeModal(true, {
      mode: 'add',
    });
  }
  function handleEdit(item: IDrugCatalogue) {
    openAddTypeModal(true, {
      mode: 'edit',
      data: {
        ...item,
      },
    });
  }
  const { runAsync: delTypeListRunAsync } = useRequest(delDrugCatalogue, {
    showSuccessMessage: true,
    manual: true,
    onSuccess: () => {
      getDrugCatalogueListAsync();
    },
  });

  function handleDel(item: IDrugCatalogue) {
    createConfirm({
      iconType: 'warning',
      title: '删除目录',
      content: `确定删除该目录吗？`,
      onOk: () => {
        console.log(item);
        delTypeListRunAsync(item.id);
      },
    });
  }
</script>

<template>
  <div class="flex gap-2 h-[calc(100%-10px)] w-[calc(100%-10px)] rounded rounded-lt-none">
    <div class="flex flex-col gap-3 w-228px bg-#fff rounded-[0px_8px_8px_8px] p-3">
      <span class="text-#333333 fw-bold">寄生虫病药品目录</span>
      <div class="search flex items-center text-center gap-9px">
        <Input allow-clear placeholder="输入关键字" v-model:value="searchValue">
          <template #suffix>
            <Icon icon="ant-design:search-outlined" />
          </template>
        </Input>
        <div
          @click="handleAddType"
          class="w-20px h-20px rounded-50% bg-primary-color cursor-pointer p-1 flex items-center"
        >
          <Icon color="#fff" icon="ant-design:plus-outlined" size="12" />
        </div>
      </div>
      <div class="pr-4 of-y-auto of-x-hidden min-w-0">
        <StyledList
          :items="items || []"
          v-model="activeId"
          v-model:value="activeItem"
          valueField="id"
          class="flex-1"
          :width="216"
          :loading="loading"
        >
          <template #default="item">
            <div class="flex justify-between items-center w-full relative">
              <div
                class="w-[calc(100%-20px)] overflow-hidden truncate"
                :class="{
                  'text-#999999': item?.enableFlag === 0,
                }"
              >
                {{ item?.catalogName }}
              </div>
              <Dropdown placement="bottom" :class="[activeId === item?.id ? '!block' : '!hidden']">
                <div
                  class="hover:bg-#DAEBE2 absolute right-10px rounded-2px flex items-center px-4px py-2px pt-5px"
                >
                  <Icon @click.prevent color="#303133" :size="18" icon="ant-design:more-outlined" />
                </div>
                <template #overlay>
                  <div
                    class="bg-#fff color-#333 w-fit min-w-80px rounded-3px text-left cursor-pointer shadow-md"
                  >
                    <TreeDropMenu :createMenu="createMenu(item)" />
                  </div>
                </template>
              </Dropdown>
            </div>
          </template>
        </StyledList>
      </div>
    </div>
    <div class="flex flex-col gap-2 flex-1 min-w-757px rounded-2 bg-white p-4">
      <DateList ref="dateListRef" :activeItem="activeItem" />
      <AddTypeModal @register="registerAddTypeModal" @success="getDrugCatalogueListAsync" />
    </div>
  </div>
</template>

<style lang="less" scoped></style>
