<script setup lang="ts">
  import { computed, ref } from 'vue';

  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { addDrugCatalogueModal } from './data';
  import { addDrugCatalogue, editDrugCatalogue } from '/@/api/drug-management/inventory';
  // import { ruleKindAdd, ruleKindEdit } from '/@/api/rule-management';

  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm({
    schemas: addDrugCatalogueModal,
    labelWidth: 100,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });
  const mode = ref<'add' | 'edit'>('add');
  const title = computed(() => {
    return mode.value === 'edit' ? '编辑药品目录' : '新增药品目录';
  });
  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data?.mode;
    formAction.setFieldsValue(data.data);
    if (mode.value === 'edit') {
      formAction.setFieldsValue(data.data);
    }
  });
  const { loading, runAsync: typeSaveRunAsync } = useRequest(
    (params) => (mode.value === 'add' ? addDrugCatalogue(params) : editDrugCatalogue(params)),
    {
      showSuccessMessage: true,
      manual: true,
    },
  );

  async function handleOk() {
    formAction.validate().then((values) => {
      typeSaveRunAsync({
        ...values,
      }).then(() => {
        emit('success');
        closeModal();
      });
    });
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="title"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    width="480px"
    @register="register"
    @ok="handleOk"
    centered
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
