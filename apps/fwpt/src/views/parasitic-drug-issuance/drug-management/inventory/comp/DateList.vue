<script setup lang="ts">
  // import DataSetModal from '../../StandardDataset/comp/dateModal.vue';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { useModal } from '@ft/internal/components/Modal';
  import { Button } from 'ant-design-vue';
  import { watch } from 'vue';
  import { BasicTitle } from '@ft/internal/components/Basic';
  import { useRequest } from '@ft/request';
  import { columns, searchSchema } from './data';
  import AddModal from './AddModal.vue';
  import { delDrugInfo, getDrugInfoPage } from '/@/api/drug-management/dict';

  const props = defineProps<{
    activeItem: any;
  }>();

  const [register, ActionTable] = useTable({
    api: getDrugInfoPage,
    showIndexColumn: false,
    useSearchForm: true,
    formConfig: {
      labelWidth: 80,
      schemas: searchSchema,
    },
    inset: true,
    immediate: false,
    beforeFetch(params) {
      params.pkCatalogue = props.activeItem.id || '';
      return params;
    },
    fetchSetting: {
      sizeField: 'pageSize',
      pageField: 'pageNum',
      listField: 'list',
    },
    actionColumn: {
      width: 100,
      title: '操作',
      align: 'left',
      dataIndex: 'action',
    },
    columns,
  });

  watch(
    () => props.activeItem,
    () => {
      ActionTable.getForm().resetFields();
    },
    {
      deep: true,
    },
  );

  function createActions(record, column) {
    const actions: ActionItem[] = [
      {
        label: '编辑',
        onClick: handleRange.bind(null, record, column),
      },
      {
        label: '删除',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: handleDel.bind(null, record),
        },
      },
    ];

    return actions;
  }
  const [registerModal, { openModal: openModal }] = useModal();

  function handleSuccess() {
    ActionTable.reload();
  }
  function handleRange(record, _) {
    openModal(true, {
      data: {
        ...record,
        pkCatalogue: props.activeItem.id,
      },
      mode: 'edit',
    });
  }

  function handleAdd() {
    openModal(true, {
      data: {
        pkCatalogue: props.activeItem.id,
      },
      mode: 'add',
    });
  }
  const { runAsync: runDrugInfoDel } = useRequest(delDrugInfo, {
    manual: true,
    showSuccessMessage: true,
  });
  function handleDel(record) {
    runDrugInfoDel(record.id).then(() => {
      ActionTable.reload();
    });
  }
  defineExpose({ ActionTable });
</script>

<template>
  <div class="h-full">
    <BasicTable class="role-list" @register="register">
      <template #headerTop>
        <BasicTitle class="mb-3" span normal>{{ activeItem?.catalogName }}-药品列表</BasicTitle>
      </template>
      <template #tableTitle>
        <Button type="primary" @click="handleAdd"> 新增药品 </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
    <AddModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<style scoped lang="less">
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table-form-container .ant-form {
      padding: 0;
      margin-bottom: 10px;
    }
  }
</style>
