<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { addModal } from './data';
  import { addDrugInfo, editDrugInfo } from '/@/api/drug-management/dict';

  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm({
    schemas: addModal,
    labelWidth: 120,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });
  const mode = ref<'add' | 'edit'>('add');
  const title = computed(() => {
    return mode.value === 'edit' ? '编辑药品' : '新增药品';
  });
  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data?.mode;
    formAction.setFieldsValue({ ...data.data });
    if (mode.value === 'edit') {
      formAction.setFieldsValue(data.data);
    }
  });
  const { loading, runAsync: drugSaveRunAsync } = useRequest(
    (params) => (mode.value === 'add' ? addDrugInfo(params) : editDrugInfo(params)),
    {
      showSuccessMessage: true,
      manual: true,
    },
  );
  async function handleOk() {
    formAction.validate().then((values) => {
      drugSaveRunAsync({
        ...values,
      }).then(() => {
        emit('success');
        closeModal();
      });
    });
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="title"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    width="830px"
    @register="register"
    @ok="handleOk"
    centered
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
