import type { BasicColumn } from '@ft/internal/components/Table';
import type { FormSchema } from '@ft/internal/components/Form';
import { DictEnum, getDictItemList } from '@ft/internal/api';
// import { ruleKindList } from '/@/api/rule-management';

/**
药品编码
药品名称
药品规格
药品单位
生产厂家
最小包装量
最小包装单位 
价格
用法 
剂型
批准文号 
注意事项
 */
export const columns: BasicColumn[] = [
  {
    title: '药品编码',
    dataIndex: 'drugCode',
    align: 'left',
  },
  {
    title: '药品名称',
    dataIndex: 'drugName',
    align: 'left',
  },
  {
    title: '药品规格',
    dataIndex: 'specs',
    align: 'left',
  },
  {
    title: '药品单位',
    dataIndex: 'unit',
    align: 'left',
  },
  {
    title: '生产厂家',
    dataIndex: 'manufacturer',
    align: 'left',
  },
  {
    title: '最小包装量',
    dataIndex: 'mpq',
    align: 'left',
  },
  {
    title: '最小包装单位',
    dataIndex: 'mpqUnit',
    align: 'left',
  },
  {
    title: '价格',
    dataIndex: 'price',
    align: 'left',
  },
  {
    title: '用法',
    dataIndex: 'drugUsageName',
    align: 'left',
  },
  {
    title: '剂型',
    dataIndex: 'dosageForm',
    align: 'left',
  },
  {
    title: '批准文号',
    dataIndex: 'approvalNumber',
    align: 'left',
  },
  {
    title: '注意事项',
    dataIndex: 'note',
    align: 'left',
  },
];
/**
 * 计划名称：
 * 计划状态：
 */
export const searchSchema: FormSchema[] = [
  {
    field: 'drugCode',
    label: '药品编码',
    component: 'Input',
    colProps: { span: 6 },
    // itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'drugName',
    label: '药品名称',
    component: 'Input',
    colProps: { span: 6 },
    // itemProps: { wrapperCol: { span: 14 } },
  },
];
export const modalSchema: FormSchema[] = [
  {
    field: 'orgId',
    component: 'Input',
    label: 'orgId',
    show: false,
    colProps: { span: 12 },
  },
  {
    field: 'deptId',
    component: 'Input',
    label: 'deptId',
    show: false,
    colProps: { span: 12 },
  },
  {
    field: 'deptCode',
    component: 'Input',
    label: '科室编码',
    required: true,
    colProps: { span: 12 },
  },
  {
    field: 'deptName',
    component: 'Input',
    label: '科室名称',
    required: true,
    colProps: { span: 12 },
  },
  {
    field: 'deptType',
    component: 'Input',
    label: '科室分类',
    componentProps: {
      // api: () => getDictItemList('DEPT_TYPE'),
      labelField: 'dictItemName',
      valueField: 'dictItemValue',
    },
    required: true,
    colProps: { span: 12 },
  },
  {
    field: 'deptIntroduction',
    component: 'Input',
    label: '科室介绍',
    colProps: { span: 24 },
  },
  {
    field: 'deptDiseaseType',
    component: 'Input',
    label: '科室病种',
    colProps: { span: 24 },
  },
  {
    field: 'deptManager',
    component: 'Input',
    label: '科室负责人',
    colProps: { span: 12 },
  },
  {
    field: 'deptTelephone',
    component: 'Input',
    label: '科室联系电话',
    colProps: { span: 12 },
  },
  {
    field: 'deptAddress',
    component: 'Input',
    label: '科室地址',
    colProps: { span: 24 },
  },
  {
    field: 'enableFlag',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 0 },
        { label: '禁用', value: 1 },
      ],
    },
    label: '科室状态',
    colProps: { span: 12 },
  },
];
/**
 * 目录名称
 * 药品类型
 * 药品层级
 */
export const addDrugCatalogueModal: FormSchema[] = [
  {
    field: 'id',
    component: 'Input',
    label: 'id',
    colProps: { span: 24 },
    componentProps: {},
    show: false,
  },
  {
    field: 'catalogName',
    component: 'Input',
    label: '目录名称',
    colProps: { span: 24 },
    componentProps: {},
    required: true,
  },
  {
    field: 'drugTypeDesc',
    component: 'ApiSelect',
    label: '药品类型',
    colProps: { span: 24 },
    componentProps: () => {
      return {
        api: () => getDictItemList(DictEnum.DRUG_TYPE),
        getPopupContainer: () => document.body,

        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        showSearch: true,
        optionFilterProp: 'label',
      };
    },
  },
  {
    field: 'drugLevelCode',
    component: 'ApiSelect',
    label: '药品层级',
    colProps: { span: 24 },
    componentProps: () => {
      return {
        api: () => getDictItemList(DictEnum.DRUG_LEVEL),
        getPopupContainer: () => document.body,
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        showSearch: true,
        optionFilterProp: 'label',
      };
    },
  },
  {
    field: 'enableFlag',
    component: 'Switch',
    label: '状态',
    colProps: { span: 12 },
    componentProps: {
      checkedValue: 1,
      uncheckedValue: 0,
      checkedChildren: '启用',
      unCheckedChildren: '禁用',
    },
    defaultValue: 1,
  },
];
/**
药品编码
药品名称
药品规格
药品单位
生产厂家
最小包装量
最小包装单位
价格
用法
剂型
批准文号
注意事项
状态
 */
export const addModal: FormSchema[] = [
  {
    field: 'id',
    component: 'Input',
    label: '药品主键',
    colProps: { span: 12 },
    componentProps: {},
    show: false,
  },
  {
    field: 'pkCatalogue',
    component: 'Input',
    label: '药品目录主键',
    colProps: { span: 12 },
    componentProps: {},
    show: false,
  },
  {
    field: 'drugCode',
    component: 'Input',
    label: '药品编码',
    colProps: { span: 12 },
    required: true,
    rules: [
      {
        pattern: /^[a-zA-Z0-9]+$/,
        message: '只能输入英文字母和数字',
        trigger: 'change',
      },
    ],
  },
  {
    field: 'drugName',
    component: 'Input',
    label: '药品名称',
    required: true,
    colProps: { span: 12 },
    componentProps: {},
  },
  {
    field: 'specs',
    component: 'Input',
    label: '药品规格',
    required: true,
    colProps: { span: 12 },
    componentProps: {},
  },
  {
    field: 'unit',
    component: 'Input',
    label: '药品单位',
    required: true,
    colProps: { span: 12 },
    componentProps: {},
  },
  {
    field: 'manufacturer',
    component: 'Input',
    label: '生产厂家',
    required: true,
    colProps: { span: 12 },
    componentProps: {},
  },
  {
    field: 'mpq',
    component: 'Input',
    label: '最小包装量',
    colProps: { span: 12 },
    componentProps: {},
    required: true,
  },
  {
    field: 'mpqUnit',
    component: 'Input',
    label: '最小包装单位',
    colProps: { span: 12 },
    componentProps: {},
    required: true,
  },
  {
    field: 'price',
    component: 'InputNumber',
    label: '价格',
    colProps: { span: 12 },
    componentProps: {
      min: 0,
    },
  },
  {
    field: 'drugUsageName',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'drugUsage',
    component: 'ApiSelect',
    label: '用法',
    colProps: { span: 12 },
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.DRUG_USAGE),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        showSearch: true,
        optionFilterProp: 'label',
        onChange: (_, opt) => {
          formModel.drugUsageName = opt?.label;
        },
      };
    },
  },
  {
    field: 'dosageForm',
    component: 'Input',
    label: '剂型',
    colProps: { span: 12 },
    componentProps: {},
  },
  {
    field: 'approvalNumber',
    component: 'Input',
    label: '批准文号',
    colProps: { span: 12 },
    componentProps: {},
  },
  {
    field: 'note',
    component: 'InputTextArea',
    label: '注意事项',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 24 },
  },
  {
    field: 'enableFlag',
    component: 'Select',
    label: '状态',
    colProps: { span: 12 },
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 },
      ],
    },
    defaultValue: 1,
  },
];
