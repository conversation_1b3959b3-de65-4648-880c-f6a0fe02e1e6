<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { computed, ref } from 'vue';
  import { useUserStore } from '@ft/internal/store/modules/user';
  import { drugStorageSchema } from './data';
  import { addStorage, detailStorage, editStorage } from '/@/api/drug-management/dict';
  // import { postDict, putDict } from '/@/api/dict';

  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm({
    schemas: drugStorageSchema,
    labelWidth: 180,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });
  const mode = ref('');
  const getTitle = computed(() => (mode.value === 'edit' ? '编辑药品信息' : '药品入库'));
  const { userInfo } = useUserStore();

  const { runAsync: getInfoRunAsync } = useRequest((params) => detailStorage(params), {
    manual: true,
    onSuccess: (res) => {
      formAction.updateSchema({
        field: 'drugCode',
        componentProps: {
          disabled: true,
        },
      });
      formAction.setFieldsValue({ ...res });
    },
  });
  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data.mode;
    if (mode.value === 'edit') {
      getInfoRunAsync(data.id);
    }
    formAction.setFieldsValue({ responsiblePerson: userInfo?.employeeName });
  });

  const { loading, runAsync } = useRequest(
    (params) => (mode.value === 'edit' ? editStorage(params) : addStorage(params)),
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess: () => {
        emit('success');
        closeModal();
      },
    },
  );

  async function handleOk() {
    formAction.validate().then((values) => {
      runAsync({ ...values, responsiblePerson: userInfo?.employeeName });
    });
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    :width="600"
    v-bind="$attrs"
    :title="getTitle"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    @register="register"
    @ok="handleOk"
    :min-height="120"
    centered
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
