<script setup lang="ts">
  import type { Ref } from 'vue';
  import { computed, ref, watch } from 'vue';
  import { RangePicker, Tabs } from 'ant-design-vue';
  import type { ActionItem, BasicColumn, TableRowSelection } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { useModal } from '@ft/internal/components/Modal';
  import type { Dayjs } from 'dayjs';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import DrugEdit from './DrugEdit.vue';
  import { leftColumns, leftFormSchema, rightIssueColumns, rightWarehousingColumns } from './data';
  import type { IDrugStorageList, IStorage } from '/@/api/drug-management/dict';
  import {
    delStorage,
    getDrugInfoPage,
    getOutboundList,
    getStorageList,
    inventoryExport,
  } from '/@/api/drug-management/dict';

  const activeKey = ref('1');
  const dateTime = ref<[Dayjs, Dayjs]>();
  const selectedRowKeys = ref<TableRowSelection['selectedRowKeys']>([]);
  const drugCode = computed(() => (selectedRowKeys.value?.[0] || '') as string);
  const drugName = computed(() => {
    return (
      ActionTable.getDataSource().find((item) => item.drugCode === drugCode.value)?.drugName || ''
    );
  });
  const [registerLeftTable, ActionTable] = useTable({
    inset: true,
    api: getDrugInfoPage,
    columns: leftColumns,
    resizeHeightOffset: 16,
    formConfig: {
      colon: true,
      labelWidth: 90,
      showAdvancedButton: false,
      actionColOptions: {
        span: 8,
      },
      schemas: leftFormSchema,
    },
    useSearchForm: true,
    showIndexColumn: true,
    bordered: false,
    size: 'small',
    rowKey: 'drugCode',
    beforeFetch: (params) => {
      params.enableFlag = 1;
      return params;
    },
    afterFetch: (data) => {
      selectedRowKeys.value = [data[0]?.drugCode || ''];
      return data;
    },
  });

  const {
    loading,
    data: reloadDruglist = {} as unknown as Ref<IStorage>,
    runAsync: reloadDrugItemRunAsync,
  } = useRequest(
    (params) =>
      activeKey.value === '1'
        ? getStorageList({
            storageTimeStart: dateTime.value?.[0]?.format('YYYY-MM-DD') || '',
            storageTimeEnd: dateTime.value?.[1]?.format('YYYY-MM-DD') || '',
            ...params,
          })
        : getOutboundList({
            outboundTimeStart: dateTime.value?.[0]?.format('YYYY-MM-DD') || '',
            outboundTimeEnd: dateTime.value?.[1]?.format('YYYY-MM-DD') || '',
            ...params,
          }),
    {
      manual: true,
    },
  );

  const dataSource = computed(() => {
    return activeKey.value === '1'
      ? reloadDruglist.value?.drugStorageList || []
      : reloadDruglist.value?.drugOutboundList || [];
  });

  const rightColumns = computed(() => {
    return (
      {
        '1': rightWarehousingColumns,
        '2': rightIssueColumns,
      }[activeKey.value] || []
    );
  });
  const [registerRightTable] = useTable({
    inset: true,
    columns: rightColumns,
    dataSource,
    size: 'small',
    bordered: false,
    immediate: false,
    // canResize: false,
    useSearchForm: false,
    loading: loading,
    resizeHeightOffset: 40,
    actionColumn: computed(() => {
      return activeKey.value === '1'
        ? {
            width: 120,
            title: '操作',
            dataIndex: 'action',
            align: 'center',
          }
        : undefined;
    }),
    // scroll: { y: '40vh' },
  });

  watch(drugCode, (val) => {
    val && handleRightDataSource();
  });

  function handleRightDataSource() {
    reloadDrugItemRunAsync({
      drugCode: drugCode.value || '',
    });
  }
  function handleTabChange() {
    dateTime.value = undefined;
    handleRightDataSource();
  }

  const rowSelection = computed<TableRowSelection>(() => {
    return {
      type: 'radio',
      selectedRowKeys: selectedRowKeys.value,
      onChange: (keys: any, _) => {
        if (keys.length === 0) {
          return;
        }
        selectedRowKeys.value = keys;
        dateTime.value = undefined;
      },
    };
  });

  const [register, { openModal: openDrugModal }] = useModal();

  function addDrug() {
    openDrugModal(true, {
      mode: 'add',
      drugCode: drugCode.value,
    });
  }
  function editDrug(record: IDrugStorageList) {
    openDrugModal(true, {
      mode: 'edit',
      id: record.id,
    });
  }

  function createActionsItem(record: IDrugStorageList, column: BasicColumn): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '编辑',
        type: 'link',
        size: 'small',
        onClick: editDrug.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: handleDelItem.bind(null, record, column),
          okButtonProps: {
            danger: true,
          },
        },
      },
    ];
    return actions;
  }

  const { run: delDrugItemRun } = useRequest(delStorage, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      handleRightDataSource();
    },
  });

  function handleDelItem(record: IDrugStorageList, _column) {
    delDrugItemRun(record.id);
  }
  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(inventoryExport, {
    manual: true,
    showSuccessMessage: true,
  });
  function onExportExpert() {
    exportUtil(exportExpertRunAsync(ActionTable.getForm().getFieldsValue()));
  }
  const activeText = computed(() => {
    return activeKey.value === '1' ? '入库' : '出库';
  });
</script>

<template>
  <div class="h-full w-full pr-2 pb-2 flex gap-2">
    <div class="h-full bg-white p-4 flex-1 of-hidden rounded rounded-lt-none">
      <div class="text-16px font-500">库存列表</div>
      <BasicTable :row-selection="rowSelection" @register="registerLeftTable">
        <template #tableTitle>
          <Button type="primary" @click="addDrug"> 药品入库 </Button>
        </template>
        <template #toolbar>
          <Button :loading="exportLoading" @click="onExportExpert"> 批量导出 </Button>
        </template>
      </BasicTable>
    </div>
    <div class="h-full bg-white p-4 flex-1 of-hidden rounded">
      <div class="text-16px font-500">库存明细</div>
      <Tabs v-model:active-key="activeKey" @change="handleTabChange">
        <Tabs.TabPane key="1" tab="入库明细" />
        <Tabs.TabPane key="2" tab="出库明细" />
      </Tabs>
      <div class="flex-1 of-hidden">
        <BasicTable class="rightTable" @register="registerRightTable">
          <template #headerTop>
            <div class="flex py-4 px-8 gap-80px bg-#F5F7FA w-full">
              <div class="flex">
                <div class="text-#999">药品编码：</div>
                <div class="text-#333">{{ drugCode }}</div>
              </div>
              <div class="flex">
                <div class="text-#999">药品名称：</div>
                <div class="text-#333">{{ drugName }}</div>
              </div>
            </div>
          </template>
          <template #tableTitle>
            <div class="flex items-center mt-4">
              <span>{{ activeText }}时间：</span>
              <RangePicker
                v-model:value="dateTime"
                type="daterange"
                @change="handleRightDataSource"
              />
            </div>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
              <TableAction :divider="false" :actions="createActionsItem(record, column)" />
            </template>
          </template>
          <template #footer>
            <div
              >{{ activeText }}数量合计：<span class="text-#FF7D34">{{
                reloadDruglist?.total
              }}</span></div
            >
          </template>
        </BasicTable>
      </div>
      <DrugEdit @register="register" @success="handleRightDataSource" />
    </div>
  </div>
</template>

<style lang="less" scoped>
  ::v-deep {
    .rightTable {
      .ant-table-footer {
        background-color: transparent;
      }
    }
  }
</style>
