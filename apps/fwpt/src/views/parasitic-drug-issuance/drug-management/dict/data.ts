import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { getDrugInfoDictList } from '/@/api/drug-management/dict';
import dayjs from 'dayjs';
/**
药品编码 
药品名称 
药品规格
药品单位
库存数量
生产厂家
最小包装量
最小包装单位
价格
用法
剂型
批准文号
注意事项
 */
export const leftColumns: BasicColumn[] = [
  {
    dataIndex: 'drugCode',
    title: '药品编码',
    width: 120,
  },
  {
    dataIndex: 'drugName',
    title: '药品名称',
    width: 150,
  },
  {
    dataIndex: 'specs',
    title: '药品规格',
    width: 80,
  },
  {
    dataIndex: 'unit',
    title: '药品单位',
    width: 80,
  },
  {
    dataIndex: 'drugInventory',
    title: '库存数量(最小包装单位)',
    width: 180,
  },
  {
    dataIndex: 'mpqUnit',
    title: '最小包装单位',
    width: 100,
  },
  {
    dataIndex: 'manufacturer',
    title: '生产厂家',
    width: 150,
  },
  {
    dataIndex: 'mpq',
    title: '最小包装量',
    width: 150,
  },

  {
    dataIndex: 'price',
    title: '价格',
    width: 150,
  },
  {
    dataIndex: 'usage',
    title: '用法',
    width: 150,
  },
  {
    dataIndex: 'dosageForm',
    title: '剂型',
    width: 150,
  },
  {
    dataIndex: 'approvalNumber',
    title: '批准文号',
    width: 150,
  },
  {
    dataIndex: 'note',
    title: '注意事项',
    width: 150,
  },
];
/**
出库时间
出库数量
出库医疗机构
出库患者
出库责任人
 */

export const rightIssueColumns: BasicColumn[] = [
  {
    title: '出库时间',
    dataIndex: 'dispensingTime',
    width: 160,
  },
  {
    title: '出库数量',
    dataIndex: 'quantity',
    width: 100,
  },
  {
    title: '出库医疗机构',
    dataIndex: 'medicalInstitutionNae',
    width: 160,
  },
  {
    title: '出库患者',
    dataIndex: 'patientName',
    width: 100,
  },
  {
    title: '出库责任人',
    dataIndex: 'dispenser',
    width: 100,
  },
];
/**
 * 入库时间
 * 入库数量
 * 入库人
 */
export const rightWarehousingColumns: BasicColumn[] = [
  {
    title: '入库时间',
    dataIndex: 'storageTime',
    width: 100,
  },
  {
    title: '入库数量',
    dataIndex: 'quantity',
    width: 100,
  },
  {
    title: '入库人',
    dataIndex: 'responsiblePerson',
    width: 100,
  },
];
/**
 * 药品编码
 * 药品名称
 */

export const leftFormSchema: FormSchema[] = [
  {
    field: 'drugCode',
    label: '药品编码',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'drugName',
    label: '药品名称',
    component: 'Input',
    colProps: { span: 8 },
  },
];

export const rightFormSchema: FormSchema[] = [
  {
    field: 'dictItemName',
    label: '字典值名称',
    component: 'Input',
    colProps: { span: 12 },
  },
];

/**
入库时间
入库药品
药品规格
生产厂家
入库数量
入库责任人
 */
export const drugStorageSchema: FormSchema[] = [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false,
    colProps: { span: 24 },
  },
  {
    field: 'storageTime',
    label: '入库时间',
    component: 'DatePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      showTime: false,
      style: {
        width: '100%',
      },
      disabled: false,
      getPopupContainer: () => document.body,
    },
    required: true,
    isHandleDateDefaultValue: false,
    defaultValue: dayjs().format('YYYY-MM-DD'),
    colProps: { span: 20 },
  },
  {
    field: 'drugCode',
    label: '入库药品',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () =>
          getDrugInfoDictList().then((res) => {
            return res.map((item) => ({
              ...item,
              label: `${item.drugCode}-${item.drugName}`,
            }));
          }),
        valueField: 'drugCode',
        optionFilterProp: 'label',
        showSearch: true,
        disabled: false,
        getPopupContainer: () => document.body, // 防止弹出层被modal隐藏掉
        allowClear: false,
        onChange: (_, opt) => {
          opt && (formModel.specs = opt?.specs);
          opt && (formModel.manufacturer = opt?.manufacturer);
          opt && (formModel.mpqUnit = opt?.mpqUnit);
        },
      };
    },
    required: true,
    colProps: { span: 20 },
  },
  {
    field: 'specs',
    label: '药品规格',
    component: 'Input',
    required: true,
    componentProps: {
      disabled: true,
    },
    colProps: { span: 20 },
  },
  {
    field: 'manufacturer',
    label: '生产厂家',
    component: 'Input',
    required: true,
    componentProps: {
      disabled: true,
    },
    colProps: { span: 20 },
  },
  {
    field: 'quantity',
    label: '入库数量(最小包装单位)',
    component: 'InputNumber',
    componentProps: {
      min: 0,
      disabled: false,
    },
    suffix({ model }) {
      return model.mpqUnit ?? '';
    },
    required: true,
    colProps: { span: 20 },
  },
  {
    field: 'responsiblePerson',
    label: '入库责任人',
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    colProps: { span: 20 },
  },
];
