<script setup lang="ts">
  import { Button } from '@ft/internal/components/Button';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Tabs } from 'ant-design-vue';
  import { ref } from 'vue';
  import { useModal } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import { SearchSchemas, columns } from './data';
  import PrescriptionDetailModal from './PrescriptionDetailModal.vue';
  import { getPrescriptionPage, prescriptionExport } from '/@/api/drug-dispensing';

  const activeKey = ref<any>(undefined);

  const [registerTable, ActionTable] = useTable({
    api: getPrescriptionPage,
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas: SearchSchemas,
      fieldMapToTime: [
        ['prescriptionDate', ['prescCreateTimeStart', 'prescCreateTimeEnd'], 'YYYY-MM-DD'],
      ],
    },
    columns,
    actionColumn: {
      title: '操作',
      dataIndex: 'action',
      width: 100,
    },
    beforeFetch: (params) => {
      params.prescriptionStatus = activeKey.value;
      return params;
    },
  });

  const [registerDetail, { openModal }] = useModal();
  const pkPrescription = ref<string>('');
  function onPrescriptionDetail(record) {
    pkPrescription.value = record.id;
    openModal();
  }

  function createActions(record, _column): ActionItem[] {
    return [
      {
        label: '处方详情',
        onClick: onPrescriptionDetail.bind(null, record),
      },
    ];
  }
  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(
    prescriptionExport,
    {
      manual: true,
      showSuccessMessage: true,
    },
  );
  function onExportExpert() {
    exportUtil(
      exportExpertRunAsync({
        ...ActionTable.getForm().getFieldsValue(),
        prescriptionStatus: activeKey.value,
      }),
    );
  }
</script>

<template>
  <div class="h-[calc(100%-10px)] w-[calc(100%-16px)] rounded rounded-lt-none">
    <BasicTable @register="registerTable">
      <template #headerTop>
        <div class="text-base font-bold">处方记录列表</div>
        <div>
          <Tabs v-model:activeKey="activeKey" @change="() => ActionTable.reload()">
            <Tabs.TabPane :key="undefined" tab="全部" />
            <Tabs.TabPane :key="0" tab="待审核发药" />
            <Tabs.TabPane :key="1" tab="已审核发药" />
            <Tabs.TabPane :key="2" tab="已驳回" />
          </Tabs>
        </div>
      </template>
      <template #toolbar>
        <Button :loading="exportLoading" @click="onExportExpert"> 批量导出 </Button>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
    <PrescriptionDetailModal @register="registerDetail" :prescriptionId="pkPrescription" />
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }

    .vben-basic-table-form-container .ant-form,
    .vben-basic-table .ant-table-wrapper {
      border-radius: 4px;
    }

    .vben-basic-table-form-container .ant-form {
      border-top-left-radius: 0;
      margin-bottom: 10px;
    }
  }
</style>
