import type { FormSchema } from '@ft/internal/components/Form';
import type { BasicColumn } from '@ft/internal/components/Table';

/**
 * 开具日期：
 * 开方医院：
 * 开方医生：
 * 主要诊断：
 * 患者姓名：
 */
export const SearchSchemas: FormSchema[] = [
  {
    field: 'prescriptionDate',
    label: '开具日期',
    component: 'RangePicker',
    componentProps: {
      style: { width: '100%' },
    },
    colProps: { span: 6 },
  },
  {
    field: 'prescCreateHospital',
    label: '开方医院',
    component: 'Input',
    componentProps: {},
    colProps: { span: 6 },
  },
  {
    field: 'prescCreateDoc',
    label: '开方医生',
    component: 'Input',
    componentProps: {},
    colProps: { span: 6 },
  },
  {
    field: 'mainDiagName',
    label: '主要诊断',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'patientName',
    label: '患者姓名',
    component: 'Input',
    colProps: { span: 6 },
  },
];

/**
 * 开方时间
 * 患者姓名
 * 性别
 * 年龄
 * 患者身份证号
 * 患者联系方式
 * 患者所属辖区
 * 主要诊断
 * 开方医院
 * 开方医生
 * 开方科室
 */
export const columns: BasicColumn[] = [
  {
    title: '开方时间',
    dataIndex: 'prescCreateTime',
    width: 150,
  },
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    width: 150,
  },
  {
    title: '性别',
    dataIndex: 'patientSexValue',
    width: 100,
  },
  {
    title: '年龄',
    dataIndex: 'patientAge',
    width: 100,
  },
  {
    title: '患者身份证号',
    dataIndex: 'patientIdCard',
    width: 200,
  },
  {
    title: '患者联系方式',
    dataIndex: 'patientPhone',
    width: 150,
  },
  // {
  //   title: '患者所属辖区',
  //   dataIndex: 'patientAreaName',
  //   width: 150,
  // },
  {
    title: '主要诊断',
    dataIndex: 'mainDiagName',
    width: 150,
  },
  {
    title: '开方医院',
    dataIndex: 'prescCreateHospital',
    width: 150,
  },
  {
    title: '开方医生',
    dataIndex: 'prescCreateDoc',
    width: 150,
  },
  {
    title: '开方科室',
    dataIndex: 'patientDeptName',
    width: 150,
  },
];

/**
 * 处方项目
 * 用量
 * 规格
 * 用法
 * 频次
 * 天数
 * 总量
 */
export const DetailColumns: BasicColumn[] = [
  {
    title: '处方项目',
    dataIndex: 'drugName',
    width: 120,
  },
  {
    title: '用量',
    dataIndex: 'dosage',
    width: 80,
  },
  {
    title: '规格',
    dataIndex: 'specs',
    width: 80,
  },
  {
    title: '用法',
    dataIndex: 'drugUsageName',
    width: 80,
  },
  {
    title: '频次',
    dataIndex: 'frequency',
    width: 80,
  },
  {
    title: '天数',
    dataIndex: 'dayNum',
    width: 80,
  },
  {
    title: '总量',
    dataIndex: 'total',
    width: 80,
  },
];
