import type { FormSchema } from '@ft/internal/components/Form';
import type { BasicColumn } from '@ft/internal/components/Table';
import { getDrugInfoDictList } from '/@/api/drug-management/dict';
import dayjs from 'dayjs';
import quarterOfYear from 'dayjs/plugin/quarterOfYear';

dayjs.extend(quarterOfYear);

/**
 * 统计年度：
 * 统计季度：
 * 统计月份：
 * 药品名称：
 * 开方机构：
 * 主要诊断：
 * 患者姓名：
 * 发药时间：
 */
export const SearchSchemas: FormSchema[] = [
  {
    field: 'year',
    label: '统计年度',
    component: 'DatePicker',
    componentProps: {
      picker: 'year',
      style: { width: '100%' },
      format: 'YYYY',
      valueFormat: 'YYYY',
    },
    isHandleDateDefaultValue: false,
    defaultValue: dayjs().format('YYYY'),
    colProps: { span: 6 },
  },
  {
    field: 'quarter',
    label: '统计季度',
    component: 'Select',
    componentProps: {
      options: [
        { label: '第一季度', value: 1 },
        { label: '第二季度', value: 2 },
        { label: '第三季度', value: 3 },
        { label: '第四季度', value: 4 },
      ],
    },
    colProps: { span: 6 },
    defaultValue: dayjs().quarter(),
  },
  {
    field: 'month',
    label: '统计月份',
    component: 'DatePicker',
    componentProps: {
      picker: 'month',
      style: { width: '100%' },
      format: 'M',
      valueFormat: 'M',
    },
    colProps: { span: 6 },
    isHandleDateDefaultValue: false,
    defaultValue: dayjs().format('M'),
  },
  {
    field: 'drugCode',
    label: '药品名称',
    component: 'ApiSelect',
    componentProps: {
      placeholder: '药品名称',
      api: getDrugInfoDictList,
      labelField: 'drugName',
      valueField: 'drugCode',
      optionFilterProp: 'label',
      showSearch: true,
    },
    colProps: { span: 6 },
  },
  {
    field: 'prescCreateHospital',
    label: '开方机构',
    component: 'Input',
    componentProps: {
      placeholder: '开方机构',
    },
    colProps: { span: 6 },
  },
  {
    field: 'mainDiagName',
    label: '主要诊断',
    component: 'Input',
    componentProps: {},
    colProps: { span: 6 },
  },
  {
    field: 'patientName',
    label: '患者姓名',
    component: 'Input',
    componentProps: {},
    colProps: { span: 6 },
  },
  {
    field: 'dispensingTime',
    label: '发药时间',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      style: { width: '100%' },
    },
    colProps: { span: 6 },
  },
];

/**
 * 发药时间
 * 药品编码
 * 药品名称
 * 发药数量
 * 患者姓名
 * 患者就诊号
 * 患者性别
 * 患者年龄
 * 患者身份证号
 * 患者联系方式
 * 患者科室
 * 发药状态
 * 发药人
 * 开单医生
 * 处方开具医院
 * 处方开具时间
 */
export const columns: BasicColumn[] = [
  {
    title: '发药时间',
    dataIndex: 'dispensingTime',
    width: 180,
  },
  {
    title: '药品编码',
    dataIndex: 'drugCode',
    width: 120,
  },
  {
    title: '药品名称',
    dataIndex: 'drugName',
    width: 120,
  },
  {
    title: '发药数量',
    dataIndex: 'quantity',
    width: 120,
  },
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    width: 120,
  },

  {
    title: '患者性别',
    dataIndex: 'patientSexValue',
    width: 120,
  },
  {
    title: '患者年龄',
    dataIndex: 'patientAge',
    width: 120,
  },
  {
    title: '患者身份证号',
    dataIndex: 'patientIdCard',
    width: 180,
  },
  {
    title: '患者联系方式',
    dataIndex: 'patientPhone',
    width: 140,
  },
  {
    title: '患者科室',
    dataIndex: 'patientDeptName',
    width: 120,
  },
  {
    title: '发药状态',
    dataIndex: 'prescriptionStatusDesc',
    width: 120,
  },
  {
    title: '发药人',
    dataIndex: 'dispenser',
    width: 120,
  },
  {
    title: '开单医生',
    dataIndex: 'prescCreateDoc',
    width: 120,
  },
  {
    title: '处方开具医院',
    dataIndex: 'prescCreateHospital',
    width: 180,
  },
  {
    title: '处方开具时间',
    dataIndex: 'prescCreateTime',
    width: 180,
  },
];
