<script setup lang="ts">
  import { Button } from '@ft/internal/components/Button';
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import { SearchSchemas, columns } from './data';
  import { getOutboundStatisticsList, statisticsListexport } from '/@/api/drug-management/dict';

  const [registerTable, ActionTable] = useTable({
    api: getOutboundStatisticsList,
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas: SearchSchemas,
      fieldMapToTime: [
        ['dispensingTime', ['dispensingTimeStart', 'dispensingTimeEnd'], 'YYYY-MM-DD'],
      ],
    },
    columns,
  });
  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(
    statisticsListexport,
    {
      manual: true,
      showSuccessMessage: true,
    },
  );
  function onExportExpert() {
    exportUtil(exportExpertRunAsync(ActionTable.getForm().getFieldsValue()));
  }
</script>

<template>
  <div class="h-[calc(100%-10px)] w-[calc(100%-16px)] rounded rounded-lt-none">
    <BasicTable @register="registerTable">
      <template #toolbar>
        <Button :loading="exportLoading" @click="onExportExpert"> 批量导出 </Button>
      </template>
    </BasicTable>
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }

    .vben-basic-table-form-container .ant-form,
    .vben-basic-table .ant-table-wrapper {
      border-radius: 4px;
    }

    .vben-basic-table-form-container .ant-form {
      border-top-left-radius: 0;
      margin-bottom: 10px;
    }
  }
</style>
