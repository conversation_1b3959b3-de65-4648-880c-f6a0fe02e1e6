<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { backDrug } from '/@/api/drug-dispensing';

  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm({
    schemas: [
      {
        field: 'pkPrescription',
        label: '处方id',
        component: 'Input',
        show: false,
      },
      {
        field: 'backReason',
        label: '驳回原因',
        required: true,
        component: 'InputTextArea',
        componentProps: {
          autoSize: { minRows: 3, maxRows: 3 },
        },
        colProps: { span: 22 },
        itemProps: { wrapperCol: { span: 18 } },
      },
    ],
    labelWidth: 100,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });
  const [register, { closeModal }] = useModalInner((data) => {
    formAction.setFieldsValue({ ...data });
  });
  const { loading, runAsync } = useRequest(backDrug, {
    showSuccessMessage: true,
    manual: true,
    onSuccess: () => {
      closeModal();
      emit('success');
    },
  });

  async function handleOk() {
    formAction.validate().then((values) => {
      runAsync({
        ...values,
      });
    });
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    title="驳回"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    :width="450"
    :min-height="100"
    @register="register"
    @ok="handleOk"
    centered
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
