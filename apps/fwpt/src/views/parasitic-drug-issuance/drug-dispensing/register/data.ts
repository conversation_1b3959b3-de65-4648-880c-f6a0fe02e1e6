import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import dayjs from 'dayjs';
import { h } from 'vue';
import Barcode from './barcode.vue';
/**
 * 开方时间
 * 主要诊断
 * 开方医院
 * 患者姓名
 */
export const SearchSchemas: FormSchema[] = [
  {
    field: 'prescriptionTime',
    label: '',
    component: 'RangePicker',
    componentProps: {
      style: { width: '100%' },
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      showTime: false,
    },
    colProps: { span: 24 },
  },
  {
    field: 'mainDiagName',
    label: '',
    component: 'Input',
    componentProps: {
      placeholder: '主要诊断',
    },
    colProps: { span: 24 },
  },
  {
    field: 'prescCreateHospital',
    label: '',
    component: 'Input',
    componentProps: {
      placeholder: '开方医院',
    },
    colProps: { span: 24 },
  },
  {
    field: 'patientName',
    label: '',
    component: 'Input',
    componentProps: {
      placeholder: '患者姓名',
    },
    colProps: { span: 24 },
  },
];
/**
 * 处方项目
 * 用量
 * 规格
 * 用法
 * 频次
 * 天数
 * 总量
 */
export const columns: BasicColumn[] = [
  {
    title: '处方项目',
    dataIndex: 'drugName',
    width: 150,
  },
  {
    title: '药品条码',
    dataIndex: 'drugCode',
    width: 240,
    customRender: ({ text }) => {
      return h(Barcode, {
        data: text,
        options: {
          format: 'CODE128',
          width: 1.5,
          height: 50,
          displayValue: false,
        },
      });
    },
  },
  {
    title: '用量',
    dataIndex: 'dosage',
    width: 150,
    customRender: ({ text, record }) => {
      return text + (record.dosageUnit ?? '');
    },
  },
  {
    title: '规格',
    dataIndex: 'specs',
    width: 150,
  },
  {
    title: '用法',
    dataIndex: 'drugUsageName',
    width: 150,
  },
  {
    title: '频次',
    dataIndex: 'frequency',
    width: 150,
  },
  {
    title: '天数',
    dataIndex: 'dayNum',
    width: 150,
  },
  {
    title: '总量',
    dataIndex: 'total',
    width: 150,
    customRender: ({ text, record }) => {
      return text + (record.dosageUnit ?? '');
    },
  },
];
//
export const dispensingFrom: FormSchema[] = [
  {
    field: 'pkPrescription',
    label: 'pkPrescription',
    component: 'Input',
    show: false,
  },
  // * 发药时间：
  {
    field: 'dispensingTime',
    label: '发药时间',
    component: 'DatePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm',
      format: 'YYYY-MM-DD HH:mm',
      style: { width: '100%' },
      disabledDate: (currentDate) => {
        return currentDate && currentDate < dayjs().endOf('day');
      },
      getPopupContainer: () => document.body, // 防止弹出层被modal隐藏掉
    },
    colProps: { span: 24 },
    required: true,
  },
  // * 发药人：
  {
    field: 'dispenser',
    label: '发药人',
    component: 'Input',
    colProps: { span: 24 },
    required: true,
  },
];
