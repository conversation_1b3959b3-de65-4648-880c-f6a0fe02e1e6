<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import dayjs from 'dayjs';
  import { useRequest } from '@ft/request';
  import { useUserStore } from '@ft/internal/store/modules/user';
  import { dispensingFrom } from './data';
  import { getOutboundDispensing } from '/@/api/drug-management/dict';

  const emit = defineEmits(['register', 'sussess']);

  const [registerForm, formAction] = useForm({
    labelWidth: 100,
    schemas: dispensingFrom,
    showActionButtonGroup: false,
  });
  const { userInfo } = useUserStore();
  const [register, { closeModal }] = useModalInner((data) => {
    formAction.setFieldsValue({
      dispensingTime: dayjs().format('YYYY-MM-DD HH:mm'),
      pkPrescription: '3423432',
      dispenser: userInfo?.employeeName,
      ...data,
    });
  });
  const { runAsync } = useRequest((params) => getOutboundDispensing(params), {
    manual: true,
    showSuccessMessage: false,
    onSuccess: () => {
      emit('sussess');
      closeModal();
    },
  });

  function save() {
    formAction.validate().then(() => {
      runAsync(formAction.getFieldsValue());
    });
  }
</script>

<template>
  <BasicModal
    destroy-on-close
    :can-fullscreen="false"
    v-bind="$attrs"
    @register="register"
    :width="450"
    :min-height="100"
    title="自提发药"
    @ok="save"
    centered
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<style scoped></style>
