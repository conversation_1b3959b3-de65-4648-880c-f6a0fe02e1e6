<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { Divider, Empty, Input, Spin, Tabs, message } from 'ant-design-vue';
  import type { Ref } from 'vue';
  import { computed, ref, watch } from 'vue';
  import { Icon } from '@ft/internal/components/Icon';
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { useModal } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { useEventListener } from '@vueuse/core';
  import Barcode from 'vue3-barcode';
  import { SearchSchemas, columns } from './data';
  import DispensingModal from './DispensingModal.vue';
  import RejectModal from './RejectModal.vue';
  import type { DetailPrescriptionInfo } from '/@/api/drug-dispensing';
  import {
    detailPrescription,
    getByPrescriptionIdPage,
    getPrescriptionList,
  } from '/@/api/drug-dispensing';
  import { getDrugInfoDictList } from '/@/api/drug-management/dict';

  const [register, { getFieldsValue }] = useForm({
    showActionButtonGroup: false,
    labelWidth: 0,
    schemas: SearchSchemas,
  });
  const prescriptionStatus = ref(0);
  const activePatient = ref('');
  const drugId = ref('');

  const {
    loading,
    data: prescriptionList,
    runAsync: getPrescriptionListRunAsync,
  } = useRequest(
    () =>
      getPrescriptionList({
        prescriptionStatus: prescriptionStatus.value,
        ...getFieldsValue(),
        prescCreateTimeStart: getFieldsValue()?.prescriptionTime
          ? getFieldsValue()?.prescriptionTime[0]
          : '',
        prescCreateTimeEnd: getFieldsValue()?.prescriptionTime
          ? getFieldsValue()?.prescriptionTime[1]
          : '',
      }),
    {
      onSuccess: (data) => {
        activePatient.value = data?.length > 0 ? data[0].id : '';
        if (data && data.length > 0) {
          getPrescribingInfoRunAsync();
          reload();
        } else {
          prescribingInfo.value = {} as unknown as DetailPrescriptionInfo;
          setTableData([]);
        }
      },
    },
  );

  const {
    data: prescribingInfo = {} as unknown as Ref<DetailPrescriptionInfo>,
    runAsync: getPrescribingInfoRunAsync,
  } = useRequest(() => detailPrescription(activePatient.value), {
    manual: true,
  });

  const [registerTable, { reload, setTableData, getDataSource }] = useTable({
    api: getByPrescriptionIdPage,
    columns,
    immediate: false,
    scroll: {
      y: 200,
    },
    beforeFetch: (params) => {
      params.pkPrescription = activePatient.value;
      return params;
    },
    inset: true,
  });
  const [registerTableBottom, bottomTableAction] = useTable({
    columns: [
      {
        title: '药品编码',
        dataIndex: 'drugCode',
        align: 'left',
      },
      {
        title: '药品名称',
        dataIndex: 'drugName',
        align: 'left',
      },
      {
        title: '药品规格',
        dataIndex: 'specs',
        align: 'left',
      },
      {
        title: '药品单位',
        dataIndex: 'unit',
        align: 'left',
      },
      {
        title: '生产厂家',
        dataIndex: 'manufacturer',
        align: 'left',
      },
      // {
      //   title: '价格',
      //   dataIndex: 'price',
      //   align: 'left',
      // },
    ],
    inset: true,
  });

  const [registerModal, { openModal }] = useModal();

  function onDispensing() {
    openModal(true, {
      pkPrescription: activePatient.value,
    });
  }
  function handleClickPatient(val) {
    activePatient.value = val;
    getPrescribingInfoRunAsync();
    reload();
    bottomTableAction.setTableData([]);
  }
  //  prescriptionStatus 处方状态 0 待发药 1 已发药
  const disabled = computed(() => {
    return (
      !prescriptionList.value?.length ||
      //@ts-ignore
      [1, 2].includes(prescribingInfo.value?.prescriptionStatus) ||
      !getDataSource().length
    );
  });
  const drugCode = ref('');
  const [registerRejectModal, { openModal: openRejectModal }] = useModal();
  function onReject() {
    openRejectModal(true, {
      pkPrescription: activePatient.value,
    });
  }

  const { run: refreshBottomTable } = useRequest(
    (code) => getDrugInfoDictList({ pageSize: 999, pageNum: 1, drugCode: code }),
    {
      manual: true,
      onSuccess: (res) => {
        if (res.length) {
          const data = bottomTableAction.getDataSource();
          const uniqueData = res.filter(
            (item) => !data.some((existingItem) => existingItem.drugCode === item.drugCode),
          );
          bottomTableAction.setTableData([...data, ...uniqueData]);
        } else {
          message.warn('药品不存在');
        }
      },
      onError: () => {
        message.error('未找到该药品');
      },
    },
  );
  function onSearch() {
    refreshBottomTable(drugCode.value);
  }
  let lastKeyTime = 0;
  let inputBuffer = '';
  useEventListener(document, 'keydown', (event) => {
    const currentTime = Date.now();
    const timeDiff = currentTime - lastKeyTime;
    console.log(event);
    if (timeDiff > 50) {
      inputBuffer = '';
    }
    if (event.key === 'Enter') {
      drugId.value = inputBuffer;
      console.log(drugId.value, 'drugId.value');
      inputBuffer = '';
    } else {
      if (event.key === 'Shift') {
        return;
      }
      console.log(inputBuffer, 'inputBuffer');
      inputBuffer += event.key;
      console.log(inputBuffer, 'inputBuffer');
    }

    lastKeyTime = currentTime;
  });

  watch(drugId, (val) => {
    refreshBottomTable(val);
  });
</script>

<template>
  <div class="h-[calc(100%-10px)] w-[calc(100%-16px)] bg-white rounded rounded-lt-none flex">
    <div class="border-r-1 border-[#EDEEF0] w-272px flex flex-col">
      <div class="flex flex-col gap-3 p-4 pb-0">
        <div class="text-base font-bold">处方列表</div>
        <BasicForm @register="register" @field-value-change="getPrescriptionListRunAsync" />
        <Tabs v-model:active-key="prescriptionStatus" @change="getPrescriptionListRunAsync">
          <Tabs.TabPane :key="0" tab="待发药" />
          <Tabs.TabPane :key="1" tab="已发药" />
          <!-- <Tabs.TabPane :key="2" tab="已驳回" /> -->
          <Tabs.TabPane key="" tab="全部" />
        </Tabs>
      </div>
      <div class="patient-list flex-1 min-h-0 basis-0 of-y-auto p-4">
        <Spin class="!p-5 w-full" v-if="loading" tip="加载中" />
        <div v-if="prescriptionList && prescriptionList.length > 0">
          <div
            class="patient-item relative p-4 border-1 border-[#DCDFE6] rounded cursor-pointer mb-2 hover:border-primary-color transition-all"
            :class="{
              'border-primary-color shadow-[0_0_0_2px_rgba(51,188,113,0.2)]':
                activePatient === patient.id,
            }"
            v-for="patient in prescriptionList"
            :key="patient.id"
            @click="handleClickPatient(patient.id)"
          >
            <div class="flex items-center mb-2">
              <span class="font-bold">{{ patient.patientName }}</span>
              <Icon icon="male|svg" class="ml-2" v-if="patient.patientSexValue === '男'" />
              <Icon icon="female|svg" class="ml-2" v-else />
            </div>
            <div class="flex items-center whitespace-nowrap">
              <span>{{ patient.patientAge }} </span>
              <Divider type="vertical" />
              <span>{{ patient.prescriptionNo }}</span>
            </div>
            <div class="flex items-center">
              <span class="text-info-text-color">开方时间：</span>
              <span>{{ patient.prescCreateTime }}</span>
            </div>
            <div
              class="absolute right-0 top-4 py-2px px-10px rounded-l text-white font-bold text-shadow-[0px_1px_2px_rgba(0_0_0_0.24)]"
              :class="{
                'dispensing-tag-padding': patient.prescriptionStatus === 0,
                'dispensing-tag-done': patient.prescriptionStatuDesc === '已发药',
                'dispensing-tag-reject': patient.prescriptionStatuDesc === '已驳回',
              }"
            >
              {{ patient.prescriptionStatuDesc }}
            </div>
          </div></div
        >
        <Empty v-else :image="Empty.PRESENTED_IMAGE_SIMPLE" />
      </div>
    </div>
    <div class="flex-1 of-hidden">
      <div class="p-4 flex flex-col gap-4 border-b-1 border-[#EDEEF0]">
        <div class="title font-bold text-base">处方基本信息</div>
        <div class="bg-[#F5F7FA] py-4 px-6 rounded flex flex-col gap-4">
          <div class="flex">
            <div class="flex-1">
              <span class="text-info-text-color">开方时间：</span>
              <span>{{ prescribingInfo?.prescCreateTime }}</span>
            </div>
            <div class="flex-1">
              <span class="text-info-text-color">开方机构：</span>
              <span>{{ prescribingInfo?.prescCreateHospital }}</span>
            </div>
            <div class="flex-1">
              <span class="text-info-text-color">开方科室：</span>
              <span>{{ prescribingInfo?.patientDeptName }}</span>
            </div>
            <div class="flex-1">
              <span class="text-info-text-color">开方医生：</span>
              <span>{{ prescribingInfo?.prescCreateDoc }}</span>
            </div>
          </div>
          <div class="basic-info-row flex">
            <div class="flex-1">
              <span class="text-info-text-color">开方患者：</span>
              <span>{{ prescribingInfo?.patientName }}</span>
            </div>

            <div class="flex-1">
              <span class="text-info-text-color">患者性别：</span>
              <span>{{ prescribingInfo?.patientSexValue }}</span>
            </div>
            <div class="flex-1">
              <span class="text-info-text-color">患者年龄：</span>
              <span>{{ prescribingInfo?.patientAge }}</span>
            </div>
            <div class="flex-1">
              <span class="text-info-text-color">主要诊断：</span>
              <span>{{ prescribingInfo?.mainDiagName }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="prescription-details p-4 flex flex-col gap-4">
        <div class="title font-bold text-base">处方详情</div>

        <BasicTable @register="registerTable">
          <template #tableTitle>
            <Button
              type="primary"
              :disabled="disabled"
              @click="onDispensing"
              class="mr-2"
              :loading="loading"
              >自提发药
            </Button>
            <!-- 驳回 -->
            <Button type="primary" :disabled="disabled" :loading="loading" @click="onReject">
              驳回
            </Button>
          </template>
          <template #bodyCell="{ column, record }">
            <div v-if="column.key === 'id2'">
              <Barcode
                :value="record.id"
                :format="'CODE128'"
                :width="1.5"
                :height="50"
                :displayValue="false"
              />
            </div>
          </template>
        </BasicTable>

        <BasicTable @register="registerTableBottom">
          <template #toolbar>
            <div>
              <Input allow-clear placeholder="扫描或手动输入药品编码" v-model:value="drugCode">
                <template #suffix>
                  <Icon
                    class="cursor-pointer"
                    icon="ant-design:search-outlined"
                    @click="onSearch"
                  />
                </template>
              </Input>
            </div>
          </template>
        </BasicTable>
      </div>
    </div>
    <DispensingModal @register="registerModal" @sussess="getPrescriptionListRunAsync" />
    <!-- 驳回弹窗 -->
    <RejectModal @register="registerRejectModal" @success="getPrescriptionListRunAsync" />
  </div>
</template>

<style lang="less" scoped>
  .patient-list {
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #dcdfe6;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background-color: #f3f3f3;
    }
  }

  .dispensing-tag-padding {
    background: linear-gradient(114deg, #feef8f 0%, #ffb768 100%);
  }

  .dispensing-tag-done {
    background: linear-gradient(114deg, #ebedf5 0%, #b6bee7 100%);
  }

  .dispensing-tag-reject {
    background: linear-gradient(114deg, #fea78f 0%, #ff6868 100%);
  }
</style>
