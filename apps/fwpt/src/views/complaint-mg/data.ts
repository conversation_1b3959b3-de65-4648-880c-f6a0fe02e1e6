import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import type { ModalEditDT } from '/@/types';
import type { IComplaintRecord } from '/@/api/complaint-mg';
import { getDeptList, getUserList } from '@ft/internal/api';

export type ComplaintEditModalDT = ModalEditDT<IComplaintRecord>;

/**
 * 投诉日期
 * 投诉部门
 * 投诉状态
 * 投诉人员
 */
export const SearchSchemas: FormSchema[] = [
  {
    field: 'complaintDate',
    label: '投诉日期',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      style: { width: '100%' },
    },
    colProps: { span: 6 },
  },
  {
    field: 'complaintDeptCode',
    label: '投诉部门',
    component: 'ApiSelect',
    componentProps: {
      api: getDeptList,
      labelField: 'deptName',
      valueField: 'deptCode',
      keyField: 'deptId',
      showSearch: true,
      optionFilterProp: 'label',
    },
    colProps: { span: 6 },
  },
  {
    field: 'complaintStatus',
    label: '投诉状态',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '已登记',
          value: 0,
        },
        {
          label: '撤销',
          value: 1,
        },
      ],
    },
    colProps: { span: 6 },
  },
  {
    field: 'complaintPersonnel',
    label: '投诉人员',
    component: 'Input',
    colProps: { span: 6 },
  },
];

/**
complainant	投诉者	string	
complainantContact	投诉者联系电话	string	
complaintChannel	投诉渠道 1手机 2微信	integer	
complaintDate	投诉日期	string	
complaintDept	投诉部门	string	
complaintDeptCode	投诉部门编码	string	
complaintPersonnel	投诉人员	string	
complaintPersonnelContact	投诉人员联系方式	string	
complaintPersonnelNumber	投诉人员工号	string	
complaintPersonnelStatus	投诉者状态 1门诊 3住院 5患者家属	integer	
complaintPersonnelTitle	投诉人员职称	string	
complaintReason	投诉原因	string	
complaintStatus	投诉状态 0已登记 1撤销	integer	
createUser	创建人	string	
id	主键id	string	
mainReason	主要内容	string
 */
export const columns: BasicColumn[] = [
  {
    title: '投诉日期',
    dataIndex: 'complaintDate',
    width: 120,
  },
  {
    title: '投诉部门',
    dataIndex: 'complaintDept',
    width: 120,
  },
  {
    title: '投诉人员',
    dataIndex: 'complaintPersonnel',
    width: 120,
  },
  {
    title: '人员职称',
    dataIndex: 'complaintPersonnelTitle',
    width: 120,
  },
  {
    title: '投诉人员联系电话',
    dataIndex: 'complaintPersonnelContact',
    width: 140,
  },
  {
    title: '投诉者',
    dataIndex: 'complainant',
    width: 120,
  },
  {
    title: '投诉渠道',
    dataIndex: 'complaintChannel',
    width: 120,
    customRender: ({ value }) => {
      // 投诉渠道 1手机 2微信
      const textMap = {
        1: '手机',
        2: '微信',
      };
      return textMap[value];
    },
  },
  {
    title: '投诉者状态',
    dataIndex: 'complaintPersonnelStatus',
    width: 120,
    customRender: ({ value }) => {
      //  1门诊 3住院 5患者家属
      const textMap = {
        1: '门诊',
        3: '住院',
        5: '患者家属',
      };
      return textMap[value];
    },
  },
  {
    title: '投诉者联系电话',
    dataIndex: 'complainantContact',
    width: 120,
  },
  {
    title: '投诉原因',
    dataIndex: 'complaintReason',
    width: 120,
  },
  {
    title: '主要内容',
    dataIndex: 'mainContent',
    width: 120,
  },
  {
    title: '投诉状态',
    dataIndex: 'complaintStatus',
    width: 120,
    customRender: ({ value }) => {
      return value === 0 ? '已登记' : '撤销';
    },
  },
  {
    title: '登记人',
    dataIndex: 'createUser',
    width: 120,
  },
];

export const EditSchema: (orgId: string) => FormSchema[] = (orgId) => [
  // id
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'complaintDate',
    label: '投诉日期',
    component: 'DatePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      style: { width: '100%' },
    },
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 18 } },
    required: true,
  },
  // 查询用户用
  {
    field: 'deptId',
    label: '部门id',
    component: 'Input',
    ifShow: false,
  },
  {
    field: 'complaintDept',
    label: '投诉部门',
    component: 'Input',
    show: false,
  },
  {
    field: 'complaintDeptCode',
    label: '投诉部门',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => getDeptList({ orgId }),
        labelField: 'deptName',
        valueField: 'deptCode',
        keyField: 'deptId',
        showSearch: true,
        optionFilterProp: 'label',
        onChange: (_, opt) => {
          formModel['complaintDept'] = opt?.label;
          formModel['deptId'] = opt?.deptId;

          // clear complaintPersonnel info
          formModel['complaintPersonnelNumber'] = undefined;
          formModel['complaintPersonnelTitle'] = undefined;
          formModel['complaintPersonnelContact'] = undefined;
          formModel['complaintPersonnel'] = undefined;
        },
      };
    },
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 18 } },
    required: true,
  },
  // complaintPersonnelNumber
  {
    field: 'complaintPersonnelNumber',
    label: '投诉人员工号',
    component: 'Input',
    show: false,
  },
  {
    field: 'complaintPersonnelTitle',
    label: '投诉人员职称',
    component: 'Input',
    show: false,
  },
  // complaintPersonnelContact
  {
    field: 'complaintPersonnelContact',
    label: '投诉人员联系电话',
    component: 'Input',
    show: false,
  },
  {
    field: 'complaintPersonnel',
    label: '投诉人员',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => {
          if (!formModel.deptId) {
            return Promise.resolve([]);
          }
          return getUserList({ deptId: formModel.deptId });
        },
        labelField: 'employeeName',
        valueField: 'employeeName',
        keyField: 'id',
        showSearch: true,
        optionFilterProp: 'label',
        onChange: (_, opt) => {
          formModel['complaintPersonnelNumber'] = opt?.id;
          formModel['complaintPersonnelTitle'] = opt?.jobTitleName;
          formModel['complaintPersonnelContact'] = opt?.phone;
        },
      };
    },
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 18 } },
    required: true,
  },
  {
    field: 'complainant',
    label: '投诉者',
    component: 'Input',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 18 } },
  },
  {
    field: 'complaintChannel',
    label: '投诉渠道',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '手机',
          value: 1,
        },
        {
          label: '微信',
          value: 2,
        },
      ],
    },
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 18 } },
  },
  {
    field: 'complaintPersonnelStatus',
    label: '投诉者状态',
    component: 'Select',
    componentProps: {
      // 门诊、住院、患者家属
      options: [
        {
          label: '门诊',
          value: 1,
        },
        {
          label: '住院',
          value: 3,
        },
        {
          label: '患者家属',
          value: 5,
        },
      ],
    },
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 18 } },
    required: true,
  },
  {
    field: 'complainantContact',
    label: '投诉者联系电话',
    component: 'Input',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 18 } },
    required: true,
    rules: [
      {
        pattern: /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
        message: '请输入正确的联系电话',
      },
    ],
  },
  {
    field: 'complaintStatus',
    label: '投诉状态',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '已登记',
          value: 0,
        },
        {
          label: '撤销',
          value: 1,
        },
      ],
    },
    defaultValue: 0,
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 18 } },
  },
  {
    field: 'complaintReason',
    label: '投诉原因',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 3, maxRows: 3 },
    },
    colProps: { span: 20 },
    required: true,
  },
  {
    field: 'mainContent',
    label: '主要内容',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 3, maxRows: 3 },
    },
    colProps: { span: 20 },
    required: true,
  },
];
