<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { addComplaintRecord } from '/@/api/complaint-mg';
  import { computed, ref } from 'vue';
  import { useUserStore } from '@ft/internal/store/modules/user';
  import type { ComplaintEditModalDT } from './data';
  import { EditSchema } from './data';

  const emit = defineEmits(['register', 'sussess']);
  const mode = ref<'add' | 'edit' | 'view'>('add');

  const disabled = computed(() => mode.value === 'view');
  const userStore = useUserStore();

  const [registerForm, formAction] = useForm({
    labelWidth: 120,
    schemas: EditSchema(userStore.getUserInfo.orgId),
    showActionButtonGroup: false,
    disabled,
  });

  const [register, { closeModal }] = useModalInner<ComplaintEditModalDT>((data) => {
    mode.value = data.mode;
    if (!data.record) return;
    formAction.setFieldsValue(data.record);
    formAction.clearValidate();
  });

  const { run: addComplaintRecordRun } = useRequest(addComplaintRecord, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      emit('sussess');
      closeModal();
    },
  });

  function onOk() {
    formAction.validate().then((values) => {
      addComplaintRecordRun(values);
    });
  }
</script>

<template>
  <BasicModal
    destroy-on-close
    v-bind="$attrs"
    @register="register"
    width="50%"
    :min-height="100"
    title="投诉登记"
    centered
    :footer="disabled ? null : undefined"
    @ok="onOk"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<style scoped></style>
