<script setup lang="ts">
  import { Button } from '@ft/internal/components/Button';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { useModal } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import type { ComplaintEditModalDT } from './data';
  import { SearchSchemas, columns } from './data';
  import ComplaintEditModal from './ComplaintEditModal.vue';
  import { cancelComplaintRecord, getComplaintRecordPage } from '/@/api/complaint-mg';
  import type { IComplaintRecord } from '/@/api/complaint-mg';

  const [registerTable, tableIns] = useTable({
    api: getComplaintRecordPage,
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas: SearchSchemas,
      fieldMapToTime: [['complaintDate', ['complaintStartTime', 'complaintEndTime'], 'YYYY-MM-DD']],
    },
    columns,
    actionColumn: {
      width: 140,
      title: '操作',
      dataIndex: 'action',
      align: 'left',
    },
  });

  const [registerModal, { openModal }] = useModal();

  function onRegister() {
    openModal<ComplaintEditModalDT>(true, {
      mode: 'add',
    });
  }

  function onDetail(record: IComplaintRecord) {
    openModal<ComplaintEditModalDT>(true, {
      mode: 'view',
      record,
    });
  }

  const { run: cancelComplaintRecordRun } = useRequest(cancelComplaintRecord, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      tableIns.reload();
    },
  });

  function onRevoke(record: IComplaintRecord) {
    cancelComplaintRecordRun(record.id);
  }

  function createActions(record: IComplaintRecord, _column): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '查看详情',
        onClick: onDetail.bind(null, record),
      },
    ];

    if (record.complaintStatus === 0) {
      actions.push({
        label: '撤销',
        danger: true,
        popConfirm: {
          placement: 'topRight',
          title: '确认撤销吗？',
          okButtonProps: { danger: true },
          confirm: onRevoke.bind(null, record),
        },
      });
    }

    return actions;
  }
</script>

<template>
  <div class="h-[calc(100%-16px)] w-[calc(100%-16px)] rounded rounded-lt-none">
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <Button type="primary" @click="onRegister">登记</Button>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
    <ComplaintEditModal @register="registerModal" @sussess="tableIns.reload" />
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }

    .vben-basic-table-form-container .ant-form,
    .vben-basic-table .ant-table-wrapper {
      border-radius: 4px;
    }

    .vben-basic-table-form-container .ant-form {
      border-top-left-radius: 0;
      margin-bottom: 10px;
    }
  }
</style>
