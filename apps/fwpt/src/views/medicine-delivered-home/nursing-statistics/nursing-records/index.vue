<script setup lang="ts">
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { useRoute } from 'vue-router';
  import { Button } from 'ant-design-vue';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import { columns, formSchema } from './data';
  import type { INurseOrderGoingDown } from '/@/api/medicine-delivered-home/nursing-statistics';
  import {
    getNurseOrderGoingDown,
    nurseOrderRecordsExport,
  } from '/@/api/medicine-delivered-home/nursing-statistics';

  /**
   * 护理记录列表
   */
  const query = useRoute().query;

  const [registerTable, tableIns] = useTable({
    api: getNurseOrderGoingDown,
    formConfig: {
      colon: true,
      labelWidth: 100,
      showAdvancedButton: false,
      actionColOptions: {
        span: 24,
        style: {
          textAlign: 'right',
        },
      },
      schemas: formSchema,
      fieldMapToTime: [
        ['orderTime', ['orderStartTime', 'orderEndTime'], 'YYYY-MM-DD'],
        ['nurseTime', ['nurseStartTime', 'nurseEndTime'], 'YYYY-MM-DD'],
      ],
    },
    fetchSetting: {
      sizeField: 'pageSize',
      pageField: 'pageNum',
      listField: 'records',
    },
    beforeFetch: (params) => {
      return {
        ...params,
        ...(query || {}),
      };
    },
    useSearchForm: true,
    showIndexColumn: false,
    columns,
    bordered: false,
    size: 'small',
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
  });

  function createActions(record: INurseOrderGoingDown): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '护理明细查看',
        type: 'link',
        size: 'small',
        onClick: handleNursingDetails.bind(null, record),
      },
    ];
    return actions;
  }
  const go = useGo();
  function handleNursingDetails(record: INurseOrderGoingDown) {
    go({
      name: 'NursingManagementOrderDetails',
      query: {
        orderId: record.id,
      },
    });
  }
  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(
    nurseOrderRecordsExport,
    {
      manual: true,
      showSuccessMessage: true,
    },
  );
  function onExportExpert() {
    exportUtil(
      exportExpertRunAsync({
        ...tableIns.getForm().getFieldsValue(),
        ...(query || {}),
      }),
    );
  }
</script>

<template>
  <div class="w-full h-full pr-4 rounded rounded-lt-none">
    <BasicTable @register="registerTable" class="ft-main-table">
      <template #headerTop>
        <div class="text-16px font-500 mb-4">医服到家护理统计</div>
      </template>
      <template #tableTitle> </template>
      <template #toolbar>
        <Button :loading="exportLoading" @click="onExportExpert"> 批量导出 </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
