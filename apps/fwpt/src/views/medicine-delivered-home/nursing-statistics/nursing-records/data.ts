import { DictEnum, getDictItemList, getNurseDeptList } from '@ft/internal/api';
import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { useUserStoreWithOut } from '@ft/internal/store/modules/user';

/**
 * 护理人员
 * 护理人员科室
 * 订单时间
 * 订单编号
 * 患者姓名
 * 患者性别
 * 患者年龄
 * 护理项目
 * 预约上门时间
 * 上门时间
 * 完成时间
 * 订单状态
 * 患者地址
 */
export const columns: BasicColumn[] = [
  {
    title: '护理人员',
    dataIndex: 'nurseName',
    align: 'left',
    width: 100,
  },
  {
    title: '护理人员科室',
    dataIndex: 'deptName',
    align: 'left',
    width: 100,
  },
  {
    title: '订单时间',
    dataIndex: 'orderTime',
    align: 'left',
    width: 170,
  },
  {
    title: '订单编号',
    dataIndex: 'code',
    align: 'left',
    width: 130,
  },
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    align: 'left',
    width: 100,
  },
  {
    title: '患者性别',
    dataIndex: 'sex',
    align: 'left',
    width: 100,
  },
  {
    title: '患者年龄',
    dataIndex: 'age',
    align: 'left',

    width: 100,
  },
  {
    title: '组套名称',
    dataIndex: 'itemName',
    align: 'left',
    width: 100,
  },
  {
    title: '预约上门时间',
    dataIndex: 'appointTime',
    align: 'left',
    width: 170,
  },
  {
    title: '上门时间',
    dataIndex: 'visitTime',
    align: 'left',
    width: 170,
  },
  {
    title: '完成时间',
    dataIndex: 'finishTime',
    align: 'left',
    width: 170,
  },
  {
    title: '订单状态',
    dataIndex: 'orderstatusValue',
    align: 'left',
    width: 100,
  },
  {
    title: '患者地址',
    dataIndex: 'address',
    align: 'left',
    width: 100,
  },
];

/**
 * 订单时间
 * 护理上门时间
 * 护理分类 Select
 * 护理人员科室 Select
 */
const userStore = useUserStoreWithOut();

export const formSchema: FormSchema[] = [
  {
    field: 'orderTime',
    component: 'RangePicker',
    label: '订单时间',
    colProps: { span: 6 },
  },
  {
    field: 'nurseTime',
    component: 'RangePicker',
    label: '护理上门时间',
    colProps: { span: 6 },
  },
  {
    field: 'itemTypeId',
    component: 'ApiSelect',
    label: '护理分类',
    componentProps: {
      api: () => getDictItemList(DictEnum.NURSING_SERVICE_CATEGORY),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      getPopupContainer: () => document.body,
      showSearch: true,
      optionFilterProp: 'label',
    },
    colProps: { span: 6 },
  },
  {
    field: 'nurseId',
    component: 'ApiSelect',
    label: '护理人员科室',
    componentProps: {
      api: () => getNurseDeptList(userStore.getUserInfo.orgId),
      labelField: 'deptName',
      valueField: 'deptId',
      getPopupContainer: () => document.body,
      showSearch: true,
      optionFilterProp: 'label',
    },
    colProps: { span: 6 },
  },
];
