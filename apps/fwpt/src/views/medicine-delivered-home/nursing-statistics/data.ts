import { DictEnum, getDictItemList, getNurseDeptList } from '@ft/internal/api';
import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { useUserStoreWithOut } from '@ft/internal/store/modules/user';

/**
 * 护理人员科室
 * 老年护理数
 * 外科护理
 * 内科护理
 * 社区护理
 * 儿科护理
 * 妇科护理
 */
export const columns: BasicColumn[] = [
  {
    title: '护理人员科室',
    dataIndex: 'nursingDepartment',
    align: 'left',
    width: 100,
  },
  {
    title: '老年护理数',
    dataIndex: 'oldNursingNum',
    align: 'left',
    width: 100,
  },
  {
    title: '外科护理',
    dataIndex: 'outNursingNum',
    align: 'left',
    width: 100,
  },
  {
    title: '内科护理',
    dataIndex: 'inNursingNum',
    align: 'left',
    width: 100,
  },
  {
    title: '社区护理',
    dataIndex: 'communityNursingNum',
    align: 'left',
    width: 100,
  },
  {
    title: '儿科护理',
    dataIndex: 'childNursingNum',
    align: 'left',
    width: 100,
  },
  {
    title: '妇科护理',
    dataIndex: 'womanNursingNum',
    align: 'left',
    width: 100,
  },
];

/**
 * 订单时间
 * 护理上门时间
 * 护理分类 Select
 * 护理人员科室 Select
 */
const userStore = useUserStoreWithOut();
export const formSchema: FormSchema[] = [
  {
    field: 'orderTime',
    component: 'RangePicker',
    label: '订单时间',
    colProps: { span: 6 },
  },
  {
    field: 'nurseTime',
    component: 'RangePicker',
    label: '护理上门时间',
    colProps: { span: 6 },
  },
  {
    field: 'itemTypeId',
    component: 'ApiSelect',
    label: '护理分类',
    componentProps: {
      api: () => getDictItemList(DictEnum.NURSING_SERVICE_CATEGORY),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      getPopupContainer: () => document.body,
      showSearch: true,
      optionFilterProp: 'label',
    },
    colProps: { span: 6 },
  },
  {
    field: 'deptCode',
    component: 'ApiSelect',
    label: '护理人员科室',
    componentProps: {
      api: () => getNurseDeptList(userStore.getUserInfo.orgId),
      labelField: 'deptName',
      valueField: 'deptCode',
      getPopupContainer: () => document.body,
      showSearch: true,
      optionFilterProp: 'label',
    },
    colProps: { span: 6 },
  },
];
