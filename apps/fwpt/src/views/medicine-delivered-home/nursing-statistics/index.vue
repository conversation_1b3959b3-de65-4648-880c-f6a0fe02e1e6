<script setup lang="ts">
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { useRequest } from '@ft/request';
  import { DictEnum, getDictItemList } from '@ft/internal/api';
  import { computed } from 'vue';
  import { Button } from 'ant-design-vue';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { exportUtil } from '@ft/internal/utils';
  import { formSchema } from './data';
  import {
    getNurseOrderStatisticsPage,
    nurseOrderStatisticsExport,
  } from '/@/api/medicine-delivered-home/nursing-statistics';

  /**
   * 护理统计
   */

  const { data: columnsList } = useRequest(() =>
    getDictItemList(DictEnum.NURSING_SERVICE_CATEGORY),
  );
  const columns = computed(() => {
    return (
      columnsList.value?.map((item) => {
        return {
          title: item.dictItemName,
          dataIndex: 'column' + item.dictItemCode,
          width: 100,
        };
      }) || []
    );
  });

  const [registerTable, tableIns] = useTable({
    api: getNurseOrderStatisticsPage,
    formConfig: {
      colon: true,
      labelWidth: 100,
      showAdvancedButton: false,
      actionColOptions: {
        span: 24,
        style: {
          textAlign: 'right',
        },
      },
      schemas: formSchema,
      fieldMapToTime: [
        ['orderTime', ['orderStartTime', 'orderEndTime'], 'YYYY-MM-DD'],
        ['nurseTime', ['nurseStartTime', 'nurseEndTime'], 'YYYY-MM-DD'],
      ],
    },
    fetchSetting: {
      sizeField: 'pageSize',
      pageField: 'pageNum',
      listField: 'records',
    },
    useSearchForm: true,
    showIndexColumn: false,
    columns: computed(() => {
      return [
        {
          title: '护理人员科室',
          dataIndex: 'column0',
          width: 120,
        },
        ...columns.value,
      ];
    }),
    bordered: false,
    size: 'small',
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
  });

  function createActions(record): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '护理记录列表',
        type: 'link',
        size: 'small',
        onClick: handleNursingDetails.bind(null, record),
      },
    ];
    return actions;
  }
  const go = useGo();
  function handleNursingDetails(record) {
    go({
      name: 'NursingStatisticsNursingRecords',
      query: {
        deptCode: record.deptCode,
      },
    });
  }
  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(
    nurseOrderStatisticsExport,
    {
      manual: true,
      showSuccessMessage: true,
    },
  );
  function onExportExpert() {
    exportUtil(
      exportExpertRunAsync({
        ...tableIns.getForm().getFieldsValue(),
      }),
    );
  }
</script>

<template>
  <div class="w-full h-full pr-4 rounded rounded-lt-none">
    <BasicTable @register="registerTable" class="ft-main-table">
      <template #headerTop>
        <div class="text-16px font-500 mb-4">医服到家护理统计</div>
      </template>
      <template #tableTitle> </template>
      <template #toolbar>
        <Button :loading="exportLoading" @click="onExportExpert"> 批量导出 </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record)" />
        </template>
        <template v-if="column.dataIndex !== 'column0' && column.dataIndex !== 'action'">
          <span>{{ record[column.dataIndex] || 0 }} </span>
        </template>
      </template>
    </BasicTable>
  </div>
</template>
