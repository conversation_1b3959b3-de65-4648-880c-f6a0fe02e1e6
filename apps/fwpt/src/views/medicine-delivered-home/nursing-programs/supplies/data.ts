import { DictEnum, getDictItemList } from '@ft/internal/api';
import type { FormSchema } from '@ft/internal/components/Form';
import type { BasicColumn } from '@ft/internal/components/Table';

/**
 * 耗材编码
 * 耗材名称
 * 耗材状态
 */
export const SearchSchemas: FormSchema[] = [
  {
    field: 'code',
    label: '耗材编码',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'name',
    label: '耗材名称',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'status',
    label: '耗材状态',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '启用',
          value: 1,
        },
        {
          label: '禁用',
          value: 0,
        },
      ],
    },
    colProps: { span: 6 },
  },
];

/**
  耗材编码
  耗材名称
  耗材类型
  耗材单位
  耗材数量
  耗材单价（元）
  启用状态
  创建时间
  创建人员
 */
export const columns: BasicColumn[] = [
  {
    title: '耗材编码',
    dataIndex: 'code',
    width: 150,
  },
  {
    title: '耗材名称',
    dataIndex: 'name',
    width: 150,
  },
  {
    title: '耗材类型',
    dataIndex: 'categoryName',
    width: 150,
  },
  {
    title: '耗材单位',
    dataIndex: 'unit',
    width: 150,
  },
  {
    title: '耗材数量',
    dataIndex: 'quantity',
    width: 150,
  },
  {
    title: '耗材单价（元）',
    dataIndex: 'price',
    width: 150,
  },
  {
    title: '启用状态',
    dataIndex: 'status',
    width: 150,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
  },
  {
    title: '创建人员',
    dataIndex: 'createUser',
    width: 150,
  },
];

/**
  耗材编码
  耗材名称
  耗材类型
  耗材单位
  耗材数量
  耗材单价（元）
  耗材状态
 */
export const formSchema: FormSchema[] = [
  {
    field: 'id',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'code',
    label: '耗材编码',
    component: 'Input',
    required: true,
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    field: 'name',
    label: '耗材名称',
    component: 'Input',
    required: true,
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
  },

  {
    field: 'categoryCode',
    label: '耗材类型',
    component: 'ApiSelect',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
    required: true,
    componentProps: () => {
      return {
        api: () => getDictItemList(DictEnum.NURSING_CONSUMABLES),
        getPopupContainer: () => document.body,
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        showSearch: true,
        optionFilterProp: 'label',
      };
    },
  },
  {
    field: 'unit',
    label: '耗材单位',
    component: 'Input',
    required: true,
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    field: 'quantity',
    label: '耗材数量',
    component: 'InputNumber',
    required: true,
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
    componentProps: {
      min: 1,
    },
    rules: [{ pattern: /^\d+$/, message: '请输入大于等于1的正整数' }],
    defaultValue: 1,
  },
  {
    field: 'price',
    label: '耗材单价（元）',
    component: 'InputNumber',
    required: true,
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
    componentProps: {
      min: 0,
    },
  },
  {
    field: 'status',
    label: '耗材状态',
    component: 'Select',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 },
      ],
    },
    defaultValue: 1,
  },
];
