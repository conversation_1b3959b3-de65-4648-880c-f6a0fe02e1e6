<script setup lang="ts">
  import { computed, ref, unref } from 'vue';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { Transfer } from 'ant-design-vue';
  import { useRequest } from '@ft/request';
  import {
    batchSavePackContent,
    getNursingConsumablesList,
    getNursingPackDetailList,
    getNursingProjectList,
  } from '/@/api/medicine-delivered-home/nursing-programs';
  import { omit } from 'lodash-es';

  const emit = defineEmits(['register', 'success']);
  const mode = ref<'add' | 'edit'>('add');
  const type = ref<number>();
  const id = ref('');
  const activeRow = ref<any>();
  const title = computed(() => {
    return type.value === 1 ? '维护项目' : '维护耗材';
  });
  const targetKeys = ref<string[]>([]);
  const { data: mockData, runAsync } = useRequest(
    () =>
      type.value === 1
        ? getNursingProjectList({
            pageNum: 1,
            pageSize: 9999,
            status: 1,
          })
        : getNursingConsumablesList({
            pageNum: 1,
            pageSize: 9999,
            status: 1,
          }),
    {
      showSuccessMessage: false,
      manual: true,
      onSuccess: (res: any) => {
        mockData.value = res.list;
      },
    },
  );
  const { data: existList, runAsync: getExistList } = useRequest(
    () => getNursingPackDetailList({ itemType: type.value, packId: id.value }),
    {
      showSuccessMessage: false,
      manual: true,
      onSuccess: (res: any) => {
        existList.value = res.list;
        targetKeys.value = res.list.map((item) => item.optionCode) || [];
        console.log(targetKeys.value, 'targetKeys');
      },
    },
  );
  const getDataSource = computed(() => {
    return unref(mockData)?.map((item) => {
      return {
        key: item.nursingProjectCode || item.code,
        title: item.nursingProjectName || item.name,
        description: item.price,
        chosen: false,
      };
    });
  });
  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data?.mode;
    type.value = data?.type;
    id.value = data?.parentItemId;
    activeRow.value = data?.activeRow;
    runAsync();
    getExistList();
  });
  // const { createMessage } = useMessage();
  const { loading, runAsync: runAsyncSave } = useRequest(batchSavePackContent, {
    showSuccessMessage: true,
    manual: true,
    // onBefore: async () => {
    //   if (targetKeys.value.length === 0) {
    //     createMessage.warning('请选择');
    //     return false;
    //   } else {
    //     return true;
    //   }
    // },
    onSuccess: () => {
      emit('success');
      closeModal();
    },
  });
  const selectKeys = computed(() => {
    const filterData = mockData.value?.filter((item) =>
      targetKeys.value.includes(item.nursingProjectCode || item.code),
    );
    const res = filterData?.map((item) => {
      const exist = existList.value?.find(
        (item2) => (item.nursingProjectCode || item.code) === item2.optionCode,
      );
      if (exist) {
        return exist;
      } else {
        return {
          ...omit(item, ['id', 'code', 'name', 'nursingProjectCode', 'nursingProjectName']),
          optionId: item.id,
          itemType: type.value,
          packId: id.value,
          optionCode: item.nursingProjectCode || item.code,
          optionName: item.nursingProjectName || item.name,
          price: item.price,
          quantity: 1,
        };
      }
    });
    return res;
  });
  const handleOk = () => {
    runAsyncSave({
      packId: id.value,
      itemType: type.value,
      optionItemList: selectKeys.value,
    });
  };
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="title"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    width="60%"
    @register="register"
    @ok="handleOk"
  >
    <div class="flex mb-4 gap-4">
      <div class="flex">
        <div class="text-#999">组套编码：</div>
        <div class="text-#333">{{ activeRow?.itemCode }}</div>
      </div>
      <div class="flex">
        <div class="text-#999">组套名称：</div>
        <div class="text-#333">{{ activeRow?.itemName }}</div>
      </div>
    </div>
    <Transfer
      v-model:target-keys="targetKeys"
      :data-source="getDataSource"
      :list-style="{
        width: '523px',
        height: '400px',
      }"
      :titles="['', '']"
      pagination
    >
      <template #render="item">
        <div class="flex gap-0">
          <span class="flex-1 overflow-hidden truncate"
            >{{ item.key + ' ' + item.title + ' ' + item.description }}
          </span>
        </div>
      </template>
      <template #notFoundContent>
        <span>没数据</span>
      </template>
    </Transfer>
  </BasicModal>
</template>
