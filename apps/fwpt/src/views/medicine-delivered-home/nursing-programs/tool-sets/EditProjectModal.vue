<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { EditProjectSchemas } from './data';
  import { addNursingPack, editNursingPack } from '/@/api/medicine-delivered-home/nursing-programs';

  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm({
    schemas: EditProjectSchemas,
    labelWidth: 100,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });
  const mode = ref<'add' | 'edit'>('add');
  const title = computed(() => {
    return mode.value === 'edit' ? '编辑组套' : '新增组套';
  });
  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data?.mode;
    if (!data.record) return;
    formAction.setFieldsValue(data.record);
  });
  const { loading, runAsync: runAsyncSaveProject } = useRequest(
    (params) => {
      return mode.value === 'add' ? addNursingPack(params) : editNursingPack(params);
    },
    {
      showSuccessMessage: true,
      manual: true,
      onSuccess: () => {
        emit('success');
        closeModal();
      },
    },
  );
  function onOk() {
    formAction.validate().then((values) => {
      runAsyncSaveProject(values);
    });
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="title"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    width="680px"
    @register="register"
    @ok="onOk"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
