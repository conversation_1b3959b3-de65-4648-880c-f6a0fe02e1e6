import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { DictEnum, getDictItemList } from '@ft/internal/api';
export const SearchFormSchemas: FormSchema[] = [
  {
    field: 'itemCode',
    label: '项目组套编码',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'itemName',
    label: '项目组套名称',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'itemTypeId',
    label: '服务分类',
    component: 'ApiSelect',
    componentProps: () => ({
      api: () => getDictItemList(DictEnum.NURSING_SERVICE_CATEGORY),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      getPopupContainer: () => document.body,
    }),
    colProps: { span: 6 },
  },
];

/**
 * 组套编码
 * 组套名称
 * 护理级别
 * 服务分类
 * 护理内容
 * 服务提示
 * 服务注意事项
 * 总价
 */
export const columns: BasicColumn[] = [
  {
    title: '组套编码',
    dataIndex: 'itemCode',
    width: 100,
    align: 'left',
  },
  {
    title: '组套名称',
    dataIndex: 'itemName',
    width: 100,
    align: 'left',
  },
  {
    title: '护理级别',
    dataIndex: 'levelName',
    width: 120,
    align: 'left',
  },
  {
    title: '服务分类',
    dataIndex: 'itemTypeName',
    width: 100,
    align: 'left',
  },
  {
    title: '护理内容',
    dataIndex: 'nursingContent',
    width: 100,
    align: 'left',
  },
  {
    title: '服务提示',
    dataIndex: 'warn',
    width: 100,
    align: 'left',
  },
  {
    title: '服务注意事项',
    dataIndex: 'notice',
    width: 100,
    align: 'left',
  },
  {
    title: '总价',
    dataIndex: 'packPrice',
    width: 100,
    align: 'left',
  },
];

/**
 * 服务分类
 * 护理级别
 * 组套编码
 * 组套名称
 * 护理内容
 * 服务提示
 * 服务注意事项
 * 知情同意内容
 * 状态
 * 组套总价
 */

export const EditProjectSchemas: FormSchema[] = [
  {
    field: 'packId',
    label: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'itemTypeName',
    label: '服务分类名称',
    component: 'Input',
    show: false,
  },
  {
    field: 'itemTypeId',
    label: '服务分类',
    component: 'ApiSelect',
    required: true,
    componentProps: ({ formModel }) => ({
      api: () => getDictItemList(DictEnum.NURSING_SERVICE_CATEGORY),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      getPopupContainer: () => document.body,
      onChange(_, opt) {
        if (opt?.label) formModel.itemTypeName = opt?.label;
      },
    }),
    colProps: { span: 12 },
  },
  {
    field: 'levelName',
    label: '级别名称',
    component: 'Input',
    show: false,
  },
  {
    field: 'levelId',
    label: '护理级别',
    component: 'ApiSelect',
    required: true,
    componentProps: ({ formModel }) => ({
      api: () => getDictItemList(DictEnum.NURSING_LEVEL),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      getPopupContainer: () => document.body,
      onChange(_, opt) {
        if (opt?.label) formModel.levelName = opt?.label;
      },
    }),
    colProps: { span: 12 },
  },
  {
    field: 'itemCode',
    label: '组套编码',
    component: 'Input',
    required: true,
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    field: 'itemName',
    label: '组套名称',
    component: 'Input',
    required: true,
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    field: 'nursingContent',
    label: '护理内容',
    component: 'InputTextArea',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
    componentProps: {
      autoSize: { minRows: 3, maxRows: 3 },
    },
  },
  {
    field: 'warn',
    label: '服务提示',
    component: 'InputTextArea',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
    componentProps: {
      autoSize: { minRows: 3, maxRows: 3 },
    },
  },
  {
    field: 'notice',
    label: '服务注意事项',
    component: 'InputTextArea',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
    componentProps: {
      autoSize: { minRows: 3, maxRows: 3 },
    },
  },
  {
    field: 'agreementContent',
    label: '知情同意内容',
    component: 'InputTextArea',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
    componentProps: {
      autoSize: { minRows: 3, maxRows: 3 },
    },
  },
  {
    field: 'status',
    label: '项目状态',
    component: 'Select',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 },
      ],
    },
    defaultValue: 1,
  },

  {
    field: 'packPrice',
    label: '项目费用（元）',
    component: 'InputNumber',
    defaultValue: 0.0,
    show: false,
  },
];

/** 护理项目
 * 项目编码
 * 项目名称
 * 适用人群
 * 服务内容与方法
 * 项目单位
 * 项目单价
 * 组套内项目数量
 * 服务项目价格（元）
 * 服务提示
 * 服务流程
 * 服务注意事项
 */
export const subItemColumns: BasicColumn[] = [
  {
    title: '项目编码',
    dataIndex: 'optionCode',
    width: 100,
    align: 'left',
  },
  {
    title: '项目名称',
    dataIndex: 'optionName',
    width: 100,
    align: 'left',
  },
  {
    title: '适用人群',
    dataIndex: 'groupCategory',
    width: 100,
    align: 'left',
  },
  {
    title: '服务内容与方法',
    dataIndex: 'contentWay',
    width: 120,
    align: 'left',
  },
  {
    title: '项目单位',
    dataIndex: 'unit',
    width: 100,
    align: 'left',
  },
  {
    title: '项目单价（元）',
    dataIndex: 'price',
    width: 120,
    align: 'left',
  },
  {
    title: '组套内项目数量',
    dataIndex: 'quantity',
    width: 150,
    align: 'left',
  },
  {
    title: '服务项目价格（元）',
    dataIndex: 'totalPrice',
    width: 150,
    align: 'left',
  },
  {
    title: '服务提示',
    dataIndex: 'warn',
    width: 150,
    align: 'left',
  },
  {
    title: '服务流程',
    dataIndex: 'flow',
    width: 150,
    align: 'left',
  },
  {
    title: '服务注意事项',
    dataIndex: 'notice',
    width: 150,
    align: 'left',
  },
];

/**
 * 耗材编码
 * 耗材名称
 * 耗材类型
 * 耗材单位
 * 耗材单价（元）
 * 组套内耗材数量
 * 服务耗材价格（元）
 */

export const subItemColumns1: BasicColumn[] = [
  {
    title: '耗材编码',
    dataIndex: 'optionCode',
    width: 100,
    align: 'left',
  },
  {
    title: '耗材名称',
    dataIndex: 'optionName',
    width: 100,
    align: 'left',
  },
  {
    title: '耗材类型',
    dataIndex: 'categoryName',
    width: 100,
    align: 'left',
  },
  {
    title: '耗材单位',
    dataIndex: 'unit',
    width: 100,
    align: 'left',
  },
  {
    title: '耗材单价（元）',
    dataIndex: 'price',
    width: 120,
    align: 'left',
  },
  {
    title: '组套内耗材数量',
    dataIndex: 'quantity',
    width: 150,
    align: 'left',
  },
  {
    title: '服务耗材价格（元）',
    dataIndex: 'totalPrice',
    width: 150,
    align: 'left',
  },
];
