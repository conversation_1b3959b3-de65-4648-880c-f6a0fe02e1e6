<script setup lang="ts">
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import type { ActionItem, TableRowSelection } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { useModal } from '@ft/internal/components/Modal';
  import { BasicTitle } from '@ft/internal/components/Basic';
  import { InputNumber, Tabs } from 'ant-design-vue';
  import { useRequest } from '@ft/request';
  import { computed, ref, watch } from 'vue';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { SearchFormSchemas, columns, subItemColumns, subItemColumns1 } from './data';
  import EditProjectModal from './EditProjectModal.vue';
  import EditSubItemModal from './EditSubItemModal.vue';
  import {
    deleteNursingPack,
    editPackContentQuantity,
    getNursingPackDetailList,
    getNursingPackList,
  } from '/@/api/medicine-delivered-home/nursing-programs';

  const [registerForm, formAction] = useForm({
    schemas: SearchFormSchemas,
    labelWidth: 100,
    baseColProps: {
      span: 6,
      style: {
        textAlign: 'left',
      },
    },
    actionColOptions: { span: 6 },
    autoSubmitOnEnter: false,
    showActionButtonGroup: true,
  });
  function reset() {
    leftTableReload();
  }
  function handleSubmit(_values) {
    leftTableReload();
  }
  const selectedRowKeys = ref<any[]>([]);
  const id = computed(() => selectedRowKeys.value[0] || '');
  const rowSelection = computed<TableRowSelection>(() => {
    return {
      type: 'radio',
      selectedRowKeys: selectedRowKeys.value,
      onChange: (keys: any) => {
        if (keys.length === 0) {
          return;
        }
        selectedRowKeys.value = keys;
      },
    };
  });
  const activeRow = ref<any>();
  const [registerTable, { reload: leftTableReload, getDataSource: getDataSourceLeft }] = useTable({
    api: getNursingPackList,
    columns,
    dataSource: [{}],
    useSearchForm: false,
    formConfig: {
      labelWidth: 100,
      schemas: SearchFormSchemas,
    },
    actionColumn: {
      title: '操作',
      width: 100,
      dataIndex: 'action',
    },
    resizeHeightOffset: 16,
    // inset: true,
    beforeFetch: (params) => {
      return {
        ...params,
        ...formAction.getFieldsValue(),
      };
    },
    afterFetch: (data) => {
      selectedRowKeys.value = [data[0]?.packId];
      onRowClick(data[0]);
      return data;
    },
    rowKey: 'packId',
  });
  watch(id, () => {
    subItemTableIns.reload();
  });
  function onRowClick(record) {
    activeRow.value = record;
  }
  function onSelectionChange({ keys }) {
    const selectedRow = getDataSourceLeft().find((item) => item.packId === keys[0]);
    console.log(selectedRow, 'selectedRow', keys);
    if (selectedRow) {
      onRowClick(selectedRow);
    }
  }
  const [registerEditProjectModal, { openModal }] = useModal();

  function onAddProject() {
    openModal(true, {
      mode: 'add',
    });
  }

  function onEditProject(record) {
    openModal(true, {
      mode: 'edit',
      record,
    });
  }

  const { runAsync: delProjectRunAsync } = useRequest(deleteNursingPack, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      leftTableReload();
    },
  });

  function onDelProject(record) {
    delProjectRunAsync(record.packId);
  }

  function createActions(record, _column): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: onEditProject.bind(null, record),
      },
      {
        label: '删除',
        type: 'link',
        danger: true,
        popConfirm: {
          title: '确定删除该项目吗？',
          placement: 'topRight',
          okButtonProps: { danger: true },
          confirm: onDelProject.bind(null, record),
        },
      },
    ];
  }
  const activeTab = ref(1);
  const [registerSubItemTable, subItemTableIns] = useTable({
    api: getNursingPackDetailList,
    dataSource: [{}],
    columns: computed(() => (activeTab.value === 1 ? subItemColumns : subItemColumns1)),
    useSearchForm: false,
    immediate: false,
    // actionColumn: {
    //   title: '操作',
    //   width: 100,
    //   dataIndex: 'action',
    // },
    resizeHeightOffset: 16,
    beforeFetch: (params) => {
      return {
        ...params,
        itemType: activeTab.value,
        packId: id.value,
      };
    },
  });
  function createSubItemActions(record, _column): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: onEditSubItem.bind(null, record),
      },
      {
        label: '删除',
        danger: true,
        popConfirm: {
          title: '确定删除吗？',
          placement: 'topRight',
          confirm: onDelSubItem.bind(null, record),
          okButtonProps: {
            danger: true,
          },
        },
      },
    ];
  }
  const [registerEditSubItemModal, { openModal: openEditSubItemModal }] = useModal();
  function onAddSubItem() {
    openEditSubItemModal(true, {
      mode: 'add',
      type: activeTab.value,
      parentItemId: id.value,
      activeRow: activeRow.value,
    });
    // if (id.value !== '') {
    //   openEditSubItemModal(true, {
    //     mode: 'add',
    //     parentItemId: id.value,
    //   });
    // } else {
    //   createMessage.error('请先在左侧新增项目');
    // }
  }
  function onEditSubItem(record) {
    openEditSubItemModal(true, {
      mode: 'edit',
      parentItemId: id.value,
      record,
    });
  }
  const { runAsync: delSubRunAsync } = useRequest((_P) => Promise.resolve({}), {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      subItemChange();
    },
  });
  function onDelSubItem(record) {
    delSubRunAsync(record.id);
  }
  function subItemChange() {
    leftTableReload();
    subItemTableIns.reload();
  }
  const { runAsync: editPackContentQuantityRunAsync } = useRequest(editPackContentQuantity, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      subItemChange();
    },
  });
  function onSubItemNumChange(value, record) {
    console.log('onSubItemNumChange', value, record);
    editPackContentQuantityRunAsync({
      ...record,
      quantity: value,
    });
  }
</script>

<template>
  <div class="flex flex-col h-[calc(100%-16px)] w-[calc(100%-16px)]">
    <div class="bg-white p-4 mb-4">
      <BasicForm @register="registerForm" @reset="reset" @submit="handleSubmit" />
    </div>
    <div class="flex-1 flex">
      <div class="flex-1 of-hidden bg-white rounded rounded-lt-none p-4 flex flex-col">
        <div class="text-base font-bold">护理服务项目组套列表</div>
        <div class="flex-1 of-hidden">
          <BasicTable
            @register="registerTable"
            :rowSelection="rowSelection"
            @selection-change="onSelectionChange"
          >
            <template #tableTitle>
              <Button type="primary" @click="onAddProject">新增组套</Button>
            </template>
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'action'">
                <TableAction :divider="false" :actions="createActions(record, column)" />
              </template>
            </template>
          </BasicTable>
        </div>
      </div>
      <div class="flex-1 of-hidden bg-white rounded p-4 of-auto basis-0">
        <div class="project-info">
          <BasicTitle class="mb-4" span normal>组套详情</BasicTitle>
          <div class="flex">
            <div class="label leading-24px font-500">组套名称：</div>
            <div class="desc leading-24px text-secondary-text-color">
              {{ activeRow?.itemName }}
            </div>
          </div>
        </div>
        <div class="project-sub-table">
          <BasicTable @register="registerSubItemTable">
            <template #headerTop>
              <Tabs v-model:activeKey="activeTab" @change="subItemTableIns.reload()">
                <Tabs.TabPane :key="1" tab="护理项目" />
                <Tabs.TabPane :key="2" tab="护理耗材" />
              </Tabs>
            </template>
            <template #tableTitle>
              <Button :disabled="!id" v-if="activeTab === 1" type="primary" @click="onAddSubItem"
                >维护项目</Button
              >
              <Button :disabled="!id" v-if="activeTab === 2" type="primary" @click="onAddSubItem"
                >维护耗材</Button
              >
            </template>
            <template #bodyCell="{ record, column }">
              <template v-if="column.dataIndex === 'quantity'">
                <InputNumber
                  v-model:value="record[column.dataIndex]"
                  @change="onSubItemNumChange($event, record)"
                />
              </template>
              <template v-if="column.dataIndex === 'action'">
                <TableAction :divider="false" :actions="createSubItemActions(record, column)" />
              </template>
            </template>
          </BasicTable>
        </div>
      </div>
    </div>
    <EditProjectModal @register="registerEditProjectModal" @success="leftTableReload" />
    <EditSubItemModal @register="registerEditSubItemModal" @success="subItemChange" />
  </div>
</template>
