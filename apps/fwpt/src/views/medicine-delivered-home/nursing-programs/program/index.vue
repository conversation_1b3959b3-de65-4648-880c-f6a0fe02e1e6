<script setup lang="ts">
  import { Button } from '@ft/internal/components/Button';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Switch, Upload } from 'ant-design-vue';
  import { useModal } from '@ft/internal/components/Modal';
  import type { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import { SearchSchemas, columns } from './data';
  import AddModal from './AddModal.vue';
  import {
    deleteNursingProject,
    downloadNursingProjectTemplate,
    editNursingProject,
    exportNursingProject,
    getNursingProjectList,
    importNursingProject,
  } from '/@/api/medicine-delivered-home/nursing-programs';
  const [registerTable, tableIns] = useTable({
    api: getNursingProjectList,
    dataSource: [{}],
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas: SearchSchemas,
    },
    columns,
    actionColumn: {
      title: '操作',
      dataIndex: 'action',
      width: 100,
    },
    afterFetch: (data) => {
      data.forEach((item) => {
        item.loading = false;
      });
      return data;
    },
  });

  const [registerAdd, { openModal }] = useModal();
  function handleEdit(record) {
    openModal(true, {
      mode: 'edit',
      record,
    });
  }
  function handleAdd() {
    openModal(true, {
      mode: 'add',
    });
  }

  const { runAsync: delProjectRunAsync } = useRequest(deleteNursingProject, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      tableIns.reload();
    },
  });

  function handleDel(record) {
    delProjectRunAsync(record.id);
  }

  function createActions(record, _column): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },

      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: handleDel.bind(null, record),
          okButtonProps: {
            danger: true,
          },
        },
      },
    ];
  }
  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(
    exportNursingProject,
    {
      manual: true,
      showSuccessMessage: true,
    },
  );
  function onExportExpert() {
    exportUtil(exportExpertRunAsync(tableIns.getForm().getFieldsValue()));
  }
  const { loading: templateLoading, runAsync: runAsyncTemplateDownload } = useRequest(
    downloadNursingProjectTemplate,
    {
      manual: true,
    },
  );
  function onDownloadTemplate() {
    exportUtil(runAsyncTemplateDownload());
  }
  const { loading: importLoading, runAsync: runAsyncBatchImportImport } = useRequest(
    importNursingProject,
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess() {
        tableIns.reload();
      },
    },
  );

  function onImport(e: UploadRequestOption<any>) {
    console.log(e);
    const { file } = e;
    runAsyncBatchImportImport({
      // @ts-ignore
      file,
    });
  }
  const { runAsync: editRunAsync } = useRequest(editNursingProject, {
    manual: true,
    showSuccessMessage: true,
    onBefore([record]) {
      record.loading = true;
    },
    onSuccess() {
      tableIns.reload();
    },
    onFinally([record]) {
      record.loading = false;
    },
  });
  function onSwitchStatus(checked, record) {
    editRunAsync({
      ...record,
      status: checked,
    });
  }
</script>

<template>
  <div class="h-[calc(100%-10px)] w-[calc(100%-16px)] rounded rounded-lt-none">
    <BasicTable @register="registerTable">
      <template #headerTop>
        <div class="text-base font-bold mb-3">护理项目列表</div>
      </template>
      <template #tableTitle>
        <Button class="mr-2" type="primary" @click="handleAdd"> 新增项目 </Button>
      </template>
      <template #toolbar>
        <Button :loading="templateLoading" @click="onDownloadTemplate"> 模板下载 </Button>
        <Upload :max-count="1" :show-upload-list="false" :custom-request="onImport">
          <Button :loading="importLoading"> 批量导入 </Button>
        </Upload>
        <Button :loading="exportLoading" @click="onExportExpert"> 批量导出 </Button>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'status'">
          <Switch
            :loading="record.loading"
            :checkedValue="1"
            :unCheckedValue="0"
            v-model:checked="record[column.dataIndex]"
            @change="onSwitchStatus($event, record)"
          />
        </template>
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
    <AddModal @register="registerAdd" @success="tableIns.reload" />
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }

    .vben-basic-table-form-container .ant-form,
    .vben-basic-table .ant-table-wrapper {
      border-radius: 4px;
    }

    .vben-basic-table-form-container .ant-form {
      border-top-left-radius: 0;
      margin-bottom: 10px;
    }
  }
</style>
