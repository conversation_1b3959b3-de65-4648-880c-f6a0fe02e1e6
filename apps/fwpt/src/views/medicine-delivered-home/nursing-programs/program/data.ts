import type { FormSchema } from '@ft/internal/components/Form';
import type { BasicColumn } from '@ft/internal/components/Table';

/**
 * 项目编码
 * 项目名称
 * 项目状态
 */
export const SearchSchemas: FormSchema[] = [
  {
    field: 'nursingProjectCode',
    label: '项目编码',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'nursingProjectName',
    label: '项目名称',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'status',
    label: '项目状态',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '启用',
          value: 1,
        },
        {
          label: '禁用',
          value: 0,
        },
      ],
    },
    colProps: { span: 6 },
  },
];

/**
  项目编码
  项目名称
  适用人群
  服务内容与方法
  服务提示
  服务流程
  服务注意事项
  项目单位
  项目数量
  项目单价（元）
  项目状态
 */
export const columns: BasicColumn[] = [
  {
    title: '项目编码',
    dataIndex: 'nursingProjectCode',
    width: 150,
  },
  {
    title: '项目名称',
    dataIndex: 'nursingProjectName',
    width: 150,
  },
  {
    title: '适用人群',
    dataIndex: 'groupCategory',
    width: 150,
  },
  {
    title: '服务内容与方法',
    dataIndex: 'contentWay',
    width: 150,
  },
  {
    title: '服务提示',
    dataIndex: 'warn',
    width: 150,
  },
  {
    title: '服务流程',
    dataIndex: 'flow',
    width: 150,
  },
  {
    title: '服务注意事项',
    dataIndex: 'notice',
    width: 150,
  },
  {
    title: '项目单位',
    dataIndex: 'unit',
    width: 150,
  },
  {
    title: '项目数量',
    dataIndex: 'quantity',
    width: 150,
  },
  {
    title: '项目单价（元）',
    dataIndex: 'price',
    width: 150,
  },
  {
    title: '项目状态',
    dataIndex: 'status',
    width: 150,
  },
];

/**
  项目编码
  项目名称
  适用人群
  服务内容与方法
  服务提示
  服务流程
  服务注意事项
  项目单位
  项目数量
  项目单价（元）
  项目状态
 */
export const formSchema: FormSchema[] = [
  {
    field: 'id',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'nursingProjectCode',
    label: '项目编码',
    component: 'Input',
    required: true,
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    field: 'nursingProjectName',
    label: '项目名称',
    component: 'Input',
    required: true,
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    field: 'groupCategory',
    label: '适用人群',
    component: 'InputTextArea',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
    componentProps: {
      autoSize: { minRows: 3, maxRows: 3 },
    },
  },
  {
    field: 'contentWay',
    label: '服务内容与方法',
    component: 'InputTextArea',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
    componentProps: {
      autoSize: { minRows: 3, maxRows: 3 },
    },
  },
  {
    field: 'warn',
    label: '服务提示',
    component: 'InputTextArea',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
    componentProps: {
      autoSize: { minRows: 3, maxRows: 3 },
    },
  },
  {
    field: 'flow',
    label: '服务流程',
    component: 'InputTextArea',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
    componentProps: {
      autoSize: { minRows: 3, maxRows: 3 },
    },
  },
  {
    field: 'notice',
    label: '服务注意事项',
    component: 'InputTextArea',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
    componentProps: {
      autoSize: { minRows: 3, maxRows: 3 },
    },
  },
  {
    field: 'unit',
    label: '项目单位',
    component: 'Input',
    required: true,
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    field: 'quantity',
    label: '项目数量',
    component: 'InputNumber',
    required: true,
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
    componentProps: {
      min: 1,
    },
    rules: [{ pattern: /^\d+$/, message: '请输入大于等于1的正整数' }],
    defaultValue: 1,
  },
  {
    field: 'price',
    label: '项目单价（元）',
    component: 'InputNumber',
    required: true,
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
    componentProps: {
      min: 0,
    },
  },
  {
    field: 'status',
    label: '项目状态',
    component: 'Select',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 },
      ],
    },
    defaultValue: 1,
  },
];
