<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { computed, ref } from 'vue';
  import { formSchema } from './data';
  import {
    addNursingProject,
    editNursingProject,
  } from '/@/api/medicine-delivered-home/nursing-programs';

  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm({
    schemas: formSchema,
    labelWidth: 120,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });
  const mode = ref('');
  const getTitle = computed(() => (mode.value === 'edit' ? '编辑项目' : '新增项目'));
  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data.mode;
    if (!data.record) return;
    formAction.setFieldsValue(data.record);
  });

  const { loading, runAsync: runAsyncSaveProject } = useRequest(
    (params) => (mode.value === 'edit' ? editNursingProject(params) : addNursingProject(params)),
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess: () => {
        emit('success');
        closeModal();
      },
    },
  );
  function onOk() {
    formAction.validate().then((values) => {
      runAsyncSaveProject(values);
    });
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="getTitle"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    @register="register"
    @ok="onOk"
    :min-height="120"
    centered
    width="680px"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
