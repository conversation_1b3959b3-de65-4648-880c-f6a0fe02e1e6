import type { FormSchema } from '@ft/internal/components/Table';
import { buildShortUUID } from '@ft/internal/utils/uuid';
import type { NursingVisitContentList } from '/@/api/medicine-delivered-home/nursing-template';

export const ComponentList = [
  {
    component: 'Input',
    name: '输入框',
  },
  {
    component: 'Select',
    name: '下拉选择框',
    optional: true,
  },
  {
    component: 'RadioGroup',
    name: '单选',
    optional: true,
    group: true,
  },
  {
    component: 'CheckboxGroup',
    name: '多选',
    optional: true,
    group: true,
  },
  {
    component: 'DatePicker',
    name: '日期',
  },
];

export const optionComponents = ComponentList.filter((i) => i.optional).map((i) => i.component);
export const groupComponents = ComponentList.filter((i) => i.group).map((i) => i.component);

export function genSchemas({ content }: { content: NursingVisitContentList[] }) {
  // 按 dataHeader 分组
  const groupedContent = content.reduce((acc, item) => {
    const header = item.dataHeader || '其他';
    if (!acc[header]) {
      acc[header] = [];
    }
    acc[header].push(item);
    return acc;
  }, {} as Record<string, NursingVisitContentList[]>);

  const schemas: any[] = [];
  let groupCount = 0;

  Object.entries(groupedContent).forEach(([header, items]) => {
    // if (header !== '其他') {
    groupCount++;
    // 添加分组标题
    schemas.push({
      field: `moduleName${groupCount}`,
      component: 'Divider',
      label: header,
      colSlot: 'moduleName',
      colProps: { span: 24 },
    } as FormSchema);
    // }

    // 添加分组内的表单项
    items.forEach((item) => {
      schemas.push({
        label: `${item.dataName}`,
        field: item.id || buildShortUUID(),
        component: item.dataType,
        componentProps: optionComponents.includes(item.dataType)
          ? {
              options:
                item.dataItemList?.map((item) => {
                  return { label: item.dataItem, value: item.dataItem };
                }) ?? [],
            }
          : {
              style: { width: '100%' },
              ...(item.dataType === 'DatePicker'
                ? {
                    format: 'YYYY-MM-DD',
                    valueFormat: 'YYYY-MM-DD',
                  }
                : {}),
            },
        colProps: { span: 24 },
        itemProps: {
          labelCol: { span: 4 },
          wrapperCol: groupComponents.includes(item.dataType) ? { span: 20 } : { span: 6 },
        },
      });
    });
  });

  return schemas;
}
