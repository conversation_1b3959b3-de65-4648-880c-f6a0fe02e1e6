<script setup lang="ts">
  import { Select } from 'ant-design-vue';
  import { computed } from 'vue';
  import { useRequest } from '@ft/request';
  import { DictEnum, getDictItemList } from '@ft/internal/api';
  import type { NursingServiceItemTypeList } from '/@/api/medicine-delivered-home/nursing-template';

  const props = defineProps<{
    value?: NursingServiceItemTypeList[];
  }>();

  const emit = defineEmits(['update:value', 'change']);
  const state = computed({
    get: () => props.value?.map((item) => item.itemId),
    set: (newVal) => {
      const val = options.value
        ?.filter((item) => newVal?.includes(item.value))
        .map((i) => {
          return {
            itemId: i.value,
            itemName: i.label,
          };
        });
      emit('update:value', val);
      emit('change', val);
    },
  });

  const { data } = useRequest(() => getDictItemList(DictEnum.NURSING_SERVICE_CATEGORY));

  const options = computed(() => {
    return [
      {
        label: '全部',
        value: '99',
      },
      ...(data.value?.map((item) => ({
        ...item,
        label: item.dictItemName,
        value: item.dictItemCode,
      })) ?? []),
    ];
  });
</script>

<template>
  <Select
    class="w-full"
    v-bind="$attrs"
    v-model:value="state"
    mode="multiple"
    :maxTagCount="4"
    :options="options"
    showSearch
    optionFilterProp="label"
  />
</template>
