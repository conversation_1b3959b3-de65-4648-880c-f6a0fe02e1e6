<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { computed, ref } from 'vue';
  import { BasicTitle } from '@ft/internal/components/Basic';
  import { useRequest } from '@ft/request';
  import { genSchemas } from './data';
  import { getNursingDocumentTemplateDetail } from '/@/api/medicine-delivered-home/nursing-template';

  defineEmits(['register']);

  const templateName = ref('');

  const getModalTitle = computed(() => {
    return '护理文书模板预览';
  });
  const getModalWidth = computed(() => {
    return '85%';
  });

  const [registerForm, formAction] = useForm({
    showActionButtonGroup: false,
    labelWidth: 150,
  });

  const { runAsync: getTemplateDetailRun } = useRequest(getNursingDocumentTemplateDetail, {
    manual: true,
  });

  const [register] = useModalInner((data) => {
    if (data.id) {
      // 查询模板详情
      getTemplateDetailRun(data.id).then((result) => {
        templateName.value = result.templateName;
        const schemas = genSchemas({
          content: result.visitContentList,
        });
        formAction.resetSchema(schemas);
      });
    }

    if (!data.formState) return;
    templateName.value = data.formState.templateName;
    const schemas = genSchemas({
      content: data.formState.visitContentList,
    });
    formAction.resetSchema(schemas);
  });
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="getModalTitle"
    :can-fullscreen="false"
    :width="getModalWidth"
    :min-height="650"
    :height="650"
    @register="register"
    :footer="null"
  >
    <div class="text-center text-2xl py-3 mb-3">{{ templateName }}</div>
    <BasicForm class="template-preview-form" @register="registerForm">
      <template #moduleName="{ schema }">
        <BasicTitle span>{{ schema?.label }}</BasicTitle>
      </template>
    </BasicForm>
  </BasicModal>
</template>
<style lang="less" scoped></style>
