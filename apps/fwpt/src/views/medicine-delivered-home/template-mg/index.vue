<template>
  <div class="h-[calc(100%-16px)] w-[calc(100%-10px)] bg-white rounded-b-4px rounded-tr-4px">
    <div class="p-16px">
      <BasicTable @register="registerTable">
        <template #headerTop>
          <div class="text-16px font-bold">模板管理</div>
          <Tabs size="small" v-model:activeKey="activeTab" @change="handleTabChange">
            <Tabs.TabPane tab="评价模板" :key="TemplateType.EvaluationTemplate" />
            <Tabs.TabPane tab="护理文书模板" :key="TemplateType.NursingDocumentTemplate" />
          </Tabs>
        </template>
        <template #tableTitle>
          <Button type="primary" @click="handleAdd">新增模板</Button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :actions="createActions(record)" :divider="false" />
          </template>
        </template>
      </BasicTable>
    </div>
    <EvaluationTemplateModal
      @register="registerEvaluationTemplateModal"
      @success="tableFn.reload"
    />
    <NursingDocPreviewModal @register="registerNursingDocPreviewModal" />
  </div>
</template>

<script lang="ts" setup>
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Tabs } from 'ant-design-vue';
  import { computed, nextTick, ref } from 'vue';
  import { Button } from '@ft/internal/components/Button';
  import { useModal } from '@ft/internal/components/Modal';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { useRequest } from '@ft/request';
  import { EvaluationTemplateColumns, NursingDocumentTemplateColumns } from './data';
  import EvaluationTemplateModal from './EvaluationTemplateModal.vue';
  import {
    deleteNursingDocumentTemplate,
    getNursingDocumentTemplatePage,
  } from '/@/api/medicine-delivered-home/nursing-template';
  import NursingDocPreviewModal from './nursing-doc-edit/PreviewModal.vue';
  import {
    deleteNursingEvaluateTemplate,
    getNursingEvaluateTemplatePage,
  } from '/@/api/medicine-delivered-home/evaluation-template';

  enum TemplateType {
    /** 评价模板 */
    EvaluationTemplate = 'evaluationTemplate',
    /** 护理文档模板 */
    NursingDocumentTemplate = 'nursingDocumentTemplate',
  }

  const apiMap = {
    [TemplateType.EvaluationTemplate]: getNursingEvaluateTemplatePage,
    [TemplateType.NursingDocumentTemplate]: getNursingDocumentTemplatePage,
  };

  const activeTab = ref(TemplateType.EvaluationTemplate);

  const columns = computed(() => {
    return activeTab.value === TemplateType.EvaluationTemplate
      ? EvaluationTemplateColumns
      : NursingDocumentTemplateColumns;
  });
  const api = computed(() => apiMap[activeTab.value]);

  const [registerTable, tableFn] = useTable({
    api,
    columns,
    actionColumn: {
      width: 140,
      dataIndex: 'action',
      title: '操作',
    },
  });

  function handleTabChange() {
    nextTick(tableFn.reload);
  }

  const [registerEvaluationTemplateModal, { openModal }] = useModal();

  const go = useGo();
  function handleAdd() {
    if (activeTab.value === TemplateType.EvaluationTemplate) {
      openModal(true);
    } else {
      go({
        name: 'TemplateMgNursingDocEdit',
      });
    }
  }

  function handleEdit(record: any) {
    if (activeTab.value === TemplateType.NursingDocumentTemplate) {
      go({
        name: 'TemplateMgNursingDocEdit',
        query: {
          mode: 'edit',
          templateId: record.id,
        },
      });
    } else {
      openModal(true, {
        mode: 'edit',
        record,
      });
    }
  }

  const { runAsync: runDeleteNursingDocumentTemplate } = useRequest(deleteNursingDocumentTemplate, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      tableFn.reload();
    },
  });
  const { runAsync: runDeleteEvaluationTemplate } = useRequest(deleteNursingEvaluateTemplate, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      tableFn.reload();
    },
  });

  function handleDelete(record: any) {
    if (activeTab.value === TemplateType.NursingDocumentTemplate) {
      runDeleteNursingDocumentTemplate(record.id);
    } else {
      runDeleteEvaluationTemplate(record.id);
    }
  }
  const [registerNursingDocPreviewModal, { openModal: openNursingDocPreviewModal }] = useModal();
  function handlePreview(record: any) {
    openNursingDocPreviewModal(true, record);
  }

  function createActions(record: any): ActionItem[] {
    return [
      {
        label: '查看',
        onClick: handlePreview.bind(null, record),
        ifShow: activeTab.value === TemplateType.NursingDocumentTemplate,
      },
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
      {
        label: '删除',
        danger: true,
        popConfirm: {
          title: '确定删除该模板吗？',
          confirm: handleDelete.bind(null, record),
        },
      },
    ];
  }
</script>

<style scoped></style>
