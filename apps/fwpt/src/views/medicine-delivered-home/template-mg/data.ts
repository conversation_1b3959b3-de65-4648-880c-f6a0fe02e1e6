import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';

/**
 * 评价等级
 * 满意程度
 * 满意度评分
 * 标签1
 * 标签2
 * 标签3
 * 标签4
 * 标签5
 * 标签6
 */
export const EvaluationTemplateColumns: BasicColumn[] = [
  { title: '评价等级', dataIndex: 'evaluationLevel', width: 120 },
  { title: '满意程度', dataIndex: 'satisfactionLevel', width: 120 },
  { title: '满意度评分', dataIndex: 'satisfactionScore', width: 120 },
  { title: '标签1', dataIndex: 'tag1', minWidth: 120 },
  { title: '标签2', dataIndex: 'tag2', minWidth: 120 },
  { title: '标签3', dataIndex: 'tag3', minWidth: 120 },
  { title: '标签4', dataIndex: 'tag4', minWidth: 120 },
  { title: '标签5', dataIndex: 'tag5', minWidth: 120 },
  { title: '标签6', dataIndex: 'tag6', minWidth: 120 },
  { title: '状态', dataIndex: 'enableStatus', width: 120 },
];

export const EvaluationTemplateSchema: FormSchema[] = [
  // id
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'evaluationLevel',
    label: '评价等级',
    component: 'Select',
    componentProps: {
      options: [
        { label: '一星', value: '1' },
        { label: '二星', value: '2' },
        { label: '三星', value: '3' },
        { label: '四星', value: '4' },
        { label: '五星', value: '5' },
      ],
      getPopupContainer: () => document.body,
    },
    colProps: { span: 8 },
    required: true,
  },
  {
    field: 'satisfactionLevel',
    label: '满意程度',
    component: 'Select',
    componentProps: {
      options: [
        /**
         * 1-非常满意，2-满意，3-一般，4-不满意，5-非常不满意
         */
        { label: '非常满意', value: '1' },
        { label: '满意', value: '2' },
        { label: '一般', value: '3' },
        { label: '不满意', value: '4' },
        { label: '非常不满意', value: '5' },
      ],
      getPopupContainer: () => document.body,
    },
    colProps: { span: 8 },
    required: true,
  },
  {
    field: 'satisfactionScore',
    label: '满意度评分',
    component: 'InputNumber',
    colProps: { span: 8 },
    required: true,
  },
  {
    field: 'tag1',
    label: '标签1',
    component: 'Input',
    colProps: { span: 8 },
    required: true,
  },
  {
    field: 'tag2',
    label: '标签2',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'tag3',
    label: '标签3',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'tag4',
    label: '标签4',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'tag5',
    label: '标签5',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'tag6',
    label: '标签6',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'enableStatus',
    label: '状态',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '启用', value: '0' },
        { label: '禁用', value: '1' },
      ],
      getPopupContainer: () => document.body,
    },
    defaultValue: '0',
    colProps: { span: 8 },
    required: true,
  },
];

/**
 * 模板名称
 * 服务分类
 * 创建时间
 * 创建人
 * 启用状态
 */
export const NursingDocumentTemplateColumns: BasicColumn[] = [
  { title: '模板名称', dataIndex: 'templateName', minWidth: 120 },
  { title: '服务分类', dataIndex: 'itemTypeDesc', minWidth: 120 },
  { title: '创建时间', dataIndex: 'createTime', width: 180 },
  { title: '创建人', dataIndex: 'createUser', width: 120 },
  { title: '启用状态', dataIndex: 'statusDesc', width: 120 },
];
