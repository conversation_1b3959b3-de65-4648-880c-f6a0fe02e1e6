<template>
  <BasicModal
    @register="register"
    :title="getTitle"
    width="900px"
    @ok="onOk"
    centered
    @cancel="onCancel"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts" setup>
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { computed, ref } from 'vue';
  import { useRequest } from '@ft/request';
  import { EvaluationTemplateSchema } from './data';
  import {
    getNursingEvaluateTemplateDetail,
    saveNursingEvaluateTemplate,
    updateNursingEvaluateTemplate,
  } from '/@/api/medicine-delivered-home/evaluation-template';

  const emit = defineEmits(['register', 'success']);
  const mode = ref<'add' | 'edit'>('add');

  const [registerForm, formAction] = useForm({
    labelWidth: 100,
    schemas: EvaluationTemplateSchema,
    showActionButtonGroup: false,
  });

  const { runAsync: getEvaluationTemplateDetail } = useRequest(getNursingEvaluateTemplateDetail, {
    manual: true,
  });

  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data.mode;
    if (data.mode === 'edit') {
      getEvaluationTemplateDetail(data.record.id).then((res) => {
        formAction.setFieldsValue(res);
      });
    }
  });

  function onCancel() {
    formAction.resetFields();
    closeModal();
  }

  const getTitle = computed(() => {
    return mode.value === 'add' ? '新增评价模板' : '编辑评价模板';
  });

  const { run: saveEvaluationTemplateRun } = useRequest(
    (params) =>
      mode.value === 'add'
        ? saveNursingEvaluateTemplate(params)
        : updateNursingEvaluateTemplate(params),
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess: () => {
        emit('success');
        formAction.resetFields();
        closeModal();
      },
    },
  );

  function onOk() {
    formAction.validate().then((values) => {
      saveEvaluationTemplateRun(values);
    });
  }
</script>
