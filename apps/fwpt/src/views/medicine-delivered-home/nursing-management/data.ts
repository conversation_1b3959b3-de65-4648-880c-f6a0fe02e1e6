import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import type { INurseOrder } from '/@/api/medicine-delivered-home/nursing-management';
import { getNurseListByClass } from '/@/api/medicine-delivered-home/nursing-management';
import { useUserStoreWithOut } from '@ft/internal/store/modules/user';

/**
 * 订单时间
 * 订单编号
 * 患者姓名
 * 患者性别
 * 患者年龄
 * 护理项目
 * 预约上门时间
 * 上门时间
 * 完成时间
 * 订单状态
 * 护理人员
 * 患者地址
 */
export const columns: BasicColumn[] = [
  {
    title: '订单时间',
    dataIndex: 'orderTime',
    width: 170,
  },
  {
    title: '订单编号',
    dataIndex: 'code',
    width: 130,
  },
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    width: 100,
  },
  {
    title: '患者性别',
    dataIndex: 'sex',
    width: 80,
    // customRender: ({ text }) => {
    //   return text === '1' ? '男' : text === '2' ? '女' : '';
    // },
  },
  {
    title: '患者年龄',
    dataIndex: 'age',
    width: 80,
  },
  {
    title: '组套名称',
    dataIndex: 'itemName',
    width: 100,
  },
  {
    title: '预约上门时间',
    dataIndex: 'appointTime',
    width: 170,
  },
  {
    title: '上门时间',
    dataIndex: 'visitTime',
    width: 170,
  },
  {
    title: '完成时间',
    dataIndex: 'finishTime',
    width: 170,
  },
  {
    title: '订单状态',
    dataIndex: 'orderstatusValue',
    width: 100,
  },
  {
    title: '护理人员',
    dataIndex: 'nurseName',
    width: 100,
  },
  {
    title: '患者地址',
    dataIndex: 'address',
    width: 100,
  },
];

/**
 * 订单时间
 * 订单编号
 * 患者姓名
 * 护理人员
 * 护理时间
 */
export const formSchema: FormSchema[] = [
  {
    field: 'orderTime',
    component: 'RangePicker',
    label: '订单时间',
    colProps: { span: 6 },
  },
  {
    field: 'code',
    component: 'Input',
    label: '订单编号',
    colProps: { span: 6 },
  },
  {
    field: 'userName',
    component: 'Input',
    label: '患者姓名',
    colProps: { span: 6 },
  },
  {
    field: 'nurseName',
    component: 'Input',
    label: '护理人员',
    colProps: { span: 6 },
  },
  {
    field: 'nurseTime',
    component: 'RangePicker',
    label: '护理时间',
    colProps: { span: 6 },
  },
];
const userStore = useUserStoreWithOut();
export const formSchemas = (type: string, info: INurseOrder): FormSchema[] => {
  return [
    {
      field: 'id',
      label: '订单id',
      component: 'Input',
      show: false,
    },
    {
      field: 'rejectReason',
      label: '拒单原因',
      required: true,
      component: 'InputTextArea',
      componentProps: {
        placeholder: '请输入拒单原因',
        autoSize: { minRows: 4, maxRows: 4 },
      },
      ifShow: type == 'reject',
      colProps: { span: 20 },
      itemProps: { wrapperCol: { span: 12 } },
    },
    {
      field: 'rejectReason',
      label: '护士拒单原因',
      required: true,
      component: 'InputTextArea',
      componentProps: {
        placeholder: '请输入拒单原因',
        autoSize: { minRows: 4, maxRows: 4 },
      },
      ifShow: type == 'nurseReject',
      colProps: { span: 20 },
      itemProps: { wrapperCol: { span: 12 } },
    },
    {
      field: 'nurseName',
      label: '指派执行人员',
      component: 'Input',
      show: false,
    },
    {
      field: 'nurseId',
      label: '指派执行人员',
      required: true,
      component: 'ApiSelect',
      componentProps: ({ formModel }) => {
        return {
          api: () =>
            info &&
            getNurseListByClass({
              orgId: userStore.getUserInfo?.orgId,
              classManageId: info?.mshItemClassId,
              appointTime: info?.appointTime,
              classTypeCode: info?.itemTypeId,
            }),
          labelField: 'nurseName',
          valueField: 'nurseId',
          getPopupContainer: () => document.body,
          onChange: (_, opt) => {
            if (opt?.label) formModel.nurseName = opt?.label;
          },
        };
      },
      ifShow: type == 'assign',
      colProps: { span: 20 },
      itemProps: { wrapperCol: { span: 12 } },
    },
  ];
};
