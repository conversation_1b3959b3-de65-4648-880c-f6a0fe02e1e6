<template>
  <div class="flex-1 overflow-y-auto basis-0 w-full pb-4">
    <BasicTitle span normal class="mt-2">患者基本信息</BasicTitle>
    <div class="flex items-center gap-4">
      <span>患者姓名：{{ patientBaseInfo?.patientName }}</span>
      <span>患者性别：{{ patientBaseInfo?.sex }}</span>
      <span>患者年龄：{{ patientBaseInfo?.age }}</span>
      <span>患者电话：{{ patientBaseInfo?.phone }}</span>
      <span>患者地址：{{ patientBaseInfo?.patientAddress }}</span>
    </div>
    <BasicTitle span normal class="mt-2">护理服务目的</BasicTitle>
    <div class="flex items-center gap-4">
      <span>服务分类：{{ patientBaseInfo?.itemTypeDesc }}</span>
    </div>
    <Spin :spinning="loading">
      <Empty
        v-if="!nurseDocumentContent || nurseDocumentContent?.visitContentDataList?.length === 0"
      />
      <template v-else>
        <template v-for="([header, items], index) in Object.entries(groupedContent)" :key="index">
          <BasicTitle span normal class="mt-2">{{ header }}</BasicTitle>
          <div class="grid grid-cols-3 gap-3">
            <div v-for="item in items" :key="item.id">
              {{ item.dataName }}:{{ item.dataValue }}
            </div>
          </div>
        </template>
      </template>
    </Spin>
  </div>
</template>

<script setup lang="ts">
  import { BasicTitle } from '@ft/internal/components/Basic';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { useRequest } from '@ft/request';
  import type { NurseDataList } from '/@/api/medicine-delivered-home/nursing-template';
  import {
    getNurseDocumentContent,
    getNurseDocumentPatientBaseInfo,
  } from '/@/api/medicine-delivered-home/nursing-template';
  import { computed } from 'vue';
  import { Empty, Spin } from 'ant-design-vue';

  const orderId = useRouteQuery('orderId', '', { transform: String });
  const { data: patientBaseInfo } = useRequest(getNurseDocumentPatientBaseInfo, {
    defaultParams: [orderId.value],
  });
  const { data: nurseDocumentContent, loading } = useRequest(getNurseDocumentContent, {
    defaultParams: [orderId.value],
  });

  const groupedContent = computed(() => {
    return (
      nurseDocumentContent.value?.visitContentDataList?.reduce((acc, item) => {
        acc[item.dataHeader || '其他'] = [...(acc[item.dataHeader || '其他'] || []), item];
        return acc;
      }, {} as Record<string, Partial<NurseDataList>[]>) || {}
    );
  });
</script>
