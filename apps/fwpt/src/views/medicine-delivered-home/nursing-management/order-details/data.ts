import { DictEnum, getDictItemList } from '@ft/internal/api';
import type { FormSchema } from '@ft/internal/components/Form';

/**
 * 症状内容
 * 附件
 */
export const formSchemas = (activeKeyTabs: number): FormSchema[] => {
  return [
    {
      field: 'diseaseSymptom',
      label: '症状内容',
      component: 'InputTextArea',
      componentProps: {
        placeholder: '请输入症状内容',
        autoSize: { minRows: 4, maxRows: 4 },
      },
      colProps: { span: 20 },
      ifShow: activeKeyTabs == 1,
      itemProps: { wrapperCol: { span: 12 } },
    },
    {
      field: 'symptomPictureList',
      label: '附件',
      component: 'UploadAsset',
      ifShow: activeKeyTabs == 1,
      componentProps: {
        uploadType: 'file',
        download: true,
      },
      colProps: {
        span: 10,
      },
      itemProps: {
        wrapperCol: {
          span: 12,
        },
      },
    },
    {
      field: 'remark',
      label: '服务备注',
      component: 'InputTextArea',
      componentProps: {
        placeholder: '请输入服务备注',
        autoSize: { minRows: 4, maxRows: 4 },
      },
      ifShow: activeKeyTabs == 2,
      colProps: { span: 20 },
      itemProps: { wrapperCol: { span: 12 } },
    },
    {
      field: 'evaluationLevel',
      label: '评价等级',
      component: 'ApiSelect',
      componentProps: ({ formModel }) => {
        return {
          api: () => getDictItemList(DictEnum.EVALUATION_LEVEL),
          labelField: 'dictItemName',
          valueField: 'dictItemCode',
          valueToString: formModel.evaluationLevel ? true : false,
        };
      },
      ifShow: activeKeyTabs == 4,
      colProps: { span: 14 },
      itemProps: { wrapperCol: { span: 10 } },
    },
    {
      field: 'satisfactionLevel',
      label: '满意程度',
      component: 'ApiSelect',
      componentProps: ({ formModel }) => {
        return {
          api: () => getDictItemList(DictEnum.SATISFACTION_LEVEL),
          labelField: 'dictItemName',
          valueField: 'dictItemCode',
          valueToString: formModel.satisfactionLevel ? true : false,
        };
      },
      ifShow: activeKeyTabs == 4,
      colProps: { span: 14 },
      itemProps: { wrapperCol: { span: 10 } },
    },
    {
      field: 'satisfactionScore',
      label: '满意度评分',
      component: 'Input',
      componentProps: {
        placeholder: '请输入满意度评分',
        min: 0,
      },
      show: activeKeyTabs == 4,
      colProps: { span: 14 },
      itemProps: { wrapperCol: { span: 10 } },
    },
    {
      field: 'evaluateTags',
      label: '标签内容',
      component: 'Input',
      slot: 'evaluateTags',
      show: activeKeyTabs == 4,
      colProps: { span: 20 },
      itemProps: { wrapperCol: { span: 12 } },
    },
  ];
};
