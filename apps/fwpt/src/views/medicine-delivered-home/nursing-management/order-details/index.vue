<script setup lang="ts">
  import { useRequest } from '@ft/request';
  import { Descriptions, Step, Steps, Tabs, Tag } from 'ant-design-vue';
  import { computed, ref } from 'vue';
  import { getNurseOrderDetailById } from '/@/api/medicine-delivered-home/nursing-management';
  import { useRoute } from 'vue-router';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { DictEnum, getDictItemList } from '@ft/internal/api';
  import { formSchemas } from './data';
  import { getNurseOrderEvaluateDetail } from '/@/api/medicine-delivered-home/service-evaluation';
  import NursingDocView from './NursingDocView.vue';
  /**
   * 订单详情
   */
  const activeKey = ref(1);
  const sexInfo = {
    1: '男',
    2: '女',
  };
  const { data: stepList } = useRequest(() => getDictItemList(DictEnum.ORDER_STATUS_PC));
  const query = useRoute().query;
  const { data: orderDetailInfo } = useRequest(
    () => getNurseOrderDetailById(query.orderId as string),
    {
      ready: !!query.orderId,
      onSuccess: async (data) => {
        formAction.setFieldsValue({
          ...data,
          ...data.nurseOrder,
        });
        data?.nurseOrder?.code &&
          (await getNurseOrderEvaluateDetailRunAsync(data?.nurseOrder?.code));
      },
    },
  );
  const { runAsync: getNurseOrderEvaluateDetailRunAsync, data: nurseOrderEvaluateDetail } =
    useRequest(getNurseOrderEvaluateDetail, {
      manual: true,
      onSuccess: async (data) => {
        await formAction.setFieldsValue({
          ...orderDetailInfo.value,
          ...orderDetailInfo.value?.nurseOrder,
          ...data,
        });
      },
    });
  //病历/医嘱信息 预约备注 护理文书 评价明细;
  const tabsList = [
    {
      label: '病历/医嘱信息',
      key: 1,
    },
    {
      label: '预约备注',
      key: 2,
    },
    {
      label: '护理文书',
      key: 3,
    },
    {
      label: '评价明细',
      key: 4,
    },
  ];
  const [registerFrom, formAction] = useForm({
    labelWidth: 100,
    showActionButtonGroup: false,
    disabled: true,
    schemas: computed(() => formSchemas(activeKey.value)),
  });
</script>

<template>
  <div class="w-full h-full pr-4 flex flex-col gap-2 pb-2 order-details overflow-hidden">
    <div class="flex flex-col gap-2 bg-white rounded p-4 pb-0">
      <div class="text-16px font-500">订单信息</div>
      <div class="flex gap-2 items-center">
        <div class="min-w-70px">订单追踪：</div>
        <Steps :current="orderDetailInfo?.status" size="small">
          <Step v-for="(i, index) in stepList" :key="index" :title="i.dictItemName" />
        </Steps>
      </div>
      <div class="border-b-1px border-dashed border-#cfcfcf"></div>
      <Descriptions
        class="flex-1"
        :label-style="{ color: '#B0B1B4' }"
        :content-style="{ color: '#252931' }"
        :column="4"
      >
        <Descriptions.Item label="订单时间">{{
          orderDetailInfo?.nurseOrder?.orderTime
        }}</Descriptions.Item>
        <Descriptions.Item label="订单编号">{{
          orderDetailInfo?.nurseOrder?.code
        }}</Descriptions.Item>
        <Descriptions.Item label="预约上门时间">{{
          orderDetailInfo?.nurseOrder?.appointTime
        }}</Descriptions.Item>
        <Descriptions.Item label="订单地址">{{
          orderDetailInfo?.nurseOrder?.address
        }}</Descriptions.Item>
        <Descriptions.Item label="组套名称">{{
          orderDetailInfo?.nurseOrder?.itemName
        }}</Descriptions.Item>
        <Descriptions.Item label="护理人员">{{
          orderDetailInfo?.nurseOrder?.nurseName
        }}</Descriptions.Item>
        <Descriptions.Item label="上门时间">{{
          orderDetailInfo?.nurseOrder?.visitTime
        }}</Descriptions.Item>
      </Descriptions>
    </div>
    <div class="flex-1 bg-white rounded p-4 pb-0 flex flex-col">
      <div class="text-16px font-500 mb-4">患者信息</div>
      <Descriptions
        :label-style="{ color: '#B0B1B4' }"
        :content-style="{ color: '#252931' }"
        :column="24"
      >
        <Descriptions.Item :span="1" label="姓名">{{
          orderDetailInfo?.patientName
        }}</Descriptions.Item>
        <Descriptions.Item :span="1" label="性别">{{
          orderDetailInfo?.sex || sexInfo[orderDetailInfo?.nurseOrder?.sex || '']
        }}</Descriptions.Item>
        <Descriptions.Item :span="1" label="年龄">{{ orderDetailInfo?.age }}</Descriptions.Item>
        <Descriptions.Item :span="2" label="电话">{{ orderDetailInfo?.phone }} </Descriptions.Item>
        <Descriptions.Item :span="2" label="身份证号">{{
          orderDetailInfo?.idCardNo
        }}</Descriptions.Item>
      </Descriptions>
      <Tabs v-model:activeKey="activeKey" class="-mt-4">
        <Tabs.TabPane v-for="item in tabsList" :key="item.key" :tab="item.label" />
      </Tabs>
      <BasicForm v-show="activeKey !== 3" @register="registerFrom">
        <template #moduleName="{ schema }">
          <div class="flex items-center gap-2 mb-3">
            <span v-if="schema.label" class="inline-block bg-primary-color w-3px h-1em"></span>
            <span>{{ schema.label }}</span>
          </div>
        </template>
        <template #evaluateTags>
          <div v-if="nurseOrderEvaluateDetail?.evaluateTags" class="flex items-center gap-2">
            <Tag v-for="item in nurseOrderEvaluateDetail?.evaluateTags?.split(',')" :key="item">{{
              item
            }}</Tag>
          </div>
          <div v-else>暂无</div>
        </template>
      </BasicForm>
      <NursingDocView v-show="activeKey === 3" />
    </div>
  </div>
</template>
