<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { Descriptions } from 'ant-design-vue';
  import { computed, ref } from 'vue';
  import { formSchemas } from './data';
  import type { INurseOrder } from '/@/api/medicine-delivered-home/nursing-management';
  import {
    getDesignate,
    getNurseOrderDetailById,
    getReject,
  } from '/@/api/medicine-delivered-home/nursing-management';

  const emit = defineEmits(['register', 'success']);
  const mode = ref('add');
  const type = ref('reject');
  const titleInfo = {
    reject: '确认拒单',
    nurseReject: '护士拒单原因',
    assign: '指派执行人员',
  };
  const info = ref<Partial<INurseOrder>>({});

  const [registerForm, formAction] = useForm({
    schemas: computed(() => formSchemas(type.value, info.value as INurseOrder)),
    labelWidth: 120,
    colon: true,
    autoSubmitOnEnter: false,
    disabled: computed(() => mode.value === 'view'),
    showActionButtonGroup: false,
  });
  const { data: orderDetailInfo, runAsync } = useRequest(getNurseOrderDetailById, {
    manual: true,
  });
  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data.mode;
    type.value = data.type;
    info.value = data;
    formAction.setFieldsValue({
      ...data,
    });
    formAction.clearValidate();
    runAsync(data.id);
  });

  const { loading, runAsync: saveRunAsync } = useRequest(
    (params) => {
      return type.value === 'reject' ? getReject(params) : getDesignate(params);
    },
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess: () => {
        emit('success');
        closeModal();
      },
    },
  );

  async function handleOk() {
    formAction.validate().then((values) => {
      saveRunAsync(values);
    });
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="titleInfo[type]"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    width="800px"
    @register="register"
    :show-ok-btn="mode !== 'view'"
    @ok="handleOk"
    centered
  >
    <Descriptions
      class="mb-4 px-6"
      :column="24"
      :label-style="{ color: '#B0B1B4' }"
      :content-style="{ color: '#252931' }"
    >
      <Descriptions.Item :span="8" label="订单时间">{{
        orderDetailInfo?.nurseOrder.orderTime
      }}</Descriptions.Item>
      <Descriptions.Item :span="10" label="订单编号">
        {{ orderDetailInfo?.nurseOrder?.code }}</Descriptions.Item
      >
      <Descriptions.Item :span="6" label="患者姓名">{{
        orderDetailInfo?.patientName
      }}</Descriptions.Item>
      <Descriptions.Item :span="8" label="组套名称">{{
        orderDetailInfo?.nurseOrder?.itemName
      }}</Descriptions.Item>
      <Descriptions.Item :span="16" label="预约上门时间">{{
        orderDetailInfo?.nurseOrder?.appointTime
      }}</Descriptions.Item>
    </Descriptions>
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
