<script setup lang="ts">
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button, Tabs } from 'ant-design-vue';
  import { computed, onMounted, ref } from 'vue';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { useModal } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { DictEnum, getDictItemList } from '@ft/internal/api';
  import { exportUtil } from '@ft/internal/utils';
  import Modal from './Modal.vue';
  import { columns, formSchema } from './data';
  import type {
    INurseOrder,
    INurseOrderTopIndex,
  } from '/@/api/medicine-delivered-home/nursing-management';
  import {
    getNurseOrderPage,
    getNurseOrderTopIndex,
    getTake,
    nurseOrderExport,
  } from '/@/api/medicine-delivered-home/nursing-management';

  /**
   * 护理管理
   */
  /**activeKey 0:待审核 1:待分配 2待接单 3:待上门 4:服务中 5:订单完成 6护理文书编写 7已拒 */
  const activeKey = ref();
  const topInfo = ref<INurseOrderTopIndex>();
  onMounted(() => {
    getNurseOrderTopIndex().then((res) => {
      topInfo.value = res;
    });
  });

  const { data: tabsList } = useRequest(() => getDictItemList(DictEnum.ORDER_STATUS_PC), {
    onSuccess: (data) => {
      activeKey.value = data?.length > 0 ? data[0].dictItemCode : undefined;
      tableIns.reload();
    },
  });
  const orderList = computed(() => {
    return [
      {
        label: '预约护理总',
        bgClass: 'sumAmount',
        value: topInfo.value?.sumAmount,
      },
      {
        label: '待分配',
        bgClass: 'distributedAmount',
        value: topInfo.value?.distributedAmount,
      },
      {
        label: '待上门',
        bgClass: 'waitToSeeAmount',
        value: topInfo.value?.waitToSeeAmount,
      },
      {
        label: '上门进行中',
        bgClass: 'inProcessAmount',
        value: topInfo.value?.inProcessAmount,
      },
      {
        label: '已完成',
        bgClass: 'finishAmount',
        value: topInfo.value?.finishAmount,
      },
      {
        label: '护理文书编写',
        bgClass: 'writAmount',
        value: topInfo.value?.writAmount,
      },
      {
        label: '已拒',
        bgClass: 'turnDownAmount',
        value: topInfo.value?.turnDownAmount,
      },
    ];
  });

  const [registerTable, tableIns] = useTable({
    api: getNurseOrderPage,
    fetchSetting: {
      sizeField: 'pageSize',
      pageField: 'pageNum',
      listField: 'records',
    },
    formConfig: {
      colon: true,
      labelWidth: 100,
      showAdvancedButton: false,
      actionColOptions: {
        span: 18,
        style: {
          textAlign: 'right',
        },
      },
      fieldMapToTime: [
        ['orderTime', ['orderStartTime', 'orderEndTime'], 'YYYY-MM-DD'],
        ['nurseTime', ['nurseStartTime', 'nurseEndTime'], 'YYYY-MM-DD'],
      ],
      schemas: formSchema,
    },
    beforeFetch: (params) => {
      params.status = Number(activeKey.value);
      return params;
    },
    immediate: false,
    useSearchForm: true,
    showIndexColumn: true,
    columns,
    bordered: false,
    size: 'small',
    actionColumn: {
      width: 270,
      title: '操作',
      dataIndex: 'action',
    },
  });
  const [registerModal, { openModal }] = useModal();
  function createActions(record: INurseOrder): ActionItem[] {
    /**
     * orderStatus 订单状态 -1：超时关闭 0：待支付 1：已支付(待审核) 2：待分配 3：已拒绝 4：已分配(待接单) 5：待上门 6：待服务 7：服务中 8：已完成 9：订单取消
     */
    const actions: ActionItem[] = [
      {
        label: '订单详情',
        type: 'link',
        size: 'small',
        onClick: handleOrderDetails.bind(null, record),
      },
      //指派执行人员
      {
        label: '指派执行人员',
        type: 'link',
        size: 'small',
        ifShow: () => record.orderStatus == 2,
        onClick: handleAssign.bind(null, record),
      },
      //拒单原因
      {
        label: '护士拒单原因',
        type: 'link',
        size: 'small',
        ifShow: () => record.orderStatus == 2 && !!record.rejectReason,
        onClick: handleNurseRefuseReason.bind(null, record),
      },
      //拒单原因
      {
        label: '拒单原因',
        type: 'link',
        size: 'small',
        ifShow: () => activeKey.value == 7,
        onClick: handleRefuseReason.bind(null, record),
      },
    ];
    return actions;
  }
  const go = useGo();
  function handleOrderDetails(record) {
    go({
      name: 'NursingManagementOrderDetails',
      query: {
        orderId: record.id,
      },
    });
  }
  function handleAssign(record) {
    openModal(true, {
      type: 'assign',
      ...record,
    });
  }
  function handleRefuseReason(record) {
    openModal(true, {
      type: 'reject',
      mode: 'view',
      ...record,
    });
  }
  //护士拒单原因
  function handleNurseRefuseReason(record) {
    openModal(true, {
      type: 'nurseReject',
      mode: 'view',
      ...record,
    });
  }
  function createDropDownActions(record): ActionItem[] {
    /**
     * orderStatus 订单状态 -1：超时关闭 0：待支付 1：已支付(待审核) 2：待分配 3：已拒绝 4：已分配(待接单) 5：待上门 6：待服务 7：服务中 8：已完成 9：订单取消
     */
    const actions: ActionItem[] = [
      {
        label: '接单',
        type: 'link',
        size: 'small',
        popConfirm: {
          title: '确定接单吗？',
          placement: 'topRight',
          confirm: handleAccept.bind(null, record),
        },
        ifShow: () => record.orderStatus == 1,
      },
      {
        label: '拒单',
        type: 'link',
        size: 'small',
        onClick: handleRefuse.bind(null, record),
        ifShow: () => record.orderStatus == 1,
      },
    ];
    return actions;
  }
  const { runAsync: runAsyncTake } = useRequest(getTake, {
    manual: true,
    onSuccess: () => {
      tableIns.reload();
    },
  });
  function handleAccept(record) {
    runAsyncTake(record.id);
  }
  function handleRefuse(record) {
    openModal(true, {
      type: 'reject',
      mode: 'add',
      ...record,
    });
  }
  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(nurseOrderExport, {
    manual: true,
    showSuccessMessage: true,
  });
  function onExportExpert() {
    exportUtil(
      exportExpertRunAsync({
        status: activeKey.value,
        ...tableIns.getForm().getFieldsValue(),
      }),
    );
  }
</script>

<template>
  <div class="w-full h-full pr-4 flex flex-col pb-2 nursing-management">
    <div class="grid grid-cols-7 gap-10px mb-10px">
      <div
        v-for="item in orderList"
        :key="item.label"
        class="flex gap-10px items-center bg-#F5F7FA p-4 rounded-1 rounded-lt-none"
        :class="item.bgClass"
      >
        <div class="flex flex-col flex-1 of-hidden">
          <div class="text-#999999 truncate" :title="item.label">{{ item.label }}订单量(个)</div>
          <div class="text-#333 text-24px fw-bold">{{ item.value }}</div>
        </div>
      </div>
    </div>
    <div class="flex-1 bg-white rounded p-4 flex flex-col">
      <div class="text-16px font-500 mb-4">预约护理订单列表</div>
      <BasicTable @register="registerTable" class="ft-main-table flex-1">
        <template #headerTop>
          <Tabs v-model:activeKey="activeKey" @change="tableIns.reload()">
            <Tabs.TabPane
              v-for="item in tabsList"
              :key="item.dictItemCode"
              :tab="item.dictItemName + '订单'"
            />
          </Tabs>
        </template>
        <template #toolbar>
          <Button :loading="exportLoading" @click="onExportExpert"> 批量导出 </Button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction
              :divider="false"
              :actions="createActions(record)"
              :drop-down-actions="createDropDownActions(record)"
            >
              <template #more>
                <Button size="small" type="link"> 审核 </Button>
              </template>
            </TableAction>
          </template>
          <template v-if="column.dataIndex === 'orderStatus'">
            <div>
              {{ record.orderStatusName }}
            </div>
          </template>
        </template>
      </BasicTable>
    </div>
    <Modal @register="registerModal" @success="tableIns.reload()" />
  </div>
</template>
<style lang="less" scoped>
  .nursing-management {
    :deep {
      .ft-main-table form,
      .ft-main-table .ant-table-wrapper {
        padding: 0 !important;
        margin-bottom: 0 !important;
      }
    }

    .sumAmount {
      background: url('/@/assets/images/total-order.png') no-repeat 100% 90%/25% 50%,
        linear-gradient(180deg, #f0fee8 0%, #fff 100%);
    }

    .distributedAmount {
      background: url('/@/assets/images/unassigned-order.png') no-repeat 97% 90%/25% 50%,
        linear-gradient(180deg, #fffdf7 0%, #fff 100%);
    }

    .waitToSeeAmount {
      background: url('/@/assets/images/to-visit-order.png') no-repeat 97% 90%/25% 50%,
        linear-gradient(180deg, #f5fbff 0%, #fff 100%);
    }

    .inProcessAmount {
      background: url('/@/assets/images/visiting-order.png') no-repeat 97% 90%/25% 50%,
        linear-gradient(180deg, #f1f0ff 0%, #fff 100%);
    }

    .finishAmount {
      background: url('/@/assets/images/complete-order.png') no-repeat 97% 90%/25% 50%,
        linear-gradient(180deg, #fffdf7 0%, #fff 100%);
    }

    .writAmount {
      background: url('/@/assets/images/writing-order.png') no-repeat 97% 90%/25% 50%,
        linear-gradient(180deg, #f5fbff 0%, #fff 100%);
    }

    .turnDownAmount {
      background: url('/@/assets/images/refuse-order.png') no-repeat 95% 88%/22% 48%,
        linear-gradient(180deg, #ffecee 0%, #fff 100%);
    }
  }
</style>
