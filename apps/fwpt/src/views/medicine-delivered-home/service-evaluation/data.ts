import { DictEnum, getDictItemList } from '@ft/internal/api';
import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';

/**
 * 订单时间
 * 订单编号
 * 患者姓名
 * 患者性别
 * 患者年龄
 * 护理项目
 * 预约上门时间
 * 上门时间
 * 完成时问
 * 护理人员
 * 评价等级 activeKey=1
 * 满意程度 activeKey=1
 * 满意度评分 activeKey=1
 * 标签内容 activeKey=1
 * 投诉内容 activeKey=2
 * 报警时间 activeKey=3
 * 患者地址
 */
export const columns = (activeKey: string): BasicColumn[] => {
  return [
    {
      title: '订单时间',
      dataIndex: 'orderTime',
      align: 'left',
      width: 170,
    },
    {
      title: '订单编号',
      dataIndex: 'orderNo',
      align: 'left',
      width: 120,
    },
    {
      title: '患者姓名',
      dataIndex: 'patientName',
      align: 'left',
      width: 80,
    },
    {
      title: '患者性别',
      dataIndex: 'patientSex',
      align: 'left',
      width: 80,
    },
    {
      title: '患者年龄',
      dataIndex: 'patientAge',
      align: 'left',
      width: 80,
    },
    {
      title: '护理项目',
      dataIndex: 'nursingItemName',
      align: 'left',
      width: 120,
    },
    {
      title: '预约上门时间',
      dataIndex: 'appointTime',
      align: 'left',
      width: 170,
    },
    {
      title: '上门时间',
      dataIndex: 'visitTime',
      align: 'left',
      width: 170,
    },
    {
      title: '完成时问',
      dataIndex: 'finishTime',
      align: 'left',
      width: 170,
    },
    {
      title: '护理人员',
      dataIndex: 'nurseName',
      align: 'left',
      width: 80,
    },
    {
      title: '评价等级',
      dataIndex: 'evaluationLevel',
      align: 'left',
      ifShow: activeKey === '1',
      width: 80,
    },
    {
      title: '满意程度',
      dataIndex: 'satisfactionLevel',
      align: 'left',
      ifShow: activeKey === '1',
      width: 80,
    },
    {
      title: '满意度评分',
      dataIndex: 'satisfactionScore',
      align: 'left',
      ifShow: activeKey === '1',
      width: 80,
    },
    {
      title: '标签内容',
      dataIndex: 'tagContent',
      align: 'left',
      ifShow: activeKey === '1',
      width: 180,
    },
    {
      title: '投诉内容',
      dataIndex: 'complaintContent',
      align: 'left',
      ifShow: activeKey === '2',
      width: 180,
    },
    {
      title: '报警时间',
      dataIndex: 'alarmTime',
      align: 'left',
      ifShow: activeKey === '3',
      width: 170,
    },
    {
      title: '患者地址',
      dataIndex: 'orderAddress',
      align: 'left',
      width: 120,
    },
  ];
};

/**
 * 订单时间
 * 订单编号
 * 患者姓名
 * 护理人员
 * 报警时间
 */
export const formSchema = (activeKey: string): FormSchema[] => {
  return [
    {
      field: 'orderTime',
      component: 'RangePicker',
      label: '订单时间',
      colProps: { span: 6 },
    },
    {
      field: 'orderNo',
      component: 'Input',
      label: '订单编号',
      colProps: { span: 6 },
    },
    {
      field: 'patientName',
      component: 'Input',
      label: '患者姓名',
      colProps: { span: 6 },
    },
    {
      field: 'nurseName',
      component: 'Input',
      label: '护理人员',
      colProps: { span: 6 },
    },
    //评价等级
    {
      field: 'evaluationLevel',
      component: 'ApiSelect',
      label: '评价等级',
      componentProps: {
        api: () => getDictItemList(DictEnum.EVALUATION_LEVEL),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        showSearch: true,
        optionFilterProp: 'label',
      },
      colProps: { span: 6 },
      ifShow: activeKey === '1',
    },
    {
      field: 'alarmTime',
      component: 'RangePicker',
      label: '报警时间',
      colProps: { span: 6 },
      ifShow: activeKey === '3',
    },
  ];
};
