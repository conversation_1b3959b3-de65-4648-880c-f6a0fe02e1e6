<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { Tabs } from 'ant-design-vue';
  import { computed, ref } from 'vue';
  import { useRequest } from '@ft/request';
  import { DictEnum, getDictItemList } from '@ft/internal/api';
  import { columns, formSchema } from './data';
  import { getNurseOrderEvaluatePage } from '/@/api/medicine-delivered-home/service-evaluation';

  /**
   * 服务评价
   */
  const activeKey = ref();
  const { data: tabsList } = useRequest(() => getDictItemList(DictEnum.SERVICE_EVALUATION_TYPE), {
    onSuccess: (data) => {
      activeKey.value = data?.length > 0 ? data[0].dictItemCode : undefined;
      tableIns.reload();
    },
  });
  const [registerTable, tableIns] = useTable({
    api: getNurseOrderEvaluatePage,
    formConfig: {
      colon: true,
      labelWidth: 100,
      showAdvancedButton: false,
      actionColOptions: {
        //@ts-ignore
        span: computed(() => (activeKey.value === '2' ? 24 : 18)),
        style: {
          textAlign: 'right',
        },
      },
      schemas: computed(() => formSchema(activeKey.value)),
      fieldMapToTime: [
        ['orderTime', ['orderStartDate', 'orderEndDate'], 'YYYY-MM-DD'],
        ['alarmTime', ['alarmStartDate', 'alarmEndDate'], 'YYYY-MM-DD'],
      ],
    },
    immediate: false,
    beforeFetch: (params) => {
      return {
        bizType: activeKey.value,
        ...params,
      };
    },
    useSearchForm: true,
    showIndexColumn: true,
    columns: computed(() => columns(activeKey.value)),
    bordered: false,
    size: 'small',
  });
</script>

<template>
  <div class="w-full h-full pr-4 rounded rounded-lt-none">
    <BasicTable @register="registerTable" class="ft-main-table">
      <template #headerTop>
        <div class="text-16px font-500 mb-4">护理服务评价列表</div>
        <Tabs v-model:activeKey="activeKey" @change="tableIns.reload()">
          <Tabs.TabPane
            v-for="item in tabsList"
            :key="item.dictItemCode"
            :tab="item.dictItemName"
          />
        </Tabs>
      </template>
    </BasicTable>
  </div>
</template>
