import { DictEnum, getDictItemList, queryOrganizationList } from '@ft/internal/api';
import type { FormSchema } from '@ft/internal/components/Form';
import type { BasicColumn } from '@ft/internal/components/Table';
import { useUserStoreWithOut } from '@ft/internal/store/modules/user';

const { userInfo } = useUserStoreWithOut();
export const SearchSchemas: FormSchema[] = [
  {
    field: 'orgId',
    label: '医疗机构',
    component: 'ApiSelect',
    componentProps: {
      api: () => queryOrganizationList({ orgType: 1, enableFlag: 0 }),
      labelField: 'orgName',
      valueField: 'id',
      showSearch: true,
      optionFilterProp: 'label',
    },
    defaultValue: userInfo?.orgId,
    colProps: { span: 6 },
  },
  {
    field: 'classTypeCode',
    label: '护理分类',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.NURSING_SERVICE_CATEGORY),
      getPopupContainer: () => document.body,
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
    },
    colProps: { span: 6 },
  },
];

export const columns: BasicColumn[] = [
  {
    title: '医疗机构',
    dataIndex: 'orgName',
    width: 150,
  },
  {
    title: '护理分类',
    dataIndex: 'classTypeCodeName',
    width: 150,
  },
  {
    title: '班别',
    dataIndex: 'className',
    width: 150,
  },
  {
    title: '班别起止时间',
    dataIndex: 'classTime',
    width: 150,
  },
  {
    title: '班别状态',
    dataIndex: 'status',
    width: 150,
  },
  {
    title: '维护时间',
    dataIndex: 'updateTime',
    width: 150,
  },
  {
    title: '维护人',
    dataIndex: 'updateUser',
    width: 150,
  },
];

/**
医疗机构
班别科室
班别名称
起止时间
班别状态
 */
export const formSchema: FormSchema[] = [
  {
    field: 'id',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'orgId',
    label: '',
    component: 'Input',
    defaultValue: userInfo?.orgId,
    show: false,
  },
  {
    field: 'classTypeCodeName',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'orgName',
    label: '医疗机构',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
    componentProps: {
      disabled: true,
    },
    defaultValue: userInfo?.orgName,
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    field: 'classTypeCode',
    label: '护理分类',
    component: 'ApiSelect',
    required: true,
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.NURSING_SERVICE_CATEGORY),
        getPopupContainer: () => document.body,
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        onChange(_, opt) {
          if (opt?.label) {
            formModel.classTypeCodeName = opt.label;
          }
        },
      };
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    field: 'className',
    label: '班别名称',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    field: 'classTime',
    label: '起止时间',
    component: 'TimePicker',
    required: true,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
    slot: 'classTime',
  },
  {
    field: 'status',
    label: '班别状态',
    component: 'RadioGroup',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 },
      ],
    },
    defaultValue: 1,
  },
];
