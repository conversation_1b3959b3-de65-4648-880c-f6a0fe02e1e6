<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { computed, ref } from 'vue';
  import { TimeRangePicker } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { formSchema } from './data';
  import { addClass, editClass } from '/@/api/medicine-delivered-home/nursing-scheduling';

  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm({
    schemas: formSchema,
    labelWidth: 120,
    colon: true,
    autoSubmitOnEnter: false,
    fieldMapToTime: [['classTime', ['classTimeStart', 'classTimeEnd'], 'HH:mm']],
    showActionButtonGroup: false,
  });
  const mode = ref('');
  const getTitle = computed(() => (mode.value === 'edit' ? '编辑班别' : '新增班别'));
  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data.mode;
    data.mode == 'edit' &&
      formAction.setFieldsValue({
        ...data,
        classTime: [
          dayjs(data.classTime?.split('-')[0], 'HH:mm'),
          dayjs(data.classTime?.split('-')[1], 'HH:mm'),
        ],
      });
  });

  const { loading, runAsync } = useRequest(
    (params) => (mode.value === 'edit' ? editClass(params) : addClass(params)),
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess: () => {
        emit('success');
        closeModal();
      },
    },
  );

  async function handleOk() {
    formAction.validate().then(() => {
      const values = formAction.getFieldsValue();
      runAsync({ ...values, classTime: values.classTimeStart + '-' + values.classTimeEnd });
    });
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="getTitle"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    @register="register"
    @ok="handleOk"
    :min-height="120"
    centered
  >
    <BasicForm @register="registerForm">
      <template #classTime="{ model, field }">
        <TimeRangePicker
          style="width: 100%"
          v-model:value="model[field]"
          format="HH:mm"
          :order="false"
        />
      </template>
    </BasicForm>
  </BasicModal>
</template>
