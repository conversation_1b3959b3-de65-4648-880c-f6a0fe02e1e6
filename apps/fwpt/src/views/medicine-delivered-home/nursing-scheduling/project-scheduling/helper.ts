import dayjs from 'dayjs';

export function getFirstDayOfWeek(date) {
  const day = date.getDay();
  const diff = date.getDate() - day + (day === 0 ? -6 : 1);
  return dayjs(date.setDate(diff)).format('YYYY-MM-DD');
}

export function getWeekDay(date) {
  const day = dayjs(date).day();
  switch (day) {
    case 0:
      return '星期日';
    case 1:
      return '星期一';
    case 2:
      return '星期二';
    case 3:
      return '星期三';
    case 4:
      return '星期四';
    case 5:
      return '星期五';
    case 6:
      return '星期六';
    default:
  }
  return day;
}
