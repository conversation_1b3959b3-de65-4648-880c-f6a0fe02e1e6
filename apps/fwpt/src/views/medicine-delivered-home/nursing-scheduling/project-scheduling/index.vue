<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { StyledList } from '@ft/components';
  import { Button, InputSearch } from 'ant-design-vue';
  import { computed, ref } from 'vue';
  import dayjs from 'dayjs';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue';
  import { useRequest } from '@ft/request';
  import { useUserStoreWithOut } from '@ft/internal/store/modules/user';
  import { DictEnum, getDictItemList } from '@ft/internal/api';
  import { getFirstDayOfWeek, getWeekDay } from './helper';
  import {
    getProjectAuditStatus,
    getProjectClassList,
  } from '/@/api/medicine-delivered-home/nursing-scheduling';

  const { userInfo } = useUserStoreWithOut();
  const go = useGo();
  const scheduled = ref(false);
  const remakeText = ref('');
  const classTypeCode = ref('');
  const classTypeName = computed(() => {
    return items.value?.find((item) => item.dictItemCode === classTypeCode.value)?.dictItemName;
  });
  const { data: items } = useRequest(() => getDictItemList(DictEnum.NURSING_SERVICE_CATEGORY), {
    onSuccess(data) {
      classTypeCode.value = data?.[0].dictItemCode;
    },
  });
  const searchTypeName = ref('');
  const filteredItems = computed(() => {
    return (items.value || []).filter((item) => {
      return item.dictItemName.includes(searchTypeName.value);
    });
  });

  //获取本周的第一天日期
  const date = ref(getFirstDayOfWeek(new Date()));
  const firstDate = computed(() => {
    return dayjs(date.value).format('YYYY-MM-DD');
  });
  const lastDate = computed(() => {
    return dayjs(date.value).add(6, 'day').format('YYYY-MM-DD');
  });
  const { data: auditStatus } = useRequest(
    () =>
      classTypeCode.value
        ? getProjectAuditStatus({
            orgId: userInfo?.orgId,
            startDate: firstDate.value,
            endDate: lastDate.value,
            classTypeCode: classTypeCode.value,
          })
        : Promise.resolve([]),
    {
      refreshDeps: [classTypeCode, lastDate, firstDate],
    },
  );
  const addDisabled = computed(() => {
    return (
      getFirstDayOfWeek(new Date()).valueOf() > dayjs(date.value).format('YYYY-MM-DD').valueOf() ||
      scheduled.value ||
      auditStatus.value
    );
  });
  const editDisabled = computed(() => {
    return (
      getFirstDayOfWeek(new Date()).valueOf() > dayjs(date.value).format('YYYY-MM-DD').valueOf() ||
      !scheduled.value ||
      auditStatus.value
    );
  });
  const columns = computed(() => {
    const _columns = [
      {
        title: '项目名称',
        dataIndex: 'itemName',
        align: 'center',
        width: 160,
        customHeaderCell() {
          return {
            style: 'position: relative',
          };
        },
      },
    ];
    for (let i = 0; i < 7; i++) {
      const dynamicDate = dayjs(firstDate.value).add(i, 'day');
      const week = getWeekDay(dynamicDate.format('YYYY-MM-DD'));
      _columns.push({
        title: `${week}\n${dynamicDate.format('MM-DD')}`,
        dataIndex: `${dynamicDate.format('YYYY-MM-DD')}`,
        align: 'center',
        width: 160,
      } as any);
    }
    return _columns;
  });
  const { run, loading } = useRequest(
    () =>
      getProjectClassList({
        orgId: userInfo?.orgId,
        startDate: firstDate.value,
        endDate: lastDate.value,
        classTypeCode: classTypeCode.value,
      }),
    {
      onSuccess: (data) => {
        const map = new Map();
        const result = data.map((item) => {
          const weekClassList = item.weekClassList[0];
          const dates = Object.keys(weekClassList);
          const transformedItem = { ...item };
          dates.forEach((date) => {
            //没有排班的默认休息
            transformedItem[date] =
              weekClassList[date].length > 0
                ? weekClassList[date]
                    .map((classManage) => {
                      map.set(classManage.classManageName, classManage.classTime);
                      return classManage.classManageName + `(${classManage.callNum})`;
                    })
                    .join('、')
                : '休';
          });
          return transformedItem;
        });
        remakeText.value = Array.from(map.entries())
          .map((entry) => `${entry[0]} ${entry[1]}`)
          .join(', ');
        tableAction.setTableData(result);
        scheduled.value = result.length > 0;
      },
      ready: classTypeCode,
    },
  );

  const [register, tableAction] = useTable({
    loading,
    showIndexColumn: false,
    useSearchForm: false,
    bordered: true,
    inset: true,
    immediate: false,
    pagination: false,
    //@ts-ignore
    columns: computed(() => columns.value),
  });

  function handleLeftDate() {
    date.value = dayjs(date.value).subtract(7, 'day').format('YYYY-MM-DD');
    run();
  }
  function handleRightDate() {
    date.value = dayjs(date.value).add(7, 'day').format('YYYY-MM-DD');
    run();
  }
  function addOrEdit(flag = false) {
    go({
      name: 'NursingSchedulingProjectSchedulingAddClass',
      query: {
        classTypeName: classTypeName.value,
        classTypeCode: classTypeCode.value,
        firstDate: firstDate.value,
        lastDate: lastDate.value,
        mode: flag ? 'edit' : 'add',
      },
    });
  }
</script>

<template>
  <div class="flex h-full p-4 bg-white min-w-757px rounded-2 rounded-lt-none">
    <div
      class="h-full bg-white rounded rounded-lt-0 flex flex-col pr-4"
      style="flex-shrink: 0; border-right: 1px solid #edeef0"
    >
      <span class="text-base font-bold">护理服务分类</span>
      <InputSearch v-model:value="searchTypeName" class="mt-4 mb-2" placeholder="请输入关键字" />
      <div class="flex-1 basis-0 of-y-auto of-x-hidden">
        <StyledList
          :width="210"
          :items="filteredItems"
          v-model="classTypeCode"
          labelField="dictItemName"
          valueField="dictItemCode"
        />
      </div>
    </div>
    <div class="flex-1 of-hidden px-2 relative">
      <BasicTable @register="register">
        <template #headerTop>
          <div class="flex justify-between">
            <div> 排班状态： {{ auditStatus ? '已审核' : '未审核' }}</div>
            <div>
              <Button :disabled="addDisabled" type="primary" class="mr-2" @click="addOrEdit()">
                新增排班
              </Button>
              <Button :disabled="editDisabled" @click="addOrEdit(true)"> 修改排班 </Button>
            </div>
          </div>
          <div class="flex justify-center items-center flex-col">
            <div class="text-20px text-#333">{{ classTypeName }}</div>
            <div class="flex justify-center items-center gap-4">
              <Button shape="circle" size="small" @click="handleLeftDate">
                <template #icon> <LeftOutlined /> </template>
              </Button>
              <div>{{ firstDate }}～{{ lastDate }}</div>
              <Button shape="circle" size="small" @click="handleRightDate">
                <template #icon> <RightOutlined /> </template>
              </Button>
            </div>
          </div>
        </template>
        <template #headerCell="{ column }">
          <div v-if="column.dataIndex === 'itemName'">
            <div class="text-right">日期</div>
            <svg class="svg-line" xmlns="http://www.w3.org/2000/svg">
              <line x1="0" y1="0" x2="100%" y2="100%" stroke="#dcdfe6" stroke-width="1" />
            </svg>
            <div class="text-left">项目名称</div>
          </div>
          <div class="whitespace-pre" v-else>{{ column.customTitle }} </div>
        </template>
      </BasicTable>
      <div class="h-40px absolute bottom-16px left-32px"> 备注：{{ remakeText }} </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table-form-container .ant-form {
      padding: 0;
      margin-bottom: 10px;
    }

    .svg-line {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
    }
  }
</style>
