<script setup lang="ts">
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useUserStore } from '@ft/internal/store/modules/user';
  import { useRequest } from '@ft/request';
  import { InputNumber } from 'ant-design-vue';
  import { ref } from 'vue';
  import { getClassList } from '/@/api/medicine-delivered-home/nursing-scheduling';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';

  const emit = defineEmits(['register', 'success']);
  const { userInfo } = useUserStore();
  const classTypeCode = useRouteQuery('classTypeCode', '', { transform: String });
  const itemName = ref('');
  const day = ref('');
  const { data: classTypeList } = useRequest(
    () =>
      getClassList({
        orgId: userInfo?.orgId,
        classTypeCode: classTypeCode.value,
      }),
    {
      ready: !!userInfo?.orgId,
    },
  );

  const itemClasses = ref<any[]>([]);
  const [register, { closeModal }] = useModalInner((data) => {
    // record.value = data || {};
    itemName.value = data.itemName;
    day.value = data.day;
    itemClasses.value = (classTypeList.value || [])
      ?.filter((item) => item.status === 1)
      ?.map((item) => {
        const selectedItem = (data.itemClasses || []).find((s) => s.classManageId === item.id);
        return {
          classManageId: item.id,
          classManageName: item.className,
          classTime: item.classTime,
          classDate: data.day,
          callNum: selectedItem?.callNum || 0,
        };
      });
  });
  function onOk() {
    emit('success', {
      day: day.value,
      itemClasses: itemClasses.value.filter((item) => item.callNum > 0),
    });
    closeModal();
  }
</script>

<template>
  <BasicModal @register="register" :width="600" title="配置号源" @ok="onOk">
    <div>
      <div class="flex gap-8 mb-4">
        <div>护理项目：{{ itemName }}</div>
        <div>排班日期：{{ day }}</div>
      </div>
      <div>
        <div>班别时段项目可服务数量配置:</div>
        <div v-for="item in itemClasses" :key="item" class="flex items-center mt-2 ml-4 gap-4">
          <div>{{ item.classManageName }} {{ item.classTime }}</div>
          <InputNumber :min="0" style="width: 100px !important" v-model:value="item.callNum" />
        </div>
      </div>
    </div>
  </BasicModal>
</template>
<style scoped></style>
