<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { Button, Select } from 'ant-design-vue';
  import { computed, nextTick, ref } from 'vue';
  import dayjs from 'dayjs';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { useRequest } from '@ft/request';
  import { useUserStoreWithOut } from '@ft/internal/store/modules/user';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { useModal } from '@ft/internal/components/Modal';
  import { getWeekDay } from './helper';
  import {
    getClassList,
    getItemList,
    getProjectClassList,
    saveOrUpdateClass,
  } from '/@/api/medicine-delivered-home/nursing-scheduling';
  import SourceConfigModal from './source-config.vue';
  const [registerModal, { openModal }] = useModal();
  const { userInfo } = useUserStoreWithOut();
  const go = useGo();
  const remakeText = ref('');
  const classTypeCode = useRouteQuery('classTypeCode', '', { transform: String });
  const classTypeName = useRouteQuery('classTypeName', '', { transform: String });
  const lastDate = useRouteQuery('lastDate', '', { transform: String });
  const firstDate = useRouteQuery('firstDate', '', { transform: String });
  const mode = useRouteQuery('mode', 'add', { transform: String });
  const columns = computed(() => {
    const _columns = [
      {
        title: '项目名称',
        dataIndex: 'itemName',
        align: 'center',
        width: 160,
        customHeaderCell() {
          return {
            style: 'position: relative',
          };
        },
      },
    ];
    for (let i = 0; i < 7; i++) {
      const dynamicDate = dayjs(firstDate.value).add(i, 'day');
      const week = getWeekDay(dynamicDate.format('YYYY-MM-DD'));
      _columns.push({
        title: `${week}\n${dynamicDate.format('MM-DD')}`,
        dataIndex: `${dynamicDate.format('YYYY-MM-DD')}`,
        align: 'center',
        width: 160,
      } as any);
    }
    return _columns;
  });
  const { loading } = useRequest(
    () =>
      getProjectClassList({
        orgId: userInfo?.orgId,
        classTypeCode: classTypeCode.value,
        startDate: firstDate.value,
        endDate: lastDate.value,
      }),
    {
      onSuccess: (data) => {
        const result = data.map((item) => {
          const weekClassList = item.weekClassList[0];
          const dates = Object.keys(weekClassList);
          const transformedItem = { ...item };
          dates.forEach((date) => {
            transformedItem[date] =
              weekClassList[date].length > 0
                ? weekClassList[date].map((item) => ({ ...item, classDate: date }))
                : [];
          });
          return transformedItem;
        });
        const days: Recordable = {};
        for (let i = 0; i < 7; i++) {
          const dynamicDate = dayjs(firstDate.value).add(i, 'day');
          days[dynamicDate.format('YYYY-MM-DD')] = [];
        }
        if (result.length < 6) {
          const j = 6 - result.length;
          for (let i = 0; i < j; i++) {
            result.push({
              itemCode: '',
              itemName: '',
              ...days,
            });
          }
        }
        tableAction.setTableData(result);
        refreshItemOption();
      },
    },
  );

  const [register, tableAction] = useTable({
    loading,
    showIndexColumn: false,
    useSearchForm: false,
    bordered: true,
    inset: true,
    immediate: false,
    pagination: false,
    //@ts-ignore
    columns: computed(() => columns.value),
  });

  const getTitle = computed(() => {
    return mode.value === 'add' ? '新增排班' : '编辑排班';
  });
  const { data: itemList = [] as any } = useRequest(
    () =>
      getItemList({ status: 1, pageSize: 999, itemTypeId: classTypeCode.value }).then(
        (res) => res.list,
      ),
    {
      onSuccess() {
        refreshItemOption();
      },
    },
  );
  const itemOption = ref([]);

  const { loading: saveLoading, runAsync: saveRunAsync } = useRequest(saveOrUpdateClass, {
    manual: true,
    showSuccessMessage: true,
  });
  function handleSave() {
    const dataSource = tableAction.getDataSource();
    const classeNurses = (dataSource || []).map((record) => {
      const keys = Object.keys(record).filter((key) => key.match(/^\d{4}-\d{2}-\d{2}$/));
      const itemClasses = keys
        .map((key) => record[key])
        .flat()
        .map((item) => {
          // eslint-disable-next-line unused-imports/no-unused-vars, no-unused-vars, @typescript-eslint/no-unused-vars
          const { id, ...rest } = item;
          return rest;
        });
      return {
        itemCode: record.itemCode,
        itemName: record.itemName,
        itemClasses,
      };
    });
    saveRunAsync({
      orgId: userInfo?.orgId,
      orgName: userInfo?.orgName,
      startDate: firstDate.value,
      endDate: lastDate.value,
      classTypeCode: classTypeCode.value,
      classTypeCodeName: classTypeName.value,
      classeNurses: classeNurses.filter((item) => item.itemCode),
    });
  }
  let activeRecord = {};
  function configSource(record, day) {
    activeRecord = record;
    openModal(true, {
      day,
      itemName: record.itemName,
      itemClasses: record[day],
    });
  }

  function onConfigCb(data) {
    activeRecord[data.day] = data.itemClasses;
  }
  useRequest(
    () =>
      getClassList({
        orgId: userInfo?.orgId,
        classTypeCode: classTypeCode.value,
      }),
    {
      ready: !!userInfo?.orgId,
      onSuccess(res) {
        remakeText.value = res.map((item) => `${item.className} ${item.classTime}`).join(' ');
      },
    },
  );
  function onItemChange(val, model) {
    const selected = itemList.value.find((item) => item.itemCode === val) || {};
    model.itemCode = selected.itemCode;
    model.itemName = selected.itemName;
    nextTick(() => {
      refreshItemOption();
    });
  }

  function refreshItemOption() {
    const itemCodes = tableAction.getDataSource().map((item) => item.itemCode);
    itemOption.value = (itemList.value || []).map((item) => {
      return {
        label: item.itemName,
        value: item.itemCode,
        disabled: itemCodes.includes(item.itemCode),
      };
    });
  }
</script>

<template>
  <div class="flex h-full p-4 bg-white min-w-757px rounded-2 rounded-lt-none">
    <BasicTable @register="register">
      <template #headerTop>
        <div class="flex justify-between">
          <div>{{ getTitle }}</div>
          <div>
            <Button class="mr-2" @click="go(-1)"> 返回 </Button>
            <Button type="primary" :loading="saveLoading" @click="handleSave"> 提交 </Button>
          </div>
        </div>
        <div class="flex justify-center items-center flex-col">
          <div class="text-20px text-#333">{{ classTypeName }}</div>
          <div class="flex justify-center items-center gap-4">
            <div>{{ firstDate }}～{{ lastDate }}</div>
          </div>
        </div>
      </template>
      <template #headerCell="{ column }">
        <div v-if="column.dataIndex === 'itemName'">
          <div class="text-right">日期</div>
          <svg class="svg-line" xmlns="http://www.w3.org/2000/svg">
            <line x1="0" y1="0" x2="100%" y2="100%" stroke="#dcdfe6" stroke-width="1" />
          </svg>
          <div class="text-left">项目名称</div>
        </div>
        <div class="whitespace-pre" v-else>{{ column.customTitle }} </div>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.customTitle?.includes('星期')">
          <div
            v-if="record[column.dataIndex]?.[0]?.classManageId"
            @click="configSource(record, column.dataIndex)"
          >
            {{
              record[column.dataIndex]
                .map((item) => `${item.classManageName}(${item.callNum})`)
                .join('、 ')
            }}
          </div>
          <Button v-else type="primary" @click="configSource(record, column.dataIndex)">
            点击配置号源
          </Button>
        </template>
        <template v-else>
          <Select
            :options="itemOption"
            allow-clear
            style="width: 100%"
            v-model:value="record[column.dataIndex]"
            placeholder="请选择项目"
            @change="onItemChange($event, record)"
        /></template>
      </template>
    </BasicTable>
    <div class="h-40px absolute bottom-16px left-32px"> 备注：{{ remakeText }} </div>
    <SourceConfigModal @register="registerModal" @success="onConfigCb" />
  </div>
</template>

<style scoped lang="less">
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table-form-container .ant-form {
      padding: 0;
      margin-bottom: 10px;
    }

    .svg-line {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
    }
  }
</style>
