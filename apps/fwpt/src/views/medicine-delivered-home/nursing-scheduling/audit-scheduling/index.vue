<script setup lang="ts">
  import type { TableRowSelection } from '@ft/internal/components/Table';
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { computed, nextTick, ref, watch } from 'vue';
  import { useRequest } from '@ft/request';
  import dayjs from 'dayjs';
  import print from 'print-js';
  import { Button, Tabs } from 'ant-design-vue';
  import { useUserStoreWithOut } from '@ft/internal/store/modules/user';
  import { DictEnum, getDictItemList } from '@ft/internal/api';
  import { getWeekDay } from './helper';
  import {
    auditClass,
    getClassAuditList,
    getNurseClassList,
    getProjectClassList,
  } from '/@/api/medicine-delivered-home/nursing-scheduling';
  const { userInfo } = useUserStoreWithOut();
  const remakeText = ref('');
  const activeLeftKey = ref(0);
  const activeRightKey = ref(2);
  const activeRecord = ref<Recordable>({});
  const hasResult = ref(false);
  const classTypeName = computed(() => activeRecord.value?.classTypeCodeName || '');
  const columns = computed(() => {
    const _columns = [
      {
        title: '',
        dataIndex: 'name',
        align: 'center',
        width: 100,
        customHeaderCell() {
          return {
            style: 'position: relative',
          };
        },
      },
    ];
    for (let i = 0; i < 7; i++) {
      const dynamicDate = dayjs(activeRecord.value.startDate).add(i, 'day');
      const week = getWeekDay(dynamicDate.format('YYYY-MM-DD'));
      _columns.push({
        title: `${week}\n${dynamicDate.format('MM-DD')}`,
        dataIndex: `${dynamicDate.format('YYYY-MM-DD')}`,
        align: 'center',
        width: 100,
      } as any);
    }
    return _columns;
  });
  const selectedRowKeys = ref<TableRowSelection['selectedRowKeys']>([]);
  const handleSelectionChange: TableRowSelection['onChange'] = (keys) => {
    selectedRowKeys.value = keys;
  };
  watch(selectedRowKeys, (val, oldVal) => {
    if (val?.[0] && val[0] !== oldVal?.[0]) {
      activeRecord.value = JSON.parse(val[0] as any);
      handleRightTabChange();
    } else if (val?.length === 0) {
      activeRecord.value = {};
      handleRightTabChange();
    }
  });
  const { run: handleAudit } = useRequest(auditClass, {
    manual: true,
    showSuccessMessage: true,
    onSuccess() {
      handleFormSubmit();
      handleRightTabChange();
    },
  });
  const [registerForm, formAction] = useForm({
    schemas: [
      {
        field: 'orgId',
        label: '医疗机构',
        component: 'ApiSelect',
        // componentProps: {
        //   api: () => queryOrganizationList({ orgType: 1, enableFlag: 0 }),
        //   labelField: 'orgName',
        //   valueField: 'id',
        //   showSearch: true,
        //   optionFilterProp: 'label',
        // },
        defaultValue: userInfo?.orgId,
        colProps: { span: 6 },
        show: false,
      },
      {
        field: 'classTypeCode',
        label: '护理分类',
        component: 'ApiSelect',
        componentProps: ({ formModel }) => {
          return {
            api: () =>
              getDictItemList(DictEnum.NURSING_SERVICE_CATEGORY).then((res) => {
                formModel.classTypeCode = res[0].dictItemCode;
                nextTick(() => {
                  handleFormSubmit();
                });
                return res;
              }),
            getPopupContainer: () => document.body,
            labelField: 'dictItemName',
            valueField: 'dictItemCode',
          };
        },
        colProps: { span: 6 },
      },
      {
        field: 'queryTime',
        label: '排班日期',
        component: 'RangePicker',
        defaultValue: [dayjs().subtract(6, 'day'), dayjs()],
        colProps: { span: 6 },
      },
    ],
    labelWidth: 100,
    fieldMapToTime: [['queryTime', ['startDate', 'endDate'], 'YYYY-MM-DD']],
    actionColOptions: {
      span: 12,
    },
  });
  const [registerLeftTable, leftTableAction] = useTable({
    columns: [
      {
        dataIndex: 'classTypeCodeName',
        title: '护理分类',
        width: 100,
      },
      {
        dataIndex: 'date',
        title: '排班时间',
        width: 260,
        customRender: ({ record }) => {
          return (
            dayjs(record.startDate).format('YYYY年MM月DD日') +
            '至' +
            dayjs(record.endDate).format('YYYY年MM月DD日')
          );
        },
      },
    ],
    api: (args) =>
      getClassAuditList(args).then((res) =>
        res.map((item) => ({ ...item, id: JSON.stringify(item) })),
      ),
    showIndexColumn: false,
    pagination: false,
    immediate: false,
    afterFetch(data) {
      selectedRowKeys.value = [data[0]?.id];
    },
  });
  const [registerRightTable, rightTableAction] = useTable({
    showIndexColumn: false,
    useSearchForm: false,
    bordered: true,
    inset: true,
    immediate: false,
    pagination: false,
    //@ts-ignore
    columns: computed(() => columns.value),
    scroll: {
      x: 1000,
    },
  });

  const onPrint = () => {
    print({
      type: 'json',
      properties: [
        { field: 'name', displayName: activeRightKey.value === 1 ? '项目名称' : '姓名' },
      ].concat(
        columns.value
          .slice(1)
          .map((item) => ({ field: item.dataIndex, displayName: item.dataIndex })),
      ),
      printable: rightTableAction.getDataSource(),
      documentTitle:
        classTypeName.value + (activeRightKey.value === 1 ? '项目排班' : '人员排班') + '表',
    });
  };

  function handleFormSubmit() {
    activeLeftKey.value = 0;
    handleLeftTabChange();
  }
  function handleLeftTabChange() {
    const val = formAction.getFieldsValue();
    leftTableAction.reload({
      searchInfo: {
        ...val,
        auditStatus: activeLeftKey.value,
      },
    });
  }
  function handleRightTabChange() {
    const val = formAction.getFieldsValue();
    const api = activeRightKey.value === 1 ? getProjectClassList : getNurseClassList;
    if (!activeRecord.value.classTypeCode) {
      remakeText.value = '';
      rightTableAction.setTableData([]);
      hasResult.value = false;
      return;
    }
    api({
      orgId: val.orgId,
      auditStatus: activeLeftKey.value,
      ...activeRecord.value,
    }).then((data) => {
      const map = new Map();
      const result = data.map((item) => {
        const weekClassList = item.weekClassList[0];
        const dates = Object.keys(weekClassList);
        const transformedItem = { ...item, name: item.itemName || item.nurseName };
        dates.forEach((date) => {
          //没有排班的默认休息
          transformedItem[date] =
            weekClassList[date].length > 0
              ? weekClassList[date]
                  .map((classManage) => {
                    map.set(classManage.classManageName, classManage.classTime);
                    return activeRightKey.value === 1
                      ? classManage.classManageName + `(${classManage.callNum})`
                      : classManage.classManageName;
                  })
                  .join('、')
              : '休';
        });
        return transformedItem;
      });
      remakeText.value = Array.from(map.entries())
        .map((entry) => `${entry[0]} ${entry[1]}`)
        .join(', ');
      rightTableAction.setTableData(result);
      hasResult.value = result.length > 0;
    });
  }

  const handleBatchAudit = () => {
    if (!selectedRowKeys.value?.length) {
      return;
    }
    const dateList = selectedRowKeys.value.map((key) => {
      const record = JSON.parse(key as string);
      return {
        classTypeCode: record.classTypeCode,
        startDate: record.startDate,
        endDate: record.endDate,
      };
    });
    handleAudit({
      type: '',
      orgId: userInfo?.orgId,
      dateList,
    });
  };
</script>

<template>
  <div class="h-full rounded-2 rounded-lt-none">
    <div class="bg-white mb-2 pt-4 pr-4">
      <BasicForm @register="registerForm" @submit="handleFormSubmit" />
    </div>
    <div class="flex h-[calc(100%-74px)] gap-2">
      <div
        class="h-full bg-white rounded rounded-lt-0 flex flex-col p-4 w-480px"
        style="flex-shrink: 0; border-right: 1px solid #edeef0"
      >
        <div class="text-base font-bold">护理分类排班列表</div>
        <Tabs v-model:active-key="activeLeftKey" @change="handleLeftTabChange">
          <Tabs.TabPane :key="0" tab="待审核" />
          <Tabs.TabPane :key="1" tab="已审核" />
        </Tabs>
        <div class="flex justify-end my-2">
          <Button type="primary" :disabled="activeLeftKey === 1" @click="handleBatchAudit"
            >批量审核</Button
          >
        </div>
        <BasicTable
          @register="registerLeftTable"
          row-key="id"
          :row-selection="{ type: 'checkbox', selectedRowKeys, onChange: handleSelectionChange }"
        />
      </div>
      <div
        class="flex-1 of-hidden px-4 py-2 relative bg-white"
        id="print-dom"
        :class="{ 'no-print': true }"
      >
        <BasicTable @register="registerRightTable">
          <template #headerTop>
            <div class="flex justify-center items-center flex-col mt-2">
              <div class="text-20px text-#333">{{ classTypeName }}</div>
              <div class="flex justify-center items-center gap-4 relative w-full">
                <div>
                  {{ activeRecord.startDate?.substring(0, 10) }}
                  ～
                  {{ activeRecord.endDate?.substring(0, 10) }}
                </div>
                <div class="absolute right-0">
                  <Button @click="onPrint" class="mr-2">打印</Button>
                  <Button
                    type="primary"
                    :disabled="!hasResult || activeLeftKey === 1"
                    @click="
                      handleAudit({
                        type: activeRightKey,
                        orgId: userInfo?.orgId,
                        dateList: [
                          {
                            classTypeCode: activeRecord.classTypeCode,
                            startDate: activeRecord.startDate,
                            endDate: activeRecord.endDate,
                          },
                        ],
                      })
                    "
                  >
                    审核
                  </Button>
                </div>
              </div>
            </div>
            <div>
              <Tabs v-model:active-key="activeRightKey" @change="handleRightTabChange">
                <Tabs.TabPane :key="2" tab="人员排班" />
                <Tabs.TabPane :key="1" tab="项目排班" />
              </Tabs>
            </div>
          </template>
          <template #headerCell="{ column }">
            <div v-if="column.dataIndex === 'name'">
              <div class="text-right">日期</div>
              <svg class="svg-line" xmlns="http://www.w3.org/2000/svg">
                <line x1="0" y1="0" x2="100%" y2="100%" stroke="#dcdfe6" stroke-width="1" />
              </svg>
              <div class="text-left">{{ activeRightKey === 1 ? '项目名称' : '姓名' }}</div>
            </div>
            <div class="whitespace-pre" v-else>{{ column.customTitle }} </div>
          </template>
          <template #bodyCell="{ record, column }">
            <template v-if="column.customTitle?.includes('星期')">
              <div>{{ record[column.dataIndex] }}</div>
            </template>
            <template v-else>{{ record[column.dataIndex] }} </template>
          </template>
        </BasicTable>
        <div class="h-40px absolute bottom-16px left-32px"> 备注：{{ remakeText }} </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table-form-container .ant-form {
      padding: 0;
      margin-bottom: 10px;
    }

    .svg-line {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
    }
  }
</style>
