<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { Button, Select } from 'ant-design-vue';
  import { computed, nextTick, ref } from 'vue';
  import dayjs from 'dayjs';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { useRequest } from '@ft/request';
  import { useUserStoreWithOut } from '@ft/internal/store/modules/user';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import { getNurseList } from '@ft/internal/api';
  import { getWeekDay } from './helper';
  import {
    getClassList,
    getNurseClassList,
    saveOrUpdateNurseClass,
  } from '/@/api/medicine-delivered-home/nursing-scheduling';
  const { userInfo } = useUserStoreWithOut();
  const go = useGo();
  const classTypeCode = useRouteQuery('classTypeCode', '', { transform: String });
  const classTypeName = useRouteQuery('classTypeName', '', { transform: String });
  const lastDate = useRouteQuery('lastDate', '', { transform: String });
  const firstDate = useRouteQuery('firstDate', '', { transform: String });
  const mode = useRouteQuery('mode', 'add', { transform: String });
  const columns = computed(() => {
    const _columns = [
      {
        title: '姓名',
        dataIndex: 'nurseName',
        align: 'center',
        width: 160,
        customHeaderCell() {
          return {
            style: 'position: relative',
          };
        },
      },
    ];
    for (let i = 0; i < 7; i++) {
      const dynamicDate = dayjs(firstDate.value).add(i, 'day');
      const week = getWeekDay(dynamicDate.format('YYYY-MM-DD'));
      _columns.push({
        title: `${week}\n${dynamicDate.format('MM-DD')}`,
        dataIndex: `${dynamicDate.format('YYYY-MM-DD')}`,
        align: 'center',
        width: 160,
      } as any);
    }
    return _columns;
  });
  const { loading } = useRequest(
    () =>
      getNurseClassList({
        orgId: userInfo?.orgId,
        classTypeCode: classTypeCode.value,
        startDate: firstDate.value,
        endDate: lastDate.value,
      }),
    {
      onSuccess: (data) => {
        const result = data.map((item) => {
          const weekClassList = item.weekClassList[0];
          const dates = Object.keys(weekClassList);
          const transformedItem = { ...item };
          dates.forEach((date) => {
            //没有排班的默认休息
            transformedItem[date] =
              weekClassList[date].length > 0
                ? weekClassList[date].map((classManage) => classManage.classManageId)
                : ['-1'];
          });
          return transformedItem;
        });
        const days: Recordable = {};
        for (let i = 0; i < 7; i++) {
          const dynamicDate = dayjs(firstDate.value).add(i, 'day');
          days[dynamicDate.format('YYYY-MM-DD')] = ['-1'];
        }
        if (result.length < 6) {
          const j = 6 - result.length;
          for (let i = 0; i < j; i++) {
            result.push({
              nurseId: '',
              nurseName: '',
              ...days,
            });
          }
        }
        tableAction.setTableData(result);
        refreshNurseOption();
      },
    },
  );
  const [register, tableAction] = useTable({
    loading,
    showIndexColumn: false,
    useSearchForm: false,
    bordered: true,
    inset: true,
    immediate: false,
    pagination: false,
    //@ts-ignore
    columns: computed(() => columns.value),
  });
  const getTitle = computed(() => {
    return mode.value === 'add' ? '新增排班' : '编辑排班';
  });
  const { data: nurseList = [] as any } = useRequest(() => getNurseList(userInfo?.orgId), {
    ready: !!userInfo?.orgId,
    onSuccess() {
      refreshNurseOption();
    },
  });
  const nurseOptions = ref([]);

  const { data: classTypeList = [] as any } = useRequest(
    () =>
      getClassList({
        orgId: userInfo?.orgId,
        classTypeCode: classTypeCode.value,
      }),
    {
      ready: !!userInfo?.orgId,
    },
  );

  const classTypeOptions = computed(() => {
    return (classTypeList.value || [])
      .map((item) => {
        return {
          label: item.className,
          value: item.id,
          classTime: item.classTime,
          disabled: item.status === 0,
        };
      })
      .concat({
        label: '休',
        value: '-1',
      });
  });
  const { loading: saveLoading, runAsync: saveRunAsync } = useRequest(saveOrUpdateNurseClass, {
    manual: true,
    showSuccessMessage: true,
  });
  function handleSave() {
    const data = tableAction.getDataSource();
    const isValuesEmpty = data
      .filter((item) => item.nurseId)
      .every((obj) => {
        return Object.values(obj).every((value) => value?.length > 0);
      });
    const { createConfirm } = useMessage();
    if (!isValuesEmpty) {
      createConfirm({
        iconType: 'warning',
        title: '提示',
        content: '温馨提醒，排班有空项未排，请检查',
      });
      return;
    } else {
      const dataSource = tableAction.getDataSource();
      const classeNurses = (dataSource || []).map((record) => {
        const keys = Object.keys(record).filter((key) => key.match(/^\d{4}-\d{2}-\d{2}$/));
        const nurseClasses = keys
          .map((key) => {
            const classManageIds = record[key];
            return classManageIds.map((id) => ({
              classManageId: id,
              classManageName:
                classTypeList.value.find((item) => item.id === id)?.className || '休',
              classDate: key,
            }));
          })
          .flat();
        return {
          deptId: record.deptId,
          deptName: record.deptName,
          nurseId: record.nurseId,
          nurseName: record.nurseName,
          nurseClasses,
        };
      });
      saveRunAsync({
        orgId: userInfo?.orgId,
        orgName: userInfo?.orgName,
        startDate: firstDate.value,
        endDate: lastDate.value,
        classTypeCode: classTypeCode.value,
        classTypeCodeName: classTypeName.value,
        classeNurses: classeNurses.filter((item) => item.nurseId),
      });
    }
  }

  function onClassTypeChange(val, model, key) {
    if (!model[key]) {
      model[key] = (val || []).filter((item) => item !== '-1');
    } else if (model[key]?.length === 1 && model[key][0] === '-1') {
      model[key] = (val || []).filter((item) => item !== '-1');
    } else if ((val || []).includes('-1')) {
      model[key] = ['-1'];
    } else {
      model[key] = val;
    }
  }

  function onNurseChange(val, model) {
    const selected = nurseList.value.find((item) => item.userId === val) || {};
    model.nurseId = selected.userId;
    model.nurseName = selected.employeeName;
    model.deptId = selected.deptId;
    model.deptName = selected.deptName;
    nextTick(() => {
      refreshNurseOption();
    });
  }

  function refreshNurseOption() {
    const nurseIds = tableAction.getDataSource().map((item) => item.nurseId);
    nurseOptions.value = (nurseList.value || []).map((item) => {
      return {
        label: item.employeeName,
        value: item.userId,
        disabled: nurseIds.includes(item.userId),
      };
    });
  }
</script>

<template>
  <div class="flex h-full p-4 bg-white min-w-757px rounded-2 rounded-lt-none">
    <BasicTable @register="register">
      <template #headerTop>
        <div class="flex justify-between">
          <div>{{ getTitle }}</div>
          <div>
            <Button class="mr-2" @click="go(-1)"> 返回 </Button>
            <Button type="primary" :loading="saveLoading" @click="handleSave"> 提交 </Button>
          </div>
        </div>
        <div class="flex justify-center items-center flex-col">
          <div class="text-20px text-#333">{{ classTypeName }}</div>
          <div class="flex justify-center items-center gap-4">
            <div>{{ firstDate }}～{{ lastDate }}</div>
          </div>
        </div>
      </template>
      <template #headerCell="{ column }">
        <div v-if="column.dataIndex === 'expertGroupName'">
          <div class="text-right">日期</div>
          <svg class="svg-line" xmlns="http://www.w3.org/2000/svg">
            <line x1="0" y1="0" x2="100%" y2="100%" stroke="#dcdfe6" stroke-width="1" />
          </svg>
          <div class="text-left">姓名</div>
        </div>
        <div class="whitespace-pre" v-else>{{ column.customTitle }} </div>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.customTitle?.includes('星期')">
          <Select
            :options="classTypeOptions"
            allow-clear
            style="width: 100%"
            mode="multiple"
            :value="record[column.dataIndex]"
            :defaultValue="['-1']"
            placeholder="请选择班次"
            @change="onClassTypeChange($event, record, column.dataIndex)"
          />
        </template>
        <template v-else>
          <Select
            :options="nurseOptions"
            allow-clear
            style="width: 100%"
            v-model:value="record[column.dataIndex]"
            placeholder="请选择护士"
            @change="onNurseChange($event, record)"
          />
        </template>
      </template>
    </BasicTable>
    <div class="h-40px absolute bottom-16px left-32px">
      备注：{{
        classTypeOptions
          .filter((item) => item.value !== '-1')
          .map((item) => `${item.label} ${item.classTime}`)
          .join(', ')
      }}
    </div>
  </div>
</template>

<style scoped lang="less">
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table-form-container .ant-form {
      padding: 0;
      margin-bottom: 10px;
    }

    .svg-line {
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
    }
  }
</style>
