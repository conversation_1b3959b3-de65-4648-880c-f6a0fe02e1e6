<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { Button } from 'ant-design-vue';
  import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue';
  import { useRequest } from '@ft/request';
  import { pushEducationContent, querySpecificDiseasesUser } from '/@/api/content';
  import { computed, nextTick, onMounted, ref } from 'vue';
  import type { TableRowSelection } from 'ant-design-vue/lib/table/interface';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { FixedAction } from '@ft/components';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { DictEnum, getDictItemList } from '@ft/internal/api';
  import dayjs from 'dayjs';
  import { cloneDeep } from 'lodash-es';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import { pushModalSchema } from './data';

  const columnId = useRouteQuery('columnId', '', { transform: String });
  const columnName = useRouteQuery('columnName', '', { transform: String });
  const selectedRowKeys = useRouteQuery('selectedRowKeys', '', {
    transform: (val: string) => val?.split(','),
  });
  const diseaseCode = useRouteQuery('diseaseCode', '', { transform: Number });

  const dictMap = {
    /**病毒性肝炎 */
    1: DictEnum.GA_HEPATITIS_TYPE,
    /**结核病 */
    3: DictEnum.TB_TYPE,
  };

  const divRef = ref<HTMLElement | null>(null);
  const go = useGo();

  function onBack() {
    go(-1);
  }

  const rightList = ref<any>([]);

  const [registerForm, formAction] = useForm({
    schemas: pushModalSchema,
    labelWidth: 150,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });

  onMounted(() => {
    formAction.setFieldsValue({
      columnName: columnName.value,
      columnId: columnId.value,
      educationIdList: selectedRowKeys.value,
    });
  });

  const { createMessage: msg } = useMessage();

  const { loading, runAsync: runAsyncPushEdcContent } = useRequest(
    () => pushEducationContent(formAction.getFieldsValue()),
    {
      manual: true,
      showSuccessMessage: true,
      async onBefore() {
        try {
          const userIdList = formAction.getFieldsValue().userIdList;
          const phoneList = formAction.getFieldsValue().phoneList;
          if (!userIdList?.length && !phoneList?.length) {
            msg.error('请选择患者');
            return false;
          }
          await formAction.validate();
          return true;
        } catch (e) {
          return false;
        }
      },
      onSuccess() {
        onBack();
      },
    },
  );

  const TableColumns: BasicColumn[] = [
    {
      dataIndex: 'idCardNo',
      title: '身份证号',
      width: 170,
    },
    {
      dataIndex: 'phone',
      title: '手机号',
      width: 120,
    },
    {
      dataIndex: 'patientName',
      title: '姓名',
      width: 80,
      align: 'left',
    },
    {
      dataIndex: 'sexName',
      title: '性别',
      width: 80,
    },
    {
      dataIndex: 'lastVisitTime',
      title: '上次治疗时间',
      width: 100,
    },
    // 个案类型
    {
      dataIndex: 'profileType',
      title: '个案类型',
      width: 100,
    },
    // 个案状态
    {
      dataIndex: 'caseStatusDesc',
      title: '个案状态',
      width: 100,
    },
  ];

  const schemas: FormSchema[] = [
    {
      field: 'keyword',
      label: ' ',
      disabledLabelWidth: true,
      component: 'Input',
      componentProps: {
        placeholder: '请输入身份证号或姓名',
      },
      colProps: { span: 8 },
    },
    // caseStatusCode
    {
      field: 'caseStatusCode',
      label: ' ',
      disabledLabelWidth: true,
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请选择个案状态',
        api: () => getDictItemList(DictEnum.CASE_STUTAS),
        style: 'width: 100%',
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        showSearch: true,
        optionFilterProp: 'label',
        getPopupContainer: () => document.body,
        disabled: !dictMap[diseaseCode.value],
      },
      defaultValue: !dictMap[diseaseCode.value] ? undefined : '1',
      colProps: { span: 8 },
    },
    // diseaseSubCodeList 个案子类型编码
    {
      field: 'diseaseSubCodeList',
      label: ' ',
      disabledLabelWidth: true,
      component: 'ApiSelect',
      componentProps: {
        placeholder: '请选择个案类型',
        api: () => {
          if (!dictMap[diseaseCode.value]) return Promise.resolve([]);
          return getDictItemList(dictMap[diseaseCode.value]);
        },
        disabled: !dictMap[diseaseCode.value],
        style: 'width: 100%',
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        showSearch: true,
        optionFilterProp: 'label',
        getPopupContainer: () => document.body,
        mode: 'multiple',
        maxTagCount: 1,
      },
      colProps: { span: 8 },
    },

    // lastVisitDate
    {
      field: 'lastVisitDate',
      label: '上次治疗时间',
      component: 'RangePicker',
      componentProps: {
        format: 'YYYY-MM-DD',
        style: { width: '100%' },
        getPopupContainer: () => document.body,
      },
      colProps: { span: 16 },
    },
  ];

  function filterList(params, list) {
    return new Promise((resolve) => {
      setTimeout(() => {
        let filterList = cloneDeep(list);
        if (params.keyword) {
          filterList = filterList.filter(
            (item) =>
              item.idCardNo.includes(params.keyword) || item.patientName.includes(params.keyword),
          );
        }
        if (params.caseStatusCode) {
          filterList = filterList.filter(
            (item) => item.caseStatusCode === Number(params.caseStatusCode),
          );
        }
        if (params.diseaseCode) {
          filterList = filterList.filter(
            (item) => item.infectionSubDisease === Number(params.diseaseCode),
          );
        }
        if (params.lastVisitDateStart && params.lastVisitDateEnd) {
          filterList = filterList.filter((item) => {
            return (
              item.lastVisitTime &&
              dayjs(item.lastVisitTime) >= dayjs(params.lastVisitDateStart) &&
              dayjs(item.lastVisitTime) <= dayjs(params.lastVisitDateEnd)
            );
          });
        }
        resolve(filterList);
      }, 100);
    });
  }

  function getRightList(params) {
    return filterList(params, rightList.value);
  }

  const uniqKey = 'patientProfileRecordId';

  async function warpperApi(params) {
    const res = await querySpecificDiseasesUser(params);
    return res?.filter(
      (item) => !rightList.value?.map((item) => item[uniqKey])?.includes(item[uniqKey]),
    );
  }
  const [registerLeftTable, leftTableAction] = useTable({
    api: warpperApi,
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas,
      fieldMapToTime: [['lastVisitDate', ['lastVisitDateStart', 'lastVisitDateEnd']]],
      actionColOptions: {
        span: 8,
      },
    },
    beforeFetch(params) {
      return {
        ...params,
        diseaseCode: diseaseCode.value,
      };
    },
    afterFetch(data) {
      return data?.filter(
        (item) => !rightList.value?.map((item) => item[uniqKey])?.includes(item[uniqKey]),
      );
    },
    columns: TableColumns,
    showIndexColumn: false,
    rowKey: uniqKey,
    isCanResizeParent: true,
    canResize: true,
    size: 'small',
    scroll: {
      y: 250,
    },
    pagination: {
      pageSize: 20,
      pageSizeOptions: ['20', '50', '100'],
    },
  });

  const [registerRightTable, rightTableAction] = useTable({
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas,
      fieldMapToTime: [['lastVisitDate', ['lastVisitDateStart', 'lastVisitDateEnd']]],
      actionColOptions: {
        span: 8,
      },
    },
    api: getRightList,
    afterFetch(data) {
      console.log(`afterFetch data`, data);
      return data;
    },
    columns: TableColumns,
    showIndexColumn: false,
    rowKey: uniqKey,
    isCanResizeParent: true,
    canResize: true,
    size: 'small',
    scroll: {
      y: 250,
    },
    pagination: {
      pageSize: 20,
      pageSizeOptions: ['20', '50', '100'],
    },
  });

  const leftSelectedRowKeys = ref<string[]>([]);
  const leftRowSelection = computed<TableRowSelection>(() => {
    return {
      type: 'checkbox',
      selectedRowKeys: leftSelectedRowKeys.value,
      onChange: (keys: any, _) => {
        leftSelectedRowKeys.value = keys;
      },
    };
  });
  const rightSelectedRowKeys = ref<string[]>([]);

  const rightRowSelection = computed<TableRowSelection>(() => {
    return {
      type: 'checkbox',
      selectedRowKeys: rightSelectedRowKeys.value,
      onChange: (keys: any, _) => {
        rightSelectedRowKeys.value = keys;
      },
    };
  });

  const handleAdd = (model, field) => {
    const targetKeys = rightList.value.map((item) => item[uniqKey]);
    const diff = leftSelectedRowKeys.value.filter((val) => !targetKeys.includes(val));
    const filterLeftList = leftTableAction.getDataSource();
    rightList.value = rightList.value.concat(
      diff.map((key) => filterLeftList.find((user) => user[uniqKey] === key)),
    );
    leftSelectedRowKeys.value = [];
    model[field] = rightList.value.map((item) => item.id).filter(Boolean);
    model.phoneList = rightList.value.map((item) => item.phone);
    nextTick(leftTableAction.reload);
    nextTick(rightTableAction.reload);
  };

  const handleRemove = (model, field) => {
    rightList.value = rightList.value.filter(
      (item) => !rightSelectedRowKeys.value.includes(item[uniqKey]),
    );
    rightSelectedRowKeys.value = [];
    model[field] = rightList.value.map((item) => item.id).filter(Boolean);
    model.phoneList = rightList.value.map((item) => item.phone);
    nextTick(leftTableAction.reload);
    nextTick(rightTableAction.reload);
  };

  const handlePush = () => {
    if (formAction.getFieldsValue().pushType === 0) {
      runAsyncPushEdcContent();
    } else {
      loading.value = true;
      setTimeout(() => {
        loading.value = false;
        onBack();
      }, 500);
    }
  };
</script>

<template>
  <div ref="divRef" class="h-[calc(100%-16px)] w-[calc(100%-16px)] bg-white rounded">
    <div class="h-full w-full flex flex-col">
      <div class="flex p-4 items-center justify-between border-b border-[#EDEEF0]">
        <span class="text-base font-bold"> 定向推送 </span>
      </div>
      <div class="page-body p-4 flex-1 basis-0 min-h-0 of-y-auto pb-20">
        <BasicForm @register="registerForm">
          <template #userIdListSlot="{ model, field }">
            <div class="w-[150px] text-right">选择患者：</div>
            <div class="flex gap-4 px-4 h-[480px]">
              <div class="flex-1 of-hidden border border-[#EDEEF0] rounded">
                <BasicTable :row-selection="leftRowSelection" @register="registerLeftTable" />
              </div>
              <div class="flex flex-col gap-2 justify-center">
                <Button
                  @click="handleAdd(model, field)"
                  :disabled="leftSelectedRowKeys.length === 0"
                >
                  <RightOutlined />
                </Button>
                <Button
                  @click="handleRemove(model, field)"
                  :disabled="rightSelectedRowKeys.length === 0"
                >
                  <LeftOutlined />
                </Button>
              </div>
              <div class="flex-1 of-hidden border border-[#EDEEF0] rounded">
                <BasicTable :row-selection="rightRowSelection" @register="registerRightTable" />
              </div>
            </div>
          </template>
          <template #hint>
            <span class="text-#bfbfbf">
              注:若查不到某专病的患者，可能是该患者未注册宜健通进本平台，需先注册宜健通使用本平台后才能定向推送
            </span>
          </template>
        </BasicForm>
      </div>
    </div>
    <FixedAction class="justify-end pr-4" :reference-el="divRef">
      <Button @click="onBack"> 返回 </Button>
      <!-- <Button type="primary" ghost @click="onReset" v-if="!isEditMode">重置</Button> -->
      <Button type="primary" :loading="loading" @click="handlePush"> 确认 </Button>
    </FixedAction>
  </div>
</template>
