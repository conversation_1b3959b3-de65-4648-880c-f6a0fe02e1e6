<script setup lang="ts">
  import { StyledList } from '@ft/components';
  import { ref } from 'vue';
  import Right from './Right.vue';
  import { useRequest } from '@ft/request';
  import { queryEducationColumnList } from '/@/api/edc';
  const items = ref([]);
  const activeKey = ref('');
  const activeName = ref('');
  const activeDiseaseCode = ref<number>(0);

  useRequest(() => queryEducationColumnList({ columnStatus: 0 }), {
    onSuccess(data) {
      items.value = data?.map((v) => {
        return {
          label: v.columnName,
          value: v.id,
          ...v,
        };
      });
      activeKey.value = data[0]?.id;
      activeName.value = data[0]?.columnName;
      activeDiseaseCode.value = data[0]?.diseaseCode;
    },
  });
  function handleChange(val) {
    activeName.value = val.label;
    activeDiseaseCode.value = val.diseaseCode;
  }
</script>

<template>
  <div class="h-full pr-4 pb-4">
    <div class="h-full bg-white rounded rounded-lt-0 flex">
      <div class="p-4 border-r-1 border-[#EDEEF0]">
        <span class="text-base font-bold">宣教分类</span>
        <div class="mt-4">
          <StyledList :items="items" v-model="activeKey" @change="handleChange" />
        </div>
      </div>
      <div class="content flex-1 of-hidden">
        <Right
          :activeItemName="activeName"
          :columnId="activeKey"
          :diseaseCode="activeDiseaseCode"
        />
      </div>
    </div>
  </div>
</template>
<style scoped></style>
