import type { BasicColumn } from '@ft/internal/components/Table';
import type { FormSchema } from '@ft/internal/components/Form';
import { EDU_DOWNLOAD_URL } from '/@/api/content';
const targetOptions = [
  { label: '患者', value: 0 },
  { label: '本机构医务人员', value: 1 },
  { label: '本市医务人员', value: 2 },
];
export const columns: BasicColumn[] = [
  {
    title: '宣教名称',
    dataIndex: 'name',
    width: 110,
    align: 'left',
  },
  {
    title: '宣教内容',
    dataIndex: 'content',
    width: 110,
    align: 'left',
  },
  {
    title: '宣教对象',
    dataIndex: 'target',
    width: 110,
    align: 'left',
    format: (text) => {
      return targetOptions.find((item) => item.value === +text)!.label;
    },
  },
  {
    title: '附件',
    dataIndex: 'fileName',
    width: 180,
    align: 'left',
  },
  {
    title: '创建机构',
    dataIndex: 'orgName',
    width: 110,
    align: 'left',
  },
  {
    title: '创建人',
    dataIndex: 'createUser',
    width: 110,
    align: 'left',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150,
    align: 'left',
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'name',
    label: '宣教名称',
    component: 'Input',
    colProps: { span: 12 },
  },
];
export const modalSchema: FormSchema[] = [
  {
    field: 'id',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'columnId',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'columnName',
    label: '宣教分类',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'name',
    label: '宣教名称',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
    required: true,
  },
  {
    label: '宣教内容',
    field: 'content',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  // {
  //   field: 'content',
  //   label: '宣教内容',
  //   component: 'Tinymce',
  //   componentProps: {
  //     height: 250,
  //     showImageUpload: false,
  //     toolbar:
  //       'undo redo fontselect align forecolor bold italic underline strikethrough bullist numlist link code blockquote',
  //     options: {
  //       menubar: false,
  //       statusbar: false,
  //       // skin: 'childcare',
  //     },
  //   },
  //   itemProps: {
  //     wrapperCol: {
  //       span: 20,
  //     },
  //   },
  // },
  {
    label: '宣教对象',
    field: 'target',
    component: 'Select',
    componentProps: {
      options: [
        { label: '患者', value: 0 },
        { label: '本机构医务人员', value: 1 },
        { label: '本市医务人员', value: 2 },
      ],
    },
    defaultValue: 0,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '宣教发布状态',
    field: 'status',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 0 },
        { label: '未启用', value: 1 },
      ],
      allowClear: false,
    },
    defaultValue: 0,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    field: 'fileName',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'fileUrl',
    label: '上传附件',
    component: 'UploadAsset',
    componentProps: {
      uploadType: 'file',
      resultField: 'data.url',
      modelNameField: 'fileName',
      maxCount: 1,
      // download: true,
      newWindowPreview: true,
      action: EDU_DOWNLOAD_URL,
    },
    colProps: {
      span: 24,
    },
    itemProps: {
      wrapperCol: {
        span: 16,
      },
    },
  },
];

export const pushModalSchema: FormSchema[] = [
  {
    field: 'columnId',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'columnName',
    label: '宣教分类',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'educationIdList',
    label: '宣教名称',
    component: 'Select',
    show: false,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '选择患者',
    field: 'userIdList',
    component: 'Input',
    colSlot: 'userIdListSlot',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
    required: true,
  },
  // 推送对象用户手机号集合
  {
    field: 'phoneList',
    label: '推送对象用户手机号集合',
    component: 'CheckboxGroup',
    show: false,
  },
  {
    field: 'hint',
    label: ' ',
    disabledLabelWidth: true,
    component: 'Input',
    slot: 'hint',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 }, colon: false },
  },
  // 推送方式
  {
    field: 'pushType',
    label: '推送方式',
    component: 'RadioGroup',
    componentProps: {
      options: [{ label: '短信', value: 0 }],
    },
    colProps: { span: 24 },
    defaultValue: 0,
  },
];
