<script setup lang="ts">
  import { computed, nextTick, ref, watch } from 'vue';
  import type { ActionItem, TableRowSelection } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Switch, Tabs } from 'ant-design-vue';
  import { Button } from '@ft/internal/components/Button';
  import { useModal } from '@ft/internal/components/Modal';
  import { useUserStore } from '@ft/internal/store/modules/user';
  import { useRequest } from '@ft/request';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import Modal from './Modal.vue';
  import { columns, formSchema } from './data';
  import type { IEducationContentItem } from '/@/api/content';
  import {
    deleteEducationContent,
    queryEducationContentPage,
    updateEducationContent,
    updateEducationContentTop,
  } from '/@/api/content';

  const userStore = useUserStore();
  let getOrgId = computed(() => {
    if (activeTab.value === 'org') {
      return userStore.getUserInfo.orgId;
    } else {
      return '';
    }
  });
  const props = defineProps({
    activeItemName: {
      type: String,
      default: '',
    },
    columnId: {
      type: String,
      default: '',
    },
    diseaseCode: {
      type: Number,
      default: 0,
    },
  });
  const newColumns = computed(() =>
    columns.concat(
      activeTab.value === 'org'
        ? {
            title: '宣教发布状态',
            dataIndex: 'status',
            width: 110,
            align: 'left',
          }
        : [],
    ),
  );
  const activeTab = ref('org');
  const [registerTable, tableInstance] = useTable({
    api: queryEducationContentPage,
    columns: newColumns,
    formConfig: {
      colon: true,
      labelWidth: 70,
      showAdvancedButton: false,
      actionColOptions: {
        span: 12,
        style: {
          textAlign: 'right',
        },
      },
      schemas: formSchema,
    },
    useSearchForm: true,
    bordered: false,
    size: 'small',
    resizeHeightOffset: 10,
    actionColumn: {
      width: 220,
      title: '操作',
      dataIndex: 'action',
    },
    immediate: false,
    beforeFetch: (params) => {
      params.orgId = getOrgId.value;
      params.columnId = props.columnId;
      return params;
    },
    afterFetch: (data) => {
      data.forEach((item) => {
        item.loading = false;
      });
      return data;
    },
  });
  watch(
    () => props.columnId,
    (val, oldVal) => {
      if (val === oldVal) return;
      if (!val) return;
      nextTick(() => {
        tableInstance?.reload();
      });
    },
    {
      immediate: true,
    },
  );
  watch(activeTab, (val) => {
    console.log(80, val);
    nextTick(() => {
      tableInstance.reload();
    });
  });

  const [registerModal, { openModal }] = useModal();
  function onAdd() {
    openModal(true, {
      mode: 'add',
    });
  }
  function onLook(record) {
    openModal(true, {
      mode: 'look',
      record,
    });
  }
  function onEdit(record) {
    openModal(true, {
      mode: 'edit',
      record,
    });
  }
  const { runAsync: deleteRunAsync } = useRequest(deleteEducationContent, {
    manual: true,
    showSuccessMessage: true,
    onSuccess() {
      tableInstance.reload();
    },
  });
  function onRemove(record) {
    deleteRunAsync(record.id);
  }
  const { runAsync: runUpdateEducationContentTop } = useRequest(updateEducationContentTop, {
    manual: true,
    showSuccessMessage: () => '操作成功',
    onSuccess() {
      tableInstance.reload();
    },
  });
  function onTop(record: IEducationContentItem) {
    runUpdateEducationContentTop({
      id: record.id!,
      topFlag: record.topFlag === 0 ? 1 : 0,
    });
  }
  function createActions(record: Recordable, _column): ActionItem[] {
    if (activeTab.value === 'org') {
      return [
        {
          label: record.topFlag === 0 ? '置顶' : '取消置顶',
          type: 'link',
          onClick: onTop.bind(null, record),
        },
        {
          label: '查看',
          type: 'link',
          onClick: onLook.bind(null, record),
        },
        {
          label: '编辑',
          type: 'link',
          onClick: onEdit.bind(null, record),
        },
        {
          label: '删除',
          type: 'link',
          danger: true,
          popConfirm: {
            title: '确定删除吗？',
            placement: 'topRight',
            okButtonProps: { danger: true },
            confirm: onRemove.bind(null, record),
          },
        },
      ];
    } else {
      return [
        {
          label: '查看',
          type: 'link',
          onClick: onLook.bind(null, record),
        },
      ];
    }
  }
  const { runAsync: runAsyncUpdateEducationContent } = useRequest(updateEducationContent, {
    manual: true,
    showSuccessMessage: true,
    onBefore([record]) {
      record.loading = true;
    },
    onSuccess() {
      tableInstance.reload();
    },
    onFinally([record]) {
      record.loading = false;
    },
  });
  function onSwitchChange(checked: string, record) {
    runAsyncUpdateEducationContent({
      ...record,
      status: checked as unknown as number,
    });
  }
  const selectedRowKeys = ref<TableRowSelection['selectedRowKeys']>([]);
  const handleSelectionChange: TableRowSelection['onChange'] = (keys) => {
    selectedRowKeys.value = keys;
    console.log(selectedRowKeys.value);
  };

  const go = useGo();
  function onPush() {
    go({
      name: 'ReleasePush',
      query: {
        columnId: props.columnId,
        columnName: props.activeItemName,
        selectedRowKeys: selectedRowKeys.value?.join(','),
        diseaseCode: props.diseaseCode,
      },
    });
  }
</script>

<template>
  <BasicTable
    class="ft-main-table"
    :row-selection="
      activeTab === 'org'
        ? {
            selectedRowKeys,
            onChange: handleSelectionChange,
          }
        : null
    "
    row-key="id"
    @register="registerTable"
  >
    <template #headerTop>
      <div class="text-base font-bold">{{ activeItemName }}宣教列表</div>
      <Tabs v-model:activeKey="activeTab">
        <Tabs.TabPane key="org" tab="本机构发布" />
        <Tabs.TabPane key="all" tab="全市发布" />
      </Tabs>
    </template>
    <template #tableTitle>
      <div class="flex gap-2" v-if="activeTab === 'org'">
        <Button type="primary" @click="onAdd">新增</Button>
        <Button :disabled="selectedRowKeys?.length === 0" @click="onPush">定向推送</Button>
      </div>
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'fileName'">
        <a :href="record.fileUrl" target="_blank">{{ record.fileName }}</a>
      </template>
      <template v-if="column.dataIndex === 'status'">
        <Switch
          :loading="record.loading"
          :checked-value="0"
          :un-checked-value="1"
          v-model:checked="record[column.dataIndex]"
          @change="onSwitchChange($event as unknown as any, record)"
        />
      </template>
      <template v-if="column.dataIndex === 'action'">
        <TableAction :actions="createActions(record, column)" />
      </template>
    </template>
  </BasicTable>
  <Modal
    :columnName="activeItemName"
    :columnId="columnId"
    :orgId="getOrgId"
    @register="registerModal"
    @success="tableInstance.reload"
  />
</template>

<style lang="less" scoped>
  :deep {
    .ft-main-table .vben-basic-form--compact .ant-col .ant-form-item {
      margin-bottom: 6px !important;
    }

    .ant-form {
      margin-bottom: 0 !important;
    }
  }
</style>
