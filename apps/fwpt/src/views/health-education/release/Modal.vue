<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { modalSchema } from './data';
  import { useRequest } from '@ft/request';
  import { addEducationContent, updateEducationContent } from '/@/api/content';
  const props = defineProps({
    columnId: {
      type: String,
      default: '',
    },
    columnName: {
      type: String,
      default: '',
    },
    orgId: {
      type: String,
      default: '',
    },
  });
  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm({
    schemas: modalSchema,
    labelWidth: 150,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });

  const mode = ref('');
  const getTitle = computed(() => {
    if (mode.value === 'add') {
      return '新增宣教';
    } else if (mode.value === 'edit') {
      return '编辑宣教';
    } else {
      return '查看宣教';
    }
  });

  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data?.mode;
    if (!data.record) {
      formAction.setFieldsValue({
        columnName: props.columnName,
        columnId: props.columnId,
      });
    } else {
      if (mode.value == 'look')
        formAction.setProps({
          disabled: true,
        });
      formAction.setFieldsValue(data.record);
    }
  });

  const { loading, runAsync: runAsyncSaveEdcContent } = useRequest(
    (params) =>
      mode.value === 'edit' ? updateEducationContent(params) : addEducationContent(params),
    { manual: true, showSuccessMessage: true },
  );

  async function handleOk() {
    if (mode.value === 'look') {
      closeModal();
    } else {
      formAction.validate().then((values) => {
        console.log('103', values);
        runAsyncSaveEdcContent({
          ...values,
          orgId: props.orgId,
        }).then(() => {
          emit('success');
          closeModal();
        });
      });
    }
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="getTitle"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    width="600px"
    @register="register"
    @ok="handleOk"
  >
    <BasicForm style="transform: translateX(-36px)" @register="registerForm" />
  </BasicModal>
</template>
