import { DictEnum, getDictItemList } from '@ft/internal/api';
import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';

/**
 * 宣教栏目
 * 状态
 */
export const searchFormSchema: FormSchema[] = [
  {
    field: 'columnName',
    label: '宣教栏目',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'columnStatus',
    label: '状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 0 },
        { label: '未启用', value: 1 },
      ],
    },
    // defaultValue: '1',
    colProps: { span: 6 },
  },
];

/**
 * 宣教栏目
 * 栏目备注
 * 栏目状态
 * 创建人
 * 创建时间
 */
export const columns: BasicColumn[] = [
  {
    title: '宣教栏目',
    dataIndex: 'columnName',
    width: 200,
    align: 'left',
  },
  {
    title: '栏目备注',
    dataIndex: 'columnRemark',
    width: 200,
    align: 'left',
  },
  {
    title: '栏目状态',
    dataIndex: 'columnStatus',
    width: 200,
    align: 'left',
  },
  {
    title: '创建人',
    dataIndex: 'createUser',
    width: 200,
    align: 'left',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 200,
    align: 'left',
  },
];

/**
 * 宣教栏目
 * 栏目状态
 * 栏目备注
 */
export const editFormSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '宣教栏目',
    field: 'columnName',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  // 病种
  {
    label: '病种',
    field: 'diseaseCode',
    component: 'ApiSelect',
    required: true,
    componentProps: ({ formModel }) => {
      return {
        style: 'width: 100%',
        api: () => getDictItemList(DictEnum.INFECTIOUS_DISEASE),
        getPopupContainer: () => document.body,
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        valueToString: formModel?.diseaseCode ? true : false,
      };
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '栏目状态',
    field: 'columnStatus',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 0 },
        { label: '未启用', value: 1 },
      ],
      allowClear: false,
    },
    defaultValue: 1,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '栏目备注',
    field: 'columnRemark',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
];
