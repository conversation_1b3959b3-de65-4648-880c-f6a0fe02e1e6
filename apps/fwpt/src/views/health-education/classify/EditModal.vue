<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { computed, ref } from 'vue';
  import { editFormSchema } from './data';
  import { useRequest } from '@ft/request';
  import { addEducationColumn, updateEducationColumn } from '/@/api/edc';

  const emit = defineEmits(['register', 'success']);

  const mode = ref('add');
  const getTitle = computed(() => (mode.value === 'add' ? '新增宣教栏目' : '编辑宣教栏目'));

  const [registerForm, formAction] = useForm({
    labelWidth: 100,
    schemas: editFormSchema,
    colon: true,
    showActionButtonGroup: false,
  });
  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data.mode;
    if (!data.record) return;
    formAction.setFieldsValue(data.record);
  });
  const { loading, runAsync: runAsyncSaveEdc } = useRequest(
    () =>
      mode.value === 'edit'
        ? updateEducationColumn(formAction.getFieldsValue())
        : addEducationColumn(formAction.getFieldsValue()),
    {
      manual: true,
      showSuccessMessage: true,
      async onBefore() {
        try {
          await formAction.validate();
          return true;
        } catch (e) {
          return false;
        }
      },
      onSuccess() {
        emit('success');
        closeModal();
      },
    },
  );
</script>

<template>
  <BasicModal
    @register="register"
    :destroy-on-close="true"
    :can-fullscreen="false"
    v-bind="$attrs"
    :width="450"
    :title="getTitle"
    :ok-button-props="{
      loading,
    }"
    @ok="runAsyncSaveEdc"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<style scoped></style>
