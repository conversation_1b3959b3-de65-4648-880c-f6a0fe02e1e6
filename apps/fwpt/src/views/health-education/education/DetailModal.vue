<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import Icon from '@ft/internal/components/Icon/src/Icon.vue';
  import { useRequest } from '@ft/request';
  import { queryEducationContentDetail } from '/@/api/content';
  import { ref } from 'vue';
  import { detailFormSchema } from './data';
  defineEmits(['register']);

  const [registerForm, formAction] = useForm({
    labelWidth: 100,
    schemas: detailFormSchema,
    showActionButtonGroup: false,
    disabled: true,
  });
  const fileDetail = ref({
    fileName: '',
    fileUrl: '',
  });
  const { runAsync } = useRequest(queryEducationContentDetail, { manual: true });
  const [register, { closeModal }] = useModalInner((data) => {
    runAsync(data.id).then((values) => {
      fileDetail.value.fileName = values.fileName;
      fileDetail.value.fileUrl = values.fileUrl;
      formAction.setFieldsValue(values);
    });
  });
  function handleOk() {
    closeModal();
  }
</script>

<template>
  <BasicModal @register="register" :width="600" title="宣教内容" @ok="handleOk">
    <BasicForm @register="registerForm">
      <template #fileLinkSlot>
        <Icon icon="file|svg" :size="16" />
        <a :href="fileDetail.fileUrl" download="" class="ml-2">{{ fileDetail.fileName }}</a>
      </template>
    </BasicForm>
  </BasicModal>
</template>
<style scoped></style>
