import type { FormSchema } from '@ft/internal/components/Table';

/**
 * 宣教分类
 * 宣教名称
 * 宣教内容
 */
export const detailFormSchema: FormSchema[] = [
  {
    label: '宣教分类',
    field: 'columnName',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '宣教名称',
    field: 'name',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '宣教内容',
    field: 'content',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: ' ',
    field: 'link',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
    slot: 'fileLinkSlot',
  },
];
