<script setup lang="ts">
  import { StyledList } from '@ft/components';
  import { ref } from 'vue';
  import Right from './Right.vue';
  import { useRequest } from '@ft/request';
  import { queryEducationColumnList } from '/@/api/edc';

  const activeKey = ref('');
  const activeName = ref('');
  const items = ref([]);
  useRequest(() => queryEducationColumnList({ columnStatus: 0 }), {
    onSuccess(data) {
      items.value = data?.map((v) => {
        return {
          label: v.columnName,
          value: v.id,
        };
      });
      activeKey.value = data[0]?.id;
      activeName.value = data[0]?.columnName;
    },
  });
  function handleChange(val) {
    activeName.value = val.label;
  }
</script>

<template>
  <div class="h-full pr-4 pb-4">
    <div class="h-full bg-white rounded rounded-lt-0 flex">
      <div class="p-4 border-r-1 border-[#EDEEF0]">
        <span class="text-base font-bold">宣教分类</span>
        <div class="mt-4">
          <StyledList :items="items" v-model="activeKey" @change="handleChange" />
        </div>
      </div>
      <div class="content flex-1">
        <Right :activeName="activeName" :columnId="activeKey" />
      </div>
    </div>
  </div>
</template>
<style scoped></style>
