<script setup lang="ts">
  import { Input, Pagination } from 'ant-design-vue';
  import { Icon } from '@ft/internal/components/Icon';
  import { ref, watch } from 'vue';
  import { useModal } from '@ft/internal/components/Modal';
  import DetailModal from './DetailModal.vue';
  import { queryEducationContentPage } from '/@/api/content';
  import type { IEducationContentItem } from '/@/api/content';

  const props = defineProps({
    activeName: {
      type: String,
      default: '',
    },
    columnId: {
      type: String,
      default: '',
    },
  });
  const current = ref(1);
  const pageSize = ref(10);
  const total = ref(0);
  const searchValue = ref('');
  const educationContentList = ref<IEducationContentItem[]>([]);
  async function getEducationContentList() {
    const res = await queryEducationContentPage({
      columnId: props.columnId,
      name: searchValue.value,
      pageNum: current.value,
    });
    educationContentList.value = res.list;
    total.value = res.total;
  }
  watch(
    () => props.columnId,
    (val) => {
      if (!val) return false;
      getEducationContentList();
    },
  );
  const [registerDetailModal, { openModal }] = useModal();
  function onPreview(id: String) {
    openModal(true, {
      id,
    });
  }
  function onChange() {
    getEducationContentList();
  }
</script>

<template>
  <div class="h-full">
    <div class="border-b border-[#EDEEF0] p-4">
      <span class="text-base font-bold">{{ activeName }}</span>
    </div>
    <div class="h-[calc(100%-57px)] flex flex-col p-4">
      <div class="page-heder flex items-center justify-between mb-16px">
        <div>健康宣教：</div>
        <div>
          <Input allow-clear placeholder="搜索" v-model:value="searchValue">
            <template #suffix>
              <Icon
                class="cursor-pointer"
                icon="ant-design:search-outlined"
                @click="getEducationContentList"
              />
            </template>
          </Input>
        </div>
      </div>
      <div
        class="page-body grid sm:grid-cols-2 md:grid-cols-3 2xl:grid-cols-5 gap-4 auto-rows-min flex-1 basis-0 of-y-auto"
      >
        <div
          class="relative p-4 border rounded border-[#DCDFE6] min-w-266px transition hover:shadow-[0px_4px_16px_0px_rgba(0,0,0,0.08)] cursor-pointer group"
          v-for="item in educationContentList"
          :key="item.id"
          @click="onPreview(item.id)"
        >
          <div class="font-bold mb-2">{{ item.name }}</div>
          <div>
            <span class="text-info-text-color">创建人：</span>
            <span>{{ item.createUser }}</span>
          </div>
          <div>
            <span class="text-info-text-color">创建时间：</span>
            <span>{{ item.createTime }}</span>
          </div>
        </div>
      </div>
      <div class="footer flex justify-end py-3 px-4">
        <Pagination
          :show-total="(total) => `共${total}条`"
          show-quick-jumper
          v-model:current="current"
          v-model:page-size="pageSize"
          :total="total"
          show-size-changer
          @change="onChange"
        />
      </div>
    </div>
    <DetailModal @register="registerDetailModal" />
  </div>
</template>
<style scoped></style>
