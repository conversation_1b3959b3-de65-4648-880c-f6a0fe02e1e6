import { infectiousDiseaseClassificationRelevanceQueryByCode } from '@ft/internal/api';
import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';

/**
患者姓名
性别
年龄
身份证号
电话
就诊类别
就诊诊断
就诊机构
离院日期

 */
export const columns: BasicColumn[] = [
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    width: 150,
    align: 'left',
  },
  {
    title: '性别',
    dataIndex: 'sex',
    width: 80,
    align: 'left',
  },
  {
    title: '年龄',
    dataIndex: 'age',
    width: 80,
    align: 'left',
  },
  {
    title: '身份证号',
    dataIndex: 'idCardNo',
    width: 180,
    align: 'left',
  },
  {
    title: '电话',
    dataIndex: 'telephone',
    width: 150,
    align: 'left',
  },
  {
    title: '就诊类别',
    dataIndex: 'visitType',
    width: 100,
    align: 'left',
  },
  {
    title: '就诊诊断',
    dataIndex: 'visitDiagnosisName',
    width: 150,
    align: 'left',
  },
  {
    title: '就诊机构',
    dataIndex: 'visitHospitalName',
    width: 180,
    align: 'left',
  },
  {
    title: '离院日期',
    dataIndex: 'leaveDate',
    width: 180,
    align: 'left',
  },
];

export const SearchSchemas: FormSchema[] = [
  {
    field: 'leaveDate',
    component: 'RangePicker',
    label: '离院日期',
    colProps: {
      span: 6,
    },
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      showTime: false,
      style: {
        width: '100%',
      },
    },
  },
  {
    field: 'chronicDiseaseCode',
    component: 'ApiSelect',
    label: '慢病病种',
    componentProps: () => {
      return {
        disabled: true,
        api: () => infectiousDiseaseClassificationRelevanceQueryByCode({ code: '005' }),
        getPopupContainer: () => document.body,
        labelField: 'infectiousDiseaseName',
        valueField: 'infectiousDiseaseCode',
      };
    },
    colProps: {
      span: 6,
    },
  },
];
