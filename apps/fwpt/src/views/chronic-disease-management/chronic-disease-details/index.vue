<script setup lang="ts">
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { useRequest } from '@ft/request';
  import { Button } from 'ant-design-vue';
  import { exportUtil } from '@ft/internal/utils';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { useRoute } from 'vue-router';
  import { SearchSchemas, columns } from './data';
  import { exportPatientList, getPatientList } from '/@/api/chronic-disease-management/workbench';

  const chronicDiseaseCode = useRouteQuery('chronicDiseaseCode', '', { transform: String });
  const { query } = useRoute();

  const [register, ActionTable] = useTable({
    columns: columns,
    api: getPatientList,
    resizeHeightOffset: 20,
    // inset: true,
    useSearchForm: true,
    showIndexColumn: false,
    actionColumn: {
      title: '操作',
      dataIndex: 'action',
      width: 120,
    },
    afterFetch: (data) => {
      ActionTable.getForm().setFieldsValue({
        chronicDiseaseCode: chronicDiseaseCode.value,
        leaveDate: [query.startTime, query.endTime],
      });
      return data;
    },
    beforeFetch: (params) => {
      params.chronicDiseaseCode = chronicDiseaseCode.value;
      params.leaveStartDate = query.startTime;
      params.leaveEndDate = query.endTime;
      return { ...params, ...query };
    },
    formConfig: {
      labelWidth: 80,
      schemas: SearchSchemas,
      fieldMapToTime: [['leaveDate', ['leaveStartDate', 'leaveEndDate'], 'YYYY-MM-DD']],
    },
  });

  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(exportPatientList, {
    manual: true,
    showSuccessMessage: true,
  });
  function onExportExpert() {
    exportUtil(
      exportExpertRunAsync({
        ...ActionTable.getForm().getFieldsValue(),
      }),
    );
  }
  function createActions(record, _column): ActionItem[] {
    return [
      {
        label: '患者详情',
        type: 'link',
        onClick: handleDetails.bind(null, record),
      },
    ];
  }
  const go = useGo();
  function handleDetails(record) {
    go({
      name: 'ChronicDiseaseStatisticsWorkbenchDetail',
      query: { mode: 'view', title: '患者详情', patientId: record.id },
    });
  }
</script>

<template>
  <div class="w-full h-full pr-4 rounded rounded-lt-none">
    <BasicTable @register="register" class="ft-main-table">
      <template #headerTop>
        <div class="text-16px font-500 mb-4">慢病患者列表</div>
      </template>
      <template #toolbar>
        <Button :loading="exportLoading" @click="onExportExpert"> 批量导出 </Button>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table .ant-pagination {
      margin: 16px 0 !important;
    }
  }
</style>
