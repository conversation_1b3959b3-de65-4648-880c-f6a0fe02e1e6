<script setup lang="ts">
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import type { ActionItem, BasicColumn } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { useRequest } from '@ft/request';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { columns, formSchema } from './data';
  import type { IPageList } from '/@/api/chronic-disease-management/workbench';
  import { deletePlan, getPlanPage } from '/@/api/chronic-disease-management/workbench';

  /**
   * 慢病干预计划
   */

  const [registerTable, tableIns] = useTable({
    api: getPlanPage,
    formConfig: {
      labelWidth: 130,
      schemas: formSchema,
    },
    useSearchForm: true,
    columns,
    size: 'small',
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
  });

  function createActions(record, column: BasicColumn): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '编辑',
        type: 'link',
        size: 'small',
        onClick: handleEdit.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: handleDel.bind(null, record, column),
          okButtonProps: {
            danger: true,
          },
        },
      },
    ];
    return actions;
  }
  const go = useGo();
  function handleAdd() {
    go({ name: 'InterventionPlanPlanConfiguration', query: { mode: 'add' } });
  }
  function handleEdit(_record: IPageList) {
    go({ name: 'InterventionPlanPlanConfiguration', query: { mode: 'edit', planId: _record.id } });
  }

  const { runAsync: delRunAsync } = useRequest(deletePlan, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      tableIns.reload();
    },
  });
  function handleDel(record, _column) {
    delRunAsync(record.id);
  }
  // const { runAsync: runEnableFlag } = useRequest(editPlan, {
  //   manual: true,
  //   showSuccessMessage: true,
  // });
  // function onSwitchChange(record) {
  //   record.loading = true;
  //   const { id } = record;
  //   runEnableFlag(id).finally(() => {
  //     record.loading = false;
  //   });
  // }
</script>

<template>
  <div class="w-full h-full pr-4">
    <BasicTable @register="registerTable" class="ft-main-table">
      <template #headerTop>
        <div class="text-16px font-500 title-label mb-4">慢病干预计划</div>
      </template>
      <template #tableTitle>
        <Button type="primary" @click="handleAdd"> 新增 </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record, column)" />
        </template>
        <!-- <template v-if="column.dataIndex === 'status'">
          <Switch
            :loading="record.loading"
            :checked-value="0"
            :un-checked-value="1"
            v-model:checked="record.status"
            @change="onSwitchChange(record)"
          />
        </template> -->
      </template>
    </BasicTable>
  </div>
</template>
