import { infectiousDiseaseClassificationRelevanceQueryByCode } from '@ft/internal/api';
import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';

/**
干预计划模板名称
干预病种
干预方式
备注
 */
export const columns: BasicColumn[] = [
  {
    title: '干预计划模板名称',
    dataIndex: 'planName',
    align: 'left',
  },
  {
    title: '干预病种',
    dataIndex: 'diseaseName',
    align: 'left',
  },
  {
    title: '干预方式',
    dataIndex: 'interveneTypeName',
    align: 'left',
  },
  {
    title: '是否启用',
    dataIndex: 'status',
    align: 'left',
    customRender: ({ text }) => {
      return text === 0 ? '启用' : '禁用';
    },
  },
  {
    title: '备注',
    dataIndex: 'remark',
    align: 'left',
  },
  {
    title: '维护人',
    dataIndex: 'updateUser',
    align: 'left',
  },
  {
    title: '维护时间',
    dataIndex: 'updateTime',
    align: 'left',
  },
];

/**
 * 干预计划模板名称
 * 是否启用
 */
export const formSchema: FormSchema[] = [
  {
    field: 'planName',
    component: 'Input',
    label: '干预计划模板名称',
    colProps: { span: 6 },
  },
  {
    field: 'diseaseCode',
    component: 'ApiSelect',
    label: '干预病种',
    componentProps: () => {
      return {
        api: () => infectiousDiseaseClassificationRelevanceQueryByCode({ code: '005' }),
        getPopupContainer: () => document.body,
        labelField: 'infectiousDiseaseName',
        valueField: 'infectiousDiseaseCode',
      };
    },
    colProps: { span: 6 },
  },
  {
    field: 'status',
    component: 'Select',
    label: '是否启用',
    componentProps: {
      options: [
        { label: '启用', value: 0 },
        { label: '禁用', value: 1 },
      ],
    },
    colProps: { span: 6 },
  },
];

/**
 * 模板名称
 * 模板内容
 * 是否启用
 */
export const addForm: FormSchema[] = [
  {
    field: 'id',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'templateName',
    label: '模板名称',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: 'templateContent',
    label: '模板内容',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: 'enableFlag',
    label: '是否启用',
    component: 'Select',
    componentProps: {
      allowClear: false,
      options: [
        { label: '启用', value: 0 },
        { label: '禁用', value: 1 },
      ],
    },
    defaultValue: 0,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },
];
