<script setup lang="ts">
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import type { ActionItem, BasicColumn } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { useModal } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import EditModel from './EditModel.vue';
  import { columns, formSchema } from './data';
  import {
    deleteEvaluateTemplate,
    getEvaluateTemplatePage,
  } from '/@/api/chronic-disease-management/evaluation-template';

  /**
   * 评价模板
   */

  const [registerTable, tableIns] = useTable({
    formConfig: {
      colon: true,
      labelWidth: 100,
      showAdvancedButton: false,
      actionColOptions: {
        span: 12,
        style: {
          textAlign: 'right',
        },
      },
      schemas: formSchema,
    },
    api: getEvaluateTemplatePage,
    useSearchForm: true,
    showIndexColumn: true,
    columns,
    bordered: false,
    size: 'small',
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
  });

  function createActions(record, column: BasicColumn): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '编辑',
        type: 'link',
        size: 'small',
        onClick: handleEdit.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: handleDel.bind(null, record, column),
          okButtonProps: {
            danger: true,
          },
        },
      },
    ];
    return actions;
  }
  const [register, { openModal }] = useModal();
  function handleAdd() {
    openModal(true, {
      mode: 'add',
    });
  }
  function handleEdit(record) {
    openModal(true, {
      mode: 'edit',
      ...record,
    });
  }

  const { runAsync: delRunAsync } = useRequest(deleteEvaluateTemplate, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      tableIns.reload();
    },
  });
  function handleDel(record, _column) {
    delRunAsync(record.id);
  }
  // const { runAsync: runEnableFlag } = useRequest(
  //   (_params) => {
  //     return Promise.resolve([]);
  //   },
  //   {
  //     manual: true,
  //     showSuccessMessage: true,
  //   },
  // );
  // function onSwitchChange(record) {
  //   record.loading = true;
  //   const { id } = record;
  //   runEnableFlag(id).finally(() => {
  //     record.loading = false;
  //   });
  // }
</script>

<template>
  <div class="w-full h-full pr-4 rounded rounded-lt-none">
    <BasicTable @register="registerTable" class="ft-main-table">
      <template #headerTop>
        <div class="text-16px font-500 title-label mb-4">模板列表</div>
      </template>
      <template #tableTitle>
        <Button type="primary" @click="handleAdd"> 新增 </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record, column)" />
        </template>
        <!-- <template v-if="column.dataIndex === 'enableFlag'">
          <Switch
            :loading="record.loading"
            :checked-value="0"
            :un-checked-value="1"
            v-model:checked="record.enableFlag"
            @change="onSwitchChange(record)"
          />
        </template> -->
      </template>
    </BasicTable>
    <EditModel @register="register" @success="tableIns.reload()" />
  </div>
</template>
