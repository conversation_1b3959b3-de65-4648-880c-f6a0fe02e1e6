import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';

/**
 * 模板名称
 * 模板内容
 * 是否启用
 * 创建人
 * 创建时间
 */
export const columns: BasicColumn[] = [
  {
    title: '模板名称',
    dataIndex: 'templateName',
    align: 'left',
  },
  {
    title: '模板内容',
    dataIndex: 'templateContent',
    align: 'left',
  },
  {
    title: '是否启用',
    dataIndex: 'templateStatus',
    align: 'left',
    customRender: ({ record }) => {
      return record.templateStatus === 0 ? '启用' : '禁用';
    },
  },
  {
    title: '创建人',
    dataIndex: 'createUser',
    align: 'left',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    align: 'left',
  },
];

/**
 * 模板名称
 * 是否启用
 */
export const formSchema: FormSchema[] = [
  {
    field: 'templateName',
    component: 'Input',
    label: '模板名称',
    colProps: { span: 6 },
  },
  {
    field: 'templateStatus',
    component: 'Select',
    label: '模板状态',
    componentProps: {
      options: [
        { label: '启用', value: 0 },
        { label: '禁用', value: 1 },
      ],
    },
    colProps: { span: 6 },
  },
];

/**
 * 模板名称
 * 模板内容
 * 是否启用
 */
export const addForm: FormSchema[] = [
  {
    field: 'id',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'templateName',
    label: '模板名称',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: 'templateContent',
    label: '模板内容',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: 'templateStatus',
    label: '是否启用',
    component: 'Select',
    componentProps: {
      allowClear: false,
      options: [
        { label: '启用', value: 0 },
        { label: '禁用', value: 1 },
      ],
    },
    defaultValue: 0,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },
];
