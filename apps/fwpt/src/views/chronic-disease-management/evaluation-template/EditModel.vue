<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { addForm } from './data';
  import {
    addEvaluateTemplate,
    updateEvaluateTemplate,
  } from '/@/api/chronic-disease-management/evaluation-template';

  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm({
    schemas: addForm,
    labelWidth: 100,
    baseColProps: { span: 6 },
    actionColOptions: { span: 24 },
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });
  const mode = ref('');
  const getTitle = computed(() => {
    return mode.value === 'add' ? '新增评价模板' : '编辑评价模板';
  });

  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data?.mode;
    formAction.setFieldsValue(data);
  });
  const { loading, runAsync } = useRequest(
    (_params) =>
      mode.value === 'add' ? addEvaluateTemplate(_params) : updateEvaluateTemplate(_params),
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess: () => {
        emit('success');
        closeModal();
      },
    },
  );
  async function handleOk() {
    formAction.validate().then((values) => {
      runAsync(values);
    });
  }
</script>

<template>
  <BasicModal
    width="500px"
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="getTitle"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    @register="register"
    @ok="handleOk"
    centered
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
