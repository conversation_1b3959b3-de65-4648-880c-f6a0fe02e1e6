<script setup lang="ts">
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { useRequest } from '@ft/request';
  import { type Ref, computed } from 'vue';
  import { Button } from 'ant-design-vue';
  import { exportUtil } from '@ft/internal/utils';
  import { usePermission } from '@ft/internal/hooks/web/usePermission';

  import { useGo } from '@ft/internal/hooks/web/usePage';
  import printJS from 'print-js';
  import ChartTypeBarLeft from './ChartTypeBarLeft.vue';
  import ChartTypePie from './ChartTypePie.vue';
  import ChartTypeBarRight from './ChartTypeBarRight.vue';
  import type { IEducationColumn } from '/@/api/drug-statistics';
  import { columns } from './data';
  import {
    exportManagementStatisticsReport,
    queryDiseaseTopStatistics,
    queryDistributionOfInterventionLevels,
    queryManagementStatisticsReport,
    queryMonthlyInterventionSituation,
    queryOnlineConsultationVolumeStatistics,
  } from '/@/api/chronic-disease-management/workbench';
  import { RoleEnum } from '/@/enums/roleEnum';

  const { hasPermission } = usePermission();

  const [register, ActionTable] = useTable({
    columns: columns,
    api: queryManagementStatisticsReport,
    resizeHeightOffset: 20,
    inset: true,
    useSearchForm: true,
    showIndexColumn: false,
    actionColumn: computed(() => {
      return hasPermission(RoleEnum.CHRONIC_DISEASE_STATISTICS_STATISTICS_PATIENT_MANAGEMENT_VIEW)
        ? {
            title: '操作',
            dataIndex: 'action',
            width: 120,
          }
        : undefined;
    }),
    formConfig: {
      labelWidth: 80,
      schemas: [
        {
          field: 'statisticsTime',
          component: 'RangePicker',
          label: '统计日期',
          colProps: {
            span: 6,
          },
          componentProps: {
            valueFormat: 'YYYY-MM-DD',
            format: 'YYYY-MM-DD',
            showTime: false,
            style: {
              width: '100%',
            },
          },
        },
        {
          field: 'chronicDiseaseName',
          component: 'Input',
          label: '慢病病种',
          colProps: {
            span: 6,
          },
        },
      ],
      fieldMapToTime: [['statisticsTime', ['startTime', 'endTime'], 'YYYY-MM-DD']],
    },
    scroll: { y: 600 },
  });
  const { data: countStatisticsList } = useRequest(queryDistributionOfInterventionLevels);
  const { data: countDistributionList = [] as unknown as Ref<IEducationColumn[]> } = useRequest(
    queryMonthlyInterventionSituation,
  );
  const { data: countStatisticsList1 } = useRequest(() =>
    hasPermission(RoleEnum.CHRONIC_DISEASE_STATISTICS_YHD_CONSULTING_STATISTICS_VIEW)
      ? queryDiseaseTopStatistics()
      : queryOnlineConsultationVolumeStatistics(),
  );

  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(
    exportManagementStatisticsReport,
    {
      manual: true,
      showSuccessMessage: true,
    },
  );
  function onExportExpert() {
    exportUtil(
      exportExpertRunAsync({
        ...ActionTable.getForm().getFieldsValue(),
      }),
    );
  }
  function createActions(record, _column): ActionItem[] {
    return [
      {
        label: '查看详情',
        type: 'link',
        onClick: handleDetails.bind(null, record),
      },
    ];
  }
  const go = useGo();
  function handleDetails(record) {
    const query = ActionTable.getForm().getFieldsValue();
    go({
      //	chronic-disease-statistics chronic-disease-details
      name: 'ChronicDiseaseStatisticsChronicDiseaseDetails',
      query: {
        mode: 'view',
        title: '管理详情',
        chronicDiseaseCode: record.chronicDiseaseCode,
        ...(query || {}),
      },
    });
  }

  async function onPrint() {
    const data = await queryManagementStatisticsReport({
      ...ActionTable.getForm().getFieldsValue(),
      pageSize: 9999,
    });
    const properties = columns.map((item) => {
      return { field: item.dataIndex, displayName: item.title };
    });

    printJS({
      printable: data.list || [],
      properties,
      type: 'json',
      style: ' @media print {html, body{ margin: 0;padding: 0; }}',
    });
  }
</script>

<template>
  <div class="w-[calc(100%-16px)] !h-[calc(100vh-102px)] of-y-auto">
    <div class="statistics h-294px flex gap-2.5">
      <div class="flex-1 of-hidden bg-white rounded p-4">
        <ChartTypeBarLeft :data="countDistributionList" title="疾病月度分布统计" />
      </div>
      <div class="flex-1 of-hidden bg-white rounded rounded-lt-none p-4">
        <ChartTypePie :data="countStatisticsList" title="当前年度疾病人员统计" />
      </div>
      <div class="flex-1 of-hidden bg-white rounded p-4">
        <ChartTypeBarRight
          v-if="
            hasPermission(RoleEnum.CHRONIC_DISEASE_STATISTICS_STATISTICS_PATIENT_MANAGEMENT_VIEW)
          "
          legendName="患者管理量人数"
          :data="countStatisticsList1"
          title="当前年度疾病统计"
        />
        <ChartTypeBarRight
          v-else-if="
            hasPermission(RoleEnum.CHRONIC_DISEASE_STATISTICS_YHD_CONSULTING_STATISTICS_VIEW)
          "
          legendName="咨询量"
          :data="countStatisticsList1"
          title="当前年度慢病线上咨询量统计"
        />
      </div>
    </div>
    <div class="bg-white p-4 flex flex-col gap-2.5 rounded mt-2.5">
      <div class="text-base font-bold">慢病管理统计表</div>
      <BasicTable @register="register">
        <template #toolbar>
          <Button :loading="exportLoading" @click="onPrint"> 打印 </Button>
          <Button :loading="exportLoading" @click="onExportExpert"> 批量导出 </Button>
        </template>
        <template #bodyCell="{ record, column }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :divider="false" :actions="createActions(record, column)" />
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table .ant-pagination {
      margin: 16px 0 !important;
    }
  }
</style>
