<script setup lang="ts">
  import { ref, watchEffect } from 'vue';
  import { useECharts } from '@ft/internal/hooks/web/useECharts';

  /**
   * 慢病月度干预情况
   */
  const props = defineProps({
    data: {
      type: Array,
      default: () => [],
    },
    title: {
      type: String,
      default: '',
    },
  });

  const chartRef = ref<HTMLDivElement>();
  const { setOptions } = useECharts(chartRef as any);

  watchEffect(() => {
    const seriesData: any = props.data.map((item: any) => ({
      name: item.typeName,
      data: item.lineChartVOList.map((vo) => vo.yvalue),
      type: 'bar',
      silent: true,
      stack: 'Total',
      barWidth: 6,
      itemStyle: {
        borderRadius: [5, 5, 0, 0],
      },
      // label: {
      //   show: true,
      //   position: 'top',
      // },
    }));
    //@ts-ignore
    const xAxisData: any = props.data[0]?.lineChartVOList?.map((item: any) => item.x);
    const legendData = props.data?.map((item: any) => item.typeName);
    setOptions({
      tooltip: {
        trigger: 'axis',
        appendToBody: true,
      },
      grid: {
        left: '8%',
        right: '8%',
        bottom: '10%',
      },
      legend: {
        data: legendData,
      },
      xAxis: {
        data: xAxisData,
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#B0B1B4',
        },
        axisLine: {
          lineStyle: {
            color: '#EDEEF0',
          },
        },
      },
      yAxis: {
        splitLine: {
          lineStyle: {
            color: '#EDEEF0',
          },
          interval: 30,
        },
        axisLabel: {
          color: '#B0B1B4',
        },
      },
      series: seriesData || [],
    });
  });
</script>

<template>
  <div class="flex flex-col h-full">
    <div class="title text-base font-bold">{{ title }}</div>
    <div ref="chartRef" class="flex-1 of-hidden"></div>
  </div>
</template>
