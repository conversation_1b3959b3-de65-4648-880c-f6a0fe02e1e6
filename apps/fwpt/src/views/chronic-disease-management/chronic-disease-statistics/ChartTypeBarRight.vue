<script setup lang="ts">
  import { ref, watchEffect } from 'vue';
  import { useECharts } from '@ft/internal/hooks/web/useECharts';

  /**
   * 当前年度疾病统计
   */
  const props = defineProps({
    data: {
      type: Array,
      default: () => [],
    },
    title: {
      type: String,
      default: '',
    },
    legendName: {
      type: String,
      default: '',
    },
  });

  const chartRef = ref<HTMLDivElement>();
  const { setOptions } = useECharts(chartRef as any);

  watchEffect(() => {
    const xAxisData = props.data?.map((i: any) => i.x);
    const seriesData = props.data?.map((i: any) => i.yvalue);
    setOptions({
      tooltip: {
        trigger: 'axis',
        appendToBody: true,
      },
      grid: {
        left: '8%',
        right: '8%',
        bottom: '10%',
      },
      legend: {
        data: [props.legendName],
        top: '5%',
      },
      xAxis: {
        data: xAxisData,
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#B0B1B4',
        },
        axisLine: {
          lineStyle: {
            color: '#EDEEF0',
          },
        },
      },
      yAxis: {
        splitLine: {
          lineStyle: {
            color: '#EDEEF0',
          },
          interval: 30,
        },
        axisLabel: {
          color: '#B0B1B4',
        },
      },
      series: [
        {
          name: props.legendName,
          data: seriesData,
          type: 'bar',
          barWidth: 6,
          itemStyle: {
            borderRadius: [5, 5, 0, 0],
          },
        },
      ],
    });
  });
</script>

<template>
  <div class="flex flex-col h-full">
    <div class="title text-base font-bold">{{ title }}</div>
    <div ref="chartRef" class="flex-1 of-hidden"></div>
  </div>
</template>
