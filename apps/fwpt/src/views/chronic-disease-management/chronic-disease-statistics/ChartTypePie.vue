<script setup lang="ts">
  import { ref, watchEffect } from 'vue';
  import { useECharts } from '@ft/internal/hooks/web/useECharts';
  /**
   * 当前年度慢病干预量分布情况
   */
  const props = defineProps({
    data: {
      type: Array,
      default: () => [],
    },
    title: {
      type: String,
      default: '',
    },
  });
  const chartRef = ref<HTMLDivElement>();
  const { setOptions } = useECharts(chartRef as any);

  watchEffect(() => {
    setOptions({
      tooltip: {
        trigger: 'item',
      },
      legend: {
        top: '5%',
        left: 'center',
      },
      //@ts-ignore
      series: [
        {
          name: 'Access From',
          type: 'pie',
          radius: ['40%', '60%'],
          avoidLabelOverlap: false,
          padAngle: 5,
          itemStyle: {
            borderRadius: 8,
          },
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 16,
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
          data: props.data || [],
        },
      ],
    });
  });
</script>

<template>
  <div class="flex flex-col h-full">
    <div class="title text-base font-bold">{{ title }}</div>
    <div ref="chartRef" class="flex-1 of-hidden"></div>
  </div>
</template>
