import {
  DictEnum,
  getDictItemList,
  infectiousDiseaseClassificationRelevanceQueryByCode,
} from '@ft/internal/api';
import type { FormSchema } from '@ft/internal/components/Form/index';
import type { ChronicDiseaseIntervenePlanDTO } from '/@/api/chronic-disease-management/workbench';

export const formSchemas = (outInId, planId): FormSchema[] => {
  return [
    {
      field: 'inpatientNo',
      component: 'Input',
      label: 'id',
      show: false,
    },
    {
      field: 'id',
      component: 'Input',
      label: 'id',
      show: false,
    },
    {
      field: 'moduleName1',
      component: 'Input',
      label: '慢病干预信息',
      colSlot: 'moduleName',
      colProps: { span: 24 },
    },
    {
      field: 'planName',
      label: '干预计划名称',
      component: 'Input',
      required: true,
      colProps: { span: 9 },
      itemProps: { wrapperCol: { span: 12 } },
    },
    {
      field: 'diseaseName',
      component: 'Input',
      label: '干预病种名称',
      show: false,
    },
    {
      field: 'diseaseCode',
      component: 'ApiSelect',
      label: '干预病种',
      required: true,
      componentProps: ({ formModel }) => {
        return {
          disabled: !!outInId && !!planId,
          api: () => infectiousDiseaseClassificationRelevanceQueryByCode({ code: '005' }),
          getPopupContainer: () => document.body,
          labelField: 'infectiousDiseaseName',
          valueField: 'infectiousDiseaseCode',
          onChange(_, opt) {
            if (opt?.label) formModel.diseaseName = opt?.label;
          },
        };
      },
      colProps: { span: 9 },
      itemProps: { wrapperCol: { span: 12 } },
    },
    {
      field: 'interveneTypeName',
      component: 'Input',
      label: '干预方式名称',
      show: false,
    },
    {
      field: 'interveneTypeCode',
      component: 'ApiSelect',
      label: '干预方式',
      required: true,
      componentProps: ({ formModel }) => {
        return {
          disabled: !!outInId && !!planId,
          api: () => getDictItemList(DictEnum.CD_INTERVENE_WAY),
          getPopupContainer: () => document.body,
          labelField: 'dictItemName',
          valueField: 'dictItemCode',
          valueToString: formModel?.interveneTypeCode ? true : false,
          onChange(_, opt) {
            if (opt?.label) formModel.interveneTypeName = opt?.label;
          },
        };
      },
      colProps: { span: 9 },
      itemProps: { wrapperCol: { span: 12 } },
    },
    {
      field: 'interveneTime',
      component: 'RangePicker',
      label: '干预时间',
      componentProps: () => {
        return {
          format: 'YYYY-MM-DD',
          style: { width: '100%' },
          valueFormat: 'YYYY-MM-DD',
          getPopupContainer: () => document.body,
        };
      },
      ifShow: () => outInId != '',
      colProps: { span: 9 },
      itemProps: { wrapperCol: { span: 12 } },
    },

    {
      field: 'status',
      component: 'Select',
      label: '干预状态',
      componentProps: {
        options: [
          { label: '启用', value: 0 },
          { label: '禁用', value: 1 },
        ],
      },
      defaultValue: 0,
      colProps: { span: 9 },
      itemProps: { wrapperCol: { span: 12 } },
    },
    {
      field: 'remark',
      component: 'InputTextArea',
      label: '备注',
      componentProps: {
        autoSize: { minRows: 4, maxRows: 4 },
      },
      colProps: { span: 9 },
      itemProps: { wrapperCol: { span: 12 } },
    },
    {
      field: 'moduleName1',
      component: 'Input',
      label: '慢病干预内容',
      colSlot: 'moduleName',
      colProps: { span: 24 },
    },
    {
      field: 'monitorIndexList',
      component: 'ApiCheckboxGroup',
      label: '干预方式名称',
      show: false,
    },
    {
      field: 'monitorIdList',
      component: 'ApiCheckboxGroup',
      label: '检测指标',
      required: true,
      componentProps: ({ formModel, schema }) => {
        return {
          api: () => getDictItemList(DictEnum.MONITOR_INDEX),
          getPopupContainer: () => document.body,
          labelField: 'dictItemName',
          valueField: 'dictItemCode',
          // valueToNumber: true,
          valueToString: true,
          onCheckedChange: (checkedList, allList) => {
            if (schema?.field) {
              formModel['monitorIndexList'] = allList.value.map((item) => {
                return {
                  status: checkedList.map((i) => i.value).includes(item.value) ? 0 : 1,
                  indexType: item.value,
                  // id: item.dictItemId,
                };
              });
            }
          },
        };
      },
      colProps: { span: 24 },
      itemProps: { wrapperCol: { span: 24 } },
    },
    {
      field: 'moduleName1',
      component: 'Input',
      label: '用药',
      colSlot: 'moduleName',
      colProps: { span: 24 },
    },
    {
      field: 'isRecommendedMedication',
      component: 'RadioGroup',
      label: '是否推荐购药',
      colProps: { span: 24 },
      componentProps: ({ formModel }) => {
        return {
          options: [
            { label: '推荐', value: 1 },
            { label: '不推荐', value: 0 },
          ],
          onChange: ({ target }) => {
            if (target?.value === 0) {
              formModel.recommendedMedication = '';
            }
          },
        };
      },
      defaultValue: 0,
      itemProps: { wrapperCol: { span: 24 } },
    },
    {
      field: 'recommendedMedication',
      component: 'InputTextArea',
      label: '药品推荐信息',
      required: ({ model }) => {
        return model.isRecommendedMedication === 1;
      },
      // disabledLabelWidth: false,
      componentProps: {
        placeholder: '请填写药品推荐信息',
        autoSize: { minRows: 4, maxRows: 4 },
      },
      show: ({ model }) => {
        return model.isRecommendedMedication === 1;
      },
      colProps: { span: 9 },
      itemProps: { wrapperCol: { span: 12 } },
    },
    {
      field: 'medicationSchedule',
      component: 'InputTextArea',
      label: '用药计划',
      componentProps: {
        placeholder: '请填写用药计划',
        autoSize: { minRows: 4, maxRows: 4 },
      },
      show: ({ model }) => {
        return model.isRecommendedMedication === 1;
      },
      colProps: { span: 9 },
      itemProps: { wrapperCol: { span: 12 } },
    },
    {
      field: 'medicationDate',
      component: 'DatePicker',
      label: '下次购药时间',
      componentProps: () => {
        return {
          // showTime: true,
          format: 'YYYY-MM-DD',
          style: { width: '100%' },
          valueFormat: 'YYYY-MM-DD',
          getPopupContainer: () => document.body,
        };
      },
      show: ({ model }) => {
        return model.isRecommendedMedication === 1;
      },
      colProps: { span: 6 },
      itemProps: { wrapperCol: { span: 20 } },
    },
    {
      field: 'moduleName1',
      component: 'Input',
      label: '生活指导',
      colSlot: 'moduleName',
      colProps: { span: 24 },
    },
    {
      field: 'sportsInstruction',
      component: 'InputTextArea',
      label: '运动指导',
      componentProps: {
        placeholder: '请输入运动指导',
        autoSize: { minRows: 4, maxRows: 4 },
      },
      colProps: { span: 9 },
      itemProps: { wrapperCol: { span: 12 } },
    },
    {
      field: 'dietaryGuidance',
      component: 'InputTextArea',
      label: '饮食指导',
      componentProps: {
        placeholder: '请输入饮食指导',
        autoSize: { minRows: 4, maxRows: 4 },
      },
      colProps: { span: 9 },
      itemProps: { wrapperCol: { span: 12 } },
    },
    {
      field: 'livingHabitGuide',
      component: 'InputTextArea',
      label: '生活习惯指导',
      componentProps: {
        placeholder: '请输入生活习惯指导',
        autoSize: { minRows: 4, maxRows: 4 },
      },
      colProps: { span: 9 },
      itemProps: { wrapperCol: { span: 12 } },
    },
    {
      field: 'psychologicalGuide',
      component: 'InputTextArea',
      label: '心理指导',
      componentProps: {
        placeholder: '请输入心理指导',
        autoSize: { minRows: 4, maxRows: 4 },
      },
      colProps: { span: 9 },
      itemProps: { wrapperCol: { span: 12 } },
    },
    {
      field: 'moduleName1',
      component: 'Input',
      label: '复诊计划',
      colSlot: 'moduleName',
      colProps: { span: 24 },
    },
    {
      field: 'reVisitRule',
      component: 'InputTextArea',
      label: '复诊规则',
      componentProps: {
        placeholder: '请输入复诊规则',
        autoSize: { minRows: 4, maxRows: 4 },
      },
      colProps: { span: 9 },
      itemProps: { wrapperCol: { span: 12 } },
    },
    {
      field: 'reVisitTime',
      component: 'DatePicker',
      label: '复诊时间',
      componentProps: () => {
        return {
          showTime: true,
          format: 'YYYY-MM-DD HH:mm:ss',
          style: { width: '100%' },
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          getPopupContainer: () => document.body,
        };
      },
      colProps: { span: 9 },
      itemProps: { wrapperCol: { span: 12 } },
    },
    {
      field: 'moduleName1',
      component: 'Input',
      label: '检验检查计划',
      colSlot: 'moduleName',
      colProps: { span: 24 },
    },
  ];
};

type IncludesFields =
  | 'inspection'
  | 'isInspection'
  | 'inspectionList'
  | 'exercisePrescriptionExecuteTime'
  | 'exercisePrescriptionInfoList'
  | 'nutritionPrescriptionExecuteTime'
  | 'nutritionPrescriptionInfoList';

export type SubFormState = Pick<ChronicDiseaseIntervenePlanDTO, IncludesFields>;
/**
 *
 * 星期 训练类型 动作示例（负荷/组数） 时长 强度区间 热量消耗（kcal） 血压/血糖预警
 */

type ExColumn = {
  label: string;
  field: string;
  width?: number | string;
};

export const ExercisePrescriptionColumns: ExColumn[] = [
  {
    label: '星期',
    field: 'week',
    width: 100,
  },
  {
    label: '训练类型',
    field: 'trainingType',
    width: 140,
  },
  {
    label: '动作示例（负荷/组数）',
    field: 'actionExample',
    width: 180,
  },
  {
    label: '时长',
    field: 'duration',
    width: 100,
  },
  {
    label: '强度区间',
    field: 'intensityRange',
    width: 100,
  },
  {
    label: '热量消耗（kcal）',
    field: 'kcal',
    width: 160,
  },
  {
    label: '血压/血糖预警',
    field: 'bpbsEarlyWarning',
    width: 120,
  },
];

export const DefaultExercisePrescriptionInfoList: ChronicDiseaseIntervenePlanDTO['exercisePrescriptionInfoList'] =
  [
    {
      week: '周一',
      trainingType: '',
      actionExample: '',
      duration: '',
      intensityRange: '',
      kcal: '',
      bpbsEarlyWarning: '',
      planId: '',
    },
    {
      week: '周二',
      trainingType: '',
      actionExample: '',
      duration: '',
      intensityRange: '',
      kcal: '',
      bpbsEarlyWarning: '',
      planId: '',
    },
    {
      week: '周三',
      trainingType: '',
      actionExample: '',
      duration: '',
      intensityRange: '',
      kcal: '',
      bpbsEarlyWarning: '',
      planId: '',
    },
    {
      week: '周四',
      trainingType: '',
      actionExample: '',
      duration: '',
      intensityRange: '',
      kcal: '',
      bpbsEarlyWarning: '',
      planId: '',
    },
    {
      week: '周五',
      trainingType: '',
      actionExample: '',
      duration: '',
      intensityRange: '',
      kcal: '',
      bpbsEarlyWarning: '',
      planId: '',
    },
    {
      week: '周六',
      trainingType: '',
      actionExample: '',
      duration: '',
      intensityRange: '',
      kcal: '',
      bpbsEarlyWarning: '',
      planId: '',
    },
    {
      week: '周日',
      trainingType: '',
      actionExample: '',
      duration: '',
      intensityRange: '',
      kcal: '',
      bpbsEarlyWarning: '',
      planId: '',
    },
  ];

/**
 * 营养素 摄入量（g/kg） 总量（g） 餐次分配（早/中/晚/加餐） 热量占比 具体示例
 */
export const NutritionPrescriptionColumns: ExColumn[] = [
  {
    label: '营养素',
    field: 'nutrient',
    width: 100,
  },
  {
    label: '摄入量（g/kg）',
    field: 'intake',
    width: 160,
  },
  {
    label: '总量（g）',
    field: 'total',
    width: 100,
  },
  {
    label: '餐次分配（早/中/晚/加餐）',
    field: 'mealAllocation',
    width: 180,
  },
  {
    label: '热量占比',
    field: 'heatPercentage',
    width: 100,
  },
  {
    label: '具体示例',
    field: 'specificExample',
    width: 200,
  },
  // action
  {
    label: '操作',
    field: 'action',
    width: 100,
  },
];

export const DefaultNutritionPrescriptionInfoList: ChronicDiseaseIntervenePlanDTO['nutritionPrescriptionInfoList'] =
  [
    {
      nutrient: '',
      intake: '',
      total: '',
      mealAllocation: '',
      heatPercentage: '',
      specificExample: '',
      planId: '',
    },
  ];
