<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { Button } from '@ft/internal/components/Button';
  import { computed, onMounted, ref } from 'vue';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { useModal } from '@ft/internal/components/Modal';
  import { FixedAction } from '@ft/components';
  import { useGo } from '@ft/internal/hooks/web/usePage';

  import { useRequest } from '@ft/request';
  import { cloneDeep } from 'lodash-es';
  import BasicInformation from '../comp/BasicInformation.vue';

  import SelectTemplateModal from '../comp/SelectTemplateModal.vue';
  import type { SubFormState } from './data';
  import {
    DefaultExercisePrescriptionInfoList,
    DefaultNutritionPrescriptionInfoList,
    formSchemas,
  } from './data';
  import { addPlan, editPlan, getPlanDetail } from '/@/api/chronic-disease-management/workbench';
  import InspectionPlan from './InspectionPlan.vue';

  const mode = useRouteQuery('mode', 'add', { transform: String });
  const planId = useRouteQuery('planId', '', { transform: String });
  const outInId = useRouteQuery('outInId', '', { transform: String });
  const isView = computed(() => mode.value === 'view');
  const divRef = ref();
  const go = useGo();

  onMounted(() => {
    planId.value && getPlanDetailRun(planId.value, outInId.value);
  });

  const { runAsync: getPlanDetailRun } = useRequest(getPlanDetail, {
    manual: true,
    onBefore: () => {
      ActionForm.clearValidate();
    },
    onSuccess: (data) => {
      ActionForm.setFieldsValue({
        ...data,
        monitorIdList: data?.monitorIndexList
          ?.filter((item) => item.status === 0)
          ?.map((item) => item.indexType?.toString()),
        inpatientNo: outInId.value,
        id: planId.value ? planId.value : data?.id,
        interveneTime: data?.interveneStartTime
          ? [data?.interveneStartTime, data?.interveneEndTime]
          : null,
      });
      formState.value = {
        inspection: data?.inspection,
        isInspection: data?.isInspection?.toString(),
        inspectionList: data?.inspectionItemList || [
          {
            itemName: '',
            appointmentTime: '',
          },
        ],
        exercisePrescriptionExecuteTime: data?.exercisePrescriptionExecuteTime,
        exercisePrescriptionInfoList:
          data?.exercisePrescriptionInfoList && data?.exercisePrescriptionInfoList?.length > 0
            ? data?.exercisePrescriptionInfoList
            : cloneDeep(DefaultExercisePrescriptionInfoList),
        nutritionPrescriptionExecuteTime: data?.nutritionPrescriptionExecuteTime,
        nutritionPrescriptionInfoList:
          data?.nutritionPrescriptionInfoList && data?.nutritionPrescriptionInfoList?.length > 0
            ? data?.nutritionPrescriptionInfoList
            : cloneDeep(DefaultNutritionPrescriptionInfoList),
      };
    },
  });
  const [registerFrom, ActionForm] = useForm({
    labelWidth: 180,
    showActionButtonGroup: false,
    disabled: computed(() => mode.value === 'view'),
    schemas: formSchemas(outInId.value, planId.value),
    fieldMapToTime: [['interveneTime', ['interveneStartTime', 'interveneEndTime'], 'YYYY-MM-DD']],
  });

  const [registerModal, { openModal }] = useModal();
  function handleSelectTemplate() {
    openModal(true, {
      planId: ActionForm.getFieldsValue().id,
    });
  }
  const { runAsync, loading } = useRequest(
    (params) => {
      return mode.value === 'add' ? addPlan(params) : editPlan(params);
    },
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess: () => {
        go(-1);
      },
    },
  );

  function handleSelectTemplateSuccess(value) {
    getPlanDetailRun(value.planId, outInId.value);
  }

  const inspectionPlanRef = ref();
  const formState = ref<SubFormState>({
    inspection: '',
    isInspection: '',
    inspectionList: [
      {
        itemName: '',
        appointmentTime: '',
      },
    ],
    exercisePrescriptionExecuteTime: undefined,
    exercisePrescriptionInfoList: cloneDeep(DefaultExercisePrescriptionInfoList),
    nutritionPrescriptionExecuteTime: undefined,
    nutritionPrescriptionInfoList: cloneDeep(DefaultNutritionPrescriptionInfoList),
  });

  async function handleSave() {
    Promise.all([ActionForm.validate(), inspectionPlanRef.value?.validate()]).then(
      ([_, _formRefValues]) => {
        const values = ActionForm.getFieldsValue();
        runAsync({
          ...values,
          ...formState.value,
          exercisePrescriptionInfoList:
            formState.value?.exercisePrescriptionInfoList?.map((item) => ({
              ...item,
              planId: planId.value,
            })) || [],
          nutritionPrescriptionInfoList:
            formState.value?.nutritionPrescriptionInfoList?.map((item) => ({
              ...item,
              planId: planId.value,
            })) || [],
        });
      },
    );
  }
</script>
<template>
  <div class="w-full h-full pr-4 flex flex-col py-2 gap-4">
    <BasicInformation v-if="isView" />
    <div ref="divRef" class="flex-1 bg-#fff rounded-2 p-4 flex flex-col gap-2 pb-100px">
      <div class="flex items-center gap-2 justify-between" v-if="!!outInId">
        <span class="text-#333333 fw-bold">管理计划配置</span>
        <Button v-if="!!outInId" type="primary" @click="handleSelectTemplate">引用模板</Button>
      </div>
      <div class="flex-1 flex-col gap-4 basis-0 min-h-0 of-y-auto overflow-hidden">
        <BasicForm @register="registerFrom">
          <template #moduleName="{ schema }">
            <div class="flex items-center gap-2 mb-3">
              <span v-if="schema.label" class="inline-block bg-primary-color w-3px h-1em"></span>
              <span>{{ schema.label }}</span>
            </div>
          </template>
        </BasicForm>
        <InspectionPlan
          ref="inspectionPlanRef"
          v-model="formState"
          :disabled="isView"
          :planId="planId"
        />
      </div>
      <FixedAction
        :referenceEl="divRef"
        class="fixed bottom-0 h-80px right-16px flex justify-end gap-4 items-center pr-4"
      >
        <Button @click="go(-1)"> 取消 </Button>
        <Button type="primary" @click="handleSave" :loading="loading"> 保存 </Button>
      </FixedAction>
    </div>

    <SelectTemplateModal @register="registerModal" @success="handleSelectTemplateSuccess" />
  </div>
</template>

<style lang="less" scoped></style>
