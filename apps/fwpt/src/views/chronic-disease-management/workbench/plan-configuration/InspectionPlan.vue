<script setup lang="ts">
  import {
    Col,
    DatePicker,
    Form,
    FormItem,
    Input,
    InputNumber,
    Radio,
    RadioGroup,
    Row,
    Textarea,
  } from 'ant-design-vue';
  import { computed, ref } from 'vue';
  import type { FormInstance, RuleObject } from 'ant-design-vue/lib/form';
  import { BasicTitle } from '@ft/internal/components/Basic';
  import { Button } from '@ft/internal/components/Button';
  import type { SubFormState } from './data';
  import { ExercisePrescriptionColumns, NutritionPrescriptionColumns } from './data';

  const props = defineProps({
    modelValue: {
      type: Object as () => SubFormState,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    planId: {
      type: String,
      default: '',
    },
  });

  const emit = defineEmits(['update:modelValue']);

  const formState = computed({
    get() {
      return props.modelValue;
    },
    set(newVal) {
      emit('update:modelValue', newVal);
    },
  });

  const formRef = ref<FormInstance>();
  const formRules: { [k: string]: RuleObject[] } = {
    isInspection: [{ required: true, message: '请选择', trigger: 'change' }],
    itemName: [{ required: true, message: '请输入申请项目', trigger: 'change' }],
    appointmentTime: [{ required: true, message: '请选择预约时间', trigger: 'change' }],
  };

  const filterNutritionPrescriptionColumns = computed(() => {
    if (props.disabled) {
      return NutritionPrescriptionColumns.filter((item) => item.field !== 'action');
    }

    return NutritionPrescriptionColumns;
  });

  function onAddDrug() {
    formState.value.inspectionList &&
      formState.value.inspectionList.push({
        itemName: '',
        appointmentTime: '',
      });
  }

  function onCalcDrugTotal(item) {
    console.log(item);
  }

  function onAddNutrition() {
    formState.value.nutritionPrescriptionInfoList &&
      formState.value.nutritionPrescriptionInfoList.push({
        nutrient: '',
        intake: '',
        total: '',
        mealAllocation: '',
        heatPercentage: '',
        specificExample: '',
        planId: props.planId,
      });
  }

  defineExpose({
    formRef,
    validate: () => formRef.value?.validate(),
  });
</script>

<template>
  <Form
    ref="formRef"
    :model="formState"
    :rules="formRules"
    :label-col="{ span: 6 }"
    :wrapper-col="{ span: 12 }"
  >
    <Row :gutter="6">
      <Col span="20">
        <FormItem label="是否需定期做相关检验检查" name="isInspection">
          <RadioGroup v-model:value="formState.isInspection" :disabled="disabled">
            <Radio value="1">是</Radio>
            <Radio value="0">否</Radio>
          </RadioGroup>
        </FormItem>
      </Col>
    </Row>
    <div v-if="formState.isInspection === '1'">
      <Row
        class="border-1 border-dashed border-[#E7E7E7] pt-6 mb-4"
        v-for="(item, index) in formState.inspectionList"
        :key="index"
      >
        <Col span="10">
          <FormItem label="申请项目" required :name="['inspectionList', index, 'itemName']">
            <Input
              v-model:value="item.itemName"
              placeholder="请输入"
              @change="onCalcDrugTotal(item)"
              :disabled="disabled"
            />
          </FormItem>
        </Col>
        <Col span="10">
          <FormItem label="预约时间" required :name="['inspectionList', index, 'appointmentTime']">
            <DatePicker
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              placeholder="请选择"
              style="width: 100%"
              v-model:value="item.appointmentTime"
              :disabled="disabled"
            />
          </FormItem>
        </Col>
        <Col span="4" v-if="!disabled">
          <div class="flex justify-end">
            <Button
              v-if="index > 0"
              pre-icon="ant-design:delete-outlined"
              type="link"
              danger
              @click="() => formState.inspectionList?.splice(index, 1)"
            >
              移除
            </Button>
            <Button
              v-if="formState.inspectionList && index === formState.inspectionList.length - 1"
              pre-icon="ant-design:plus-circle-outlined"
              type="link"
              @click="onAddDrug"
            />
          </div>
        </Col>
      </Row>
      <Row class="pt-6 mb-4">
        <Col span="12">
          <FormItem label="检查检验项目推荐信息" name="inspection">
            <Textarea
              :rows="4"
              placeholder="请输入"
              v-model:value="formState.inspection"
              :disabled="disabled"
            />
          </FormItem>
        </Col>
      </Row>
    </div>
    <div><BasicTitle normal span>营养运动处方</BasicTitle></div>
    <div><BasicTitle normal>周运动处方</BasicTitle></div>
    <Row>
      <Col span="12">
        <FormItem :label-col="{ span: 3 }" label="执行时间" name="exercisePrescriptionExecuteTime">
          <InputNumber
            placeholder="请输入"
            style="width: 100px !important"
            v-model:value="formState.exercisePrescriptionExecuteTime"
            addon-after="周"
            :disabled="disabled"
            :min="0"
          />
        </FormItem>
      </Col>
    </Row>
    <Row>
      <table class="border border-collapse border-gray-300 rounded-md">
        <thead>
          <tr>
            <th
              class="border border-gray-300 p-2 text-left"
              :style="{ width: item.width + 'px' }"
              v-for="item in ExercisePrescriptionColumns"
              :key="item.field"
            >
              {{ item.label }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(item, rowIdx) in formState.exercisePrescriptionInfoList" :key="rowIdx">
            <td
              v-for="col in ExercisePrescriptionColumns"
              :key="col.field"
              class="border border-gray-300 p-2"
              :style="{ width: col.width + 'px' }"
            >
              <FormItem noStyle :name="['exercisePrescriptionInfoList', rowIdx, 'planId']" />
              <FormItem noStyle :name="['exercisePrescriptionInfoList', rowIdx, col.field]">
                <template v-if="col.field === 'week'">
                  <span>{{ item[col.field] }}</span>
                </template>
                <template v-else>
                  <Input
                    class="!w-full"
                    v-model:value="item[col.field]"
                    placeholder="请输入"
                    :disabled="disabled"
                  />
                </template>
              </FormItem>
            </td>
          </tr>
        </tbody>
      </table>
    </Row>
    <div><BasicTitle normal>周营养处方</BasicTitle></div>
    <Row>
      <Col span="12">
        <FormItem :label-col="{ span: 3 }" label="执行时间" name="nutritionPrescriptionExecuteTime">
          <InputNumber
            placeholder="请输入"
            style="width: 100px !important"
            v-model:value="formState.nutritionPrescriptionExecuteTime"
            addon-after="周"
            :disabled="disabled"
            :min="0"
          />
        </FormItem>
      </Col>
    </Row>
    <Row>
      <table class="border border-collapse border-gray-300 rounded-md">
        <thead>
          <tr>
            <th
              class="border border-gray-300 p-2 text-left"
              :style="{ width: item.width + 'px' }"
              v-for="item in filterNutritionPrescriptionColumns"
              :key="item.field"
            >
              {{ item.label }}
            </th>
          </tr>
        </thead>
        <tbody
          v-if="
            formState.nutritionPrescriptionInfoList &&
            formState.nutritionPrescriptionInfoList.length > 0
          "
        >
          <tr v-for="(item, rowIdx) in formState.nutritionPrescriptionInfoList" :key="rowIdx">
            <td
              v-for="col in filterNutritionPrescriptionColumns"
              :key="col.field"
              class="border border-gray-300 p-2"
              :style="{ width: col.width + 'px' }"
            >
              <FormItem noStyle :name="['nutritionPrescriptionInfoList', rowIdx, 'planId']" />
              <template v-if="col.field === 'action'">
                <Button
                  v-if="!disabled"
                  class="!p-0"
                  type="link"
                  danger
                  @click="() => formState.nutritionPrescriptionInfoList?.splice(rowIdx, 1)"
                >
                  移除
                </Button>
              </template>
              <template v-else>
                <FormItem noStyle :name="['nutritionPrescriptionInfoList', rowIdx, col.field]">
                  <Input
                    class="!w-full"
                    v-model:value="item[col.field]"
                    placeholder="请输入"
                    :disabled="disabled"
                  />
                </FormItem>
              </template>
            </td>
          </tr>
        </tbody>
        <tbody v-else>
          <tr>
            <td colspan="100%">
              <div class="flex flex-col justify-center items-center h-full py-4">
                <span class="text-#999999">暂无数据</span>
                <Button
                  v-if="!disabled"
                  type="link"
                  pre-icon="ant-design:plus-circle-outlined"
                  @click="onAddNutrition"
                >
                  添加
                </Button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      <Col
        v-if="
          formState.nutritionPrescriptionInfoList &&
          formState.nutritionPrescriptionInfoList.length > 0 &&
          !disabled
        "
        class="mt-2"
        span="24"
      >
        <Button type="link" pre-icon="ant-design:plus-circle-outlined" @click="onAddNutrition">
          添加
        </Button>
      </Col>
    </Row>
  </Form>
</template>

<style lang="less" scoped></style>
