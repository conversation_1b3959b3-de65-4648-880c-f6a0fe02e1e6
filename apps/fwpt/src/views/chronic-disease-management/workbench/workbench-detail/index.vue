<script setup lang="ts">
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { useRequest } from '@ft/request';
  import VisitDetails from '../../../../../../sjyy/src/views/doctors-view/VisitDetails.vue';
  import BasicInformation from '../comp/BasicInformation.vue';
  import { queryPatientDetail } from '/@/api/chronic-disease-management/workbench';
  const patientId = useRouteQuery('patientId', '', { transform: String });
  const { data: patientInfo } = useRequest(() => queryPatientDetail(patientId.value));
</script>
<template>
  <div class="w-full h-full pr-4 flex flex-col py-2 gap-4">
    <BasicInformation :patientInfo="patientInfo" />
    <div class="flex-1 bg-#fff rounded-2 p-4 flex flex-col gap-4">
      <div class="flex items-center gap-2">
        <span class="inline-block bg-primary-color w-3px h-1em"></span>
        <span class="text-#333333 fw-bold">患者就诊详情</span>
      </div>
      <VisitDetails
        :activeItem="{ ...patientInfo, visitTypeCode: patientInfo?.visitType }"
        :patientInfo="{ ...patientInfo, visitTypeCode: patientInfo?.visitType }"
      />
    </div>
  </div>
</template>

<style lang="less" scoped></style>
