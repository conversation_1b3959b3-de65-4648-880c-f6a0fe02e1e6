<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { getPlanList } from '/@/api/chronic-disease-management/workbench';

  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm({
    labelWidth: 120,
    schemas: [
      {
        field: 'planId',
        label: '干预计划:',
        component: 'ApiRadioGroup',
        componentProps: ({}) => {
          return {
            api: () => getPlanList(),
            labelField: 'planName',
            valueField: 'id',
            required: true,
          };
        },
        colProps: { span: 24 },
        itemProps: { wrapperCol: { span: 16 } },
      },
    ],
    showActionButtonGroup: false,
  });

  const [register, { closeModal }] = useModalInner((data) => {
    formAction.setFieldsValue({
      planId: data?.planId,
    });
  });

  function onOk() {
    formAction.validate().then((values) => {
      emit('success', values);
      closeModal();
    });
  }
</script>

<template>
  <BasicModal
    destroy-on-close
    v-bind="$attrs"
    @register="register"
    :width="600"
    :min-height="100"
    title="选择干预计划"
    centered
    @ok="onOk"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<style scoped></style>
