<script setup lang="ts">
  import { computed, onMounted, ref, watchEffect } from 'vue';
  import { useECharts } from '@ft/internal/hooks/web/useECharts';
  import { graphic } from 'echarts/core';
  import { useRequest } from '@ft/request';
  import { getLineChart } from '/@/api/chronic-disease-management/workbench';
  import { DatePicker, RangePicker } from 'ant-design-vue';
  import type { Dayjs } from 'dayjs';
  import dayjs from 'dayjs';
  /**
   * 指标监测统计
   */
  const props = defineProps({
    planDetail: {
      type: Object,
      default: () => {},
    },
    patientInfo: {
      type: Object,
      default: () => {},
    },
    indexTypeName: {
      type: String,
      default: '',
    },
  });
  type RangeValue = [Dayjs, Dayjs];
  const value = ref<RangeValue>([dayjs().subtract(6, 'days'), dayjs()]);
  const selectedDate = ref<Dayjs>(dayjs());

  const hackValue = ref<RangeValue>();
  const dates = ref<RangeValue>();
  //离院时间之前的日期禁用
  const disabledLeaveDate = (current: Dayjs) => {
    return current < dayjs(props.patientInfo?.leaveDate);
  };
  const disabledDate = (current: Dayjs) => {
    if (!dates.value || (dates.value as any).length === 0) {
      return false;
    }
    const tooLate = dates.value[0] && current.diff(dates.value[0], 'days') > 6;
    const tooEarly = dates.value[1] && dates.value[1].diff(current, 'days') > 6;
    return tooEarly || tooLate;
  };
  // 指标类型编码 1-血压 2-血糖 3-身高
  const indexType = {
    血压: 1,
    血糖: 2,
    体重: 3,
  };

  onMounted(() => {
    runAsync({
      endDate: dayjs(new Date()).format('YYYY-MM-DD'),
      startDate:
        props.indexTypeName !== '血糖'
          ? dayjs(new Date()).subtract(6, 'day').format('YYYY-MM-DD')
          : dayjs(new Date()).format('YYYY-MM-DD'),
    });
  });
  const { data, runAsync } = useRequest(
    (params) =>
      getLineChart({
        planId: props.planDetail?.id,
        inpatientNo: props.planDetail?.inpatientNo,
        indexType: indexType[props.indexTypeName],
        ...params,
      }),
    {
      manual: true,
    },
  );
  const onOpenChange = (open: boolean) => {
    if (open) {
      dates.value = [] as any;
      hackValue.value = [] as any;
    } else {
      hackValue.value = undefined;
    }
  };

  const onChange = (val: RangeValue) => {
    value.value = val;
    if (val) {
      runAsync({
        startDate: val[0].format('YYYY-MM-DD'),
        endDate: val[1].format('YYYY-MM-DD'),
      });
    }
  };
  function onChangeDatePicker(val: Dayjs) {
    selectedDate.value = val;
    if (val) {
      runAsync({
        startDate: val.format('YYYY-MM-DD'),
      });
    }
  }

  const onCalendarChange = (val: RangeValue) => {
    dates.value = val;
  };

  const chartRef = ref<HTMLDivElement>();
  const { setOptions } = useECharts(chartRef as any);
  const xData = computed(() => {
    return (data.value && data.value[0]?.dataList?.map((item: any) => item?.x)) || [];
  });
  // 指标类型编码 1-血压 2-血糖 3-身高
  const titleSubtextType = {
    血压: 'mmHg',
    血糖: 'mmol/L',
    体重: 'kg',
  };
  const titleSubtext = computed(() => {
    return titleSubtextType[props.indexTypeName];
  });

  watchEffect(() => {
    setOptions({
      title: {
        subtext: titleSubtext.value,
      },
      tooltip: {
        trigger: 'axis',
        appendToBody: true,
      },
      grid: {
        left: '10%',
        right: '8%',
        bottom: '12%',
        top: '20%',
      },
      legend: {
        data: data.value?.map((item: any) => item?.name) || [],
      },
      xAxis: {
        data: xData.value,
        // axisTick: {
        //   show: false,
        // },
        axisLabel: {
          color: '#B0B1B4',
        },
        axisLine: {
          lineStyle: {
            color: '#EDEEF0',
          },
        },
      },
      yAxis: {
        splitLine: {
          lineStyle: {
            color: '#EDEEF0',
          },
          interval: 30,
        },
        axisLabel: {
          color: '#B0B1B4',
        },
      },
      series: data.value?.map((item: any) => {
        return {
          name: item?.name,
          type: 'line',
          data: item?.dataList.map((i: any) => i?.yvalue) || [],
          smooth: true,
          // itemStyle: {
          //   color: '#33BC71',
          // },
          areaStyle: {
            color: new graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(86, 255, 168, 0.16)',
              },
              {
                offset: 1,
                color: 'rgba(148, 155, 253, 0)',
              },
            ]),
          },
        };
      }),
    });
  });
</script>

<template>
  <div class="flex flex-col h-full">
    <div class="flex items-center gap-2 mb-3">
      <span>{{ indexTypeName }}</span>
      <RangePicker
        class="flex-1"
        v-if="indexTypeName !== '血糖'"
        :value="hackValue || value"
        :disabled-date="
          (current: Dayjs)=> {
            return disabledDate(current) || disabledLeaveDate(current);
          }
        "
        @change="onChange"
        @openChange="onOpenChange"
        @calendarChange="onCalendarChange"
      />
      <DatePicker
        :disabled-date="disabledLeaveDate"
        class="flex-1"
        v-else
        v-model:value="selectedDate"
        @change="onChangeDatePicker"
      />
    </div>
    <div ref="chartRef" class="flex-1 of-hidden"></div>
  </div>
</template>
