<script setup lang="ts">
  import { Descriptions } from 'ant-design-vue';
  defineProps({
    patientInfo: {
      type: Object,
    },
  });
</script>
<template>
  <div class="bg-white rounded p-4 pb-0 flex justify-between gap-4">
    <!-- <img src="/@/assets/images/hospital-follow-up.png" class="h-66px" alt="" srcset="" /> -->
    <div class="w-260px flex flex-col gap-2 text-13px text-main-text-color">
      <div class="flex items-center gap-2">
        <div class="font-bold text-24px truncate max-w-200px">{{ patientInfo?.patientName }}</div>
        <div class="border-r-1px border-r-solid border-r-#EDEEF0 p-r-2">{{ patientInfo?.sex }}</div>
        <div>{{ patientInfo?.age }}岁</div>
      </div>
      <div>{{ patientInfo?.idCardNo }}</div>
    </div>
    <Descriptions
      class="flex-1"
      :label-style="{ color: '#B0B1B4' }"
      :content-style="{ color: '#252931' }"
    >
      <Descriptions.Item label="所属区划">{{ patientInfo?.divisionName }}</Descriptions.Item>
      <Descriptions.Item label="离院日期">{{ patientInfo?.leaveDate }}</Descriptions.Item>
      <Descriptions.Item label="就诊科室">{{ patientInfo?.visitDeptName }}</Descriptions.Item>
      <Descriptions.Item label="主治医师">{{ patientInfo?.visitDoctor }}</Descriptions.Item>
      <Descriptions.Item label="联系电话">{{ patientInfo?.telephone }}</Descriptions.Item>
      <Descriptions.Item label="就诊诊断">{{ patientInfo?.visitDiagnosisName }}</Descriptions.Item>
    </Descriptions>
  </div>
</template>

<style lang="less" scoped></style>
