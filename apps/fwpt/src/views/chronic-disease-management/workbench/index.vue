<script setup lang="ts">
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button, Tabs, notification } from 'ant-design-vue';
  import { h, ref } from 'vue';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { useRequest } from '@ft/request';
  import { useRoute } from 'vue-router';
  import { SearchSchemas, columns } from './data';
  import {
    chronicDiseaseDoctorRediagnosisReminder,
    getChronicDiseaseList,
    getPatientList,
  } from '/@/api/chronic-disease-management/workbench';
  import { useAppStore } from '/@@/store/modules/app';
  import { usePermission } from '/@@/hooks/web/usePermission';
  const activeKey = ref<any>();
  const { data: tabList } = useRequest(getChronicDiseaseList, {
    onSuccess: (res) => {
      if (res.length > 0) {
        activeKey.value = res[0].code;
        ActionTable.reload();
      }
    },
  });
  const [registerTable, ActionTable] = useTable({
    useSearchForm: true,
    api: getPatientList,
    formConfig: {
      labelWidth: 100,
      schemas: SearchSchemas,
      fieldMapToTime: [['leaveDate', ['leaveStartDate', 'leaveEndDate'], 'YYYY-MM-DD']],
    },
    columns,
    actionColumn: {
      title: '操作',
      dataIndex: 'action',
      width: 400,
    },
    immediate: false,
    beforeFetch: (params) => {
      params.chronicDiseaseCode = activeKey.value;
      return params;
    },
  });
  const go = useGo();

  function handleManagementDetails(record) {
    go({
      name: 'WorkbenchManagementDetails',
      query: {
        mode: 'view',
        title: '管理详情',
        planId: record.planId,
        patientId: record.id,
      },
    });
  }
  function handleWorkbenchDetails(record) {
    go({
      name: 'WorkbenchWorkbenchDetail',
      query: { mode: 'view', title: '患者详情', patientId: record.id },
    });
  }

  function handlePlanConfiguration(record) {
    go({
      name: 'WorkbenchPlanConfiguration',
      query: {
        mode: 'edit',
        title: '管理计划配置',
        patientId: record.id,
        planId: record.planId,
        outInId: record.outInId,
      },
    });
  }
  const appStore = useAppStore();
  const { refreshMenu } = usePermission();
  const route = useRoute();
  const remark = route.meta.remark as Recordable;
  function handleJumpToHealthEducation(_record) {
    appStore.setActiveMenuId(remark.menuId);
    setTimeout(() => {
      sessionStorage.setItem('isNotGoHome', '/release');
      refreshMenu();
    }, 10);
  }

  function handleJumpToConsultation(_record) {
    appStore.setActiveMenuId(remark.menuId1);
    setTimeout(() => {
      sessionStorage.setItem('isNotGoHome', '/record-supervision');
      refreshMenu();
    }, 10);
  }
  function createActions(record, _column): ActionItem[] {
    // 患者详情 管理计划配置 管理详情
    return [
      {
        label: '患者详情',
        type: 'link',
        onClick: handleWorkbenchDetails.bind(null, record),
      },
      {
        label: '管理计划配置',
        type: 'link',
        onClick: handlePlanConfiguration.bind(null, record),
      },
      {
        label: '管理详情',
        type: 'link',
        ifShow: () => record.interveneStatus === 1,
        onClick: handleManagementDetails.bind(null, record),
      },
      {
        label: '健康宣教',
        type: 'link',
        onClick: handleJumpToHealthEducation.bind(null, record),
      },
      {
        label: '咨询问诊',
        type: 'link',
        onClick: handleJumpToConsultation.bind(null, record),
      },
    ];
  }
  useRequest(chronicDiseaseDoctorRediagnosisReminder, {
    onSuccess: (result) => {
      if (result) {
        const key = `open${Date.now()}`;
        notification.open({
          message: `${result?.mgsTypeDesc}`,
          description: `${result?.msgContent}`,
          placement: 'bottomRight',
          duration: null,
          btn: () =>
            h(
              Button,
              {
                type: 'primary',
                size: 'small',
                onClick: () => notification.close(key),
              },
              { default: () => '关闭' },
            ),
          key,
          onClose: close,
        });
      }
    },
  });
</script>

<template>
  <div class="w-[calc(100%-16px)] rounded rounded-lt-none">
    <BasicTable @register="registerTable" class="ft-main-table">
      <template #headerTop>
        <div class="text-base font-bold">慢病患者列表</div>
        <div>
          <Tabs v-model:activeKey="activeKey" @change="() => ActionTable.reload()">
            <!-- 结核病 病毒性肝炎 -->
            <Tabs.TabPane v-for="item in tabList" :key="item.code" :tab="item.name" />
          </Tabs>
        </div>
      </template>
      <template #toolbar>
        <!-- <Button :loading="exportLoading" @click="onExportExpert"> 批量导出 </Button> -->
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }

    .vben-basic-table-form-container .ant-form,
    .vben-basic-table .ant-table-wrapper {
      border-radius: 4px;
    }

    .vben-basic-table-form-container .ant-form {
      border-top-left-radius: 0;
      margin-bottom: 10px;
    }
  }
</style>
