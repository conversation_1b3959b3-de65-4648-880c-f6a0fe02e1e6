<script setup lang="ts">
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { useRequest } from '@ft/request';
  import BasicInformation from '../comp/BasicInformation.vue';
  import VisitDetails from './VisitDetails.vue';
  import InterventionRecord from './component/InterventionRecord.vue';
  import type { IPageList } from '/@/api/chronic-disease-management/workbench';
  import { getPlanDetail, queryPatientDetail } from '/@/api/chronic-disease-management/workbench';

  const title = useRouteQuery('title', '', { transform: String });
  const planId = useRouteQuery('planId', '', { transform: String });
  const patientId = useRouteQuery('patientId', '', { transform: String });

  const { data: patientInfo } = useRequest(() => queryPatientDetail(patientId.value), {
    ready: !!patientId.value,
  });
  const { data: planDetail = {} as IPageList } = useRequest(() => getPlanDetail(planId.value), {
    ready: !!planId.value,
  });
</script>
<template>
  <div class="w-full h-full pr-4 flex flex-col py-2 gap-4">
    <BasicInformation :patientInfo="patientInfo" />
    <div class="flex-1 bg-#fff rounded-2 p-4 flex flex-col gap-2">
      <div class="flex items-center gap-2">
        <span class="text-#333333 fw-bold">{{ title }}</span>
      </div>
      <VisitDetails
        :patientInfo="patientInfo"
        :planDetail="planDetail"
        v-if="title === '干预详情'"
      />
      <InterventionRecord
        class="basis-0 min-h-0 of-y-auto flex-1"
        :patientInfo="patientInfo"
        :planDetail="planDetail"
        v-if="title === '管理详情'"
      />
    </div>
  </div>
</template>

<style lang="less" scoped></style>
