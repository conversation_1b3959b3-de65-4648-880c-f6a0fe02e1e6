import type { FormSchema } from '@ft/internal/components/Form/index';
/**
 * 用药
 * 是否推荐购药
 * 生活指导
 * 运动指导
 * 饮食指导
 * 生活习惯指导
 * 心理指导
 * 检验检查计划
 * 是否需定期做相关检验检查
 */

export const formSchemas: FormSchema[] = [
  {
    field: 'moduleName1',
    component: 'Input',
    label: '指标监测',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  {
    field: 'indicatorMonitoring',
    component: 'Input',
    label: '',
    colSlot: 'indicatorMonitoring',
    colProps: { span: 24 },
  },
  {
    field: 'moduleName1',
    component: 'Input',
    label: '用药',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  {
    field: 'id',
    component: 'Input',
    label: 'id',
    show: false,
  },
  {
    field: 'isRecommended',
    component: 'RadioGroup',
    label: '是否推荐购药',
    colProps: { span: 24 },
    componentProps: {
      options: [
        { label: '推荐', value: '推荐' },
        { label: '不推荐', value: '不推荐' },
      ],
    },
    defaultValue: '不推荐',
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: 'moduleName1',
    component: 'Input',
    label: '生活指导',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  {
    field: 'isRecommended',
    component: 'InputTextArea',
    label: '运动指导',
    componentProps: {
      placeholder: '请输入运动指导',
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 10 },
    itemProps: { wrapperCol: { span: 20 } },
  },
  {
    field: 'isRecommended',
    component: 'InputTextArea',
    label: '饮食指导',
    componentProps: {
      placeholder: '请输入饮食指导',
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 10 },
    itemProps: { wrapperCol: { span: 20 } },
  },
  {
    field: 'isRecommended',
    component: 'InputTextArea',
    label: '生活习惯指导',
    componentProps: {
      placeholder: '请输入生活习惯指导',
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 10 },
    itemProps: { wrapperCol: { span: 20 } },
  },
  {
    field: 'isRecommended',
    component: 'InputTextArea',
    label: '心理指导',
    componentProps: {
      placeholder: '请输入心理指导',
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 10 },
    itemProps: { wrapperCol: { span: 20 } },
  },
  {
    field: 'moduleName1',
    component: 'Input',
    label: '检验检查计划',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  {
    field: 'isRecommended',
    component: 'RadioGroup',
    label: '是否需定期做相关检验检查',
    colProps: { span: 24 },
    componentProps: {
      options: [
        { label: '是', value: '是' },
        { label: '否', value: '否' },
      ],
    },
    defaultValue: '否',
    itemProps: { wrapperCol: { span: 24 } },
  },
];
