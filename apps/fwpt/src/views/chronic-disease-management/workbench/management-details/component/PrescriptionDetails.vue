<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';

  import { onMounted } from 'vue';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { columns } from './PrescriptionDetails.data';
  import { getElectronicPrescriptionListByIdCard } from '/@/api/chronic-disease-management/workbench';

  const idCardNo = useRouteQuery('idCardNo', '', { transform: String });

  onMounted(() => {
    idCardNo.value && reload();
  });

  const [registerTable, { reload }] = useTable({
    useSearchForm: false,
    api: () => getElectronicPrescriptionListByIdCard(idCardNo.value),
    columns,
    immediate: false,
    scroll: { y: 'max-content' },
  });
</script>

<template>
  <BasicTable @register="registerTable" />
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }

    .vben-basic-table-form-container .ant-form,
    .vben-basic-table .ant-table-wrapper {
      border-radius: 4px;
    }

    .vben-basic-table-form-container .ant-form {
      border-top-left-radius: 0;
      margin-bottom: 10px;
    }
  }
</style>
