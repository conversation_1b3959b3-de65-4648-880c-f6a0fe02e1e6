<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';

  const [registerForm, formAction] = useForm({
    disabled: true,
    schemas: [
      {
        field: 'columnName',
        label: '宣教标题',
        component: 'Input',
        colProps: { span: 24 },
      },
      {
        field: 'content',
        label: '宣教内容',
        component: 'InputTextArea',
        componentProps: { autoSize: { minRows: 4, maxRows: 4 } },
        colProps: { span: 24 },
      },

      {
        field: 'columnName',
        label: '宣教分类',
        component: 'Input',
        colProps: { span: 24 },
      },
      {
        field: 'fileUrl',
        label: '上传附件',
        component: 'UploadAsset',
        componentProps: {
          uploadType: 'file',
          resultField: 'data.url',
          modelNameField: 'fileName',
          maxCount: 1,
          // download: true,
          newWindowPreview: true,
        },
        colProps: {
          span: 24,
        },
        itemProps: {
          wrapperCol: {
            span: 16,
          },
        },
      },
    ],
    labelWidth: 80,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });
  const [register] = useModalInner((data) => {
    formAction.setFieldsValue(data);
  });
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    title="健康记录详情"
    :can-fullscreen="false"
    :show-ok-btn="false"
    @register="register"
    :min-height="320"
    width="800px"
    centered
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
