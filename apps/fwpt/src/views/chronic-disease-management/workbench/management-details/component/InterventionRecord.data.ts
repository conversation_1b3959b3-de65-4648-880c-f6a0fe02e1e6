import type { FormSchema } from '@ft/internal/components/Form/index';
/**
 * 用药
 * 是否推荐购药
 * 生活指导
 * 运动指导
 * 饮食指导
 * 生活习惯指导
 * 心理指导
 * 检验检查计划
 * 是否需定期做相关检验检查
 */

export const formSchemas: FormSchema[] = [
  {
    field: 'moduleName1',
    component: 'Input',
    label: '指标监测',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  {
    field: 'indicatorMonitoring',
    component: 'Input',
    label: '',
    colSlot: 'indicatorMonitoring',
    colProps: { span: 24 },
  },

  {
    field: 'id',
    component: 'Input',
    label: 'id',
    show: false,
  },
  {
    field: 'moduleName1',
    component: 'Input',
    label: '用药',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  {
    field: 'isRecommendedMedication',
    component: 'RadioGroup',
    label: '是否推荐购药',
    colProps: { span: 24 },
    componentProps: ({ formModel }) => {
      return {
        options: [
          { label: '推荐', value: 1 },
          { label: '不推荐', value: 0 },
        ],
        onChange: ({ target }) => {
          if (target?.value === 0) {
            formModel.recommendedMedication = '';
          }
        },
      };
    },
    defaultValue: 0,
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: 'recommendedMedication',
    component: 'InputTextArea',
    label: '药品推荐信息',
    required: ({ model }) => {
      return model.isRecommendedMedication === 1;
    },
    // disabledLabelWidth: false,
    componentProps: {
      placeholder: '请填写药品推荐信息',
      autoSize: { minRows: 4, maxRows: 4 },
    },
    show: ({ model }) => {
      return model.isRecommendedMedication === 1;
    },
    colProps: { span: 9 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    field: 'medicationSchedule',
    component: 'InputTextArea',
    label: '用药计划',
    componentProps: {
      placeholder: '请填写用药计划',
      autoSize: { minRows: 4, maxRows: 4 },
    },
    show: ({ model }) => {
      return model.isRecommendedMedication === 1;
    },
    colProps: { span: 9 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    field: 'medicationDate',
    component: 'DatePicker',
    label: '下次购药时间',
    componentProps: () => {
      return {
        // showTime: true,
        format: 'YYYY-MM-DD',
        style: { width: '100%' },
        valueFormat: 'YYYY-MM-DD',
        getPopupContainer: () => document.body,
      };
    },
    show: ({ model }) => {
      return model.isRecommendedMedication === 1;
    },
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    field: 'moduleName1',
    component: 'Input',
    label: '生活指导',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  {
    field: 'sportsInstruction',
    component: 'InputTextArea',
    label: '运动指导',
    componentProps: {
      placeholder: '请输入运动指导',
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 9 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    field: 'dietaryGuidance',
    component: 'InputTextArea',
    label: '饮食指导',
    componentProps: {
      placeholder: '请输入饮食指导',
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 9 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    field: 'livingHabitGuide',
    component: 'InputTextArea',
    label: '生活习惯指导',
    componentProps: {
      placeholder: '请输入生活习惯指导',
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 9 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    field: 'psychologicalGuide',
    component: 'InputTextArea',
    label: '心理指导',
    componentProps: {
      placeholder: '请输入心理指导',
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 9 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    field: 'moduleName1',
    component: 'Input',
    label: '复诊计划',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
  {
    field: 'reVisitRule',
    component: 'InputTextArea',
    label: '复诊规则',
    componentProps: {
      placeholder: '请输入心理指导',
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 9 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    field: 'reVisitTime',
    component: 'DatePicker',
    label: '复诊时间',
    componentProps: () => {
      return {
        showTime: true,
        style: { width: '100%' },
        format: 'YYYY-MM-DD HH:mm:ss',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        getPopupContainer: () => document.body,
      };
    },
    colProps: { span: 9 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    field: 'moduleName1',
    component: 'Input',
    label: '检验检查计划',
    colSlot: 'moduleName',
    colProps: { span: 24 },
  },
];
