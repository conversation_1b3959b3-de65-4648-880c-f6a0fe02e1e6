import type { BasicColumn } from '@ft/internal/components/Table';

/**
药品名称（规格）
剂量
用法
频次
天数
数量
 */
export const columns: BasicColumn[] = [
  {
    title: '药品名称（规格）',
    dataIndex: 'itemName',
    width: 150,
    align: 'left',
  },
  {
    title: '剂量',
    dataIndex: 'dosage',
    width: 80,
    align: 'left',
  },
  {
    title: '用法',
    dataIndex: 'usage',
    width: 80,
    align: 'left',
  },
  {
    title: '频次',
    dataIndex: 'frequency',
    width: 80,
    align: 'left',
  },
  {
    title: '天数',
    dataIndex: 'days',
    width: 80,
    align: 'left',
  },
  {
    title: '数量',
    dataIndex: 'total',
    width: 80,
    align: 'left',
    customRender: ({ record }) => {
      return `${record.total} ${record.specifications || ''}`;
    },
  },
];
