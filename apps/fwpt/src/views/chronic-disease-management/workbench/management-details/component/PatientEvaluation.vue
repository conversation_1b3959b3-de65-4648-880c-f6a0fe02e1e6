<script setup lang="ts">
  import { useRequest } from '@ft/request';
  import { Empty, Textarea } from 'ant-design-vue';
  import { getEvaluateRecordList } from '/@/api/chronic-disease-management/workbench';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  /**
   * @description
   * 患者评价
   * PatientEvaluation
   */
  const patientId = useRouteQuery('patientId', '', { transform: String });

  const { data } = useRequest(() => getEvaluateRecordList(patientId.value), {});
</script>
<template>
  <div class="w-full h-full">
    <Empty v-if="!data || data.length === 0" :image="Empty.PRESENTED_IMAGE_SIMPLE" />
    <div v-else>
      <div v-for="item in data" :key="item.id" class="mb-4">
        <div class="mb-2">{{ item?.evaluateTime }}</div>
        <Textarea class="!w-50%" :rows="4" :value="item?.evaluateContent" />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped></style>
