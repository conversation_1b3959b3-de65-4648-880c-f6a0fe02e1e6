<script setup lang="ts">
  import { useRequest } from '@ft/request';
  import { Empty, Pagination } from 'ant-design-vue';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { getEducationContentQueryPageList } from '/@/api/chronic-disease-management/workbench';
  import { computed, ref, watch } from 'vue';
  import { get } from 'lodash-es';
  import { useLoading } from '@ft/internal/components/Loading';
  import { useModal } from '@ft/internal/components/Modal';
  import HealthEducationModal from './HealthEducationModal.vue';
  /**
   * @description
   * 健康宣教记录
   * HealthEducation
   */
  const idCardNo = useRouteQuery('idCardNo', '', { transform: String });
  const pageNum = ref(1);
  const pageSize = ref(20);

  const params = computed(() => ({
    pageNum: pageNum.value,
    pageSize: pageSize.value,
    idCardNo: idCardNo.value,
  }));

  const {
    data,
    run: run,
    loading,
  } = useRequest(getEducationContentQueryPageList, {
    manual: true,
    ready: !!idCardNo.value,
  });
  const total = computed(() => get(data.value, 'total', 0));
  const list = computed(() => data.value?.list || []);
  watch(
    params,
    (params) => {
      params.idCardNo && run(params);
    },
    {
      deep: true,
      immediate: true,
    },
  );
  const listRef = ref<HTMLElement | null>(null);
  useLoading({
    target: listRef.value,
    props: {
      tip: '加载中...',
      absolute: true,
      loading: loading.value,
    },
  });
  const [register, { openModal }] = useModal();

  function onPreview(item) {
    openModal(true, { ...item, idCardNo: idCardNo.value });
  }
</script>
<template>
  <div>
    <Empty v-if="!list || list.length === 0" :image="Empty.PRESENTED_IMAGE_SIMPLE" />
    <div
      v-else
      ref="listRef"
      class="grid sm:grid-cols-3 md:grid-cols-4 2xl:grid-cols-6 gap-6 auto-rows-min flex-1 basis-0 of-y-auto"
    >
      <div
        class="relative p-4 border rounded border-[#DCDFE6] min-w-266px transition hover:shadow-[0px_4px_16px_0px_rgba(0,0,0,0.08)] cursor-pointer group hover:border-primary-color"
        v-for="item in list"
        :key="item.id"
        @click="onPreview(item)"
      >
        <div class="font-bold mb-2">{{ item?.columnName }}</div>
        <div>
          <span class="text-info-text-color">创建人：</span>
          <span>{{ item?.createUser }}</span>
        </div>
        <div>
          <span class="text-info-text-color">创建时间：</span>
          <span>{{ item?.createTime }}</span>
        </div>
      </div>
    </div>
    <div class="flex justify-end py-3 px-4 absolute bottom-4 right-4">
      <Pagination
        :show-total="(total) => `共${total}条`"
        show-quick-jumper
        v-model:current="pageNum"
        show-less-items
        :page-size="pageSize"
        :total="total"
      />
    </div>
    <HealthEducationModal @register="register" />
  </div>
</template>

<style lang="less" scoped></style>
