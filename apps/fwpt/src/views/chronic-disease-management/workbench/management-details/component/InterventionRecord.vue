<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { computed, onMounted, ref, watch } from 'vue';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { cloneDeep } from 'lodash-es';
  import IndicatorMonitoring from '../../comp/IndicatorMonitoring.vue';

  import InspectionPlan from '../../plan-configuration/InspectionPlan.vue';
  import {
    DefaultExercisePrescriptionInfoList,
    DefaultNutritionPrescriptionInfoList,
    type SubFormState,
  } from '../../plan-configuration/data';
  import { formSchemas } from './InterventionRecord.data';
  /**
   * @description
   * 干预记录
   * InterventionRecord
   */
  const mode = useRouteQuery('mode', 'add', { transform: String });
  const isView = computed(() => mode.value === 'view');
  const props = defineProps({
    planDetail: {
      type: Object,
      default: () => ({}),
    },
    patientInfo: {
      type: Object,
      default: () => ({}),
    },
  });

  const [registerFrom, { setFieldsValue }] = useForm({
    labelWidth: 180,
    showActionButtonGroup: false,
    disabled: isView,
    schemas: formSchemas,
  });
  const formState = ref<SubFormState>({
    inspection: '',
    isInspection: '',
    inspectionList: [
      {
        itemName: '',
        appointmentTime: '',
      },
    ],
    exercisePrescriptionExecuteTime: undefined,
    exercisePrescriptionInfoList: cloneDeep(DefaultExercisePrescriptionInfoList),
    nutritionPrescriptionExecuteTime: undefined,
    nutritionPrescriptionInfoList: cloneDeep(DefaultNutritionPrescriptionInfoList),
  });

  onMounted(() => {
    watch(
      () => props.planDetail,
      (val) => {
        setFieldsValue(val);
        formState.value = {
          inspection: val?.inspection,
          isInspection: val?.isInspection?.toString(),
          inspectionList: val?.inspectionItemList || [],
          exercisePrescriptionExecuteTime: val?.exercisePrescriptionExecuteTime,
          exercisePrescriptionInfoList:
            val?.exercisePrescriptionInfoList && val?.exercisePrescriptionInfoList?.length > 0
              ? val?.exercisePrescriptionInfoList
              : cloneDeep(DefaultExercisePrescriptionInfoList),
          nutritionPrescriptionExecuteTime: val?.nutritionPrescriptionExecuteTime,
          nutritionPrescriptionInfoList:
            val?.nutritionPrescriptionInfoList && val?.nutritionPrescriptionInfoList?.length > 0
              ? val?.nutritionPrescriptionInfoList
              : cloneDeep(DefaultNutritionPrescriptionInfoList),
        };
      },
      { deep: true, immediate: true },
    );
  });
</script>
<template>
  <div>
    <BasicForm @register="registerFrom">
      <template #moduleName="{ schema }">
        <div class="flex items-center gap-2 mb-3">
          <span v-if="schema.label" class="inline-block bg-primary-color w-3px h-1em"></span>
          <span>{{ schema.label }}</span>
        </div>
      </template>
      <template #indicatorMonitoring>
        <div class="flex justify-between gap-4 h-224px">
          <div class="flex-1 of-hidden bg-white rounded rounded-lt-none">
            <IndicatorMonitoring
              :patientInfo="patientInfo"
              :planDetail="planDetail"
              indexTypeName="血压"
            />
          </div>
          <div class="flex-1 of-hidden bg-white rounded rounded-lt-none">
            <IndicatorMonitoring
              :patientInfo="patientInfo"
              :planDetail="planDetail"
              indexTypeName="血糖"
            />
          </div>
          <div class="flex-1 of-hidden bg-white rounded rounded-lt-none">
            <IndicatorMonitoring
              :patientInfo="patientInfo"
              :planDetail="planDetail"
              indexTypeName="体重"
            />
          </div>
        </div>
      </template>
    </BasicForm>
    <InspectionPlan v-model="formState" disabled />
  </div>
</template>

<style lang="less" scoped></style>
