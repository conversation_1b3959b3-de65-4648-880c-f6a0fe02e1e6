<script setup lang="ts">
  import { Tabs } from 'ant-design-vue';
  import { ref } from 'vue';
  import InterventionRecord from './component/InterventionRecord.vue';
  import OnlineCommunication from './component/OnlineCommunication.vue';
  import PrescriptionDetails from './component/PrescriptionDetails.vue';
  import PatientEvaluation from './component/PatientEvaluation.vue';
  import HealthEducation from './component/HealthEducation.vue';

  defineOptions({
    components: {
      InterventionRecord: InterventionRecord,
      OnlineCommunication: OnlineCommunication,
      PrescriptionDetails: PrescriptionDetails,
      PatientEvaluation: PatientEvaluation,
      HealthEducation: HealthEducation,
    },
  });
  defineProps({
    planDetail: {
      type: Object,
      default: () => ({}),
    },
    patientInfo: {
      type: Object,
      default: () => ({}),
    },
  });

  const componentsMap = [
    {
      tab: '干预记录',
      key: 'InterventionRecord',
    },
    {
      tab: '线上沟通记录',
      key: 'OnlineCommunication',
    },
    {
      tab: '处方明细',
      key: 'PrescriptionDetails',
    },
    {
      tab: '健康宣教记录',
      key: 'HealthEducation',
    },
    {
      tab: '患者评价',
      key: 'PatientEvaluation',
    },
  ];
  const visitDetailsKey = ref('InterventionRecord');
</script>

<template>
  <div class="flex-1 of-hidden flex flex-col">
    <Tabs v-model:activeKey="visitDetailsKey" :destroyInactiveTabPane="false">
      <Tabs.TabPane v-for="item in componentsMap || []" :key="item.key" :tab="item.tab" />
    </Tabs>
    <div class="basis-0 min-h-0 of-y-auto flex-1">
      <component :patientInfo="patientInfo" :planDetail="planDetail" :is="visitDetailsKey" />
    </div>
  </div>
</template>
