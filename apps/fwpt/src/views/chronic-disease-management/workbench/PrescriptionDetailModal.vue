<script setup lang="ts">
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  // import Prescribing from '../apply-prescribing/Prescribing.vue';

  defineEmits(['register', 'sussess']);

  defineProps({
    prescriptionId: {
      type: String,
      default: '',
    },
  });

  const [register] = useModalInner();
</script>

<template>
  <BasicModal
    destroy-on-close
    v-bind="$attrs"
    @register="register"
    :width="1200"
    :min-height="400"
    title="处方详情"
    centered
    :show-ok-btn="false"
  >
    <!-- <Prescribing :prescription-id="prescriptionId" /> -->
  </BasicModal>
</template>
<style scoped></style>
