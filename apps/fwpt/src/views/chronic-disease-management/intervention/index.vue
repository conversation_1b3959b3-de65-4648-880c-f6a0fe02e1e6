<script setup lang="ts">
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Tabs } from 'ant-design-vue';
  import { ref } from 'vue';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { useRequest } from '@ft/request';
  import { SearchSchemas, columns } from './data';
  import {
    getChronicDiseaseList,
    queryPatientInterveneRecordPageList,
  } from '/@/api/chronic-disease-management/workbench';

  const activeKey = ref<any>();
  const { data: tabList } = useRequest(getChronicDiseaseList, {
    onSuccess: (res) => {
      if (res.length > 0) {
        activeKey.value = res[0].code;
        ActionTable.reload();
      }
    },
  });
  const [registerTable, ActionTable] = useTable({
    useSearchForm: true,
    api: queryPatientInterveneRecordPageList,
    dataSource: [{ diagnosisStatus: 1 }],
    formConfig: {
      labelWidth: 100,
      schemas: SearchSchemas,
      fieldMapToTime: [['interventionDate', ['startDate', 'endDate'], 'YYYY-MM-DD']],
    },
    columns,
    immediate: false,
    actionColumn: {
      title: '操作',
      dataIndex: 'action',
      width: 150,
    },
    beforeFetch: (params) => {
      params.chronicDiseaseCode = activeKey.value;
      return params;
    },
  });
  const go = useGo();

  function handleDetails(record) {
    go({
      name: 'InterventionManagementDetails',
      query: {
        mode: 'view',
        title: '干预详情',
        patientId: record.id,
        planId: record.planId,
        outInId: record.outInId,
        idCardNo: record.idCardNo,
      },
    });
  }

  function createActions(record, _column): ActionItem[] {
    // 患者详情 管理计划配置 管理详情
    return [
      {
        label: '干预详情',
        type: 'link',
        onClick: handleDetails.bind(null, record),
      },
    ];
  }
</script>

<template>
  <div class="h-[calc(100%-10px)] w-[calc(100%-16px)] rounded rounded-lt-none">
    <BasicTable @register="registerTable">
      <template #headerTop>
        <div class="text-base font-bold">慢病干预计划</div>
        <div>
          <Tabs v-model:activeKey="activeKey" @change="() => ActionTable.reload()">
            <!-- 结核病 病毒性肝炎 -->
            <Tabs.TabPane v-for="item in tabList" :key="item.code" :tab="item.name" />
          </Tabs>
        </div>
      </template>
      <template #toolbar>
        <!-- <Button :loading="exportLoading" @click="onExportExpert"> 批量导出 </Button> -->
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }

    .vben-basic-table-form-container .ant-form,
    .vben-basic-table .ant-table-wrapper {
      border-radius: 4px;
    }

    .vben-basic-table-form-container .ant-form {
      border-top-left-radius: 0;
      margin-bottom: 10px;
    }
  }
</style>
