import type { FormSchema } from '@ft/internal/components/Form';
import type { BasicColumn } from '@ft/internal/components/Table';

/**
 * 干预日期
 * 干预计划名称
 * 患者姓名
 * 就诊诊断
 */
export const SearchSchemas: FormSchema[] = [
  {
    field: 'patientName',
    label: '患者姓名',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'planName',
    label: '干预计划名称',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'interventionDate',
    label: '干预日期',
    component: 'RangePicker',
    componentProps: {
      style: { width: '100%' },
    },
    colProps: { span: 6 },
  },
  {
    field: 'visitDiagnosisName',
    label: '就诊诊断',
    component: 'Input',
    colProps: { span: 6 },
  },
];

/**
患者姓名
性别
年龄
身份证号
电话
就诊类别
就诊诊断
就诊机构
离院日期
干预计划名称

 */
export const columns: BasicColumn[] = [
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    width: 150,
    align: 'left',
  },
  {
    title: '性别',
    dataIndex: 'sex',
    width: 80,
    align: 'left',
  },
  {
    title: '年龄',
    dataIndex: 'age',
    width: 80,
    align: 'left',
  },
  {
    title: '身份证号',
    dataIndex: 'idCardNo',
    width: 180,
    align: 'left',
  },
  {
    title: '电话',
    dataIndex: 'telephone',
    width: 150,
    align: 'left',
  },
  {
    title: '就诊类别',
    dataIndex: 'visitType',
    width: 150,
    align: 'left',
  },
  {
    title: '就诊号/住院号',
    dataIndex: 'outInId',
    width: 150,
    align: 'left',
  },
  {
    title: '就诊诊断',
    dataIndex: 'visitDiagnosisName',
    width: 150,
    align: 'left',
  },
  {
    title: '就诊机构',
    dataIndex: 'visitHospitalName',
    width: 150,
    align: 'left',
  },
  {
    title: '离院日期',
    dataIndex: 'leaveDate',
    width: 180,
    align: 'left',
  },
  {
    title: '干预开始日期',
    dataIndex: 'interveneStartTime',
    width: 180,
    align: 'left',
  },
  {
    title: '干预结束日期',
    dataIndex: 'interveneEndTime',
    width: 180,
    align: 'left',
  },
  {
    title: '干预计划名称',
    dataIndex: 'planName',
    width: 150,
    align: 'left',
  },
];
