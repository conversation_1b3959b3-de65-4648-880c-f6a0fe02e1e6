<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { columns, formSchema } from './data';

  const [registerTable] = useTable({
    // api: getConsultationStatisticsCount,
    dataSource: [{}],
    columns,
    showIndexColumn: true,
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas: formSchema,
    },
  });
</script>

<template>
  <div class="h-full pr-4 pb-4">
    <div class="h-full">
      <BasicTable @register="registerTable">
        <template #headerTop>
          <span class="text-base font-bold">在线咨询财务数据</span>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }
  }
</style>
