import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { getMonthList, getQuarterList, getYearList } from '/@/api/consultation/record-supervision';
export const formSchema: FormSchema[] = [
  {
    field: 'year',
    label: '统计年度',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: getYearList,
        labelField: 'text',
        valueField: 'value',
        getPopupContainer() {
          return document.body;
        },
        onChange(_, opt) {
          if (opt?.label) {
            formModel.quarter = undefined;
            formModel.month = undefined;
          }
        },
      };
    },
    colProps: { span: 6 },
  },
  {
    field: 'quarter',
    label: '统计季度',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => formModel.year && getQuarterList(formModel.year),
        labelField: 'text',
        valueField: 'value',
        getPopupContainer() {
          return document.body;
        },
        onChange(_, opt) {
          if (opt?.label) formModel.month = undefined;
        },
      };
    },
    colProps: { span: 6 },
  },
  {
    field: 'month',
    label: '统计月份',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => formModel.year && getMonthList(formModel.year, formModel.quarter),
        labelField: 'text',
        valueField: 'value',
        getPopupContainer() {
          return document.body;
        },
      };
    },
    colProps: { span: 6 },
  },
  {
    field: 'consultExpertGroupName',
    label: '咨询专家组',
    component: 'Input',
    colProps: { span: 6 },
  },
];

/**
 * 咨询专家组
 * 快速咨询（元）
 * 护理咨询（元）
 * 在线问诊（元）
 */
export const columns: BasicColumn[] = [
  {
    title: '咨询专家组',
    dataIndex: 'expertGroupName',
  },
  {
    title: '快速咨询（元）',
    dataIndex: 'quickNum',
  },
  {
    title: '护理咨询（元）',
    dataIndex: 'nursingNum',
  },
  {
    title: '在线问诊（元）',
    dataIndex: 'onlineNum',
  },
];
