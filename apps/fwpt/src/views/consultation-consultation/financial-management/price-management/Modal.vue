<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useUserStore } from '@ft/internal/store/modules/user';
  import { computed, ref } from 'vue';
  import { useRequest } from '@ft/request';
  import { editFormSchema } from './data';
  import {
    addConsultationPrice,
    updateConsultationPrice,
  } from '/@/api/consultation/price-management';

  const emit = defineEmits(['register', 'success']);
  const { userInfo } = useUserStore();
  const mode = ref('add');
  const getTitle = computed(() => (mode.value === 'add' ? '新增定价' : '编辑定价'));

  const [registerForm, formAction] = useForm({
    labelWidth: 100,
    schemas: editFormSchema,
    colon: true,
    showActionButtonGroup: false,
  });
  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data.mode;
    if (!data.record) {
      formAction.setFieldsValue({
        orgName: userInfo?.orgName,
        orgId: userInfo?.orgId,
      });
      return;
    }
    formAction.setFieldsValue({
      ...data.record,
      orgName: userInfo?.orgName,
      orgId: userInfo?.orgId,
    });
  });
  const { loading, runAsync: runAsyncSave } = useRequest(
    () =>
      mode.value === 'edit'
        ? updateConsultationPrice(formAction.getFieldsValue())
        : addConsultationPrice(formAction.getFieldsValue()),
    {
      manual: true,
      showSuccessMessage: true,
      async onBefore() {
        try {
          await formAction.validate();
          return true;
        } catch (e) {
          return false;
        }
      },
      onSuccess() {
        emit('success');
        closeModal();
      },
    },
  );
</script>

<template>
  <BasicModal
    @register="register"
    :destroy-on-close="true"
    :can-fullscreen="false"
    v-bind="$attrs"
    :width="450"
    :title="getTitle"
    :ok-button-props="{
      loading,
    }"
    centered
    @ok="runAsyncSave"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<style scoped></style>
