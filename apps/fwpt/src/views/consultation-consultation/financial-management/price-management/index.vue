<script setup lang="ts">
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { useModal } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { columns } from './data';
  import Modal from './Modal.vue';
  import {
    deleteConsultationPrice,
    getConsultationPricePage,
  } from '/@/api/consultation/price-management';

  const [register, tableIns] = useTable({
    api: getConsultationPricePage,
    columns,
    resizeHeightOffset: 10,
    actionColumn: {
      title: '操作',
      width: 120,
      dataIndex: 'action',
    },
  });
  const [registerModal, { openModal }] = useModal();
  function onAdd() {
    openModal(true, {
      mode: 'add',
    });
  }
  function onEdit(record) {
    console.log(record);
    openModal(true, {
      mode: 'edit',
      record,
    });
  }
  const { runAsync: deleteRunAsync } = useRequest(deleteConsultationPrice, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      tableIns.reload();
    },
  });
  function onDelete(record) {
    deleteRunAsync(record.id);
  }
  function createActions(record, _column): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: onEdit.bind(null, record),
      },
      {
        label: '删除',
        danger: true,
        popConfirm: {
          placement: 'topRight',
          title: '确认删除吗？',
          okButtonProps: { danger: true },
          confirm: onDelete.bind(null, record),
        },
      },
    ];
  }
</script>

<template>
  <div class="h-full pr-4 pb-4">
    <div class="h-full">
      <BasicTable @register="register">
        <template #headerTop>
          <div class="text-base font-bold mb-4">定价管理</div>
        </template>
        <template #tableTitle>
          <Button type="primary" @click="onAdd">新增</Button>
        </template>

        <template #bodyCell="{ record, column }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :divider="false" :actions="createActions(record, column)" />
          </template>
        </template>
      </BasicTable>
    </div>
    <Modal @register="registerModal" @success="tableIns.reload" />
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }
  }
</style>
