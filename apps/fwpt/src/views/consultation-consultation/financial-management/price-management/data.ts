import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { queryExpertGroup } from '/@/api/consultation/price-management';

/**
 * 职称
 * 机构
 * 备注
 * 定价（元）
 */
export const columns: BasicColumn[] = [
  {
    title: '专家组',
    dataIndex: 'expertGroupName',
    width: 200,
    align: 'left',
  },
  {
    title: '机构',
    dataIndex: 'orgName',
    width: 200,
    align: 'left',
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
    align: 'left',
  },
  {
    title: '定价（元）',
    dataIndex: 'price',
    width: 200,
    align: 'left',
  },
];

/**
 * 机构
 * 科室
 * 职称
 * 定价（元）
 * 备注
 */
export const editFormSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '',
    field: 'orgId',
    component: 'Input',
    show: false,
  },
  {
    field: 'orgName',
    component: 'Input',
    label: '机构',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
    componentProps: {
      disabled: true,
    },
  },
  {
    label: '',
    field: 'expertGroupName',
    component: 'Input',
    show: false,
  },
  {
    label: '专家组',
    field: 'expertGroupId',
    component: 'ApiSelect',
    required: true,
    componentProps: ({ formModel }) => {
      return {
        api: queryExpertGroup,
        labelField: 'groupName',
        valueField: 'id',
        getPopupContainer() {
          return document.body;
        },
        onChange(_, opt) {
          if (opt?.label) formModel.expertGroupName = opt?.label;
        },
      };
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '定价（元）',
    field: 'price',
    component: 'InputNumber',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
    rules: [
      {
        message: '超出字符限制，请控制在1-100个字符之间',
        trigger: 'change',
        max: 100,
      },
    ],
  },
];
