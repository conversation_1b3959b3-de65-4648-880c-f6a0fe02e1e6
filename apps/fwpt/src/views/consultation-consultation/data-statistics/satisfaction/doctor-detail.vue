<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { nextTick, onMounted } from 'vue';
  import { columns1, searchFormSchema } from './data';
  import { getConsultationStatisticsMarkDetail } from '/@/api/consultation/data-statistics';

  const consultOrgId = useRouteQuery('consultOrgId', '', { transform: String });
  const expertGroupId = useRouteQuery('expertGroupId', '', { transform: String });
  const expertGroupName = useRouteQuery('expertGroupName', '', { transform: String });
  const startDate = useRouteQuery('startDate', '', { transform: String });
  const endDate = useRouteQuery('endDate', '', { transform: String });
  onMounted(() => {
    const values: Recordable = {
      consultOrgId: consultOrgId.value,
      expertGroupId: expertGroupId.value,
      expertGroupName: expertGroupName.value,
    };
    if (startDate.value && endDate.value) {
      values.statisticsDate = [startDate.value, endDate.value];
    }
    tableIns.getForm().setFieldsValue(values);
    nextTick(() => {
      tableIns.getForm().submit();
    });
  });
  const [registerTable, tableIns] = useTable({
    api: getConsultationStatisticsMarkDetail,
    columns: columns1,
    showIndexColumn: false,
    useSearchForm: true,
    immediate: false,
    formConfig: {
      labelWidth: 100,
      showActionButtonGroup: false,
      showAdvancedButton: false,
      schemas: searchFormSchema,
      disabled: true,
      fieldMapToTime: [['statisticsDate', ['startDate', 'endDate'], 'YYYY-MM-DD']],
    },
  });
</script>

<template>
  <div class="h-full pr-4 pb-4">
    <div class="h-full">
      <BasicTable @register="registerTable">
        <template #headerTop>
          <span class="text-base font-bold">满意度评分统计</span>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }
  }
</style>
