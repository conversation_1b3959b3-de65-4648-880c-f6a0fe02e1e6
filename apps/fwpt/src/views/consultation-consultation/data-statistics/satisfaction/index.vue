<script setup lang="ts">
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { Button } from '@ft/internal/components/Button';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import { columns, searchFormSchema } from './data';
  import {
    consultationStatisticsMarkExport,
    getConsultationStatisticsMarkCount,
  } from '/@/api/consultation/data-statistics';

  const [registerTable, tableIns] = useTable({
    api: getConsultationStatisticsMarkCount,
    columns,
    showIndexColumn: false,
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas: searchFormSchema,
      fieldMapToTime: [['statisticsDate', ['startDate', 'endDate'], 'YYYY-MM-DD']],
    },
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
  });
  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(
    consultationStatisticsMarkExport,
    {
      manual: true,
      // showSuccessMessage: true,
    },
  );
  function onExportExpert() {
    exportUtil(exportExpertRunAsync(tableIns.getForm().getFieldsValue()));
  }
  function createActions(record: Recordable): ActionItem[] {
    return [
      {
        label: '医生详情',
        type: 'link',
        onClick: onDetail.bind(null, record),
      },
    ];
  }
  const go = useGo();
  function onDetail(record) {
    console.log(record);
    go({
      name: 'DataStatisticsSatisfactionDoctorDetail',
      query: {
        ...tableIns.getForm().getFieldsValue(),
        expertGroupId: record.expertGroupId,
        expertGroupName: record.expertGroupName,
      },
    });
  }
</script>

<template>
  <div class="h-full pr-4 pb-4">
    <div class="h-full">
      <BasicTable @register="registerTable">
        <template #headerTop>
          <span class="text-base font-bold">满意度评分统计</span>
        </template>
        <template #toolbar>
          <Button :loading="exportLoading" @click="onExportExpert"> 导出 </Button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :divider="false" :actions="createActions(record)" />
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }
  }
</style>
