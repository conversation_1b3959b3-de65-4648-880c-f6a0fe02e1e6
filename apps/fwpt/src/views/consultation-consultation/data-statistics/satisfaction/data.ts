import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { queryOrganizationList } from '@ft/internal/api';
import { useUserStoreWithOut } from '@ft/internal/store/modules/user';

const { userInfo } = useUserStoreWithOut();

/**
 * 专家组
 * 统计日期
 */
export const searchFormSchema: FormSchema[] = [
  {
    field: 'expertGroupId',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'expertGroupName',
    label: '专家组',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'statisticsDate',
    label: '统计日期',
    component: 'RangePicker',
    componentProps: {
      style: { width: '100%' },
      format: 'YYYY-MM-DD',
    },
    colProps: { span: 6 },
  },
  {
    field: 'consultOrgId',
    label: '医疗机构',
    component: 'ApiSelect',
    componentProps: {
      api: () => queryOrganizationList({ orgType: 1, enableFlag: 0 }),
      labelField: 'orgName',
      valueField: 'id',
      showSearch: true,
      optionFilterProp: 'label',
    },
    defaultValue: userInfo?.orgId,
    colProps: { span: 6 },
  },
];

/**
 * 专家组
 * 快速咨询平均得分（分）
 * 护理咨询平均得分（分）
 * 在线问诊平均得分（分）
 */
export const columns: BasicColumn[] = [
  {
    title: '专家组',
    dataIndex: 'expertGroupName',
  },
  {
    title: '快速咨询平均得分（分）',
    dataIndex: 'quickMark',
  },
  {
    title: '护理咨询平均得分（分）',
    dataIndex: 'nursingMark',
  },
  {
    title: '在线问诊平均得分（分）',
    dataIndex: 'onlineMark',
  },
];

export const columns1: BasicColumn[] = [
  {
    title: '医生',
    dataIndex: 'expertName',
  },
  {
    title: '快速咨询平均得分（分）',
    dataIndex: 'quickMark',
  },
  {
    title: '护理咨询平均得分（分）',
    dataIndex: 'nursingMark',
  },
  {
    title: '在线问诊平均得分（分）',
    dataIndex: 'onlineMark',
  },
];
