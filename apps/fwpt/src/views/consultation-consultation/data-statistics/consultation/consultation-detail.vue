<script setup lang="ts">
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { nextTick, onMounted } from 'vue';
  import { columns2, searchFormSchema } from './data';
  import { getConsultationStatisticsConsultationPageOfDoctor } from '/@/api/consultation/data-statistics';

  const userId = useRouteQuery('userId', '', { transform: String });
  const expertName = useRouteQuery('expertName', '', { transform: String });
  const consultOrgId = useRouteQuery('consultOrgId', '', { transform: String });
  const expertGroupId = useRouteQuery('expertGroupId', '', { transform: String });
  const expertGroupName = useRouteQuery('expertGroupName', '', { transform: String });
  const startDate = useRouteQuery('startDate', '', { transform: String });
  const endDate = useRouteQuery('endDate', '', { transform: String });
  onMounted(() => {
    const values: Recordable = {
      consultOrgId: consultOrgId.value,
      expertGroupId: expertGroupId.value,
      expertGroupName: expertGroupName.value,
      userId: userId.value,
      expertName: expertName.value,
    };
    if (startDate.value && endDate.value) {
      values.statisticsDate = [startDate.value, endDate.value];
    }
    ActionTable.getForm().setFieldsValue(values);
    nextTick(() => {
      ActionTable.getForm().submit();
    });
  });
  const [register, ActionTable] = useTable({
    api: getConsultationStatisticsConsultationPageOfDoctor,
    columns: columns2,
    showIndexColumn: true,
    bordered: false,
    size: 'small',
    resizeHeightOffset: 20,
    actionColumn: {
      title: '操作',
      dataIndex: 'action',
      width: 100,
    },
    immediate: false,
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      showActionButtonGroup: false,
      showAdvancedButton: false,
      schemas: searchFormSchema,
      disabled: true,
      actionColOptions: {
        span: 6,
        offset: 6,
        style: {
          textAlign: 'right',
        },
      },
      fieldMapToTime: [['statisticsDate', ['startDate', 'endDate'], 'YYYY-MM-DD']],
    },
  });
  function createActions(record, _column): ActionItem[] {
    const actions: ActionItem[] = [];
    actions.push({
      label: '查看详情',
      onClick: onDetail.bind(null, record),
    });
    return actions;
  }
  const go = useGo();
  function onDetail(record) {
    go({
      name: 'DataStatisticsConsultationDoctorDetailConsultationDetailRecordDetail',
      query: {
        orderId: record.orderId,
        consultId: record.consultId,
      },
    });
  }
</script>
<template>
  <div class="w-full h-full pr-4">
    <BasicTable class="ft-main-table" @register="register">
      <template #headerTop>
        <span class="text-base font-bold">咨询问诊接诊列表</span>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>

<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }
  }
</style>
