import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { useUserStoreWithOut } from '@ft/internal/store/modules/user';
import { queryOrganizationList } from '@ft/internal/api';
/**
 * 咨询专家组
 * 统计日期
 */
const { userInfo } = useUserStoreWithOut();
export const searchFormSchema: FormSchema[] = [
  {
    field: 'expertGroupId',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'expertGroupName',
    label: '咨询专家组',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'statisticsDate',
    label: '统计日期',
    component: 'RangePicker',
    componentProps: {
      style: { width: '100%' },
      format: 'YYYY-MM-DD',
    },
    colProps: { span: 6 },
  },
  {
    field: 'consultOrgId',
    label: '医疗机构',
    component: 'ApiSelect',
    componentProps: {
      api: () => queryOrganizationList({ orgType: 1, enableFlag: 0 }),
      labelField: 'orgName',
      valueField: 'id',
      showSearch: true,
      optionFilterProp: 'label',
    },
    defaultValue: userInfo?.orgId,
    colProps: { span: 6 },
  },
  {
    field: 'userId',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'expertName',
    label: '',
    component: 'Input',
    show: false,
  },
];

/**
 * 咨询专家组
 * 快速咨询
 * 护理咨询
 * 在线问诊
 */
export const columns: BasicColumn[] = [
  {
    title: '咨询专家组',
    dataIndex: 'expertGroupName',
  },
  {
    title: '快速咨询',
    dataIndex: 'quickNum',
  },
  {
    title: '护理咨询',
    dataIndex: 'nursingNum',
  },
  {
    title: '在线问诊',
    dataIndex: 'onlineNum',
  },
];

export const columns1: BasicColumn[] = [
  {
    title: '咨询专家',
    dataIndex: 'expertName',
  },
  {
    title: '快速咨询',
    dataIndex: 'quickNum',
  },
  {
    title: '护理咨询',
    dataIndex: 'nursingNum',
  },
  {
    title: '在线问诊',
    dataIndex: 'onlineNum',
  },
];

/**
 * 咨询时间
 * 订单编码
 * 订单时间
 * 患者姓名
 * 性别
 * 年龄
 * 身份证号
 * 咨询机构
 * 咨询专家组
 * 订单状态
 */
export const columns2: BasicColumn[] = [
  {
    title: '咨询时间',
    dataIndex: 'consultTime',
  },
  // {
  //   title: '订单编码',
  //   dataIndex: 'orderCode',
  // },
  // {
  //   title: '订单时间',
  //   dataIndex: 'orderTime',
  // },
  {
    title: '咨询编号',
    dataIndex: 'consultId',
  },
  {
    title: '患者姓名',
    dataIndex: 'patientName',
  },
  {
    title: '性别',
    dataIndex: 'patientSex',
  },
  {
    title: '年龄',
    dataIndex: 'patientAge',
  },
  {
    title: '身份证号',
    dataIndex: 'idCardNo',
  },
  {
    title: '咨询机构',
    dataIndex: 'consultOrgName',
  },
  {
    title: '咨询专家组',
    dataIndex: 'consultExpertGroupName',
  },
  {
    title: '咨询状态',
    dataIndex: 'consultStatus',
  },
];
