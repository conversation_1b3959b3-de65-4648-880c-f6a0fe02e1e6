<script setup lang="ts">
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { watch } from 'vue';
  import { Button } from '@ft/internal/components/Button';
  import { useModal } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { columns } from './data';
  import AddModal from './AddModal.vue';
  import { deleteCommonExpression, getCommonExpressionPage } from '/@/api/term-mg';
  const props = defineProps({
    activeId: {
      type: String,
      default: '1',
    },
  });
  const [registerTable, { reload }] = useTable({
    api: getCommonExpressionPage,
    inset: true,
    columns: columns,
    useSearchForm: false,
    showIndexColumn: true,
    bordered: false,
    size: 'small',
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
    beforeFetch: (params) => {
      return params;
    },
    rowKey: 'id',
    afterFetch: (data) => {
      return data;
    },
  });

  function createActions(record): ActionItem[] {
    const actions: ActionItem[] = [];
    actions.push(
      {
        label: '编辑',
        type: 'link',
        size: 'small',
        onClick: handleEdit.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: handleDel.bind(null, record),
          okButtonProps: {
            danger: true,
          },
        },
      },
    );
    return actions;
  }
  const [register, { openModal }] = useModal();
  function handleEdit(record) {
    openModal(true, {
      mode: 'edit',
      ...record,
    });
  }

  function handleSuccess() {
    reload();
  }

  const { runAsync: delRunAsync } = useRequest(deleteCommonExpression, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      reload();
    },
  });
  function handleDel(record) {
    delRunAsync(record.id);
  }

  function handleAdd() {
    openModal(true, {
      mode: 'add',
    });
  }

  watch(
    () => props.activeId,
    (val) => {
      val && reload();
    },
  );
</script>

<template>
  <div class="flex flex-col gap-0 flex-col gap-2 p-3 rounded bg-#fff">
    <BasicTable @register="registerTable">
      <template #headerTop>
        <div class="text-#333333 fw-bold mb-3">常用语模板</div>
      </template>
      <template #tableTitle>
        <Button type="primary" @click="handleAdd"> 新增常用语 </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <AddModal @register="register" @success="handleSuccess" />
  </div>
</template>

<style scoped lang="less"></style>
