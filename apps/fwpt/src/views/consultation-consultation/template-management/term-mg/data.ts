import type { BasicColumn } from '@ft/internal/components/Table';
import type { FormSchema } from '@ft/internal/components/Form';
import { DictEnum, getDictItemList } from '@ft/internal/api';

/**
回复内容
排序
模板类型
创建人
创建时间
 */
export const columns: BasicColumn[] = [
  {
    title: '回复内容',
    dataIndex: 'replyContent',
    align: 'left',
    width: 100,
  },
  {
    title: '排序',
    dataIndex: 'sort',
    align: 'left',
    width: 100,
  },
  {
    title: '模板类型',
    dataIndex: 'ownerTypeName',
    align: 'left',
    width: 100,
  },
  {
    title: '创建人',
    dataIndex: 'createUser',
    align: 'left',
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    align: 'left',
    width: 100,
  },
];

/**
 * 回复内容
 * 排序
 * 模板类型
 */
export const addModal: FormSchema[] = [
  {
    field: 'id',
    component: 'Input',
    label: 'id',
    show: false,
  },
  {
    field: 'replyContent',
    component: 'InputTextArea',
    label: '回复内容',
    required: true,
    colProps: { span: 22 },
    componentProps: {
      rows: 4,
    },
  },
  {
    field: 'ownerType',
    component: 'ApiSelect',
    label: '模板类型',
    required: true,
    colProps: { span: 22 },
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.TEMPLATE_OWNER_TYPE),
        getPopupContainer: () => document.body,
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        valueToString: formModel?.ownerType ? true : false,
      };
    },
  },
  {
    field: 'sort',
    component: 'InputNumber',
    label: '排序',
    required: true,
    colProps: { span: 22 },
    componentProps: {
      style: { width: '100%' },
      precision: 0,
    },
  },
];
