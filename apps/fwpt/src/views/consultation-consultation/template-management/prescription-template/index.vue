<script setup lang="ts">
  import type { ActionItem, TableRowSelection } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { computed, ref, watch } from 'vue';
  import { Button } from '@ft/internal/components/Button';
  import { useModal } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { columns, columnsTop } from './data';
  import AddModal from './AddModal.vue';
  import AddTemplateModal from './AddTemplateModal.vue';
  import {
    deletePresTemplate,
    deleteTemplateDrug,
    getPresTemplateDrugInfo,
    getPresTemplatePage,
  } from '/@/api/prescription-template';

  const props = defineProps({
    activeId: {
      type: String,
      default: '',
    },
  });
  const [registerTopTable, { reload: reloadTopTable }] = useTable({
    inset: true,
    api: getPresTemplatePage,
    fetchSetting: {
      pageField: 'pageNum',
      sizeField: 'pageSize',
      listField: 'list',
    },
    scroll: { y: 200 },
    columns: columnsTop,
    useSearchForm: false,
    showIndexColumn: true,
    bordered: false,
    size: 'small',
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
    rowKey: 'id',
    afterFetch: (data) => {
      selectedRowKeys.value = [data[0]?.id || ''];
      return data;
    },
  });

  const selectedRowKeys = ref<TableRowSelection['selectedRowKeys']>([]);
  const id = computed(() => (selectedRowKeys.value?.[0] || '') as string);
  const rowSelection = computed<TableRowSelection>(() => {
    return {
      type: 'radio',
      selectedRowKeys: selectedRowKeys.value,
      onChange: (keys: any, _) => {
        if (keys.length === 0) {
          return;
        }
        selectedRowKeys.value = keys;
      },
    };
  });
  function createActionsTop(record): ActionItem[] {
    const actions: ActionItem[] = [];
    actions.push(
      {
        label: '编辑',
        type: 'link',
        size: 'small',
        onClick: handleEditTop.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: handleDelTop.bind(null, record),
          okButtonProps: {
            danger: true,
          },
        },
      },
    );
    return actions;
  }
  function handleEditTop(record) {
    openTopModal(true, {
      mode: 'edit',
      ...record,
      tid: id.value,
    });
  }
  function handleTopAdd() {
    openTopModal(true, {
      mode: 'add',
    });
  }
  const { runAsync: delTopRunAsync } = useRequest(deletePresTemplate, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      reloadTopTable();
    },
  });
  function handleDelTop(record) {
    delTopRunAsync(record.id);
  }
  const [registerBottomTable, { reload: reloadBottomTable }] = useTable({
    api: getPresTemplateDrugInfo,
    inset: true,
    columns: columns,
    fetchSetting: {
      pageField: 'pageNum',
      sizeField: 'pageSize',
      listField: 'list',
    },
    useSearchForm: false,
    showIndexColumn: true,
    bordered: false,
    immediate: false,
    size: 'small',
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
    beforeFetch: (params) => {
      params.id = id.value;
      return params;
    },
  });

  function createActions(record): ActionItem[] {
    const actions: ActionItem[] = [];
    actions.push(
      {
        label: '编辑',
        type: 'link',
        size: 'small',
        onClick: handleEdit.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: handleDel.bind(null, record),
          okButtonProps: {
            danger: true,
          },
        },
      },
    );
    return actions;
  }
  const [register, { openModal }] = useModal();
  const [registerTopAdd, { openModal: openTopModal }] = useModal();

  function handleEdit(record) {
    openModal(true, {
      mode: 'edit',
      ...record,
      tid: id.value,
    });
  }

  const { runAsync: delRunAsync } = useRequest(deleteTemplateDrug, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      reloadBottomTable();
    },
  });
  function handleDel(record) {
    console.log(record);
    delRunAsync(record.id);
  }

  function handleAdd() {
    openModal(true, {
      mode: 'add',
      tid: id.value,
    });
  }

  watch(
    () => props.activeId,
    (val) => {
      val && reloadTopTable();
    },
  );
  watch(
    () => id.value,
    (val) => {
      val && reloadBottomTable();
    },
  );
</script>

<template>
  <div class="flex flex-col gap-2 p-3 rounded bg-#fff">
    <div class="flex flex-col gap-0">
      <BasicTable :row-selection="rowSelection" @register="registerTopTable">
        <template #headerTop>
          <div class="text-#333333 fw-bold mb-3">处方模板</div>
        </template>
        <template #tableTitle>
          <Button type="primary" @click="handleTopAdd"> 新增病例模板 </Button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :divider="false" :actions="createActionsTop(record)" />
          </template>
        </template>
      </BasicTable>
    </div>
    <BasicTable class="flex-1" @register="registerBottomTable">
      <template #headerTop>
        <div class="text-#333333 fw-bold mb-3">处方详情</div>
      </template>
      <template #tableTitle>
        <Button type="primary" @click="handleAdd"> 新增药品 </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record)" />
        </template>
        <template v-if="column.dataIndex === 'dosage'">
          {{ record[column.dataIndex] }}({{ record.dosageUnit }})
        </template>
        <template v-if="column.dataIndex === 'frequency'">
          {{ record[column.dataIndex] }}(次/每日)
        </template>
      </template>
    </BasicTable>
    <AddTemplateModal @register="registerTopAdd" @success="reloadTopTable" />
    <AddModal @register="register" @success="reloadBottomTable" />
  </div>
</template>

<style scoped lang="less"></style>
