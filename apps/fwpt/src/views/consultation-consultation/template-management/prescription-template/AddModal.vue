<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { ApiSelect } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { Col, Form, Input, InputNumber, Row } from 'ant-design-vue';
  import { Button } from '@ft/internal/components/Button';
  import { editTemplateDrug, getBatchAddTemplateDrug } from '/@/api/prescription-template';
  import { DictEnum, getDictItemList } from '@ft/internal/api';
  import { getDrugInfoList } from '/@/api/drug-management/dict';

  const emit = defineEmits(['register', 'success']);
  const mode = ref<'add' | 'edit'>('add');
  const title = computed(() => {
    return mode.value === 'edit' ? '编辑药品' : '新增药品';
  });
  const formState = ref({
    tid: '',
    drugList: [
      {
        drugName: '',
        dosage: '',
        frequency: '',
        drugUsage: '',
        usageId: '',
        drugCode: '',
        specs: '',
        dosageUnit: '',
      },
    ],
  });
  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data?.mode;
    formState.value.tid = data.tid;
    if (mode.value === 'edit') {
      formState.value.drugList = data ? [data] : [];
    } else {
      formState.value.drugList = [
        {
          drugName: '',
          dosage: '',
          frequency: '',
          drugUsage: '',
          usageId: '',
          drugCode: '',
          specs: '',
          dosageUnit: '',
        },
      ];
    }
  });
  const { runAsync: saveRunAsync } = useRequest(
    () =>
      mode.value === 'add'
        ? getBatchAddTemplateDrug(formState.value)
        : editTemplateDrug(formState.value.drugList[0]),
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess: () => {
        closeModal();
        emit('success');
      },
    },
  );
  async function handleOk() {
    saveRunAsync();
  }

  function onAddFormItem() {
    formState.value.drugList.push({
      drugName: '',
      dosage: '',
      frequency: '',
      drugUsage: '',
      usageId: '',
      drugCode: '',
      specs: '',
      dosageUnit: '',
    });
  }
  function getUsedList() {
    return getDictItemList(DictEnum.DRUG_USAGE);
  }
  async function _getDrugList() {
    const drugList = await getDrugInfoList();
    return drugList.map((item) => ({ ...item, drugName: `${item.drugName}(${item.specs})` }));
  }
  function onDrugChange(opt, item) {
    item.drugName = opt?.label;
    item.specs = opt?.specs;
    item.dosageUnit = opt?.mpqUnit;
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="title"
    :can-fullscreen="false"
    :ok-button-props="{}"
    width="1000px"
    @register="register"
    @ok="handleOk"
    centered
  >
    <div class="flex-1 basis-0 min-h-0 of-y-auto">
      <Form ref="formRef" :model="formState" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <Form.Item v-show="false" label="id" name="id">
          <Input disabled placeholder="请输入" v-model:value="formState.tid" />
        </Form.Item>
        <Row>
          <Col :span="24">
            <div class="h-450px of-y-auto">
              <div
                v-for="(item, idx) in formState.drugList"
                :key="idx"
                class="border-dashed border-1px border-[#DCDFE6] pt-5 mb-2 rounded"
              >
                <Row>
                  <Col :span="12">
                    <Form.Item label="药品名称" :name="['drugList', idx, 'drugCode']" required>
                      <ApiSelect
                        :api="_getDrugList"
                        v-model:value="item.drugCode"
                        labelField="drugName"
                        valueField="drugCode"
                        @change="(_, opt) => onDrugChange(opt, item)"
                        showSearch
                        optionFilterProp="label"
                        placeholder="请选择"
                      />
                    </Form.Item>
                  </Col>
                  <Col :span="12">
                    <Form.Item label="剂量" :name="['drugList', idx, 'dosage']" required>
                      <div class="flex gap-2 items-center">
                        <Input v-model:value="item.dosage" placeholder="请输入" />
                        <span class="whitespace-nowrap">{{ item.dosageUnit }}</span>
                      </div>
                    </Form.Item>
                  </Col>
                  <Col :span="12">
                    <Form.Item label="频次" :name="['drugList', idx, 'frequency']" required>
                      <div class="flex gap-2 items-center">
                        <InputNumber :min="0" v-model:value="item.frequency" placeholder="请输入" />
                        <span class="whitespace-nowrap">次/每日</span>
                      </div>
                    </Form.Item>
                  </Col>
                  <Col :span="12">
                    <Form.Item label="用法" :name="['drugList', idx, 'usageId']" required>
                      <ApiSelect
                        :api="getUsedList"
                        label-field="dictItemName"
                        value-field="dictItemId"
                        v-model:value="item.usageId"
                        placeholder="请选择"
                        @change="(_, opt) => (item.drugUsage = opt?.label)"
                      />
                    </Form.Item>
                  </Col>
                  <Col
                    :span="4"
                    class="text-right px-10"
                    v-if="formState.drugList.length === idx + 1 && mode !== 'edit'"
                  >
                    <Button
                      class="!px-0 !text-primary-color w-100px"
                      type="text"
                      pre-icon="ant-design:plus-circle-outlined"
                      @click="onAddFormItem"
                      block
                    >
                      添加
                    </Button>
                  </Col>
                  <Col
                    :span="4"
                    class="text-right px-10"
                    v-if="formState.drugList.length > 1 && mode !== 'edit'"
                  >
                    <Button
                      class="ml-2 text-info-text-color hover:text-auxiliary-red cursor-pointer"
                      danger
                      type="text"
                      pre-icon="ant-design:delete-outlined"
                      @click="formState.drugList.splice(idx, 1)"
                      block
                    >
                      删除
                    </Button>
                  </Col>
                </Row>
              </div>
            </div>
          </Col>
        </Row>
      </Form>
    </div>
  </BasicModal>
</template>
