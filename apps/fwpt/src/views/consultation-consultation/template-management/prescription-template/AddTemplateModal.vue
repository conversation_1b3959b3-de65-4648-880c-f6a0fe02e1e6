<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { addTemplateModal } from './data';
  import { saveOrUpdatePresTemplate } from '/@/api/prescription-template';

  const emit = defineEmits(['register', 'success']);
  const [registerForm, formAction] = useForm({
    schemas: addTemplateModal,
    labelWidth: 120,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });
  const mode = ref<'add' | 'edit'>('add');
  const title = computed(() => {
    return mode.value === 'edit' ? '编辑模板' : '新增模板';
  });
  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data?.mode;
    if (mode.value === 'edit') {
      formAction.setFieldsValue(data);
    }
  });
  const { loading, runAsync: drugSaveRunAsync } = useRequest(saveOrUpdatePresTemplate, {
    showSuccessMessage: true,
    manual: true,
    onSuccess: () => {
      closeModal();
      emit('success');
    },
  });
  async function handleOk() {
    formAction.validate().then((values) => {
      drugSaveRunAsync(values);
    });
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="title"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    @register="register"
    @ok="handleOk"
    centered
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
