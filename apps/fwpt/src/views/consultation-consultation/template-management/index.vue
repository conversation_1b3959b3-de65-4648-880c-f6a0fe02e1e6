<script setup lang="ts">
  import { ref } from 'vue';
  import { StyledList } from '@ft/components';
  import { DictEnum, getDictItemList } from '@ft/internal/api';
  import { useRequest } from '@ft/request';
  import MedicalTemplate from './medical-template/index.vue';
  import PrescriptionTemplate from './prescription-template/index.vue';
  import TermMg from './term-mg/index.vue';

  const activeDictItemCode = ref<string>();
  const activeItem = ref({});

  const { loading, data: items } = useRequest(() => getDictItemList(DictEnum.TEMPLATE_TYPE), {
    onSuccess: (res) => {
      activeDictItemCode.value = res.length > 0 ? res[0].dictItemCode : '';
    },
  });
</script>

<template>
  <div class="flex gap-2 h-[calc(100%-10px)] w-[calc(100%-16px)]">
    <div class="flex flex-col gap-2 w-228px bg-#fff rounded rounded-lt-none p-3">
      <span class="text-#333333 fw-bold">模板类型</span>
      <div class="pr-4 of-y-auto of-x-hidden min-w-0">
        <StyledList
          :items="items"
          v-model="activeDictItemCode"
          v-model:value="activeItem"
          valueField="dictItemCode"
          label-field="dictItemName"
          class="flex-1"
          :width="216"
          :loading="loading"
        />
      </div>
    </div>
    <PrescriptionTemplate v-if="activeDictItemCode === '1'" :activeItem="activeItem" />
    <MedicalTemplate v-if="activeDictItemCode === '2'" :activeItem="activeItem" />
    <TermMg v-if="activeDictItemCode === '3'" :activeItem="activeItem" />
  </div>
</template>
<style lang="less" scoped></style>
