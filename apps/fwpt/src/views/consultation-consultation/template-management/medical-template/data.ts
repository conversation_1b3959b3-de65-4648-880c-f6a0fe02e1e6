import type { BasicColumn } from '@ft/internal/components/Table';
import type { FormSchema } from '@ft/internal/components/Form';
import { DictEnum, getDictItemList, getInfectiousDiagnosisList } from '@ft/internal/api';

/**
模板名称
应用诊断
模板类型
创建人
创建时间
 */
export const columns: BasicColumn[] = [
  {
    title: '模板名称',
    dataIndex: 'tname',
    align: 'left',
    width: 100,
  },
  {
    title: '应用诊断',
    dataIndex: 'applyDiagnosticName',
    align: 'left',
    width: 100,
  },
  {
    title: '模板类型',
    dataIndex: 'ownerTypeName',
    align: 'left',
    width: 100,
  },
  {
    title: '创建人',
    dataIndex: 'createUser',
    align: 'left',
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    align: 'left',
    width: 100,
  },
];

/**
 * 回复内容
 * 排序
 * 模板类型
 */
export const addModal: FormSchema[] = [
  {
    field: 'id',
    component: 'Input',
    label: 'id',
    show: false,
  },
  {
    field: 'tname',
    component: 'Input',
    label: '模板名称',
    required: true,
    colProps: { span: 22 },
    componentProps: {
      rows: 4,
    },
  },
  {
    field: 'applyDiagnosticName',
    component: 'Input',
    label: '模板类型',
    show: false,
  },
  {
    field: 'applyDiagnosticCode',
    component: 'ApiSelect',
    label: '应用诊断',
    required: true,
    colProps: { span: 22 },
    componentProps: ({ formModel }) => {
      return {
        api: () => getInfectiousDiagnosisList(),
        getPopupContainer: () => document.body,
        labelField: 'diagnosticName',
        valueField: 'diagnosticCode',
        showSearch: true,
        optionFilterProp: 'label',
        onChange(_, opt) {
          if (opt?.label) formModel.applyDiagnosticName = opt?.label;
        },
      };
    },
  },
  {
    field: 'ownerType',
    component: 'ApiSelect',
    label: '模板类型',
    required: true,
    colProps: { span: 22 },
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.TEMPLATE_OWNER_TYPE),
        getPopupContainer: () => document.body,
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        valueToString: formModel?.ownerType ? true : false,
      };
    },
  },
  {
    field: 'content',
    component: 'InputTextArea',
    label: '诊疗建议',
    required: true,
    colProps: { span: 22 },
    componentProps: {
      rows: 4,
    },
  },
];
