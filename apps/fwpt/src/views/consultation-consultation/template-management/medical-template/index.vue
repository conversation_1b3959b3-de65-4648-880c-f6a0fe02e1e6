<script setup lang="ts">
  import type { ActionItem, TableRowSelection } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { computed, ref, watch } from 'vue';
  import { Textarea } from 'ant-design-vue';
  import { Button } from '@ft/internal/components/Button';
  import { useModal } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { columns } from './data';
  import AddModal from './AddModal.vue';
  import { deleteEmrTemplate, getEmrTemplatePage } from '/@/api/medical-template';
  const props = defineProps({
    activeId: {
      type: String,
      default: '',
    },
  });
  const [registerTable, { reload }] = useTable({
    inset: true,
    api: getEmrTemplatePage,
    fetchSetting: {
      pageField: 'pageNum',
      sizeField: 'pageSize',
      listField: 'list',
    },
    scroll: { y: '90vh' },
    columns: columns,
    useSearchForm: false,
    showIndexColumn: true,
    bordered: false,
    size: 'small',
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
    beforeFetch: (params) => {
      return params;
    },
    rowKey: 'content',
    afterFetch: (data) => {
      selectedRowKeys.value = [data[0]?.content || ''];
      return data;
    },
  });

  function createActions(record): ActionItem[] {
    const actions: ActionItem[] = [];
    actions.push(
      {
        label: '编辑',
        type: 'link',
        size: 'small',
        onClick: handleEdit.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: handleDel.bind(null, record),
          okButtonProps: {
            danger: true,
          },
        },
      },
    );
    return actions;
  }

  const [register, { openModal }] = useModal();
  function handleEdit(record) {
    openModal(true, {
      mode: 'edit',
      ...record,
    });
  }

  const { runAsync: delRunAsync } = useRequest(deleteEmrTemplate, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      reload();
    },
  });
  function handleDel(record) {
    delRunAsync(record.id);
  }

  function handleAdd() {
    openModal(true, {
      mode: 'add',
    });
  }

  watch(
    () => props.activeId,
    (val) => {
      val && reload();
    },
  );
  const selectedRowKeys = ref<TableRowSelection['selectedRowKeys']>([]);
  const rowSelection = computed<TableRowSelection>(() => {
    return {
      type: 'radio',
      selectedRowKeys: selectedRowKeys.value,
      onChange: (keys: any, _) => {
        if (keys.length === 0) {
          return;
        }
        selectedRowKeys.value = keys;
      },
    };
  });
  const caseDetails = computed(() => (selectedRowKeys.value?.[0] || '') as string);
</script>

<template>
  <div class="bg-#fff rounded flex flex-col gap-2 p-3">
    <BasicTable class="flex-1" :row-selection="rowSelection" @register="registerTable">
      <template #headerTop>
        <div class="text-#333333 fw-bold mb-3">病历模板</div>
      </template>
      <template #tableTitle>
        <Button type="primary" @click="handleAdd"> 新增病例模板 </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <div class="b-white rounded-md">
      <div class="text-#333333 fw-bold mb-3">诊疗建议</div>
      <Textarea
        v-model:value="caseDetails"
        disabled
        placeholder="请输入诊疗建议"
        :auto-size="{ minRows: 5, maxRows: 10 }"
      />
    </div>
    <AddModal @register="register" @success="reload" />
  </div>
</template>

<style scoped lang="less"></style>
