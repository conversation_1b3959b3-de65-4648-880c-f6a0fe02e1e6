import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { DictEnum, getDictItemList } from '@ft/internal/api';
/**
 * 评价等级
 * 状态
 */
export const searchFormSchema: FormSchema[] = [
  {
    field: 'evaluationLevel',
    label: '评价等级',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.EVALUATION_LEVEL),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
    },
    colProps: { span: 6 },
  },
  {
    field: 'enableStatus',
    label: '状态',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.ENABLE_STATUS),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
    },
    colProps: { span: 6 },
  },
];

/**
 * 评价等级
 * 满意程度
 * 满意度评分
 * 标签1
 * 标签2
 * 标签3
 * 标签4
 * 标签5
 * 标签6
 * 状态
 */
export const columns: BasicColumn[] = [
  {
    title: '评价等级',
    dataIndex: 'evaluationLevel',
    width: 110,
    align: 'left',
  },
  {
    title: '满意程度',
    dataIndex: 'satisfactionLevel',
    width: 110,
    align: 'left',
  },
  {
    title: '满意度评分',
    dataIndex: 'satisfactionScore',
    width: 110,
    align: 'left',
  },
  {
    title: '标签1',
    dataIndex: 'tag1',
    width: 110,
    align: 'left',
  },
  {
    title: '标签2',
    dataIndex: 'tag2',
    width: 110,
    align: 'left',
  },
  {
    title: '标签3',
    dataIndex: 'tag3',
    width: 110,
    align: 'left',
  },
  {
    title: '标签4',
    dataIndex: 'tag4',
    width: 110,
    align: 'left',
  },
  {
    title: '标签5',
    dataIndex: 'tag5',
    width: 110,
    align: 'left',
  },
  {
    title: '标签6',
    dataIndex: 'tag6',
    width: 110,
    align: 'left',
  },
  {
    title: '状态',
    dataIndex: 'enableStatus',
    width: 110,
    align: 'left',
  },
];

/**
 * 评价等级
 * 满意程度
 * 满意度评分
 * 标签1
 * 标签2
 * 标签3
 * 标签4
 * 标签5
 * 标签6
 * 状态
 */
export const editFormSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'evaluationLevel',
    label: '评价等级',
    component: 'ApiSelect',
    required: true,
    componentProps: {
      api: () => getDictItemList(DictEnum.EVALUATION_LEVEL),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    field: 'satisfactionLevel',
    label: '满意程度',
    component: 'ApiSelect',
    required: true,
    componentProps: {
      api: () => getDictItemList(DictEnum.SATISFACTION_LEVEL),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '满意度评分',
    field: 'satisfactionScore',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '标签1',
    field: 'tag1',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '标签2',
    field: 'tag2',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '标签3',
    field: 'tag3',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '标签4',
    field: 'tag4',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '标签5',
    field: 'tag5',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '标签6',
    field: 'tag6',
    component: 'Input',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    label: '栏目状态',
    field: 'enableStatus',
    component: 'ApiSelect',
    required: true,
    componentProps: {
      api: () => getDictItemList(DictEnum.ENABLE_STATUS),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 16 } },
  },
];
