<script setup lang="ts">
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { useModal } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { columns, searchFormSchema } from './data';
  import EditModal from './EditModal.vue';
  import {
    deleteConsultationEvaluateTemplate,
    getConsultationEvaluateTemplatePage,
  } from '/@/api/consultation/evaluate-management';

  const [registerTable, tableIns] = useTable({
    api: getConsultationEvaluateTemplatePage,
    columns,
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas: searchFormSchema,
    },
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
    resizeHeightOffset: 10,
  });

  const [register, { openModal }] = useModal();

  function onAdd() {
    openModal(true, {
      mode: 'add',
    });
  }

  function onEdit(record) {
    openModal(true, {
      mode: 'edit',
      record,
    });
  }
  const { runAsync: deleteEdcRunAsync } = useRequest(deleteConsultationEvaluateTemplate, {
    manual: true,
    showSuccessMessage: true,
    onSuccess() {
      tableIns.reload();
    },
  });
  function onRemove(record) {
    deleteEdcRunAsync(record.id);
  }

  function createActions(record: Recordable): ActionItem[] {
    return [
      {
        label: '编辑',
        type: 'link',
        onClick: onEdit.bind(null, record),
      },
      {
        label: '删除',
        type: 'link',
        danger: true,
        popConfirm: {
          title: '确定删除吗？',
          placement: 'topRight',
          okButtonProps: { danger: true },
          confirm: onRemove.bind(null, record),
        },
      },
    ];
  }
</script>

<template>
  <div class="h-full pr-4 pb-4">
    <div class="h-full">
      <BasicTable @register="registerTable">
        <template #headerTop>
          <span class="text-base font-bold">评价模版列表</span>
        </template>
        <template #tableTitle>
          <Button class="mt-4" type="primary" @click="onAdd">新增</Button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :divider="false" :actions="createActions(record)" />
          </template>
        </template>
      </BasicTable>
    </div>
    <EditModal @register="register" @success="tableIns.reload" />
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }
  }
</style>
