<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { computed, ref } from 'vue';
  import { useRequest } from '@ft/request';
  import { editFormSchema } from './data';
  import {
    addConsultationEvaluateTemplate,
    getConsultationEvaluateTemplateDetail,
    updateConsultationEvaluateTemplate,
  } from '/@/api/consultation/evaluate-management';

  const emit = defineEmits(['register', 'success']);

  const mode = ref('add');
  const getTitle = computed(() => (mode.value === 'add' ? '新增评价' : '编辑评价'));

  const [registerForm, formAction] = useForm({
    labelWidth: 100,
    schemas: editFormSchema,
    colon: true,
    showActionButtonGroup: false,
  });
  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data.mode;
    if (!data.record) return;
    runAsyncGetDetail(data.record.id);
  });
  const { runAsync: runAsyncGetDetail } = useRequest(getConsultationEvaluateTemplateDetail, {
    manual: true,
    onSuccess(res) {
      formAction.setFieldsValue(res);
    },
  });
  const { loading, runAsync: runAsyncSave } = useRequest(
    () =>
      mode.value === 'edit'
        ? updateConsultationEvaluateTemplate(formAction.getFieldsValue())
        : addConsultationEvaluateTemplate(formAction.getFieldsValue()),
    {
      manual: true,
      showSuccessMessage: true,
      async onBefore() {
        try {
          await formAction.validate();
          return true;
        } catch (e) {
          return false;
        }
      },
      onSuccess() {
        emit('success');
        closeModal();
      },
    },
  );
</script>

<template>
  <BasicModal
    @register="register"
    :destroy-on-close="true"
    :can-fullscreen="false"
    v-bind="$attrs"
    width="50%"
    centered
    :title="getTitle"
    :ok-button-props="{
      loading,
    }"
    @ok="runAsyncSave"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<style scoped></style>
