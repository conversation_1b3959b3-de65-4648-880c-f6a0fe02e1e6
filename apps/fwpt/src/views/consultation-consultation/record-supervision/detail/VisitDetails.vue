<script setup lang="ts">
  import { Tabs } from 'ant-design-vue';
  import { ref } from 'vue';
  import PatientConsultation from './component/PatientConsultation.vue';
  import Communication from './component/Communication.vue';
  import CaseInfo from './component/CaseInfo.vue';
  import Prescription from './component/Prescription.vue';
  defineOptions({
    components: {
      PatientConsultation,
      Communication,
      CaseInfo,
      Prescription,
    },
  });

  const componentsMap = [
    {
      tab: '患者咨询信息',
      key: 'PatientConsultation',
    },
    {
      tab: '沟通信息',
      key: 'Communication',
    },
    {
      tab: '病历信息',
      key: 'CaseInfo',
    },
    {
      tab: '处方信息',
      key: 'Prescription',
    },
  ];

  const props = defineProps({
    consultId: {
      type: String,
      default: '',
    },
  });
  const visitDetailsKey = ref('PatientConsultation');
</script>

<template>
  <div class="flex-1 of-hidden flex flex-col">
    <Tabs v-model:activeKey="visitDetailsKey">
      <Tabs.TabPane v-for="item in componentsMap" :key="item.key" :tab="item.tab" />
    </Tabs>
    <component :is="visitDetailsKey" :consultId="props.consultId" />
  </div>
</template>
