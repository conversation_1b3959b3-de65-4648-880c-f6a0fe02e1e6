<script setup lang="ts">
  import { getConsultationOrderDetail } from '/@/api/consultation/record-supervision';
  import { useRequest } from '@ft/request';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import VisitDetails from './VisitDetails.vue';

  const consultId = useRouteQuery('consultId', '', { transform: String });
  const { data: orderInfo } = useRequest(() => getConsultationOrderDetail(consultId.value));
</script>
<template>
  <div class="w-full h-full pr-4">
    <div ref="VisitDetailsRef" class="flex gap-10px flex-col min-w-0 h-full">
      <div class="rounded-2 bg-#fff p-4">
        <div class="text-16px font-bold mb-4">订单基本信息</div>
        <div class="flex flex-col gap-4 py-4 px-6">
          <div class="flex">
            <div class="flex-1">
              <span class="text-#999">患者姓名：</span>
              <span class="text-#333">{{ orderInfo?.patientName || '-' }}</span>
            </div>
            <div class="flex-1">
              <span class="text-#999">年龄：</span>
              <span class="text-#333">{{ orderInfo?.patientAge || '-' }}</span>
            </div>
            <div class="flex-1">
              <span class="text-#999">性别：</span>
              <span class="text-#333">{{ orderInfo?.patientSex || '-' }}</span>
            </div>
            <div class="flex-1">
              <span class="text-#999">身份证号：</span>
              <span class="text-#333">{{ orderInfo?.idCardNo || '-' }}</span>
            </div>
            <div class="flex-1">
              <span class="text-#999">咨询时间：</span>
              <span class="text-#333">{{ orderInfo?.consultTime || '-' }}</span>
            </div>
            <div class="flex-1">
              <span class="text-#999">咨询编号：</span>
              <span class="text-#333">{{ orderInfo?.consultId || '-' }}</span>
            </div>
          </div>
          <div class="flex">
            <div class="flex-1">
              <span class="text-#999">咨询机构：</span>
              <span class="text-#333">{{ orderInfo?.consultOrgName || '-' }}</span>
            </div>
            <div class="flex-1">
              <span class="text-#999">{{
                orderInfo?.consultType === 3 ? '咨询专家：' : '咨询专家组：'
              }}</span>
              <span class="text-#333">{{
                orderInfo?.consultType === 3
                  ? orderInfo?.consultExpertName || '-'
                  : orderInfo?.consultExpertGroupName || '-'
              }}</span>
            </div>
            <div class="flex-1">
              <span class="text-#999">咨询状态：</span>
              <span class="text-#333">{{ orderInfo?.consultStatus || '-' }}</span>
            </div>
            <div class="flex-1">
              <span class="text-#999">订单金额：</span>
              <span class="text-#333">{{ orderInfo?.orderMoney || '-' }}</span>
            </div>
            <div class="flex-1"> </div>
            <div class="flex-1"> </div>
          </div>
        </div>
      </div>
      <div class="flex-1 bg-#fff rounded-2 p-4 flex flex-col gap-4">
        <div class="text-16px font-bold">咨询问诊详情</div>
        <VisitDetails :consultId="consultId" />
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped></style>
