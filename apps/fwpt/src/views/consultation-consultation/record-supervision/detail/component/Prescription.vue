<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { getElectronicPrescriptionList } from '/@/api/consultation/record-supervision';
  import { computed } from 'vue';
  const props = defineProps({
    consultId: {
      type: String,
      default: '',
    },
  });
  const [registerTable] = useTable({
    api: () => getElectronicPrescriptionList(props.consultId),
    columns: [
      {
        title: '处方项目',
        dataIndex: 'itemName',
      },
      {
        title: '用量',
        dataIndex: 'dosage',
      },
      {
        title: '规格',
        dataIndex: 'specifications',
      },
      {
        title: '用法',
        dataIndex: 'usage',
      },
      {
        title: '频次',
        dataIndex: 'frequency',
      },
      {
        title: '天数',
        dataIndex: 'days',
      },
      {
        title: '总量',
        dataIndex: 'total',
      },
    ],
    useSearchForm: false,
    showIndexColumn: true,
    immediate: computed(() => !!props.consultId),
  });
</script>

<template>
  <div class="w-full h-full">
    <BasicTable @register="registerTable" />
  </div>
</template>

<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 0;
    }
  }
</style>
