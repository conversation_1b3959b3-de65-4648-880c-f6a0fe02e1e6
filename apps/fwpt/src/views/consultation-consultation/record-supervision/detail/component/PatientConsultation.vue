<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { useRequest } from '@ft/request';
  import { schemas } from './PatientConsultation.data';
  import { getConsultationInfoDetail } from '/@/api/consultation/record-supervision';
  const props = defineProps({
    consultId: {
      type: String,
      default: '',
    },
  });

  const { data } = useRequest(() => getConsultationInfoDetail(props.consultId), {
    onSuccess: () => {
      formAction.setFieldsValue(data.value);
      formAction.setProps({
        disabled: true,
      });
    },
  });
  const [register, formAction] = useForm({
    labelWidth: 110,
    colon: true,
    showActionButtonGroup: false,
    schemas,
    // disabled: true,
  });
</script>

<template>
  <div class="w-full h-full flex flex-col">
    <div class="basis-0 of-y-auto flex-1" v-if="consultId">
      <BasicForm @register="register">
        <template #illnessComplaint>
          <div class="flex items-center gap-2">
            <span class="inline-block bg-primary-color w-3px h-1em"></span>
            <span>病情主诉</span>
          </div>
        </template>
        <template #presentHistory>
          <div class="flex items-center gap-2">
            <span class="inline-block bg-primary-color w-3px h-1em"></span>
            <span>现病史</span>
          </div>
        </template>
        <template #history>
          <div class="flex items-center gap-2">
            <span class="inline-block bg-primary-color w-3px h-1em"></span>
            <span>既往史</span>
          </div>
        </template>
        <template #allergyHistory>
          <div class="flex items-center gap-2">
            <span class="inline-block bg-primary-color w-3px h-1em"></span>
            <span>过敏史</span>
          </div>
        </template>
        <template #physicalExamination>
          <div class="flex items-center gap-2">
            <span class="inline-block bg-primary-color w-3px h-1em"></span>
            <span>体格检查</span>
          </div>
        </template>
      </BasicForm>
    </div>
  </div>
</template>

<style lang="less" scoped></style>
