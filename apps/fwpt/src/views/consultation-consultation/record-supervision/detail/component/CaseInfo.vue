<script setup lang="ts">
  import { Input } from 'ant-design-vue';
  import { ref } from 'vue';
  import { getConsultationInfoDetail } from '/@/api/consultation/record-supervision';
  import { useRequest } from '@ft/request';
  const props = defineProps({
    consultId: {
      type: String,
      default: '',
    },
  });

  useRequest(() => getConsultationInfoDetail(props.consultId), {
    onSuccess: (res) => {
      content.value = res.documentContent;
    },
  });
  const InputTextarea = Input.TextArea;
  const content = ref('');
</script>

<template>
  <div class="w-full h-full">
    <InputTextarea
      class="!w-6/10"
      :auto-size="{ minRows: 10, maxRows: 10 }"
      v-model:value="content"
      disabled
    />
  </div>
</template>

<style lang="less" scoped></style>
