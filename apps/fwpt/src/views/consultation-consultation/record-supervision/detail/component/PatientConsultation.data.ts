import type { FormSchema } from '@ft/internal/components/Form/index';

/**
 * @description: 患者咨询信息
 * 病情主诉
 * 现病史
 * 起病时间
 * 发布诱因
 * 既往史
 * 慢病
 * 其他
 * 过敏史
 */

export const schemas: FormSchema[] = [
  {
    field: 'illnessComplaint',
    component: 'Input',
    label: '',
    slot: 'illnessComplaint',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
    disabledLabelWidth: true,
  },
  {
    field: 'chiefComplaint',
    component: 'InputTextArea',
    label: '病情主诉',
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'chiefComplaintImgList',
    label: '',
    component: 'UploadAsset',
    componentProps: {
      uploadType: 'image',
      resultField: 'data.url',
      // modelNameField: 'fileName',
      maxCount: 9,
      // action: EDU_DOWNLOAD_URL,
    },
    colProps: {
      span: 18,
    },
    itemProps: {
      wrapperCol: {
        span: 14,
      },
    },
  },
  {
    field: 'presentHistory',
    component: 'InputTextArea',
    label: '',
    slot: 'presentHistory',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
    disabledLabelWidth: true,
  },
  {
    field: 'onsetTime',
    component: 'InputTextArea',
    label: '起病时间',
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'predisposingFactors',
    component: 'InputTextArea',
    label: '发作诱因',
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'history',
    component: 'InputTextArea',
    label: '',
    slot: 'history',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
    disabledLabelWidth: true,
  },
  {
    field: 'chronicDisease',
    component: 'InputTextArea',
    label: '慢病',
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'pastHistory',
    component: 'InputTextArea',
    label: '其他',
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'allergyHistory',
    component: 'InputTextArea',
    label: '',
    slot: 'allergyHistory',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
    disabledLabelWidth: true,
  },
  {
    field: 'allergyHistory',
    component: 'InputTextArea',
    label: '过敏史',
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'physicalExamination',
    component: 'InputTextArea',
    label: '',
    slot: 'physicalExamination',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
    disabledLabelWidth: true,
  },
  {
    field: 'temperature',
    component: 'Input',
    label: '体温(°C)',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 4 } },
  },
  {
    field: 'pulse',
    component: 'Input',
    label: '脉搏(次/分钟)',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 4 } },
  },
  {
    field: 'breathe',
    component: 'Input',
    label: '呼吸(次/分钟)',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 4 } },
  },
  {
    field: 'systolicPressure',
    component: 'Input',
    label: '收缩压(mmHg)',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 4 } },
  },
  {
    field: 'diastolicPressure',
    component: 'Input',
    label: '舒张压(mmHg)',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 4 } },
  },
];
