<script setup lang="ts">
  import { getConsultationChatRecordList } from '/@/api/consultation/record-supervision';
  import { useRequest } from '@ft/request';
  import patientUrl from '/@/assets/icons/patient-avatar.svg';
  import doctorUrl from '/@/assets/icons/doctor-avatar.svg';

  const props = defineProps({
    consultId: {
      type: String,
      default: '',
    },
  });
  const titleMap = {
    diagnosis: '线上诊断',
    document: '诊疗建议',
    prescription: '电子处方',
    information: '病情资料',
  };
  const { data: chatList } = useRequest(() => getConsultationChatRecordList(props.consultId));
</script>

<template>
  <div class="w-full h-full flex flex-col">
    <div class="of-y-auto basis-0 flex-1 pr-[50%] pb-5">
      <div v-for="(item, index) in chatList" :key="index">
        <div class="flex justify-start gap-3 mt-4" v-if="item.fromUserType === 2">
          <div><img :src="item.fromUserAvatar || patientUrl" alt="" width="32" height="32" /></div>
          <div class="bg-[#eee] px-3 py-2 rounded-1">
            <div v-if="item.msgType === 1">{{ item.msgContent }}</div>
            <div v-if="item.msgType === 2"
              ><img :src="item.msgContent" alt="" class="w-[200px] h-[200px]"
            /></div>
            <audio v-if="item.msgType === 3" controls :src="item.msgContent"></audio>
            <video
              v-if="item.msgType === 4"
              style="
                max-width: 100%;
                max-height: 100%;
                width: 300px;
                height: auto;
                border-radius: 10px;
              "
              loop
              controls
              :src="item.msgContent"
            ></video>
            <div v-if="item.msgType === 5"
              >[{{ titleMap[JSON.parse(item.msgContent).type] }}]:{{
                JSON.parse(item.msgContent).texts
              }}</div
            >
          </div>
        </div>
        <div v-else class="flex justify-end gap-3 mt-4">
          <div class="bg-[#33bc71] px-3 py-2 text-white rounded-1">
            <div v-if="item.msgType === 1">{{ item.msgContent }}</div>
            <div v-if="item.msgType === 2"
              ><img :src="item.msgContent" alt="" class="w-[200px] h-[200px]"
            /></div>
            <audio v-if="item.msgType === 3" controls :src="item.msgContent"></audio>
            <video
              v-if="item.msgType === 4"
              style="
                max-width: 100%;
                max-height: 100%;
                width: 300px;
                height: auto;
                border-radius: 10px;
              "
              loop
              controls
              :src="item.msgContent"
            ></video>
            <div v-if="item.msgType === 5"
              >[{{ titleMap[JSON.parse(item.msgContent).type] }}]:{{
                JSON.parse(item.msgContent).texts
              }}</div
            >
          </div>
          <div><img :src="item.fromUserAvatar || doctorUrl" alt="" width="32" height="32" /></div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped></style>
