<script setup lang="ts">
  import { But<PERSON>, Tabs } from 'ant-design-vue';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { exportUtil } from '@ft/internal/utils';
  import { useRequest } from '@ft/request';
  import { computed, ref, watch } from 'vue';
  import { columns, formSchema } from './data';
  import {
    consultationInfoExport,
    getConsultationInfoPage,
  } from '/@/api/consultation/record-supervision';

  const go = useGo();
  const activeTab = ref<number>(3);
  watch(activeTab, () => {
    tableAction.reload();
  });
  const [registerTable, tableAction] = useTable({
    api: getConsultationInfoPage,
    columns: computed(() => columns(activeTab.value)),
    formConfig: {
      labelWidth: 100,
      showAdvancedButton: false,
      actionColOptions: {
        span: 12,
        style: {
          textAlign: 'right',
        },
      },
      schemas: computed(() => formSchema(activeTab.value)),
    },
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
    beforeFetch(params) {
      params.consultType = activeTab.value;
      return params;
    },
    useSearchForm: true,
    showIndexColumn: true,
  });

  function onDetail(record: any) {
    go({
      name: 'RecordSupervisionDetail',
      query: {
        // orderId: record.orderId,
        consultId: record.consultId,
      },
    });
  }

  function createActions(record, _column): ActionItem[] {
    return [
      {
        label: '查看详情',
        type: 'link',
        onClick: onDetail.bind(null, record),
      },
    ];
  }

  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(
    consultationInfoExport,
    {
      manual: true,
      // showSuccessMessage: true,
    },
  );
  function onExportExpert() {
    exportUtil(
      exportExpertRunAsync({
        ...tableAction.getForm().getFieldsValue(),
        consultType: activeTab.value,
      }),
    );
  }
</script>
<template>
  <div class="w-full h-full pr-4">
    <BasicTable class="ft-main-table" @register="registerTable">
      <template #headerTop>
        <div class="text-16px font-bold">咨询问诊记录列表</div>
        <Tabs v-model:activeKey="activeTab">
          <Tabs.TabPane :key="3" tab="快速咨询" />
          <Tabs.TabPane :key="2" tab="护理咨询" />
          <Tabs.TabPane :key="1" tab="在线问诊" />
        </Tabs>
      </template>
      <template #toolbar>
        <Button :loading="exportLoading" @click="onExportExpert"> 批量导出 </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
