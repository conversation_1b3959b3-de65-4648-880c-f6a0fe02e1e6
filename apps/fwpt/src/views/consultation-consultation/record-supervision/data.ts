import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import {
  getConsultOrgList,
  getMonthList,
  getQuarterList,
  getYearList,
} from '/@/api/consultation/record-supervision';
export const formSchema: (activeTab: number) => FormSchema[] = (activeTab) => [
  {
    field: 'year',
    label: '统计年度',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: getYearList,
        labelField: 'text',
        valueField: 'value',
        getPopupContainer() {
          return document.body;
        },
        onChange(_, opt) {
          if (opt?.label) {
            formModel.quarter = undefined;
            formModel.month = undefined;
          }
        },
      };
    },
    colProps: { span: 6 },
  },
  {
    field: 'quarter',
    label: '统计季度',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => formModel.year && getQuarterList(formModel.year),
        labelField: 'text',
        valueField: 'value',
        getPopupContainer() {
          return document.body;
        },
        onChange(_, opt) {
          if (opt?.label) formModel.month = undefined;
        },
      };
    },
    colProps: { span: 6 },
  },
  {
    field: 'month',
    label: '统计月份',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => formModel.year && getMonthList(formModel.year, formModel.quarter),
        labelField: 'text',
        valueField: 'value',
        getPopupContainer() {
          return document.body;
        },
      };
    },
    colProps: { span: 6 },
  },
  {
    field: 'consultOrgId',
    label: '咨询机构',
    component: 'ApiSelect',
    componentProps: {
      api: getConsultOrgList,
      labelField: 'text',
      valueField: 'value',
      getPopupContainer() {
        return document.body;
      },
    },
    colProps: { span: 6 },
  },
  {
    field: 'consultExpertGroupName',
    label: '咨询专家组',
    component: 'Input',
    colProps: { span: 6 },
    show: activeTab !== 3,
  },
  {
    field: 'consultExpertName',
    label: '咨询专家',
    component: 'Input',
    colProps: { span: 6 },
    show: activeTab === 3,
  },
  {
    field: 'patientName',
    label: '患者姓名',
    component: 'Input',
    colProps: { span: 6 },
  },
];

/**
 * 咨询时间
 * 订单时间
 * 订单编码
 * 患者姓名
 * 性别
 * 年龄
 * 身份证号
 * 咨询专家组
 * 订单状态
 */

export const columns = (activeTab: number): BasicColumn[] => [
  {
    title: '咨询时间',
    dataIndex: 'consultTime',
    width: 120,
  },
  // {
  //   title: '订单编号',
  //   dataIndex: 'orderCode',
  //   width: 120,
  // },
  // {
  //   title: '订单时间',
  //   dataIndex: 'orderTime',
  //   width: 120,
  // },
  {
    title: '咨询编号',
    dataIndex: 'consultId',
    width: 120,
  },
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    width: 120,
  },
  {
    title: '性别',
    dataIndex: 'patientSex',
    width: 120,
  },
  {
    title: '年龄',
    dataIndex: 'patientAge',
    width: 120,
  },
  {
    title: '身份证号',
    dataIndex: 'idCardNo',
    width: 120,
  },
  {
    title: '咨询专家组',
    dataIndex: 'consultExpertGroupName',
    width: 120,
    ifShow: () => activeTab !== 3,
  },
  {
    title: '咨询专家',
    dataIndex: 'consultExpertName',
    width: 120,
    ifShow: () => activeTab === 3,
  },
  {
    title: '咨询状态',
    dataIndex: 'consultStatus',
    width: 120,
  },
];
