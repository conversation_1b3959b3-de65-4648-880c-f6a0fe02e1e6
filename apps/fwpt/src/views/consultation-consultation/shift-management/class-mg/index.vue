<script setup lang="ts">
  import { Button } from '@ft/internal/components/Button';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Switch } from 'ant-design-vue';
  import { useModal } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { SearchSchemas, columns } from './data';
  import AddModal from './AddModal.vue';
  import {
    deleteConsultationClassManage,
    editClassStatus,
    getClassPage,
  } from '/@/api/consultation/shift-management';
  const [registerTable, { reload }] = useTable({
    api: getClassPage,
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas: SearchSchemas,
    },
    dataSource: [{ diagnosisStatus: 1 }],
    columns,
    actionColumn: {
      title: '操作',
      dataIndex: 'action',
      width: 100,
    },
    beforeFetch: (params) => {
      return params;
    },
  });

  const [registerAdd, { openModal }] = useModal();
  function handleEdit(record) {
    openModal(true, {
      mode: 'edit',
      ...record,
    });
  }
  function handleAdd() {
    openModal(true, {
      mode: 'add',
    });
  }
  const { runAsync: delRunAsync } = useRequest(deleteConsultationClassManage, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      reload();
    },
  });

  function createActions(record, _column): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '是否确认删除',
          confirm: () => {
            delRunAsync(record.id);
          },
        },
      },
    ];
  }
  const { runAsync: updateClassTypeRun } = useRequest(editClassStatus, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      reload();
    },
  });
  function onSwitchStatus(_, record) {
    updateClassTypeRun(record.id).finally(() => {
      record.loading = false;
    });
  }
</script>

<template>
  <div class="h-[calc(100%-10px)] w-[calc(100%-16px)] rounded rounded-lt-none">
    <BasicTable @register="registerTable">
      <template #headerTop>
        <div class="text-base font-bold mb-3">医疗机构在线咨询班别管理</div>
      </template>
      <template #tableTitle>
        <Button class="mr-2" type="primary" @click="handleAdd"> 新增班别 </Button>
      </template>
      <template #toolbar>
        <!-- <Button :loading="templateLoading" @click="onDownloadTemplate"> 模板下载 </Button>
        <Upload :max-count="1" :show-upload-list="false" :custom-request="onImport">
          <Button :loading="importLoading"> 批量导入 </Button>
        </Upload>
        <Button :loading="exportLoading" @click="onExportExpert"> 批量导出 </Button> -->
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'status'">
          <Switch
            :loading="record.loading"
            :checkedValue="1"
            :unCheckedValue="0"
            v-model:checked="record[column.dataIndex]"
            @change="onSwitchStatus($event, record)"
          />
        </template>
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
    <AddModal @register="registerAdd" @success="() => reload()" />
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }

    .vben-basic-table-form-container .ant-form,
    .vben-basic-table .ant-table-wrapper {
      border-radius: 4px;
    }

    .vben-basic-table-form-container .ant-form {
      border-top-left-radius: 0;
      margin-bottom: 10px;
    }
  }
</style>
