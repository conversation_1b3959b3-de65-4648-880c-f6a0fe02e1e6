import { queryOrganizationList } from '@ft/internal/api';
import type { FormSchema } from '@ft/internal/components/Form';
import type { BasicColumn } from '@ft/internal/components/Table';
import { useUserStoreWithOut } from '@ft/internal/store/modules/user';

/**
 * 医疗机构
 * 科室
 */
const { userInfo } = useUserStoreWithOut();
export const SearchSchemas: FormSchema[] = [
  {
    field: 'orgId',
    label: '医疗机构',
    component: 'ApiSelect',
    componentProps: {
      api: () => queryOrganizationList({ orgType: 1, enableFlag: 0 }),
      labelField: 'orgName',
      valueField: 'id',
      showSearch: true,
      optionFilterProp: 'label',
    },
    defaultValue: userInfo?.orgId,
    colProps: { span: 6 },
  },
  // {
  //   field: 'orgId',
  //   label: '班别类型',
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       { label: '医生', value: '医生' },
  //       { label: '护士', value: '护士' },
  //     ],
  //   },
  //   colProps: { span: 6 },
  // },
  // {
  //   field: 'orgId',
  //   label: '专家组',
  //   component: 'ApiSelect',
  //   componentProps: {},
  //   colProps: { span: 6 },
  // },
];

/**
医疗机构
科室
班别
班别起止时间
班别状态
维护时间
维护人
 */
export const columns: BasicColumn[] = [
  {
    title: '医疗机构',
    dataIndex: 'orgName',
    width: 150,
  },
  // {
  //   title: '专组应用',
  //   dataIndex: 'classType',
  //   width: 150,
  // },
  // {
  //   title: '专家组',
  //   dataIndex: 'orgId',
  //   width: 150,
  // },
  {
    title: '班别',
    dataIndex: 'className',
    width: 150,
  },
  {
    title: '班别起止时间',
    dataIndex: 'classTime',
    width: 150,
  },
  {
    title: '班别状态',
    dataIndex: 'status',
    width: 150,
    // customRender: ({ text }) => {
    //   return text === 1 ? '启用' : '禁用';
    // },
  },
  {
    title: '维护时间',
    dataIndex: 'updateTime',
    width: 150,
  },
  {
    title: '维护人',
    dataIndex: 'updateUser',
    width: 150,
  },
];

/**
医疗机构
班别科室
班别名称
起止时间
班别状态
 */
export const formSchema: FormSchema[] = [
  {
    field: 'id',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'orgId',
    label: '',
    component: 'Input',
    defaultValue: userInfo?.orgId,
    show: false,
  },
  {
    field: 'orgName',
    label: '医疗机构',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
    componentProps: {
      disabled: true,
    },
    defaultValue: userInfo?.orgName,
    itemProps: { wrapperCol: { span: 16 } },
  },
  // {
  //   field: 'classType',
  //   label: '专组应用',
  //   component: 'Select',
  //   required: true,
  //   componentProps: {
  //     options: [
  //       { label: '医生', value: '医生' },
  //       { label: '护士', value: '护士' },
  //     ],
  //   },
  //   colProps: { span: 24 },
  //   itemProps: { wrapperCol: { span: 16 } },
  // },
  // {
  //   field: 'classDepartment',
  //   label: '专家组',
  //   component: 'Input',
  //   required: true,
  //   colProps: { span: 24 },
  //   itemProps: { wrapperCol: { span: 16 } },
  // },
  {
    field: 'className',
    label: '班别名称',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    field: 'classTime',
    label: '起止时间',
    component: 'TimePicker',
    required: true,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
    slot: 'classTime',
    // componentProps: {
    //   style: {
    //     width: '100%',
    //   },
    //   type: 'time',
    //   showHour: true,
    //   showTime: { format: 'HH:mm' },
    //   format: ' HH:mm',
    //   valueFormat: 'HH:mm',
    //   getPopupContainer: () => document.body,
    // },
  },
  {
    field: 'status',
    label: '班别状态',
    component: 'RadioGroup',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 },
      ],
    },
    defaultValue: 1,
  },
];
