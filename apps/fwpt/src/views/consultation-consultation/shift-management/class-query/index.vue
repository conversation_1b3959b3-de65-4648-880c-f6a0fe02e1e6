<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { queryOrganizationList } from '@ft/internal/api';
  import { useUserStoreWithOut } from '@ft/internal/store/modules/user';
  import { useRequest } from '@ft/request';
  import type { Dayjs } from 'dayjs';
  import dayjs from 'dayjs';
  import { computed, ref } from 'vue';
  import { getFirstDayOfWeek, getWeekDay } from '../departments-mg/helper';
  import { getConsultationExpertGroupClassList } from '/@/api/consultation/class-query';
  const { userInfo } = useUserStoreWithOut();
  //获取本周的第一天日期
  const date = ref(getFirstDayOfWeek(new Date()));
  const columns = computed(() => {
    const _columns = [
      {
        title: '专家组',
        dataIndex: 'expertGroupName',
        align: 'left',
        width: 120,
      },
      {
        title: '专组应用',
        dataIndex: 'groupTypeName',
        align: 'left',
        width: 120,
      },
    ];
    for (let i = 0; i < 7; i++) {
      const dynamicDate = dayjs(date.value).add(i, 'day');
      const week = getWeekDay(dynamicDate.format('YYYY-MM-DD'));
      _columns.push({
        title: `${week} ${dynamicDate.format('MM-DD')}`,
        dataIndex: `${dynamicDate.format('YYYY-MM-DD')}`,
        align: 'left',
        width: 160,
      });
    }
    return _columns;
  });
  const { loading, run } = useRequest(
    () =>
      getConsultationExpertGroupClassList({
        ...TableAction.getForm().getFieldsValue(),
      }),
    {
      onSuccess: (data) => {
        const result = data.map((item) => {
          const weekClassList = item.weekClassList[0];
          const dates = Object.keys(weekClassList);
          const transformedItem = { ...item };
          dates.forEach((date) => {
            const classManageNames = weekClassList[date].map(
              (classManage) => classManage.classManageName,
            );
            transformedItem[date] = classManageNames.join(',');
          });
          return transformedItem;
        });
        TableAction.setTableData(result);
      },
    },
  );

  const [registerTable, TableAction] = useTable({
    useSearchForm: true,
    loading,
    formConfig: {
      labelWidth: 100,
      schemas: [
        {
          field: 'orgId',
          label: '医疗机构',
          component: 'ApiSelect',
          componentProps: {
            api: () => queryOrganizationList({ orgType: 1, enableFlag: 0 }),
            labelField: 'orgName',
            valueField: 'id',
            showSearch: true,
            optionFilterProp: 'label',
          },
          defaultValue: userInfo?.orgId,
          colProps: { span: 6 },
        },
        {
          field: 'shiftTime',
          label: '排班日期',
          component: 'RangePicker',
          defaultValue: [
            dayjs().startOf('week').add(1, 'day'),
            dayjs().endOf('week').add(1, 'day'),
          ],
          componentProps: ({ formModel }) => {
            return {
              valueFormat: 'YYYY-MM-DD',
              format: 'YYYY-MM-DD',
              allowClear: false,
              disabledDate: (current: Dayjs) => {
                if (!formModel.shiftTime) {
                  return false;
                }
                const dates = [dayjs(formModel.shiftTime[0]), dayjs(formModel.shiftTime[1])];
                if (!dates || (dates as any).length === 0) {
                  return false;
                }
                const tooLate = dates[0] && current.diff(dates[0], 'days') > 6;
                const tooEarly = dates[1] && dates[1]?.diff(current, 'days') > 6;
                return tooEarly || tooLate;
              },
              onCalendarChange: (dates) => {
                formModel.shiftTime = dates;
              },
            };
          },
          colProps: { span: 6 },
        },
        {
          field: 'expertGroupName',
          label: '专家组',
          component: 'Input',
          colProps: { span: 6 },
        },
      ],
      submitFunc: async () => {
        date.value = getFirstDayOfWeek(new Date(TableAction.getForm().getFieldsValue().startDate));
        await run();
      },
      fieldMapToTime: [['shiftTime', ['startDate', 'endDate'], 'YYYY-MM-DD']],
    },
    pagination: false,
    showIndexColumn: true,
    //@ts-ignore
    columns: computed(() => columns.value || []),
  });
</script>

<template>
  <div class="h-[calc(100%-10px)] w-[calc(100%-16px)] rounded rounded-lt-none">
    <BasicTable @register="registerTable">
      <template #headerTop>
        <div class="text-base font-bold mb-3">排班列表</div>
      </template>
      <template #tableTitle> </template>
      <template #toolbar>
        <!-- <Button :loading="loading" @click="onSynchronizeRunAsync"> 同步 </Button> -->
      </template>
    </BasicTable>
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }

    .vben-basic-table-form-container .ant-form,
    .vben-basic-table .ant-table-wrapper {
      border-radius: 4px;
    }

    .vben-basic-table-form-container .ant-form {
      border-top-left-radius: 0;
      margin-bottom: 10px;
    }
  }
</style>
