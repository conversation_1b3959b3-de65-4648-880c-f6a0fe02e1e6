<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { Button, Select } from 'ant-design-vue';
  import { computed } from 'vue';
  import dayjs from 'dayjs';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import { useRequest } from '@ft/request';
  import { useUserStoreWithOut } from '@ft/internal/store/modules/user';
  import { getWeekDay } from './helper';
  // import { dataSource } from './data';
  import { getClassList } from '/@/api/consultation/shift-management';
  import {
    getConsultationExpertGroupClassList,
    saveOrUpdateClass,
  } from '/@/api/consultation/class-query';

  /**
   * 新增排班
   */

  const { userInfo } = useUserStoreWithOut();
  const lastDate = useRouteQuery('lastDate', '', { transform: String });
  const firstDate = useRouteQuery('firstDate', '', { transform: String });
  const mode = useRouteQuery('mode', 'add', { transform: String });
  const go = useGo();

  const columns = computed(() => {
    const _columns = [
      {
        title: '专家组',
        dataIndex: 'expertGroupName',
        align: 'center',
        width: 160,
      },
    ];
    for (let i = 0; i < 7; i++) {
      const dynamicDate = dayjs(firstDate.value).add(i, 'day');
      const week = getWeekDay(dynamicDate.format('YYYY-MM-DD'));
      _columns.push({
        title: `${week} ${dynamicDate.format('MM-DD')}`,
        dataIndex: `${dynamicDate.format('YYYY-MM-DD')}`,
        align: 'center',
        width: 160,
      });
    }
    return _columns;
  });
  const { loading } = useRequest(
    () =>
      getConsultationExpertGroupClassList({
        orgId: userInfo?.orgId,
        startDate: firstDate.value,
        endDate: dayjs(firstDate.value).add(6, 'day').format('YYYY-MM-DD'),
      }),
    {
      onSuccess: (data) => {
        const result = data.map((item) => {
          const weekClassList = item.weekClassList[0];
          const dates = Object.keys(weekClassList);
          const transformedItem = { ...item };
          dates.forEach((date) => {
            //没有排班的默认休息
            transformedItem[date] =
              weekClassList[date].length > 0
                ? weekClassList[date].map((classManage) => classManage.classManageId)
                : ['-1'];
          });
          return transformedItem;
        });
        tableAction.setTableData(result);
      },
    },
  );

  const [register, tableAction] = useTable({
    loading,
    showIndexColumn: false,
    useSearchForm: false,
    bordered: true,
    inset: true,
    immediate: false,
    pagination: false,
    //@ts-ignore
    columns: computed(() => columns.value),
  });

  const getTitle = computed(() => {
    return mode.value === 'add' ? '新增排班' : '编辑排班';
  });
  const { data: classTypeList = [] as any } = useRequest(() => getClassList(userInfo?.orgId), {
    ready: !!userInfo?.orgId,
  });

  const classTypeOptions = computed(() => {
    return (
      classTypeList.value?.map((item) => {
        return {
          label: item.className,
          value: item.id,
          disabled: item.status === 0,
        };
      }) || []
    );
  });

  const { createConfirm } = useMessage();
  const { loading: saveLoading, runAsync: saveRunAsync } = useRequest(saveOrUpdateClass, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      // emit('success');
      // closeModal();
    },
  });
  function handleSave() {
    const data = tableAction.getDataSource();
    const isValuesEmpty = data.every((obj) => {
      return Object.values(obj).every((value) => value?.length > 0);
    });
    if (!isValuesEmpty) {
      createConfirm({
        iconType: 'warning',
        title: '提示',
        content: '温馨提醒，排班有空项未排，请检查',
      });
      return;
    } else {
      const dataSource = tableAction.getDataSource();
      const result = dataSource?.reduce(
        (acc, expertGroup) => {
          const groupClasses = Object.keys(expertGroup)
            .filter((key) => key.match(/^\d{4}-\d{2}-\d{2}$/))
            .map((date) => {
              const classManageIds = expertGroup[date];
              const classManageDTOS = classManageIds.map((id) => {
                const classManageDTO = classTypeList.value.find((item) => item.id === id);
                return {
                  classManageId: id,
                  classManageName: classManageDTO?.className,
                };
              });
              return {
                classDate: date,
                classManageDTOS,
                expertGroupId: expertGroup.expertGroupId,
                expertGroupName: expertGroup.expertGroupName,
                groupTypeId: expertGroup.groupTypeId,
                groupTypeName: expertGroup.groupTypeName,
              };
            });
          acc.groupClasses.push(...groupClasses);
          return acc;
        },
        { groupClasses: [] },
      );
      saveRunAsync({
        orgId: userInfo?.orgId,
        startDate: firstDate.value,
        endDate: dayjs(firstDate.value).add(6, 'day').format('YYYY-MM-DD'),
        groupClasses: result.groupClasses,
      });
    }
  }
</script>

<template>
  <div class="flex-col flex-1 gap-2 p-4 bg-white min-w-757px rounded-2 rounded-lt-none">
    <BasicTable @register="register">
      <template #headerTop>
        <div class="flex justify-between">
          <div>{{ getTitle }}</div>
          <div>
            <Button class="mr-2" @click="go(-1)"> 返回 </Button>
            <Button type="primary" :loading="saveLoading" @click="handleSave"> 提交 </Button>
          </div>
        </div>
        <div class="flex justify-center items-center flex-col">
          <div class="text-20px text-#333">{{ userInfo?.orgName }}</div>
          <div class="flex justify-center items-center gap-4">
            <!-- <Button shape="circle" size="small" disabled @click="handleLeftDate">
              <template #icon> <LeftOutlined /> </template>
            </Button> -->
            <div>{{ firstDate }}～{{ lastDate }}</div>
            <!-- <Button shape="circle" size="small" disabled @click="handleRightDate">
              <template #icon> <RightOutlined /> </template>
            </Button> -->
          </div>
        </div>
      </template>
      <template #headerCell="{ column }">
        <div v-if="column.dataIndex === 'expertGroupName'">
          <div class="text-right expertGroupName-after">日期</div>
          <div class="text-left">专家组名称</div>
        </div>
        <div v-else>{{ column.customTitle }} </div>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.customTitle?.includes('星期')">
          <Select
            :options="classTypeOptions"
            allow-clear
            style="width: 100%"
            mode="multiple"
            v-model:value="record[column.dataIndex]"
            :defaultValue="['-1']"
            placeholder="请选择班次"
          />
        </template>
        <template v-else>{{ record[column.dataIndex] }} </template>
      </template>
      <!-- <template #footer>
        <div>备注：上午 8：00-12：00下午 14:30-15: 30</div>
      </template> -->
    </BasicTable>
  </div>
</template>

<style scoped lang="less">
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table-form-container .ant-form {
      padding: 0;
      margin-bottom: 10px;
    }
  }

  .expertGroupName-after::before {
    content: ' ';
    display: block;
    width: 200%;
    height: 1px;
    background-color: #dcdfe6;
    transform: rotate(23deg);
    position: absolute;
    left: -8%;
    top: 90%;
  }
</style>
