<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { <PERSON><PERSON>, Popconfirm } from 'ant-design-vue';
  import { computed, ref } from 'vue';
  import { LeftOutlined, RightOutlined } from '@ant-design/icons-vue';
  import dayjs from 'dayjs';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { useRequest } from '@ft/request';
  import { useUserStoreWithOut } from '@ft/internal/store/modules/user';
  import { getFirstDayOfWeek, getWeekDay } from './helper';
  import { clearClass, getConsultationExpertGroupClassList } from '/@/api/consultation/class-query';

  /**
   * 专家排班
   */
  //获取当天日期
  const day = ref(dayjs().format('YYYY-MM-DD'));
  const { userInfo } = useUserStoreWithOut();

  //获取本周的第一天日期
  const date = ref(getFirstDayOfWeek(new Date()));
  const firstDate = computed(() => {
    return dayjs(date.value).format('YYYY-MM-DD');
  });
  const lastDate = computed(() => {
    return dayjs(date.value).add(6, 'day').format('YYYY-MM-DD');
  });
  const disabled = computed(() => {
    return getFirstDayOfWeek(new Date(day.value)) > dayjs(date.value).format('YYYY-MM-DD');
  });
  const columns = computed(() => {
    const _columns = [
      {
        title: '专家组',
        dataIndex: 'expertGroupName',
        align: 'left',
        width: 160,
      },
    ];
    for (let i = 0; i < 7; i++) {
      const dynamicDate = dayjs(date.value).add(i, 'day');
      const week = getWeekDay(dynamicDate.format('YYYY-MM-DD'));
      _columns.push({
        title: `${week} ${dynamicDate.format('MM-DD')}`,
        dataIndex: `${dynamicDate.format('YYYY-MM-DD')}`,
        align: 'left',
        width: 160,
      });
    }
    return _columns;
  });
  const { loading, run } = useRequest(
    () =>
      getConsultationExpertGroupClassList({
        orgId: userInfo?.orgId,
        startDate: firstDate.value,
        endDate: dayjs(firstDate.value).add(6, 'day').format('YYYY-MM-DD'),
      }),
    {
      onSuccess: (data) => {
        const result = data.map((item) => {
          const weekClassList = item.weekClassList[0];
          const dates = Object.keys(weekClassList);
          const transformedItem = { ...item };
          dates.forEach((date) => {
            const classManageNames = weekClassList[date].map(
              (classManage) => classManage.classManageName,
            );
            transformedItem[date] = classManageNames.join(',');
          });
          return transformedItem;
        });
        TableAction.setTableData(result);
      },
    },
  );

  const [register, TableAction] = useTable({
    showIndexColumn: false,
    loading,
    useSearchForm: false,
    pagination: false,
    bordered: false,
    inset: true,
    immediate: false,
    //@ts-ignore
    columns: computed(() => columns.value),
  });

  const go = useGo();
  const { runAsync: runAsyncClearClass } = useRequest(
    () =>
      clearClass({
        endDate: lastDate.value,
        groupClasses: [],
        orgId: userInfo?.orgId,
        orgName: userInfo?.orgName,
        startDate: firstDate.value,
      }),
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess: () => {
        run();
      },
    },
  );

  function handleAdd() {
    go({
      name: 'ShiftManagementDepartmentsMgAddClass',
      query: {
        mode: 'add',
        lastDate: lastDate.value,
        firstDate: firstDate.value,
      },
    });
  }
  function handleEdit() {
    go({
      name: 'ShiftManagementDepartmentsMgAddClass',
      query: {
        mode: 'edit',
        lastDate: lastDate.value,
        firstDate: firstDate.value,
      },
    });
  }
  function handleLeftDate() {
    date.value = dayjs(date.value).subtract(7, 'day').format('YYYY-MM-DD');
    run();
  }
  function handleRightDate() {
    date.value = dayjs(date.value).add(7, 'day').format('YYYY-MM-DD');
    run();
  }
</script>

<template>
  <div class="flex-col flex-1 gap-2 p-4 bg-white min-w-757px rounded-2 rounded-lt-none">
    <BasicTable @register="register">
      <template #headerTop>
        <Button class="mr-2" type="primary" @click="handleAdd" :disabled="disabled">
          新增排班
        </Button>
        <Button @click="handleEdit" :disabled="disabled"> 修改排班 </Button>
        <div class="flex justify-center items-center flex-col">
          <div class="text-20px text-#333">{{ userInfo?.orgName }}</div>
          <div class="flex justify-center items-center gap-4">
            <Button shape="circle" size="small" @click="handleLeftDate">
              <template #icon> <LeftOutlined /> </template>
            </Button>
            <div>{{ firstDate }}～{{ lastDate }}</div>
            <Button shape="circle" size="small" @click="handleRightDate">
              <template #icon> <RightOutlined /> </template>
            </Button>
          </div>
        </div>
      </template>
      <!-- <template #tableTitle>
        <div>排班状态：待审核</div>
      </template> -->
      <template #toolbar>
        <Popconfirm title="是否清除本周排班?" @confirm="runAsyncClearClass">
          <Button type="primary" :disabled="disabled"> 清除排班 </Button>
        </Popconfirm>
      </template>
      <template #headerCell="{ column }">
        <div v-if="column.dataIndex === 'doctorName'">
          <div class="text-right doctorName-after">日期</div>
          <div class="text-left">专家组名称</div>
        </div>
        <div v-else>{{ column.customTitle }} </div>
      </template>
      <!-- <template #footer>
        <div>备注：上午 8：00-12：00下午 14:30-15: 30</div>
      </template> -->
    </BasicTable>
  </div>
</template>

<style scoped lang="less">
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table-form-container .ant-form {
      padding: 0;
      margin-bottom: 10px;
    }
  }

  .doctorName-after::before {
    content: ' ';
    display: block;
    width: 200%;
    height: 1px;
    background-color: #dcdfe6;
    transform: rotate(23deg);
    position: absolute;
    left: -8%;
    top: 90%;
  }
</style>
