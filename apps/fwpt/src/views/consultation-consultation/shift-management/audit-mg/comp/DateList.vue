<script setup lang="ts">
  // import DataSetModal from '../../StandardDataset/comp/dateModal.vue';
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { Button } from 'ant-design-vue';
  import { watch } from 'vue';
  import { BasicTitle } from '@ft/internal/components/Basic';
  import { useRequest } from '@ft/request';
  import { columns, searchSchema } from './data';
  import { delDrugInfo, getDrugInfoPage } from '/@/api/drug-management/dict';

  const props = defineProps<{
    activeItem: any;
  }>();

  const [register, ActionTable] = useTable({
    api: getDrugInfoPage,
    showIndexColumn: false,
    useSearchForm: true,
    formConfig: {
      labelWidth: 80,
      schemas: searchSchema,
    },
    bordered: true,
    inset: true,
    immediate: false,
    beforeFetch(params) {
      params.pkCatalogue = props.activeItem.id || '';
      return params;
    },
    fetchSetting: {
      sizeField: 'pageSize',
      pageField: 'pageNum',
      listField: 'list',
    },
    columns,
  });

  watch(
    () => props.activeItem,
    () => {
      ActionTable.getForm().resetFields();
    },
    {
      deep: true,
    },
  );

  const { runAsync: runDrugInfoDel } = useRequest(delDrugInfo, {
    manual: true,
    showSuccessMessage: true,
  });
  function handleAudit(record) {
    runDrugInfoDel(record.id).then(() => {
      ActionTable.reload();
    });
  }
  defineExpose({ ActionTable });
</script>

<template>
  <div class="h-full">
    <BasicTable @register="register">
      <template #headerTop>
        <BasicTitle class="mb-3" span normal>心内科护士排班表</BasicTitle>
      </template>
      <template #toolbar>
        <Button> 打印 </Button>
        <Button type="primary" @click="handleAudit"> 审核 </Button>
      </template>
      <template #bodyCell> </template>
      <template #headerCell="{ column }">
        <div v-if="column.dataIndex === 'doctorName'">
          <div class="text-right doctorName-after">日期</div>
          <div>医生姓名</div>
        </div>
        <div v-else>{{ column.customTitle }} </div>
      </template>
      <template #footer>
        <div>备注：上午 8：00-12：00下午 14:30-15: 30</div>
      </template>
    </BasicTable>
  </div>
</template>

<style scoped lang="less">
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table-form-container .ant-form {
      padding: 0;
      margin-bottom: 10px;
    }
  }

  .doctorName-after::before {
    content: ' ';
    display: block;
    width: 200%;
    height: 1px;
    background-color: #dcdfe6;
    transform: rotate(25deg);
    position: absolute;
    left: -10%;
    top: 90%;
  }
</style>
