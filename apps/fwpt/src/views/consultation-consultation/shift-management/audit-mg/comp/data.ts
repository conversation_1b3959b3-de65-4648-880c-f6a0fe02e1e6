import type { BasicColumn } from '@ft/internal/components/Table';
import type { FormSchema } from '@ft/internal/components/Form';
// import { ruleKindList } from '/@/api/rule-management';

export const columns: BasicColumn[] = [
  {
    title: '医生姓名',
    dataIndex: 'doctor<PERSON>ame',
    align: 'left',
  },
  {
    title: '星期一(10.24)',
    dataIndex: 'drugName',
    align: 'left',
  },
  {
    title: '星期二(10.25)',
    dataIndex: 'specs',
    align: 'left',
  },
  {
    title: '星期三(10.26)',
    dataIndex: 'unit',
    align: 'left',
  },
  {
    title: '星期四(10.27)',
    dataIndex: 'manufacturer',
    align: 'left',
  },
  {
    title: '星期五(10.28)',
    dataIndex: 'mpq',
    align: 'left',
  },
  {
    title: '星期六(10.29)',
    dataIndex: 'mpqUnit',
    align: 'left',
  },
  {
    title: '星期日(10.30)',
    dataIndex: 'price',
    align: 'left',
  },
  {
    title: '电话',
    dataIndex: 'tel',
    align: 'left',
  },
  {
    title: '备注',
    dataIndex: 'remarks',
    align: 'left',
  },
];
/**
 * 排班科室
 * 排班日期
 */
export const searchSchema: FormSchema[] = [
  {
    field: 'drugCode',
    label: '排班科室',
    component: 'ApiSelect',
    colProps: { span: 6 },
    // itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'drugName',
    label: '排班日期',
    component: 'RangePicker',
    colProps: { span: 6 },
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      getPopupContainer: () => document.body,
    },
    // itemProps: { wrapperCol: { span: 14 } },
  },
];
