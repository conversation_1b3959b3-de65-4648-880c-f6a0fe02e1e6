<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { Button, Checkbox, Tabs } from 'ant-design-vue';
  import { StyledList } from '@ft/components';
  import DateList from './comp/DateList.vue';

  const activeId = ref<string>('1');
  const activeItem = ref({
    id: '1',
    label: '13432421',
    checked: false,
    date: '2022-12-12',
  });
  const loading = ref(false);
  // const {
  //   loading,
  //   data: drugCatalogueList,
  //   runAsync: getDrugCatalogueListAsync,
  // } = useRequest(getDrugCatalogueList, {
  //   onSuccess: (data) => {
  //     activeId.value = data.length > 0 ? data[0].id : '';
  //     activeItem.value = data?.[0] || {};
  //   },
  // });
  const items = ref([
    {
      id: '1',
      label: '13432421',
      checked: false,
      date: '2022-12-12',
    },
    {
      id: '3',
      label: '134324',
      checked: false,
      date: '2022-12-12',
    },
  ]);
  // const items = computed(() => {
  //   return drugCatalogueList.value?.filter((item) => {
  //     return item.catalogName?.includes(searchValue.value);
  //   });
  // });
  const prescriptionStatus = ref(0);
  //批量审批
  function handleBatchApproval() {}

  function handleChange(item) {
    console.log(item);
  }
  function handleCheckBox(item) {
    item.checked = !item.checked;
    items.value = items.value.map((v) =>
      v.id === item.id ? { ...item, checked: !item.checked } : v,
    );
  }
  const selectAll = computed(() => {
    return items.value.filter((item) => item.checked === true);
  });
</script>

<template>
  <div class="flex gap-2 h-[calc(100%-10px)] w-[calc(100%-10px)] rounded rounded-lt-none">
    <div class="flex flex-col gap-3 w-228px bg-#fff rounded-[0px_8px_8px_8px] p-3">
      <span class="text-#333333 fw-bold">科室排班列表</span>
      <Tabs v-model:active-key="prescriptionStatus">
        <Tabs.TabPane :key="0" tab="待审核" />
        <Tabs.TabPane :key="1" tab="已审核" />
      </Tabs>
      <div class="-mt-3 text-right">
        <Button
          class="mr-2"
          type="primary"
          :disabled="selectAll.length === 0"
          @click="handleBatchApproval"
        >
          批量审批
        </Button>
      </div>
      <div class="min-w-0 pr-4 of-y-auto of-x-hidden">
        <StyledList
          :items="items || []"
          v-model="activeId"
          v-model:value="activeItem"
          valueField="id"
          labelField="label"
          class="flex-1"
          :width="416"
          :loading="loading"
          @change="handleChange"
        >
          <template #default="item">
            <div class="relative flex items-center justify-between w-full gap-2">
              <Checkbox v-model:checked="item.checked" @change.stop="handleCheckBox(item)" />
              <div class="overflow-hidden truncate w-70px">{{ item?.label }} </div>
              <div class="flex-1">{{ item?.date }} </div>
            </div>
          </template>
        </StyledList>
      </div>
    </div>
    <div class="flex flex-col flex-1 gap-2 p-4 bg-white min-w-757px rounded-2">
      <DateList ref="dateListRef" :activeItem="activeItem" />
    </div>
  </div>
</template>

<style lang="less" scoped></style>
