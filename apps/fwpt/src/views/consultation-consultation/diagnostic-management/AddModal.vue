<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { computed, ref } from 'vue';
  import { formSchema } from './data';
  import { addDiagnosis, editDiagnosis } from '/@/api/consultation/diagnostic-management';

  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm({
    schemas: formSchema,
    labelWidth: 120,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });
  const mode = ref('');
  const getTitle = computed(() => (mode.value === 'edit' ? '编辑诊断' : '新增诊断'));
  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data.mode;
    if (!data.record) return;
    formAction.setFieldsValue(data.record);
  });

  const { loading, runAsync: runAsyncSaveDiagnosis } = useRequest(
    () =>
      mode.value === 'edit'
        ? editDiagnosis(formAction.getFieldsValue())
        : addDiagnosis(formAction.getFieldsValue()),
    {
      manual: true,
      showSuccessMessage: true,
      async onBefore() {
        try {
          await formAction.validate();
          return true;
        } catch (e) {
          return false;
        }
      },
      onSuccess: () => {
        emit('success');
        closeModal();
      },
    },
  );
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="getTitle"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    @register="register"
    @ok="runAsyncSaveDiagnosis"
    :min-height="120"
    centered
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
