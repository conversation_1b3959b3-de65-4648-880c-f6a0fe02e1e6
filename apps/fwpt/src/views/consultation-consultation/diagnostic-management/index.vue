<script setup lang="ts">
  import { Button } from '@ft/internal/components/Button';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Switch, Upload } from 'ant-design-vue';
  import { useModal } from '@ft/internal/components/Modal';
  import type { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import {
    importInfectiousDiagnosis,
    infectiousDiagnosisExport,
    infectiousDiagnosisTemplateDownload,
  } from '@ft/internal/api';
  import { SearchSchemas, columns } from './data';
  import AddModal from './AddModal.vue';
  import {
    editDiagnosis,
    queryDiagnosisPage,
    synDiagnosis,
  } from '/@/api/consultation/diagnostic-management';
  const [registerTable, tableIns] = useTable({
    api: queryDiagnosisPage,
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas: SearchSchemas,
    },
    columns,
    actionColumn: {
      title: '操作',
      dataIndex: 'action',
      width: 100,
    },
    afterFetch: (data) => {
      data.forEach((item) => {
        item.loading = false;
      });
      return data;
    },
  });

  const [registerAdd, { openModal }] = useModal();
  function handleEdit(record) {
    openModal(true, {
      mode: 'edit',
      record,
    });
  }
  function handleAdd() {
    openModal(true, {
      mode: 'add',
    });
  }

  function createActions(record, _column): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
    ];
  }
  const { loading: importLoading, runAsync: importRunAsync } = useRequest(
    importInfectiousDiagnosis,
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess() {
        tableIns.reload();
      },
    },
  );

  function onImport(e: UploadRequestOption<any>) {
    const { file } = e;
    importRunAsync({
      // @ts-ignore
      file,
    });
  }
  const { loading: templateLoading, runAsync: runAsyncDownloadTemplate } = useRequest(
    infectiousDiagnosisTemplateDownload,
    {
      manual: true,
    },
  );
  async function onDownloadTemplate() {
    try {
      await exportUtil(runAsyncDownloadTemplate());
    } catch (error) {
      console.error(error);
    } finally {
    }
  }
  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(
    infectiousDiagnosisExport,
    {
      manual: true,
      showSuccessMessage: true,
    },
  );
  function onExportExpert() {
    exportUtil(
      exportExpertRunAsync({
        ...tableIns.getForm().getFieldsValue(),
      }),
    );
  }
  const { runAsync: editDiagnosisRunAsync } = useRequest(editDiagnosis, {
    manual: true,
    showSuccessMessage: true,
    onBefore([record]) {
      record.loading = true;
    },
    onSuccess() {
      tableIns.reload();
    },
    onFinally([record]) {
      record.loading = false;
    },
  });
  function onSwitchStatus(checked, record) {
    editDiagnosisRunAsync({
      ...record,
      status: checked,
    });
  }
  const { loading: synLoading, runAsync: runAsyncSynDiagnosis } = useRequest(synDiagnosis, {
    manual: true,
    showSuccessMessage: true,
  });
</script>

<template>
  <div class="h-[calc(100%-10px)] w-[calc(100%-16px)] rounded rounded-lt-none">
    <BasicTable @register="registerTable">
      <template #headerTop>
        <div class="text-base font-bold mb-3">诊断库列表</div>
      </template>
      <template #tableTitle>
        <Button class="mr-2" type="primary" @click="handleAdd"> 新增诊断 </Button>
        <Button :loading="synLoading" @click="runAsyncSynDiagnosis"> 同步 </Button>
      </template>
      <template #toolbar>
        <Button :loading="templateLoading" @click="onDownloadTemplate"> 模板下载 </Button>
        <Upload
          accept=".xlsx,.xls"
          :max-count="1"
          :show-upload-list="false"
          :custom-request="onImport"
        >
          <Button :loading="importLoading"> 批量导入 </Button>
        </Upload>
        <Button :loading="exportLoading" @click="onExportExpert"> 批量导出 </Button>
      </template>
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'status'">
          <Switch
            :loading="record.loading"
            :checkedValue="0"
            :unCheckedValue="1"
            v-model:checked="record[column.dataIndex]"
            @change="onSwitchStatus($event, record)"
          />
        </template>
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
    <AddModal @register="registerAdd" @success="tableIns.reload" />
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }

    .vben-basic-table-form-container .ant-form,
    .vben-basic-table .ant-table-wrapper {
      border-radius: 4px;
    }

    .vben-basic-table-form-container .ant-form {
      border-top-left-radius: 0;
      margin-bottom: 10px;
    }
  }
</style>
