import type { FormSchema } from '@ft/internal/components/Form';
import type { BasicColumn } from '@ft/internal/components/Table';
import { getInfectiousDiseaseList } from '../../../../../xtgl/src/api/infectious-diagnostic-library';

/**
 * 诊断编码
 * 诊断名称
 */
export const SearchSchemas: FormSchema[] = [
  {
    field: 'diagnosticCode',
    label: '诊断编码',
    component: 'Input',
    componentProps: {},
    colProps: { span: 6 },
  },
  {
    field: 'diagnosticName',
    label: '诊断名称',
    component: 'Input',
    componentProps: {},
    colProps: { span: 6 },
  },
];

/**
诊断编码
诊断名称
关联病种
诊断备注
中西医标识
诊断状态
 */
export const columns: BasicColumn[] = [
  {
    title: '诊断编码',
    dataIndex: 'diagnosticCode',
    width: 150,
  },
  {
    title: '诊断名称',
    dataIndex: 'diagnosticName',
    width: 150,
  },
  {
    title: '关联病种',
    dataIndex: 'infectiousDiseaseName',
    width: 150,
  },
  {
    title: '诊断类型',
    dataIndex: 'diagnosisType',
    width: 150,
    format: (text) => {
      return text ? '常用诊断' : '';
    },
  },
  {
    title: '诊断备注',
    dataIndex: 'remarks',
    width: 150,
  },
  // {
  //   title: '中西医标识',
  //   dataIndex: 'westernOrChinese',
  //   width: 150,
  // },
  {
    title: '诊断状态',
    dataIndex: 'status',
    width: 150,
  },
];

/**
诊断编码
诊断名称
关联病种
诊断备注
中西医标识
诊断状态
 */
export const formSchema: FormSchema[] = [
  {
    field: 'id',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'diagnosticCode',
    label: '诊断编码',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    field: 'diagnosticName',
    label: '诊断名称',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    field: 'diagnosisType',
    label: '诊断类型',
    component: 'Select',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
    componentProps: {
      options: [{ label: '常用诊断', value: 1 }],
    },
  },
  {
    field: 'infectiousDiseaseId',
    label: '诊断病种',
    component: 'ApiSelect',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
    required: true,
    componentProps: () => {
      return {
        api: () => getInfectiousDiseaseList(),
        getPopupContainer: () => document.body,
        labelField: 'infectiousDiseaseName',
        valueField: 'id',
        showSearch: true,
        optionFilterProp: 'label',
      };
    },
  },
  // {
  //   field: 'westernOrChinese',
  //   label: '中西医标识',
  //   component: 'Input',
  //   colProps: { span: 24 },
  //   itemProps: { wrapperCol: { span: 12 } },
  // },
  {
    field: 'remarks',
    label: '诊断备注',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    field: 'status',
    label: '诊断状态',
    component: 'Select',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 16 } },
    componentProps: {
      options: [
        { label: '启用', value: 0 },
        { label: '禁用', value: 1 },
      ],
    },
    defaultValue: 0,
  },
];
