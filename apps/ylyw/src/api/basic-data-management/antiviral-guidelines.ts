import { defHttp } from '@ft/request';

/**
 * @description: 传染病专病管理 - 基础数据 - 指导预防性服用抗病毒药物-列表查询
 * /hivGuidance/drug/queryList
 */
export interface IHivGuidanceList {
  /**用量*/
  dosage: string;
  /**药物功效*/
  efficacy: string;
  /**主键ID*/
  id: string;
  /**药物名称*/
  medicineName: string;
  /**注意事项*/
  precautions: string;
  /**用法*/
  usageMethod: string;
}
export const getHivGuidanceList = (data: any) =>
  defHttp.post<IHivGuidanceList[]>({
    url: '/hivGuidance/drug/queryList',
    data,
  });

/**
 * @description: 传染病专病管理 - 基础数据 - 指导预防性服用抗病毒药物-新增/编辑
 * /hivGuidance/drug/addOrEdit
 */
export const addOrEditHivGuidance = (data: any) =>
  defHttp.post({
    url: '/hivGuidance/drug/addOrEdit',
    data,
  });

/**
 * @description: 传染病专病管理 - 基础数据 - 指导预防性服用抗病毒药物-删除
 * /hivGuidance/drug/delete/{id}
 */
export const delHivGuidance = (id: string) =>
  defHttp.get({
    url: `/hivGuidance/drug/delete/${id}`,
  });

/**
 * @description: 传染病专病管理 - 基础数据 - 指导预防性服用抗病毒药物-导出
 * /hivGuidance/drug/export
 */
export const exportHivGuidance = (data: any) =>
  defHttp.post(
    { url: '/hivGuidance/drug/export', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );
