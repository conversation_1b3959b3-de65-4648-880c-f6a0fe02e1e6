import { defHttp } from '@ft/request';

/**
 * @description: 传染病专病管理 - 基础数据 - 感染孕产妇喂养指导-列表查询
 * /hivGuidance/feeding/queryList
 */
export interface IHivFeedingList {
  /**喂养指导*/
  feedingGuidance: string;
  /**喂养方式*/
  feedingMethod: string;
  /**主键ID*/
  id: string;
}
export const getHivFeedingList = (data: any) =>
  defHttp.post<IHivFeedingList[]>({
    url: '/hivGuidance/feeding/queryList',
    data,
  });

/**
 * @description: 传染病专病管理 - 基础数据 - 感染孕产妇喂养指导-新增/编辑
 * /hivGuidance/feeding/addOrEdit
 */
export const addOrEditFeeding = (data: any) =>
  defHttp.post({
    url: '/hivGuidance/feeding/addOrEdit',
    data,
  });

/**
 * @description: 传染病专病管理 - 基础数据 - 感染孕产妇喂养指导-删除
 * /hivGuidance/feeding/delete/{id}
 */
export const delFeeding = (id: string) =>
  defHttp.get({
    url: `/hivGuidance/feeding/delete/${id}`,
  });
/**
 * @description: 传染病专病管理 - 基础数据 - 感染孕产妇喂养指导-导出
 * /hivGuidance/feeding/export
 */
export const exportFeeding = (data: any) =>
  defHttp.post(
    { url: '/hivGuidance/feeding/export', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );
