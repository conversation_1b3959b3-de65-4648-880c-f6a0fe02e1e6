import { defHttp } from '@ft/request';

/**
 *传染病诊疗项目分类-新增
 * @returns
 */
export const addTreatmentItemClassification = (data: any) =>
  defHttp.post({
    url: '/treatmentItemClassification/add',
    data,
  });

/**
 *传染病诊疗项目分类-删除
 * @returns
 */
export const delTreatmentItemClassification = (id: string) =>
  defHttp.delete({
    url: `/treatmentItemClassification/delete/${id}`,
  });

/**
 *传染病诊疗项目分类-更新
 * @returns
 */
export const updateTreatmentItemClassification = (data: any) =>
  defHttp.post({
    url: '/treatmentItemClassification/edit',
    data,
  });

/**
 *传染病诊疗项目分类-列表
 * @returns
 */

export interface ITreatmentItemClassificationList {
  classificationName: string;
  diseaseCode: number;
  diseaseName: string;
  diseaseSubCode: number;
  id: string;
  itemType: string;
  itemTypeCode: number;
  sort: number;
}
export const getTreatmentItemClassificationList = (data: any) =>
  defHttp.post<ITreatmentItemClassificationList[]>({
    url: '/treatmentItemClassification/queryPageList',
    data,
  });

/**
 *传染病诊疗项目分类-不分页
 * @returns
 */
export const getTreatmentItemClassificationListAll = (data?: any) =>
  defHttp.post<ITreatmentItemClassificationList[]>({
    url: '/treatmentItemClassification/queryList',
    data,
  });

/**
 *传染病诊疗对照项目-新增
 * @returns
 */
export const addTreatmentItemContrast = (data: any) =>
  defHttp.post({
    url: '/treatmentItemContrast/add',
    data,
  });

/**
 *传染病诊疗对照项目-删除
 * @returns
 */
export const delTreatmentItemContrast = (id: string) =>
  defHttp.delete({
    url: `/treatmentItemContrast/delete/${id}`,
  });

/**
 *传染病诊疗对照项目-更新
 * @returns
 */
export const updateTreatmentItemContrast = (data: any) =>
  defHttp.post({
    url: '/treatmentItemContrast/edit',
    data,
  });

/**
 *传染病诊疗对照项目-列表
 * @returns
 */
export interface ITreatmentItemContrastList {
  id: string;
  itemName: string;
  parentId: string;
  remark: string;
}
export const getTreatmentItemContrastList = (data: any) =>
  defHttp.post<ITreatmentItemContrastList[]>({
    url: '/treatmentItemContrast/queryPageList',
    data,
  });

/**
 *传染病诊疗对照项目-列表(不分页)
 * @returns
 */
export const getTreatmentItemContrastListAll = (data?: any) =>
  defHttp.post<ITreatmentItemContrastList[]>({
    url: '/treatmentItemContrast/queryList',
    data,
  });

/**
 *传染病列表-根据传染病应用分类编码查询
 * @returns
 */
export interface IQueryDiseaseList {
  classificationId: string;
  id: string;
  infectiousDiseaseCode: string;
  infectiousDiseaseName: string;
  sort: number;
}
export const getQueryDiseaseList = (params?: any) =>
  defHttp.get<IQueryDiseaseList[]>({
    url: '/treatmentItemClassification/queryDiseaseList',
    params,
  });
