import { defHttp } from '@ft/request';
export interface IQueryPageList {
  actualScreeningPopulation: number;
  expectedScreeningPopulation: number;
  id: string;
  screeningDate: string;
  screeningDirector: string;
  screeningDiseaseId: number;
  screeningDiseaseName: string;
  screeningDivisionId: string;
  screeningDivisionName: string;
  screeningOrgId: string;
  screeningOrgName: string;
  screeningPlace: string;
  screeningTaskName: string;
}
/**
 * 活动日筛查任务-分页查询
 */
export const getQueryPageList = (params: any) =>
  defHttp.post<IQueryPageList[]>({
    url: '/activeDayScreeningTask/queryPageList',
    params,
  });

/**
 * 活动日筛查任务-新增
 */
export const addActiveDayScreeningTask = (data) =>
  defHttp.post({
    url: '/activeDayScreeningTask/add',
    data,
  });
/**
 * 活动日筛查任务-删除
 */
export const delActiveDayScreeningTask = (taskId: string) =>
  defHttp.delete({
    url: `/activeDayScreeningTask/delete/${taskId}`,
  });
/**
 * 活动日筛查任务-修改
 */
export const editActiveDayScreeningTask = (data) =>
  defHttp.post({
    url: '/activeDayScreeningTask/edit',
    data,
  });
/**
 * 活动日筛查任务-详情
 */
export const detailActiveDayScreeningTask = (taskId: string) =>
  defHttp.get({
    url: `/activeDayScreeningTask/queryDetail/${taskId}`,
  });
/**
 * 活动日筛查任务-导出
 */
export const activeDayScreeningTaskExport = (data) =>
  defHttp.post(
    { url: '/activeDayScreeningTask/export', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );
