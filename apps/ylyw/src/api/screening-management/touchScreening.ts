import { ContentTypeEnum } from '@ft/internal/enums/httpEnum';
import { defHttp } from '@ft/request';
import type { RcFile } from 'ant-design-vue/lib/vc-upload/interface';
export interface IQueryPageList {
  acceptFlag: number;
  address: string;
  age: number;
  createTime: string;
  createUser: string;
  ctCheckDate: string;
  ctResult: number;
  ctResultName: string;
  deleteFlag: boolean;
  divisionId: string;
  divisionName: string;
  finishFlag: number;
  id: string;
  idCard: string;
  igraDate: string;
  igraResult: number;
  igraResultName: string;
  lastScreeningDate: string;
  nextScreeningDate: string;
  objectFlag: number;
  orgId: string;
  orgName: string;
  patientSerial: number;
  phlegmCheck: string;
  phlegmCheckName: string;
  phlegmDate: string;
  phlegmResult: number;
  phlegmResultName: string;
  phone: string;
  remark: string;
  screeningCommunity: string;
  screeningDisease: number;
  screeningName: string;
  screeningResult: string;
  screeningSchool: string;
  screeningStatus: number;
  screeningStatusName: string;
  screeningTime: string;
  sex: number;
  sexName: string;
  suspiciousResult: string;
  taskId: string;
  touchPatientId: string;
  touchPatientName: string;
  touchScreeningType: number;
  tstFirstDate: string;
  tstFirstValue: string;
  tstSecondDate: string;
  tstSecondValue: string;
  updateTime: string;
  updateUser: string;
  userId: string;
  screeningResultValue: string;
}
/**
 * 筛查管理-密接筛查服务-删除
 * /touchScreening/delete
 */
export const delTouchScreening = (id: string) =>
  defHttp.post({
    url: `/touchScreening/delete?id=${id}`,
  });

/**
 * 查询密接筛查详情
 * /touchScreening/detail
 */
export const detailTouchScreening = (id: string) =>
  defHttp.get<IQueryPageList>({
    url: `/touchScreening/detail?id=${id}`,
  });

/**
 * 下载导入密接筛查模板
 * /touchScreening/downloadTemplate
 */
export const downloadTemplate = () =>
  defHttp.post(
    { url: '/touchScreening/downloadTemplate', responseType: 'blob' },
    { isReturnNativeResponse: true },
  );

/**
 * 批量导出密接筛查信息
 * /touchScreening/export
 */
export const touchScreeningExport = (data) =>
  defHttp.post(
    { url: '/touchScreening/export', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );

/**
 *分页查询肺结核密接筛查列表
 /touchScreening/query_page
 */
export const getTouchQueryPage = (params: any) =>
  defHttp.post<IQueryPageList[]>(
    {
      url: '/touchScreening/query_page',
      params,
    },
    {
      // isReturnNativeResponse: true,
      isTransformResponse: false,
    },
  );

/**
 * 肺结核社区筛查发布结果
 * /touchScreening/publishResult
 */
export const communityPublishResult = (data: any) =>
  defHttp.post({
    url: '/touchScreening/publishResult',
    data,
  });

/**
 *查询肺结核密接筛查列表
 /touchScreening/query_List
 */
export const getTouchQueryList = (params: any) =>
  defHttp.post<IQueryPageList[]>({
    url: '/touchScreening/query_List',
    params,
  });

/**
 * 新增密接筛查
 * /touchScreening/save
 */
export const saveTouchScreening = (params: any) =>
  defHttp.post({
    url: '/touchScreening/save',
    params,
  });
/**
 * 编辑密接筛查
 * /touchScreening/update
 */
export const updateTouchScreening = (params: any) =>
  defHttp.post({
    url: '/touchScreening/update',
    params,
  });

/**
 * 批量导入密接筛查登记信息
 * /touchScreening/batchImport
 */
export const touchScreeningBatchImport = (data: {
  file: RcFile;
  touchScreeningType: any;
  taskId: any;
}) => {
  const formData = new FormData();
  formData.append('file', data.file);
  formData.append('touchScreeningType', data.touchScreeningType);
  formData.append('taskId', data.taskId);

  return defHttp.post({
    url: '/touchScreening/batchImport',
    data: formData,
    headers: {
      'Content-Type': ContentTypeEnum.FORM_DATA,
    },
  });
};

/**
 * 查询筛查社区列表
 * /touchScreening/queryCommunityList
 */
export const queryCommunityList = () =>
  defHttp.get({
    url: '/touchScreening/queryCommunityList',
  });

/**
 * 查询筛查学校列表
 * /touchScreening/querySchoolList
 */
export const querySchoolList = () =>
  defHttp.get({
    url: '/touchScreening/querySchoolList',
  });

/**
 * 批量选中患者列表
 * /touchScreeningPatient/batchCheck
 */
export const getTouchScreeningPatientBatchCheck = (data) =>
  defHttp.post({
    url: '/touchScreeningPatient/batchCheck',
    data,
  });

/**
 * 查询已选择的密接筛查患者列表
 * /touchScreeningPatient/queryExistList
 */
export interface IGetTouchScreeningPatientQueryExistList {
  createTime: string;
  createUser: string;
  deleteFlag: boolean;
  id: string;
  orgId: string;
  orgName: string;
  recordNo: string;
  screeningCommunity: string;
  screeningDisease: number;
  screeningSchool: string;
  taskId: string;
  touchPatientId: string;
  touchPatientName: string;
  touchScreeningType: number;
  updateTime: string;
  updateUser: string;
  label: string;
}
export const getTouchScreeningPatientQueryExistList = (data) =>
  defHttp.post<IGetTouchScreeningPatientQueryExistList[]>({
    url: '/touchScreeningPatient/queryExistList',
    data,
  });

/**
 * 查询纳入个案未被选中的患者列表
 * /touchScreeningPatient/queryPatientProfileList
 */
export const getTouchScreeningPatientQueryPatientProfileList = (data) =>
  defHttp.post<IGetTouchScreeningPatientQueryExistList[]>({
    url: '/touchScreeningPatient/queryPatientProfileList',
    data,
  });

/**
 * 删除密接筛查患者
 * /touchScreeningPatient/delete
 */
export const delTouchScreeningPatient = (id: string) =>
  defHttp.post({
    url: '/touchScreeningPatient/delete?id=' + id,
  });

/**
 * 密接筛查-查询学校筛查任务列表
 * /activeDayScreeningTask/queryTaskList
 */
export interface IGetActiveDayScreeningTaskQueryTaskList {
  actualScreeningPopulation: number;
  expectedScreeningPopulation: number;
  id: string;
  patientList: IPatientList[];
  screeningDate: string;
  screeningDirector: string;
  screeningDiseaseId: number;
  screeningDiseaseName: string;
  screeningDivisionId: string;
  screeningDivisionName: string;
  screeningOrgId: string;
  screeningOrgName: string;
  screeningPlace: string;
  screeningTaskName: string;
  taskType: number;
  // dictItemName?: string;
}
export interface IPatientList {
  createTime: string;
  createUser: string;
  deleteFlag: boolean;
  id: string;
  orgId: string;
  orgName: string;
  recordNo: string;
  screeningCommunity: string;
  screeningDisease: number;
  screeningSchool: string;
  taskId: string;
  touchPatientId: string;
  touchPatientName: string;
  touchScreeningType: number;
  updateTime: string;
  updateUser: string;
}
export const getActiveDayScreeningTaskQueryTaskList = (data) =>
  defHttp.post<IGetActiveDayScreeningTaskQueryTaskList[]>({
    url: '/activeDayScreeningTask/queryTaskList',
    data,
  });

/**
 * 获取患者病原学结果
 * /touchScreening/getEtiologyResult
 */
export const getEtiologyResult = (data) =>
  defHttp.post({
    url: '/touchScreening/getEtiologyResult',
    data,
  });
