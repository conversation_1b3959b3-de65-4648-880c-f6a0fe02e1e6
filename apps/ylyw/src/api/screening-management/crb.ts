import { defHttp } from '@ft/request';
import { ContentTypeEnum } from '@ft/internal/enums/httpEnum';
import type { RcFile } from 'ant-design-vue/es/vc-upload/interface';
export interface IQueryPageList {
  address: string;
  age: number;
  antibodyResult: string;
  antigenResult: string;
  createTime: string;
  createUser: string;
  deleteFlag: number;
  divisionId: string;
  divisionName: string;
  doctorName: string;
  hcvAntibody: string;
  hivResult: string;
  id: string;
  idCard: string;
  name: string;
  orgId: string;
  orgName: string;
  patientSerial: number;
  phone: string;
  remark: string;
  screeningDisease: number;
  screeningDiseaseName: string;
  screeningPlace: string;
  screeningResult: string;
  screeningStatus: number;
  screeningStatusName: string;
  screeningTime: string;
  screeningType: number;
  sex: number;
  sexName: string;
  taskId: string;
  townId: string;
  townName: string;
  updateTime: string;
  updateUser: string;
  villageId: string;
  villageName: string;
}
/**
 * 分页查询筛查登记信息
 */
export const getQueryPage = (params: any) =>
  defHttp.post<IQueryPageList[]>(
    {
      url: '/diseaseScreening/query_page',
      params,
    },
    {
      // isReturnNativeResponse: true,
      isTransformResponse: false,
    },
  );

/**
 * 查询传染病筛查登记信息
 * /diseaseScreening/detail
 */
export const detailDiseaseScreening = (params: any) =>
  defHttp.get<IQueryPageList>({
    url: '/diseaseScreening/detail',
    params,
  });

/**
 * 批量导出传染病筛查登记信息
 */
export const diseaseScreeningExport = (data) =>
  defHttp.post(
    { url: '/diseaseScreening/export', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );

/**
 * 根据患者序号查询传染病筛查登记信息
 */
export const queryBySerial = (params: any) =>
  defHttp.get<IQueryPageList>({
    url: '/diseaseScreening/query_by_serial',
    params,
  });
/**
 * 新增筛查登记
 */
export const saveDiseaseScreening = (params: any) =>
  defHttp.post({
    url: '/diseaseScreening/save',
    params,
  });

/**
 * 编辑筛查登记
 */
export const updateDiseaseScreening = (params: any) =>
  defHttp.post({
    url: '/diseaseScreening/update',
    params,
  });

/**
 * 筛查登记信息-删除
 */
export const delDiseaseScreening = (id: string) =>
  defHttp.delete({
    url: `/diseaseScreening/remove/${id}`,
  });

/**
 * @description: 批量导入筛查登记信息
 */

export const batchImportImport = (data: {
  file: RcFile;
  screeningType: any;
  screeningDisease: any;
}) => {
  const formData = new FormData();
  formData.append('file', data.file);
  formData.append('screeningType', data.screeningType);
  formData.append('screeningDisease', data.screeningDisease);

  return defHttp.post({
    url: '/diseaseScreening/batchImport',
    data: formData,
    headers: {
      'Content-Type': ContentTypeEnum.FORM_DATA,
    },
  });
};

/**
 * @description: 导入模板下载
 */

export const diseaseScreeningTemplateDownload = (
  screeningType: number,
  screeningDisease: string,
) => {
  return defHttp.post(
    {
      url: `/diseaseScreening/downloadTemplate?screeningType=${screeningType}&screeningDisease=${screeningDisease}`,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 发布结果
 */
export const publishResult = (data: any) =>
  defHttp.post({
    url: '/diseaseScreening/publishResult',
    data,
  });

/**
 * 查询病毒性肝炎病种下拉列表
 * /diseaseScreening/queryDiseaseList
 */
export const queryDiseaseList = (params?: any) =>
  defHttp.get<IQueryPageList[]>({
    url: '/diseaseScreening/queryDiseaseList',
    params,
  });
