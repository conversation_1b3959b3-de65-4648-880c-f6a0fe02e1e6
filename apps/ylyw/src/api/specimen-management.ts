import { defHttp } from '@ft/request';

/**
 * 获取分页
 */
export interface ISample {
  id: string;
  applyTime: string;
  applyNo: string;
  patientName: string;
  patientNo: string;
  sexCode: number;
  sex: string;
  age: number;
  idCard: string;
  preliminaryDiagnosis: string;
  itemName: string;
  applyStatus: number;
  sampleCollectStatus: number;
  sampleCollectStatusDesc: string;
  sampleReceiveStatus: number;
  sampleReceiveStatusDesc: string;
  applyOrgCode: string;
  applyOrg: string;
  applyDeptCode: string;
  applyDept: string;
  applyDoctorId: string;
  applyDoctor: string;
  specimenCollectionType: string;
}
export const getSamplePage = (data: any) =>
  defHttp.post<ISample[]>({
    url: '/specimenManage/getApplyExamineList',
    data,
  });

/**
 * 样本采样登记
 */
export const sampleCollectRegister = (data: any) =>
  defHttp.post({
    url: '/specimenManage/sampleCollectRegister',
    data,
  });

/**
 * 样本收集登记
 */
export const sampleGatherRegister = (data: any) =>
  defHttp.post({
    url: '/specimenManage/sampleGatherRegister',
    data,
  });
/**
 * 样本采样列表
 */
export const getSampleList = (applyId: string) =>
  defHttp.post({
    url: `/specimenManage/sampleList/${applyId}`,
  });
