import { useGlobSetting } from '@ft/internal/hooks/setting';
import { defHttp } from '@ft/request';
export interface IReferralApplication {
  address: string;
  age: number;
  applicationOrgCode: string;
  applicationOrgName: string;
  applicationTime: string;
  appointmentVisitTime: string;
  areaName: string;
  communityLeader: string;
  communityTelephone: string;
  countyOrgName: string;
  createTime: string;
  createUser: string;
  diagnosisOrgName: string;
  diagnosisTime: string;
  id: string;
  idCardNo: string;
  infectionRouteDesc: string;
  patientName: string;
  patientNo: string;
  patientStatusDesc: string;
  phone: string;
  receptionTime: string;
  referralOrgCode: string;
  referralOrgName: string;
  sexName: string;
  status: number;
  treatmentStatusDesc: string;
  /**患者ID */
  pkPatientInfo: string;
}
/**
 * 获取分页列表
 */
export const getReferralApplicationPage = (params: any) =>
  defHttp.post<IReferralApplication[]>({
    url: '/referralApplication/page',
    params,
  });
/**
 * 转介接收-退回接收
 */
export const referralApplicationReceive = (params: any) =>
  defHttp.post({
    url: '/referralApplication/receive',
    params,
  });

/**
 * 转介接收-拒收
 */
export const referralApplicationRejection = (id: string) =>
  defHttp.post({
    url: `/referralApplication/rejection/${id}`,
  });
/**
 * 保存转介申请（社区端 艾滋病患者管理)
 */
export const saveReferralApplication = (params: any) =>
  defHttp.post({
    url: '/referralApplication/save',
    params,
  });
/**
 * 导出社区待接收列表
 */
export const exportCommunityReceiveList = (data) =>
  defHttp.post(
    { url: '/referralApplication/exportCommunityReceiveList', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );

/**
 * 退回清单-导出
 */
export const referralApplicationExport = (data) =>
  defHttp.post(
    { url: '/referralApplication/export', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );
/**
 * 导出社区已接收列表
 */
export const exportCommunityReceivedList = (data) =>
  defHttp.post(
    { url: '/referralApplication/exportCommunityReceivedList', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );
/**
 * 导出转介待接收列表
 */
export const exportReceiveList = (data) =>
  defHttp.post(
    { url: '/referralApplication/exportReceiveList', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );
/**
 * 导出转介已接收列表
 */
export const diseaseScreeningExport = (data) =>
  defHttp.post(
    { url: '/referralApplication/exportReceivedList', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );

/**
 * SSE-建立连接(医院端转介提醒)
 */
const { apiUrl, urlPrefix } = useGlobSetting();

export const ReferralApplicationSSEConnect = `${apiUrl}${urlPrefix}/referralApplication/sseConnect/`;

/**
 * 艾滋个案纳入-查询患者基本信息
 * /patientProfileRecord/getHivPatientBaseInfo/{pkPatientInfo}
 */
export interface IHivPatientBaseInfo {
  /**年龄*/
  age: number;
  /**患者ID*/
  id: string;
  /**患者姓名*/
  patientName: string;
  /**性别*/
  sexName: string;
  /**开始治疗时间*/
  therapyStartDate: string;
}
export const getHivPatientBaseInfo = (pkPatientInfo: string) =>
  defHttp.get<IHivPatientBaseInfo>({
    url: `/patientProfileRecord/getHivPatientBaseInfo/${pkPatientInfo}`,
  });
