import { useGlobSetting } from '@ft/internal/hooks/setting';
import { defHttp } from '@ft/request';
export interface IQueryPageList {
  actualScreeningPopulation: number;
  expectedScreeningPopulation: number;
  id: string;
  screeningDate: string;
  screeningDirector: string;
  screeningDiseaseId: number;
  screeningDiseaseName: string;
  screeningDivisionId: string;
  screeningDivisionName: string;
  screeningOrgId: string;
  screeningOrgName: string;
  screeningPlace: string;
  screeningTaskName: string;
}
export interface bringIntoManageParams {
  pkPatientInfo: string;
  recordeNo: string;
}

/**
 * 个案管理-纳入个案
 */
export const bringIntoManage = (data: any) =>
  defHttp.post({
    url: '/patientProfileRecord/bringIntoManage',
    data,
  });
/**
 * 个案管理-编辑纳入个案登记号
 * /patientProfileRecord/edit
 */
export const editBringIntoManage = (data: any) =>
  defHttp.post({
    url: '/patientProfileRecord/edit',
    data,
  });

/**
 * 个案管理-移除纳入个案
 */
export const delBringIntoManage = (patientProfileRecordId: string) =>
  defHttp.get({
    url: `/patientProfileRecord/delete/${patientProfileRecordId}`,
  });

/**
 * 个案管理-取消结案
 * /patientProfileRecord/cancelCloseCase/{patientProfileRecordId}
 */
export const cancelCloseCase = (patientProfileRecordId: string) =>
  defHttp.get({
    url: `/patientProfileRecord/cancelCloseCase/${patientProfileRecordId}`,
  });

/**
 * 个案管理-根据个案id更新个案编号
 */
export const updateRecordNo = (params) =>
  defHttp.get({
    url: '/patientProfileRecord/updateRecordNo',
    params,
  });
/**
 * 个案管理-结案
 */
export const patientProfileRecordFInish = (data: any) =>
  defHttp.post({
    url: '/patientProfileRecord/finish',
    data,
  });

/**
 * 个案管理-患者列表-批量导出
 */
export const patientProfileRecordPatientExport = (data) =>
  defHttp.post(
    { url: '/patientProfileRecord/patientExport', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );

/**
 * 个案管理-列表导出
 */
export const patientProfileRecordExport = (data) =>
  defHttp.post(
    { url: '/patientProfileRecord/export', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );

export interface IQueryPatientInfoAndTreatmentOutcomeInfo {
  address: string;
  age: number;
  areaName: string;
  birthday: string;
  certificateTypeName: string;
  diagnosticResults: string;
  ethnicity: string;
  firstTreatmentTime: string;
  householdRegistAddress: string;
  householdRegistrationName: string;
  idCardNo: string;
  inpNo: string;
  patientName: string;
  phone: string;
  pkPatientInfo: string;
  populationClassification: string;
  recordNo: string;
  recordTime: string;
  registrationOrgName: string;
  sex: string;
  sexName: string;
  therapyDiscontinuedDate: string;
  therapyDiscontinuedReason: string;
  preTreatNo: string;
  therapyStartDate: string;
  infectionName: string;
}
/**
 * 个案管理-病案记录-患者基本信息及治疗转归查询
 */
export const queryPatientInfoAndTreatmentOutcomeInfo = (params?: any) =>
  defHttp.get<IQueryPatientInfoAndTreatmentOutcomeInfo>({
    url: '/patientProfileRecord/queryPatientInfoAndTreatmentOutcomeInfo',
    params,
  });

export interface IHcvRegisterFormFill {
  alphaFetoproteinValue: string;
  alphaFetoproteinValueUpperLimit: string;
  altValue: string;
  altValueUpperLimit: string;
  astValue: string;
  astValueUpperLimit: string;
  bloodPlateletValue: string;
  idCardNo: string;
  patientName: string;
  visitOrgCode: string;
  visitOrgName: string;
}

/**
 * 个案管理 - 丙肝个案随访登记表-检验项目带入
 * /patientProfileRecord/hcvRegisterFormFill/{patientProfileRecordId}
 */
export const hcvRegisterFormFill = (patientProfileRecordId: string) =>
  defHttp.get<IHcvRegisterFormFill>({
    url: `/patientProfileRecord/hcvRegisterFormFill/${patientProfileRecordId}`,
  });

/**
 * 个案管理-个案详情-诊疗过程列表导出
 */
export const queryProfileRecordTreatmentProcessListExport = (params?: any) =>
  defHttp.get(
    {
      url: `/patientProfileRecord/queryProfileRecordTreatmentProcessListExport`,
      params,
      responseType: 'blob',
    },
    { isReturnNativeResponse: true },
  );

/**
 * 个案管理-个案详情-诊疗过程列表查询
 */
export const queryProfileRecordTreatmentProcessList = (params?: any) =>
  defHttp.get(
    {
      url: `/infection-ywxt/patientProfileRecord/queryProfileRecordTreatmentProcessList`,
      params,
    },
    { joinPrefix: false },
  );

/**
 * 个案管理-用药记录-新增
 */
export const addMedicationRecord = (data: any) =>
  defHttp.post({
    url: '/medicationRecord/add',
    data,
  });
/**
 * 个案管理-用药记录-删除
 */
export const deleteMedicationRecord = (id: string) =>
  defHttp.delete({
    url: `/medicationRecord/delete/${id}`,
  });

/**
 * 个案管理-用药记录-详情
 * /medicationRecord/queryList
 */
export interface IMedicationRecordList {
  medicationDosage: string;
  medicationEndTime: string;
  medicationFrequency: string;
  medicationFrequencyId: string;
  medicationList: IMedicationList[];
  medicationNotes: string;
  medicationStartTime: string;
}
export interface IMedicationList {
  treatmentItemClassId: string;
  treatmentItemClassName: string;
}
export const getMedicationRecordList = (data?: any) =>
  defHttp.post<IMedicationRecordList[]>({
    url: `/medicationRecord/queryList`,
    data,
  });

export interface QueryDetailParams {
  medicationEndDate: string;
  medicationStartDate: string;
  pkPatientInfo: string;
}
export interface MedicationItemList {
  itemList: any;
  medicationStartTime: string;
}
export interface QueryDetailResponse {
  medicationItemList: MedicationItemList[];
  patientTreatmentRecordId: string;
  pkPatientInfo: string;
}
/**
 * 个案管理-用药记录-详情
 */
export const detailMedicationRecord = (params: any) =>
  defHttp.get(
    {
      url: `/infection-ywxt/medicationRecord/detail`,
      params,
    },
    { joinPrefix: false },
  );

export interface IListByPatientId {
  id: string;
  medicationDosage: string;
  medicationEndTime: string;
  medicationFrequency: string;
  medicationFrequencyId: string;
  medicationId: string;
  medicationName: string;
  medicationNotes: string;
  medicationStartTime: string;
  patientProfileRecordId: string;
  pkPatientInfo: string;
}
/**
 * 个案管理-用药记录-根据患者id查询
 */
export const queryListByPatientId = (pkPatientInfo: string) =>
  defHttp.get<IListByPatientId[]>({
    url: `/medicationRecord/queryListByPatientId/${pkPatientInfo}`,
  });

/**
 * 个案管理-检查记录-新增
 */
export const addMedicalExaminationRecord = (data?: any) =>
  defHttp.post({
    url: `/medicalExaminationRecord/add`,
    data,
  });
/**
 * 个案管理-检查记录-删除
 */
export const delMedicalExaminationRecord = (id: string) =>
  defHttp.delete({
    url: `/medicalExaminationRecord/delete/${id}`,
  });

export interface DetailMedicalExaminationRecordParams {
  medicalExaminationEndDate?: string;
  medicalExaminationItemId?: string;
  medicalExaminationStartDate?: string;
  patientTreatmentRecordId: string;
  pkPatientInfo?: string;
}
export interface IDetailMedicalExaminationRecord {
  attachments: string;
  id: string;
  medicalExaminationDetail: string;
  medicalExaminationItemId: string;
  medicalExaminationItemName: string;
  medicalExaminationOrgId: string;
  medicalExaminationOrgName: string;
  medicalExaminationResult: string;
  medicalExaminationResultCode: number;
  medicalExaminationTime: string;
  patientProfileRecordId: string;
  pkPatientInfo: string;
  reportDate: string;
}

/**
 * 个案管理-检查记录-详情
 */
export const detailMedicalExaminationRecord = (data?: DetailMedicalExaminationRecordParams) =>
  defHttp.post<IDetailMedicalExaminationRecord[]>(
    {
      url: `/infection-ywxt/medicalExaminationRecord/detail`,
      data,
    },
    { joinPrefix: false },
  );

/**
 * 个案管理-诊疗记录-根据id查询诊疗记录详情-详情
 */
export const detailPatientTreatmentRecord = (data) =>
  defHttp.get({
    url: `/patientTreatmentRecord/getById/${data.patientTreatmentRecordId}`,
  });

/**
 * 个案管理-检查记录-根据患者id查询
 */
export const getMedicalExaminationRecordPatientId = (patientTreatmentRecordId?: string) =>
  defHttp.get<IDetailMedicalExaminationRecord[]>({
    url: `/medicalExaminationRecord/queryListByPatientId/${patientTreatmentRecordId}`,
  });

/**
 * 个案管理-诊疗记录-新增(返回诊疗记录id)
 */
export const addPatientTreatmentRecord = (data?: any) =>
  defHttp.post({
    url: `/patientTreatmentRecord/add`,
    data,
  });

/**
 * 个案管理-诊疗记录-整体修改
 * /patientTreatmentRecord/globalAdd
 */
export const globalAddPatientTreatmentRecord = (data?: any) =>
  defHttp.post({
    url: `/patientTreatmentRecord/globalAdd`,
    data,
  });

/**
 * 个案管理-诊疗记录-整体修改
 * /patientTreatmentRecord/globalEdit
 */
export const globalEditPatientTreatmentRecord = (data?: any) =>
  defHttp.post({
    url: `/patientTreatmentRecord/globalEdit`,
    data,
  });

/**
 * 个案管理-诊疗记录-删除
 * /patientTreatmentRecord/deleteById/{id}
 */
export const delPatientTreatmentById = (id: string) =>
  defHttp.delete({
    url: `/patientTreatmentRecord/deleteById/${id}`,
  });

/**
 * 个案管理-诊疗记录-修改(返回诊疗记录id)
 */
export const editPatientTreatmentRecord = (data?: any) =>
  defHttp.post({
    url: `/patientTreatmentRecord/edit`,
    data,
  });

const { apiUrl, urlPrefix } = useGlobSetting();

export const MEDICAL_EXAMINATION_URL = `${apiUrl}${urlPrefix}/medicalExaminationRecord/uploadAttachment`;

/**
 * 个案管理-病案记录-检查情况列表
 */
export const getProfileRecordExamList = (patientProfileRecordId: string, ids?: string[]) =>
  defHttp.post<IDetailMedicalExaminationRecord[]>(
    {
      url: `/medicalExaminationRecord/profileRecordExamList/${patientProfileRecordId}`,
      data: ids,
    },
    {
      notParmasToData: true,
    },
  );
/**
 * 个案管理-病案记录-检查情况列表 - 打印
 */
export const getProfileRecordExamListPrint = (patientProfileRecordId: string, ids?: string[]) =>
  defHttp.post<IDetailMedicalExaminationRecord[]>(
    {
      url: `/medicalExaminationRecord/profileRecordExamPrintList/${patientProfileRecordId}`,
      data: ids,
    },
    {
      notParmasToData: true,
    },
  );
/**
 * 个案管理-病案记录-检验情况列表
 */
export const getProfileRecordInspectionList = (patientProfileRecordId: string, ids?: string[]) =>
  defHttp.post<IDetailInspectionReport[]>(
    {
      url: `/inspectionReport/profileRecordInspectionList/${patientProfileRecordId}`,
      data: ids,
    },
    {
      notParmasToData: true,
    },
  );

/**
 * 个案管理-病案记录-检验情况列表 - 打印
 */
export const getProfileRecordInspectionListPrint = (
  patientProfileRecordId: string,
  ids?: string[],
) =>
  defHttp.post<IDetailInspectionReport[]>(
    {
      url: `/inspectionReport/profileRecordInspectionListPrint/${patientProfileRecordId}`,
      data: ids,
    },
    {
      notParmasToData: true,
    },
  );

/**
 * 个案管理-病案记录-用药情况列表
 */
export const getProfileRecordMedicationList = (patientProfileRecordId: string, ids?: string[]) =>
  defHttp.post<IListByPatientId[]>(
    {
      url: `/medicationRecord/profileRecordMedicationList/${patientProfileRecordId}`,
      data: ids,
    },
    {
      notParmasToData: true,
    },
  );

export interface IQueryListByPatientProfileRecordId {
  content: string;
  id: string;
  patientProfileRecordId: string;
  patientTreatmentRecordId: string;
  pkPatientInfo: string;
  recordTime: string;
  recorderId: string;
  recorderName: string;
}
/**
 * 个案管理-病案记录-病程记录列表-根据个案id查询病程记录
 */
export const getQueryListByPatientProfileRecordId = (
  patientProfileRecordId: string,
  ids?: string[],
) =>
  defHttp.post<IQueryListByPatientProfileRecordId[]>(
    {
      url: `/patientProgressRecord/queryListByPatientProfileRecordId/${patientProfileRecordId}`,
      data: ids,
    },
    {
      notParmasToData: true,
    },
  );

/**
 * 个案管理-诊疗记录-康复路程-新增
 */
export const addRecoveryJourneyRecord = (data?: any) =>
  defHttp.post({
    url: `/recoveryJourneyRecord/add`,
    data,
  });

/**
 * 个案管理-个案详情-诊疗记录-康复路程-编辑
 * /recoveryJourneyRecord/edit
 */
export const editRecoveryJourneyRecord = (data?: any) =>
  defHttp.post({
    url: `/recoveryJourneyRecord/edit`,
    data,
  });

/**
 * 个案管理-个案详情-诊疗记录-康复路程-删除
 * /recoveryJourneyRecord/delete/{id}
 */
export const delRecoveryJourneyById = (id: string) =>
  defHttp.delete({
    url: `/recoveryJourneyRecord/delete/${id}`,
  });

/**
 * 个案管理-个案详情-诊疗记录-康复路程按条件查询
 */
export const getRecoveryJourneyRecordListAll = (data?: any) =>
  defHttp.post<IQueryListByPatientProfileRecordId[]>({
    url: `/recoveryJourneyRecord/queryList`,
    data,
  });

/**
 * 个案详情-康复路程总览-根据患者id查询
 */
export const getRecoveryJourneyRecordList = (pkPatientInfo: string) =>
  defHttp.get<IQueryListByPatientProfileRecordId[]>({
    url: `/recoveryJourneyRecord/queryListByPkPatientInfo/${pkPatientInfo}`,
  });

/**
 * 个案详情-病程记录总览-根据患者id查询
 */
export const getPatientProgressRecordList = (pkPatientInfo: string) =>
  defHttp.get<IQueryListByPatientProfileRecordId[]>({
    url: `/patientProgressRecord/queryListByPkPatientInfo/${pkPatientInfo}`,
  });

/**
 * 个案管理-诊疗记录-其他治疗-新增
 */
export const addOtherTreatmentRecord = (data?: any) =>
  defHttp.post({
    url: `/otherTreatmentRecord/add`,
    data,
  });

/**
 * 个案管理-诊疗记录-病程记录-新增
 */
export const addPatientProgressRecord = (data?: any) =>
  defHttp.post({
    url: `/patientProgressRecord/add`,
    data,
  });
/**
 * 个案管理-诊疗记录-病程记录-编辑
 */
export const editPatientProgressRecord = (data?: any) =>
  defHttp.post({
    url: `/patientProgressRecord/edit`,
    data,
  });

/**
 * 个案管理-诊疗记录-病程记录-删除
 */
export const delPatientProgressRecord = (id?: any) =>
  defHttp.delete({
    url: `/patientProgressRecord/delete?id=${id}`,
  });

/**
 * 个案管理-诊疗记录-诊疗记录-删除
 */
export const delPatientTreatmentRecord = (id?: any) =>
  defHttp.delete({
    url: `/patientTreatmentRecord/delete${id}`,
  });

/**
 * 个案管理-诊疗文书-签名
 */
export const getRecordSignatureVerify = (data?: any) =>
  defHttp.post({
    url: '/patientProfileRecord/recordSignatureVerify',
    data,
  });

/**
 * 个案管理-预约提醒-是否弹出提醒窗
 */
export const getAppointmentReminderDisplay = () =>
  defHttp.get({
    url: '/patientProfileRecord/appointmentReminderDisplay',
  });

/**
 * 个案管理-诊疗记录-检验记录-新增
 */
export const addInspectionReport = (data?: any) =>
  defHttp.post({
    url: `/inspectionReport/batchAdd`,
    data,
  });

/**
 * 个案管理-诊疗记录-检验记录-删除
 */
export const delInspectionReport = (id?: any) =>
  defHttp.delete({
    url: `/inspectionReport/delete/${id}`,
  });

/**
 * 个案管理-诊疗记录-检验记录-批量编辑
 */
export const editInspectionReport = (data?: any) =>
  defHttp.post({
    url: `/inspectionReport/batchEdit`,
    data,
  });

export interface IBacteriologyList {
  applyNo: string;
  bacterialName: string;
  colonyCount: string;
  colonyCountUnit: string;
  cultureCondition: string;
  cultureTime: string;
  detectionResult: string;
  id: string;
  medium: string;
}

/**
 * 个案管理-检验指标明细-细菌检验
 */
export const detailInspectionReportBtIndicator = (params?: { applyNo: string }) =>
  defHttp.get<IBacteriologyList[]>({
    url: `/inspectionReport/queryBtIndicatorDetail`,
    params,
  });
export interface IDetailInspectionReport {
  applyNo: string;
  attachments: string;
  dataSourceCode: number;
  id: string;
  inpatientNo: string;
  inspectionDetail: string;
  inspectionItemId: string;
  inspectionItemName: string;
  inspectionOrgId: string;
  inspectionOrgName: string;
  inspectionResult: string;
  inspectionResultCode: number;
  inspectionTime: string;
  inspectionType: number;
  patientProfileRecordId: string;
  patientTreatmentRecordId: string;
  pkPatientInfo: string;
  treatmentItemClassId: string;
  treatmentItemClassName: string;
}

/**
 * 个案管理-检验记录-查询检验记录详情
 */
export const detailInspectionReport = (params?: any) =>
  defHttp.get<IDetailInspectionReport[]>({
    url: `/inspectionReport/queryDetail`,
    params,
  });

/**
 * 个案管理-检验记录-根据患者id查询
 */
export const getInspectionReportByPatientId = (pkPatientInfo: string) =>
  defHttp.get<IInspectionReportList[]>({
    url: `/inspectionReport/queryListByPatientId/${pkPatientInfo}`,
  });

export interface IInspectionReportList {
  applyNo: string;
  attachments: string;
  dataSourceCode: number;
  id: string;
  inpatientNo: string;
  inspectionDetail: string;
  inspectionItemId: string;
  inspectionItemName: string;
  inspectionOrgId: string;
  inspectionOrgName: string;
  inspectionResult: string;
  inspectionResultCode: number;
  inspectionTime: string;
  inspectionType: number;
  patientProfileRecordId: string;
  patientTreatmentRecordId: string;
  pkPatientInfo: string;
  treatmentItemClassId: string;
  treatmentItemClassName: string;
}
/**
 * 个案管理-检验记录-根据条件查询列表
 */
export const getInspectionReportList = (data?: any) =>
  defHttp.post<IInspectionReportList[]>(
    {
      url: `/infection-ywxt/inspectionReport/queryList`,
      data,
    },
    { joinPrefix: false },
  );
export interface IDetailInspectionReportRtIndicator {
  applyNo: string;
  exceptionFlag: string;
  id: string;
  indexName: string;
  referenceValue: string;
  rtResult: string;
}
/**
 * 个案管理-检验指标明细-常规检验
 */
export const detailInspectionReportRtIndicator = (params?: { applyNo: string }) =>
  defHttp.get<IDetailInspectionReportRtIndicator[]>(
    {
      url: `/infection-ywxt/inspectionReport/queryRtIndicatorDetail`,
      params,
    },
    { joinTime: false, joinPrefix: false },
  );

/**
 * 康复路程-导出
 * /recoveryJourneyRecord/export/{pkPatientInfo}
 */
export const exportRecoveryJourneyRecord = (data) =>
  defHttp.post(
    { url: `/recoveryJourneyRecord/export/${data.pkPatientInfo}`, responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );

/**
 * 首页-传染病专病个案统计
 * /patientProfileRecord/querySpecificDiseaseCaseStatistics
 */
export const querySpecificDiseaseCaseStatistics = (params) =>
  defHttp.get({
    url: `/patientProfileRecord/querySpecificDiseaseCaseStatistics`,
    params,
  });

/**
 * 艾滋合并症统计分页查询
 * /patient/queryHivCmDetailList
 */
export interface IHivCmDetailList {
  /**合并症名称*/
  comorbiditiesName: string;
  /**主键*/
  id: string;
  /**身份证号*/
  idCardNo: string;
  /**项目名称*/
  indexName: string;
  /**检测时间*/
  inspectionTime: string;
  /**姓名*/
  patientName: string;
  /**个案编码*/
  recordNo: string;
  /**检验数值*/
  rtResult: string;
}
export const queryHivCmDetailList = (data) =>
  defHttp.post<IHivCmDetailList[]>({
    url: `/patient/queryHivCmDetailList`,
    data,
  });

/**
 * 艾滋合并症统计导出
 * /patient/exportHivCmDetail
 */
export const exportHivCmDetail = (data) =>
  defHttp.post(
    { url: `/patient/exportHivCmDetail`, responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );

/**
 * 个案管理-短信发送复诊提醒
 * GET /patientProfileRecord/smsFollowupVisitReminder
 */

export function smsFollowupVisitReminder(phone: string) {
  return defHttp.get({
    url: `/patientProfileRecord/smsFollowupVisitReminder`,
    params: {
      phone,
    },
  });
}
