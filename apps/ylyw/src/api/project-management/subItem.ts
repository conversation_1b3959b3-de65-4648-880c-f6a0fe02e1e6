import { defHttp } from '@ft/request';

/**
 * 诊断子项管理-分页查询
 */
export const getSubItemPage = (data: any) =>
  defHttp.post({
    url: '/diagnosticItemManageSub/page',
    data,
  });

/**
 * 诊断子项管理-新增
 */
export const addSubItem = (data: any) =>
  defHttp.post({
    url: '/diagnosticItemManageSub/add',
    data,
  });

/**
 * 诊断子项管理-编辑
 */
export const editSubItem = (data: any) =>
  defHttp.post({
    url: '/diagnosticItemManageSub/edit',
    data,
  });

/**
 * 诊断子项管理-删除
 */
export const deleteSubItem = (id: string) =>
  defHttp.post({
    url: `/diagnosticItemManageSub/delete/${id}`,
  });
