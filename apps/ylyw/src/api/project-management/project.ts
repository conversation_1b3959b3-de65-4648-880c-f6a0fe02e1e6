import { defHttp } from '@ft/request';
import { ContentTypeEnum } from '@ft/internal/enums/httpEnum';
import type { RcFile } from 'ant-design-vue/lib/vc-upload/interface';
/**
 * 诊断项目管理-分页查询
 */
export interface IDiagnosticItem {
  id: string;
  countryCode: string;
  itemCode: string;
  itemName: string;
  itemDesc: string;
}
export const getProjectPage = (data: any) =>
  defHttp.post<IDiagnosticItem[]>({
    url: '/diagnosticItemManage/page',
    data,
  });

/**
 * 查看项目详情
 */
export interface IDiagnosticItemDetail {
  id: string;
  countryCode: string;
  itemCode: string;
  itemName: string;
  price?: any;
  itemDesc: string;
  itemMattersAttention: string;
  specimenCollectionTypeId: string;
  specimenCollectionType: string;
  collectionRequirement: string;
}
export const getProjectDetail = (id: string) =>
  defHttp.post<IDiagnosticItemDetail>({
    url: `/diagnosticItemManage/detail/${id}`,
  });

/**
 * 新增项目
 */
export const addProject = (data: any) =>
  defHttp.post({
    url: '/diagnosticItemManage/add',
    data,
  });

/**
 * 编辑项目
 */
export const editProject = (data: any) =>
  defHttp.post({
    url: '/diagnosticItemManage/edit',
    data,
  });

/**
 * 删除项目
 */
export const delProject = (id: string) =>
  defHttp.post({
    url: `/diagnosticItemManage/delete/${id}`,
  });

/**
 * 下载模版
 */
export const projectTemplateDownload = () =>
  defHttp.post(
    {
      url: '/diagnosticItemManage/downloadTemplate',
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );

/**
 * 导入数据
 */
export const importProjectData = (data: { file: RcFile }) => {
  const formData = new FormData();
  formData.append('file', data.file);
  return defHttp.post({
    url: '/diagnosticItemManage/importExcel',
    data: formData,
    headers: {
      'Content-Type': ContentTypeEnum.FORM_DATA,
    },
  });
};

/**
 * 导出数据
 */
export const exportProjectData = (data: any) =>
  defHttp.post(
    { url: '/diagnosticItemManage/exportExcel', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );
