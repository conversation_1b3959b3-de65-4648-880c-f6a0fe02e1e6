import { defHttp } from '@ft/request';
export interface IHcvFollowupServiceRecord {
  alphaFetoproteinBloodDate: string;
  alphaFetoproteinStatus: number;
  alphaFetoproteinValue: string;
  alphaFetoproteinValueUpperLimit: string;
  altValue: string;
  altValueUpperLimit: string;
  astValue: string;
  astValueUpperLimit: string;
  biochemicalBloodDate: string;
  biochemicalExamStatus: number;
  bloodPlateletDate: string;
  bloodPlateletExamStatus: number;
  bloodPlateletValue: string;
  cardNumber: string;
  clinicalDispositionStatus: number;
  followupDate: string;
  followupDoctor: string;
  hcvAntiviralRegimenCode: number;
  hcvAntiviralRegimenOther: string;
  hcvRnaResultCode: number;
  id: string;
  idCardNo: string;
  medicationEndDate: string;
  patientName: string;
  patientProfileRecordId: string;
  pkPatientInfo: string;
  recordDate: string;
  remark: string;
  treatmentFacilityName: string;
  treatmentFacilityCode: string;
  hcvRnaVirusNumber: string;
  hcvRnaBloodDate: string;
  deathDate: string;
  childParentName: string;
}
export interface HcvFollowupServiceRecordParams {
  id?: string;
  patientProfileRecordId: string;
  pkPatientInfo: string;
}

/**
 * 丙肝随访服务记录-列表查询
 * /HcvFollowupServiceRecord/queryList
 */
export const getHcvFollowupServiceRecordList = (data: HcvFollowupServiceRecordParams) =>
  defHttp.post<IHcvFollowupServiceRecord[]>({
    url: '/HcvFollowupServiceRecord/queryList',
    data,
  });

/**
 * 丙肝随访服务记录-新增
 * /HcvFollowupServiceRecord/add
 */
export const addHcvFollowupServiceRecord = (data: any) =>
  defHttp.post({
    url: '/HcvFollowupServiceRecord/add',
    data,
  });

/**
 * 丙肝随访服务记录-修改
 * /HcvFollowupServiceRecord/edit

 */
export const updateHcvFollowupServiceRecord = (data: any) =>
  defHttp.post({
    url: '/HcvFollowupServiceRecord/edit',
    data,
  });

/**
 * 个案记录-编辑
 * /patientProfileRecord/edit
 */
export const editPatientProfileRecord = (data: any) =>
  defHttp.post({
    url: '/patientProfileRecord/edit',
    data,
  });

/**
 * 肝炎个案复诊提醒
 * /patientProfileRecord/hepatitisRevisitReminder
 */

export const hepatitisRevisitReminder = (data: any) =>
  defHttp.get({
    url: '/patientProfileRecord/hepatitisRevisitReminder',
    data,
  });
