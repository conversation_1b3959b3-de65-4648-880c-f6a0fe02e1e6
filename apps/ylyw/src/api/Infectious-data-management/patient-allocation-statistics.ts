import { defHttp } from '@ft/request';

/**
 * @description: 传染病专病管理 - 数据统计 - 患者分配统计列表
 * /patient/getPatientAllocationStatistics
 */
export interface IPatientAllocationStatistics {
  aidsCount: number;
  allocationOrg: string;
  allocationOrgCode: string;
  hepatitisCount: number;
  tuberculosisCount: number;
}
export const getPatientAllocationStatistics = (data?: any) =>
  defHttp.post<IPatientAllocationStatistics[]>({
    url: '/patient/getPatientAllocationStatistics',
    data,
  });

/**
 * @description: 传染病专病管理 - 数据统计 - 导出患者分配统计列表
 * /patient/exportPatientAllocationStatistics
 */
export const exportPatientAllocationStatistics = (data) =>
  defHttp.post(
    { url: '/patient/exportPatientAllocationStatistics', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );

/**
 * @description: 传染病专病管理 - 数据统计 - 患者分配统计列表 -患者分配统计列表下钻
 * /patient/patientStatisticsDetail
 */

export interface IPatientDiagnosis {
  /**分配机构*/
  allocationOrg: string;
  /**分配机构编码*/
  allocationOrgCode: string;
  /**诊断结果名称(艾滋病等)*/
  diagnosticResults: string;
  /**诊断结果编码*/
  diagnosticResultsCode: string;
  /**身份证号*/
  idCardNo: string;
  /**传染病病种*/
  infectionDisease: number;
  /**传染病病种名称*/
  infectionDiseaseName: string;
  /**患者姓名*/
  name: string;
  /**患者编号*/
  patientId: string;
  /**就诊机构名称*/
  visitOrgName: string;
}
export const getPatientStatisticsDetail = (data?: any) =>
  defHttp.post<IPatientDiagnosis[]>({
    url: '/patient/patientStatisticsDetail',
    data,
  });
