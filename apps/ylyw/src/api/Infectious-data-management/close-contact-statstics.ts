import { defHttp } from '@ft/request';

/**
 * @description: 传染病专病管理 - 数据统计 - 密接者筛查统计服务
 * /touchScreening/statistics
 */

export interface ITouchScreeningStatistics {
  /**接受预防性治疗数*/
  acceptFlagNum: number;
  /**筛查区划名称*/
  divisionName: string;
  /**预防性治疗对象数*/
  finishFlagNum: number;
  /**活动性肺结核数量*/
  hdxfjhNum: number;
  /**结核感染数量*/
  jhgrNum: number;
  /**未发现异常数量*/
  notFoundNum: number;
  /**完成预防性治疗数*/
  objectFlagNum: number;
  /**筛查结果为其他数*/
  otherNum: number;
  /**个案管理人员数量*/
  profileNum: number;
  /**密接者数量*/
  touchScreeningNum: number;
  /**疑似肺结核数量*/
  ysfjhNum: number;
}
export const getTouchScreeningStatistics = (data?: any) =>
  defHttp.post<ITouchScreeningStatistics[]>({
    url: '/touchScreening/statistics',
    data,
  });
/**
 * @description: 传染病专病管理 - 数据统计 - 导出密接筛查统计信息
 * /touchScreening/exportStatistics
 */
export const exportTouchScreeningStatistics = (data) =>
  defHttp.post(
    { url: '/touchScreening/exportStatistics', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );

/**
 * @description: 传染病专病管理 - 数据统计 - 康复路程-统计查询
 * /recoveryJourneyRecord/queryStatistics
 */
export interface IRecoveryJourneyStatistics {
  /**康复路程次数*/
  count: number;
  /**身份证号*/
  idCardNo: string;
  /**姓名*/
  patientName: string;
  /**电话*/
  phone: string;
}
export const getRecoveryJourneyStatistics = (data?: any) =>
  defHttp.post<IRecoveryJourneyStatistics[]>({
    url: '/recoveryJourneyRecord/queryStatistics',
    data,
  });

/**
 * @description: 传染病专病管理 - 数据统计 - 康复路程-统计导出
 * /recoveryJourneyRecord/exportRecoveryJourneyStatistics
 */
export const exportRecoveryJourneyStatistics = (data) =>
  defHttp.post(
    { url: '/recoveryJourneyRecord/exportRecoveryJourneyStatistics', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );

/**
 * @description: 传染病专病管理 - 数据统计 - 康复路程-统计汇总
 * /recoveryJourneyRecord/statisticsSummary
 */
export const getStatisticsSummary = (data?: any) =>
  defHttp.get({
    url: '/recoveryJourneyRecord/statisticsSummary',
    data,
  });
