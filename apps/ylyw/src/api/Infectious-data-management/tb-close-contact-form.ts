import { defHttp } from '@ft/request';

/**
 * @description: 传染病专病管理 - 数据统计 - 肺结核患者密接者登记表
 * /touchScreening/screeningList
 */

export interface ITouchScreeningStatisticsList {
  /** 是否开始预防性治疗：0是，1否 */
  acceptFlag: number;
  /** 年龄 */
  age: number;
  /** 地市州 */
  city: string;
  /**
   * 接触场所
   * 1-住所，2-学校，3-监管场所，4-福利机构，
   * 5-精神病医院，6-工矿企业，7-娱乐场所，8-其他
   */
  contactPlace: number;
  /** 接触类型 1-家庭内，2-家庭外 */
  contactType: number;
  /** 县区 */
  county: string;
  /** 胸部影像学检测日期 */
  ctCheckDate: string;
  /**
   * 胸部X光检查结果
   * 1-未见异常，2-疑似活动性结核病变，
   * 3-非活动性结核病变，4-其他
   */
  ctResult: number;
  /**
   * 病原学结果
   * 1-利福平耐药，2-病原学阳性，
   * 3-病原学阴性，4-无病原学结果
   */
  etiologyResult: number;
  /** 随访-第一次-随访日期 */
  follow1CtDate: string;
  /** 随访-第一次-影像学结果 */
  follow1CtResult: number;
  /** 随访-第一次-影像学方法 */
  follow1InspectType: number;
  /** 随访-第一次-诊断 */
  follow1ScreeningResultValue: number;
  /** 随访-第二次-随访日期 */
  follow2CtDate: string;
  /** 随访-第二次-影像学结果 */
  follow2CtResult: number;
  /** 随访-第二次-影像学方法 */
  follow2InspectType: number;
  /** 随访-第二次-诊断 */
  follow2ScreeningResultValue: number;
  /** 随访-第三次-随访日期 */
  follow3CtDate: string;
  /** 随访-第三次-影像学结果 */
  follow3CtResult: number;
  /** 随访-第三次-影像学方法 */
  follow3InspectType: number;
  /** 随访-第三次-诊断 */
  follow3ScreeningResultValue: number;
  /** 主键ID */
  id: string;
  /** 身份证号 */
  idCard: string;
  /** 胸部影像学检查类型 1-胸部X光片，2-胸部CT */
  inspectType: number;
  /** 是否符合预防性治疗：0-是，1-否 */
  objectFlag: number;
  /** 痰检检查方法 */
  phlegmCheck: string;
  /** 痰检留痰日期 */
  phlegmDate: string;
  /** 痰检检查结果 */
  phlegmResult: number;
  /** 联系电话 */
  phone: string;
  /** 预防性治疗方案 */
  preventiveTreatmentPlan: string;
  /**
   * 结束预防性治疗原因
   * 1-完成疗程，2-因不良反应提前停药，
   * 3-自行停药，4-发生结核病，5-其他
   */
  preventiveTreatmentReason: number;
  /** 登记号 */
  recordNo: string;
  /** 备注 */
  remark: string;
  /** 接触者姓名 */
  screeningName: string;
  /** 诊断结果 */
  screeningResultValue: number;
  /** 筛查时间 */
  screeningTime: string;
  /** 性别：1-男，2-女 */
  sex: number;
  /** 性别名称：1-男，2-女 */
  sexName: string;
  /** 肺结核可疑症状 */
  suspiciousResult: string;
  /** 密接患者ID */
  touchPatientId: string;
  /** 肺结核患者姓名 */
  touchPatientName: string;
  /** 肺结核患者联系电话 */
  touchPatientPhone: string;
  /** TST首次检测日期 */
  tstFirstDate: string;
  /** 首次检测方法 */
  tstFirstMethod: string;
  /** 首次检测结果 */
  tstFirstValue: string;
  /** TST二次检测日期 */
  tstSecondDate: string;
  /** 二次检测方法 */
  tstSecondMethod: string;
  /** 二次检测结果 */
  tstSecondValue: string;
}
export const getTouchScreeningStatisticsList = (data?: any) =>
  defHttp.post<ITouchScreeningStatisticsList[]>({
    url: '/touchScreening/screeningList',
    data,
  });
/**
 * @description: 传染病专病管理 - 数据统计 - 导出肺结核患者密接者登记表
 * /touchScreening/exportScreeningList
 */
export const exportTouchScreeningScreeningList = (data) =>
  defHttp.post(
    { url: '/touchScreening/exportScreeningList', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );
