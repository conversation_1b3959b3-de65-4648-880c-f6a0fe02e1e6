import { defHttp } from '@ft/request';

/**
 * @description: 传染病专病管理 - 数据统计 - 转介统计
 * /referralApplication/getRejectionStatistics
 */
export const getRejectionStatistics = (data?: any) =>
  defHttp.post({
    url: '/referralApplication/getRejectionStatistics',
    data,
  });

/**
 * @description: 传染病专病管理 - 数据统计 - 转介统计-获取统计表头
 * /referralApplication/getStatisticTableHeader
 */
export const getStatisticTableHeader = (data?: any) =>
  defHttp.post({
    url: '/referralApplication/getStatisticTableHeader',
    data,
  });

/**
 * @description: 传染病专病管理 - 数据统计 - 导出转介统计
 * /referralApplication/exportRejectionStatistics
 */
export const exportRejectionStatistics = (data) =>
  defHttp.post(
    { url: '/referralApplication/exportRejectionStatistics', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );
