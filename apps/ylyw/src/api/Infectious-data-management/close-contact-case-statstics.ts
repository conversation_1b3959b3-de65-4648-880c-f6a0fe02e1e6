import { defHttp } from '@ft/request';

/**
 * @description: 传染病专病管理 - 数据统计 - 密接者筛查情况表
 * /touchScreeningPatient/queryExistPage
 */

export interface ITouchScreeningStatisticsPage {
  /** 主键ID */
  patientId: string;
  /** 个案患者姓名 */
  patientName: string;
  /** 纳入个案时间 (格式: YYYY-MM-DD) */
  profileTime: string;
  /** 个案登记号 */
  recordNo: string;
  /** 密接者姓名 */
  touchPatientName: string;
}

export const getTouchScreeningStatisticsPage = (data?: any) =>
  defHttp.post<ITouchScreeningStatisticsPage[]>({
    url: '/touchScreeningPatient/queryExistPage',
    data,
  });
/**
 * @description: 传染病专病管理 - 数据统计 - 导出密接者筛查情况表
 * /touchScreeningPatient/export
 */
export const exportTouchScreeningPatient = (data) =>
  defHttp.post(
    { url: '/touchScreeningPatient/export', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );
