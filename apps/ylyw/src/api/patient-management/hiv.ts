import { ContentTypeEnum } from '@ft/internal/enums/httpEnum';
import { useGlobSetting } from '@ft/internal/hooks/setting';
import { defHttp } from '@ft/request';
import type { RcFile } from 'ant-design-vue/lib/vc-upload/interface';
export interface IPatientPage {
  address: string;
  age: number;
  appointmentTime: string;
  archiveNo: string;
  areaCode: string;
  areaName: string;
  birthday: string;
  certificateType: number;
  certificateTypeName: string;
  chiefPhysician: string;
  communityLeader: string;
  communityLeaderCode: string;
  communityOrgCode: string;
  communityOrgName: string;
  communityTelephone: string;
  comorbiditiesDesc: string;
  comorbiditiesFlag: number;
  contactOne: string;
  contactOnePhone: string;
  contactTwo: string;
  contactTwoPhone: string;
  countyOrgCode: string;
  countyOrgName: string;
  diagnosisOrgCode: string;
  diagnosisOrgName: string;
  diagnosisTime: string;
  diagnosticResults: string;
  diagnosticResultsCode: string;
  ethnicity: string;
  firstTreatmentTime: string;
  householdRegistAddress: string;
  householdRegistCode: string;
  householdRegistrationCode: string;
  householdRegistrationName: string;
  id: string;
  idCardNo: string;
  infectionDisease: number;
  infectionDiseaseName: string;
  infectionRoute: number;
  infectionRouteDesc: string;
  infectionSubDisease: number;
  inpNo: string;
  lastVisitTime: string;
  nextFollowUpVisitTime: string;
  patientName: string;
  patientStatus: number;
  patientStatusDesc: string;
  phone: string;
  populationClassification: string;
  receiveDesc: string;
  receiveStatus: number;
  sex: string;
  sexName: string;
  stateInfoNetworkId: string;
  stopTreatmentTime: string;
  treatmentStatus: number;
  treatmentStatusDesc: string;
  visitOrgCode: string;
  visitOrgName: string;
  patientProfileRecordId: string;
  caseStatusCode: number;
  followUpButtonShow: boolean;
}
/**
 * 分页查询
 */
export interface IPatientPageParams {
  appointmentTimeEnd: string;
  appointmentTimeStart: string;
  archiveNo: string;
  areaCode: string;
  areaName: string;
  communityOrgCode: string;
  communityOrgName: string;
  comorbiditiesCode: string;
  comorbiditiesName: string;
  countyOrgCode: string;
  countyOrgName: string;
  diagnosisTimeEnd: string;
  diagnosisTimeStart: string;
  diagnosticResults: string;
  diseaseCode: number;
  diseaseSubCode: number;
  id: string;
  idCardNo: string;
  medicalCode: string;
  medicalName: string;
  orderBy: string;
  pageNum: number;
  pageSize: number;
  patientName: string;
  patientStatus: number;
  patientStatusDesc: string;
  receiveStatus: number;
  treatmentStatus: number;
  treatmentStatusDesc: string;
  visitOrgCode: string;
  visitOrgName: string;
}
export const getPatientPage = (params: IPatientPageParams) =>
  defHttp.post<IPatientPage[]>({
    url: '/patient/page',
    params,
  });

/**个案管理-列表分页查询 */
export const getPatientProfileRecordList = (params: IPatientPageParams) =>
  defHttp.post<IPatientPage[]>({
    url: '/patientProfileRecord/queryList',
    params,
  });
/**
 * 新增患者信息
 */
export const addPatient = (data: any) =>
  defHttp.post({
    url: '/patient/add',
    data,
  });
/**
 * 更新患者信息
 */
export const editPatient = (data: any) =>
  defHttp.post({
    url: '/patient/patientStatusChange',
    data,
  });
/**
 * 治疗状态和患者状态变更
 */
export const statusChangePatient = (data: any) =>
  defHttp.post({
    url: '/patient/statusChange',
    data,
  });
/**
 * 结核患者管理-患者接收
 */
export const receivePatient = (data: any) =>
  defHttp.post({
    url: '/patient/receive',
    data,
  });
/**
 * 删除患者信息
 */
export const delPatient = (id: string) =>
  defHttp.delete({
    url: `/patient/del/${id}`,
  });

/**
 * 患者信息-详情
 */
export const detailPatient = (id: string) =>
  defHttp.get({
    url: `/patient/detail/${id}`,
  });

/**
 * 编辑-患者信息/patient/update
 */
export const updatePatient = (data: any) =>
  defHttp.post({
    url: '/patient/update',
    data,
  });

/**
 * @description: 患者信息导入
 * /sysUser/batchImport
 */

export const importPatient = (data: { file: RcFile }) => {
  const formData = new FormData();
  formData.append('file', data.file);
  return defHttp.post({
    url: '/patient/import',
    data: formData,
    headers: {
      'Content-Type': ContentTypeEnum.FORM_DATA,
    },
  });
};
/**
 * 艾滋病患者(个案)列表-导出
 */
export const patientPatientExport = (data) =>
  defHttp.post(
    { url: '/patient/patientExport', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );

/**
 * @description: 导入模板下载
 * /sysUser/downloadTemplate
 */

export const patientTemplateDownload = () => {
  return defHttp.post(
    {
      url: '/patient/templateDownload',
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * 区域分子诊断申请服务-中心端-文件上传
 */
const { apiUrl, urlPrefix } = useGlobSetting();

export const PatientSSEConnect = `${apiUrl}${urlPrefix}/patient/sseConnect/`;

/**个案管理-艾滋病管理-随访登记跳转 */
export const hivFollowUpJump = (patientProfileRecordId) =>
  defHttp.get({
    url: `/patientProfileRecord/hivFollowUpJump/${patientProfileRecordId}`,
  });

/**
 * 个案管理-下拉搜索框
 * /patientProfileRecord/dropdownSearch
 */
export interface IDropdownSearchParams {
  /**传染病类型*/
  diseaseCode: number;
  /**传染病子类型*/
  diseaseSubCode: number | undefined;
  /**类型: 1-确诊机构 2-所属社区 3-就诊科室 4-主管医生*/
  dropdownType: number;
}
export const patientProfileRecordDropdownSearch = (params: IDropdownSearchParams) =>
  defHttp.post({
    url: '/patientProfileRecord/dropdownSearch',
    params,
  });

/**
 * 个案管理-疾控端-分配社区
 * patientProfileRecord/commDist
 */
export interface commDistParams {
  /**分配社区机构编码*/
  communityOrgCode: string;
  /**分配社区机构名称*/
  communityOrgName: string;
  /**个案记录ID*/
  patientProfileRecordId: string;
}
export const commDist = (data: commDistParams) =>
  defHttp.post({
    url: `/patientProfileRecord/commDist`,
    data,
  });
