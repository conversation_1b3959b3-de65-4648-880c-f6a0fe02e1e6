import { defHttp } from '@ft/request';

/**
 * 缴费申请列表
 * /diagnosticApplication/getDiagnosticPayApplications
 * */
export const getDiagnosticPayApplications = (data: any) =>
  defHttp.post({
    url: '/diagnosticApplication/getDiagnosticPayApplications',
    data,
  });
/**
 * 导出缴费申请列表
 * /diagnosticApplication/exportDiagnosticPayApplications
 * */
export const exportDiagnosticPayApplications = (data: any) =>
  defHttp.post(
    { url: '/diagnosticApplication/exportDiagnosticPayApplications', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );

/**
 * 诊断缴费总计
 * /diagnosticApplication/getDiagnosticPayTotal
 * */
export const getDiagnosticPayTotal = (data: any) =>
  defHttp.post({
    url: '/diagnosticApplication/getDiagnosticPayTotal',
    data,
  });
