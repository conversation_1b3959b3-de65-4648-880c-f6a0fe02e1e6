import { defHttp } from '@ft/request';
export interface IDiagnosticApplication {
  age: 0;
  applyDept: string;
  applyDeptCode: string;
  applyDoctor: string;
  applyDoctorId: string;
  applyNo: string;
  applyOrg: string;
  applyOrgCode: string;
  applyStatus: number;
  applyStatusDesc: string;
  applyTime: string;
  id: string;
  idCard: string;
  itemName: string;
  mattersAttention: string;
  patientName: string;
  patientNo: string;
  preliminaryDiagnosis: string;
  reportAttachment: string;
  reportTime: string;
  returnReason: string;
  sampleCollectStatus: number;
  sampleCollectStatusDesc: string;
  sampleCollectionTime: string;
  sampleCollectorId: string;
  sampleCollectorName: string;
  sampleReceiveDept: string;
  sampleReceiveDeptCode: string;
  sampleReceiveOrg: string;
  sampleReceiveOrgCode: string;
  sampleReceiveStatus: number;
  sampleReceiveStatusDesc: string;
  sampleReceiveTime: string;
  sampleReceiverId: string;
  sampleReceiverName: string;
  sampleSamplingStatus: number;
  sampleSamplingStatusDesc: string;
  sampleSamplingTime: string;
  sampleTaker: string;
  sampleTypeDesc: string;
  sex: string;
  sexCode: number;
  totalCost: number;
}
export interface IDetailMedicalExaminationRecord {
  age: number;
  applyDept: string;
  applyDeptCode: string;
  applyDoctor: string;
  applyDoctorId: string;
  applyNo: string;
  applyOrg: string;
  applyOrgCode: string;
  applyStatus: number;
  applyTime: string;
  id: string;
  idCard: string;
  itemList: ItemList[];
  mattersAttention: string;
  patientName: string;
  patientNo: string;
  preliminaryDiagnosis: string;
  reportAttachment: string;
  sampleCollectStatus: number;
  sampleList: SampleList[];
  sex: string;
  sexCode: number;
  totalCost: number;
  applyStatusDesc: string;
  attachmentFileName: string;
}

export interface ItemList {
  itemDesc: string;
  itemId: string;
  itemName: string;
  price: number;
}

export interface SampleList {
  applyId: string;
  sampleCode: string;
  sampleCollectStatus: number;
  sampleCollectionTime: string;
  sampleCollectorId: string;
  sampleCollectorName: string;
  sampleReceiveDept: string;
  sampleReceiveOrg: string;
  sampleReceiveStatus: number;
  sampleReceiveTime: string;
  sampleReceiver: string;
  sampleSamplingTime: string;
  sampleTaker: string;
  sampleTypeDesc: string;
  sampleTypeId: string;
}

/**
 * 区域分子诊断申请服务-医院端-新增申请
 */
export const addDiagnosticApplication = (data: any) =>
  defHttp.post({
    url: '/diagnosticApplication/add',
    data,
  });

/**
 * 区域分子诊断申请服务-医院端-编辑申请
 */
export const editDiagnosticApplication = (data: any) =>
  defHttp.post({
    url: '/diagnosticApplication/edit',
    data,
  });

/**
 * 区域分子诊断申请服务-医院端-分页查询
 */
export const getDiagnosticApplicationPage = (data: any) =>
  defHttp.post<IDiagnosticApplication[]>({
    url: '/diagnosticApplication/queryPage',
    data,
  });

/**
 * 区域分子诊断申请服务-医院端-查询申请详情(包括申请项目和项目样本信息)
 */
export const detailDiagnosticApplicationPage = (id: string) =>
  defHttp.get<IDetailMedicalExaminationRecord>({
    url: `/diagnosticApplication/queryDetail/${id}`,
  });

export interface IDetailDiagnosticApplicationById {
  age: number;
  applyDept: string;
  applyDeptCode: string;
  applyDoctor: string;
  applyDoctorId: string;
  applyItemIdList: ApplyItemIdList[];
  applyNo: string;
  applyOrg: string;
  applyOrgCode: string;
  applyTime: string;
  id: string;
  idCard: string;
  mattersAttention: string;
  patientName: string;
  patientNo: string;
  preliminaryDiagnosis: string;
  sex: string;
  sexCode: number;
  totalCost: number;
}

export interface ApplyItemIdList {
  itemDesc: string;
  itemId: string;
  itemName: string;
  price: number;
}

/**
 * 区域分子诊断申请服务-医院端-编辑申请单查看申请详情
 */
export const detailDiagnosticApplicationById = (id: string) =>
  defHttp.get<IDetailDiagnosticApplicationById>({
    url: `/diagnosticApplication/getById/${id}`,
  });

/**
 * 区域分子诊断申请服务-医院端-删除
 */
export const delDiagnosticApplication = (id: string) =>
  defHttp.delete({
    url: `/diagnosticApplication/delete/${id}`,
  });

/**
 * 区域分子诊断申请服务-医院端-申请列表导出
 */
export const exportDiagnosticApplication = (data: any) =>
  defHttp.post(
    { url: '/diagnosticApplication/listExport', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );

export interface IDiagnosticItemManageList {
  collectionRequirement: string;
  countryCode: string;
  id: string;
  itemCode: string;
  itemDesc: string;
  itemMattersAttention: string;
  itemName: string;
  price: string;
  specimenCollectionType: string;
  specimenCollectionTypeId: string;
}

/**
 * 区域分子诊断申请服务-医院端-诊断项目管理-list查询
 */
export const getDiagnosticItemManageList = (data: any) =>
  defHttp.post<IDiagnosticItemManageList[]>({
    url: '/diagnosticItemManage/list',
    data,
  });

/**
 * 医院端-根据患者门诊号-住院号查询患者信息
 * /diagnosticApplication/getPatientInfoByPatientNO/{patientNo}
 */
export const getPatientInfoByPatientNO = (patientNo: string) =>
  defHttp.get<IDiagnosticApplication>({
    url: `/diagnosticApplication/getPatientInfoByPatientNO/${patientNo}`,
  });
