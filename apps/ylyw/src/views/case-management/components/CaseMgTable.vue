<script setup lang="ts">
  import { computed, onMounted, ref, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import type { ActionItem, BasicColumn } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Tabs } from 'ant-design-vue';
  import { Button } from '@ft/internal/components/Button';
  import { exportUtil } from '@ft/internal/utils';
  import { useModal } from '@ft/internal/components/Modal';
  import { usePermission } from '@ft/internal/hooks/web/usePermission';
  import { useRequest } from '@ft/request';
  import { getToken, setActiveRoutePathCache } from '@ft/internal/utils/auth';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import { getDictItemList } from '@ft/internal/api';
  import { useUserStoreWithOut } from '@ft/internal/store/modules/user';
  import IncludingCasesModel from '../../patient-management/tuberculosis/IncludingCasesModel.vue';
  import AllocPatientModal from '../../patient-management/components/AllocPatientModal.vue';
  import type { IPatientPage } from '/@/api/patient-management/hiv';
  import { getPatientProfileRecordList, hivFollowUpJump } from '/@/api/patient-management/hiv';
  import FollowUpModal from '../follow-up-view/FollowUpModal.vue';
  import {
    cancelCloseCase,
    delBringIntoManage,
    patientProfileRecordExport,
  } from '/@/api/case-management';
  import { RoleEnum } from '/@/enums/roleEnum';
  import { useCaseStore } from '/@/store/modules/case';
  import AssignCommunityModal from './AssignCommunityModal.vue';
  import { columns, columnsHZ, formSchema } from './data';
  const { hasPermissionAndRoute, hasPermission } = usePermission();
  const patientId = useRouteQuery('patientId', '', { transform: String });
  const query = useRoute().query;
  const store = useCaseStore();
  const { userInfo } = useUserStoreWithOut();

  // const areaCode = computed(() => {
  //   if (userInfo?.orgCategory === '2') {
  //     return userInfo?.orgCode === '420500' ? '420500' : userInfo?.divisionId;
  //   } else {
  //     return undefined;
  //   }
  // });
  onMounted(() => {
    if (patientId.value !== '' && store.isFromExternal) {
      setActiveRoutePathCache(router.currentRoute.value.path);
      router.push({
        name: 'PatientDetail',
        query: {
          infectionDisease: query.infectionDisease,
          patientProfileRecordId: query.patientProfileRecordId,
          patientId: query.patientId,
          tabActiveKey: 'TherapyProcess',
          diseaseSubCode: query.diseaseSubCode,
          caseStatusCode: query.caseStatusCode,
        },
      });
      store.setFromExternal(false);
    }
    // tableInstance.getForm().setFieldsValue({
    //   areaCode: areaCode.value,
    // });
  });
  const props = defineProps({
    patientReceptionState: {
      type: String,
      default: '0',
    },
    tabsDictParams: {
      type: String,
      default: '',
    },
    typeTitle: {
      type: String,
      default: '',
    },
    /** listType 0:患者管理列表 1:个案管理列表 */
    listType: {
      type: Number,
      default: 1,
    },
    /**
     * 传染病疾病类型	 1:病毒性肝炎 2:艾滋病 3:结核病 参见字典
     */
    patientDiseaseCode: {
      type: String,
      default: '3',
    },
  });

  // const [registerAddModal, { openModal: openAddModal }] = useModal();
  const [registerAllocModal, { openModal: openAllocModal }] = useModal();

  const router = useRouter();
  const activeKey = ref();
  watch(
    () => activeKey.value,
    () => {
      tableInstance?.getForm().setFieldsValue({
        visitOrgName: undefined,
        communityOrgName: undefined,
      });
    },
  );
  const { data: tabsList } = useRequest(() => getDictItemList(props.tabsDictParams as any), {
    ready: !!props.tabsDictParams,
  });

  const [registerTable, tableInstance] = useTable({
    api: getPatientProfileRecordList,
    fetchSetting: {
      sizeField: 'pageSize',
      pageField: 'pageNum',
      listField: 'list',
    },
    columns: computed(() => {
      return (
        {
          0: columnsHZ,
          1: columns(props.patientDiseaseCode),
        }[props.listType] || []
      );
    }),
    formConfig: {
      compact: true,
      labelWidth: 100,
      defaultAdvancedLineCollapse: false,
      schemas: computed(() => formSchema(props.patientDiseaseCode, activeKey.value)),
      fieldMapToTime: [
        // ['diagnosisTime', ['diagnosisTimeStart', 'diagnosisTimeEnd'], 'YYYY-MM-DD'],
        ['appointmentTime', ['appointmentTimeStart', 'appointmentTimeEnd'], 'YYYY-MM-DD'],
        ['nextFollowUpVisitTime', ['nextVisitDateStart', 'nextVisitDateEnd'], 'YYYY-MM-DD'],
        ['therapyStartDate', ['therapyStartDateStart', 'therapyStartDateEnd'], 'YYYY-MM-DD'],
        ['lastVisitDate', ['lastVisitDateStart', 'lastVisitDateEnd'], 'YYYY-MM-DD'],
      ],
    },
    useSearchForm: true,
    showIndexColumn: false,
    bordered: false,
    size: 'small',
    // resizeHeightOffset: 20,
    beforeFetch: (params) => {
      return {
        ...params,
        diseaseCode: props.patientDiseaseCode,
        diseaseSubCode: activeKey.value,
        listType: props.listType,
      };
    },
    actionColumn: {
      width: 220,
      title: '操作',
      dataIndex: 'action',
    },
  });

  function handleDetail(_record: IPatientPage, _column) {
    setActiveRoutePathCache(router.currentRoute.value.path);
    router.push({
      name: 'PatientDetail',
      query: {
        infectionDisease: _record.infectionDisease,
        patientProfileRecordId: props.listType == 0 ? '' : _record.patientProfileRecordId,
        patientId: _record.id,
        tabActiveKey: 'TherapyProcess',
        diseaseSubCode: _record.infectionSubDisease,
        caseStatusCode: _record.caseStatusCode,
      },
    });
  }

  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(
    patientProfileRecordExport,
    {
      manual: true,
      showSuccessMessage: true,
    },
  );
  function onExportExpert() {
    exportUtil(
      exportExpertRunAsync({
        ...tableInstance.getForm().getFieldsValue(),
        diseaseCode: props.patientDiseaseCode,
        diseaseSubCode: activeKey.value,
      }),
    );
  }
  const { runAsync: runAsyncDelBringIntoManage } = useRequest(delBringIntoManage, {
    manual: true,
    onSuccess: () => {
      tableInstance.reload();
    },
  });

  function handleDelIncorporateCase(record) {
    runAsyncDelBringIntoManage(record.patientProfileRecordId);
  }
  const [registerIncludingCasesModal, { openModal: openIncludingCasesModal }] = useModal();
  function handleEditRegistrationNumber(record) {
    openIncludingCasesModal(true, {
      mode: 'edit',
      pkPatientInfo: record.id,
      ...record,
    });
  }
  function handleBasicInformation(record) {
    router.push({
      name: 'CaseManagementBasicInformation',
      query: {
        patientProfileRecordId: record.patientProfileRecordId,
        hcvBaseRecordFillStatus: record.hcvBaseRecordFillStatus,
        //患者信息对象
        treatmentFacilityCode: record.visitOrgCode,
        treatmentFacilityName: record.visitOrgName,
        patientName: record.patientName,
        idCardNo: record.idCardNo,
        pkPatientInfo: record.id,
      },
    });
  }
  function createActions(record, column: BasicColumn): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: props.patientDiseaseCode === '3' ? '个案详情' : '详情',
        type: 'link',
        size: 'small',
        auth: RoleEnum.CASE_MG_DETAIL,
        onClick: handleDetail.bind(null, record, column),
      },
      {
        label: '编辑复诊时间',
        onClick: handfollowUpEdit.bind(null, record),
        ifShow: () =>
          hasPermissionAndRoute(
            ['CaseManagementHepatitis', 'CaseManagementTub'],
            RoleEnum.CASE_MG_EDIT_FOLLOW_UP,
          ),
      },
    ];
    return actions;
  }
  function createDropDownActions(record, column: BasicColumn): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '分配社区',
        type: 'link',
        size: 'small',
        auth: RoleEnum.CASE_MG_ASSIGN_COMMUNITY,
        // 肺结核且疾控（orgCategory = 2）才显示
        ifShow: props.patientDiseaseCode === '3' && userInfo?.orgCategory === '2',
        onClick: handleAssignCommunity.bind(null, record, column),
      },
      {
        label: '基本情况表',
        onClick: handleBasicInformation.bind(null, record),
        auth: RoleEnum.CASE_MG_BASIC_INFORMATION,
        ifShow: record?.infectionSubDisease === 2 && props.patientDiseaseCode === '1',
      },
      {
        label: '治疗随访表',
        onClick: handfollowUpView.bind(null, record, column),
        auth: RoleEnum.CASE_MG_FOLLOW_UP_VIEW,
        ifShow: record?.infectionSubDisease === 2 && props.patientDiseaseCode === '1',
      },
      {
        label: '随访登记',
        onClick: handFollowUpViewToWorkbench.bind(null, record, column),
        auth: RoleEnum.CASE_MG_FOLLOW_UP_VIEW_HIV,
        ifShow: props.patientDiseaseCode === '2' && record?.followUpButtonShow,
      },
      {
        label: '随访登记',
        onClick: handFollowUpViewToWorkbench.bind(null, record, column),
        auth: RoleEnum.CASE_MG_FOLLOW_UP_VIEW_HEPATITIS,
        ifShow: props.patientDiseaseCode === '1' && record?.followUpButtonShow,
      },

      {
        label: '慢病管理',
        type: 'link',
        size: 'small',
        onClick: () => {},
        ifShow: () =>
          hasPermissionAndRoute('CaseManagementHiv', RoleEnum.CASE_MG_SLOW_CHRONIC_DISEASE),
      },
      {
        label: '治疗状态变更',
        type: 'link',
        size: 'small',
        ifShow: () => hasPermissionAndRoute('CaseManagementHiv', RoleEnum.CASE_MG_TREATMENT_STATUS),
        onClick: handleAllocPatient.bind(null, record, column),
      },
      {
        label: '编辑登记号',
        type: 'link',
        size: 'small',
        ifShow: () =>
          hasPermissionAndRoute(
            ['CaseManagementHepatitis', 'CaseManagementTub'],
            RoleEnum.CASE_MG_EDIT_REGISTRATION_NUMBER,
          ),
        onClick: handleEditRegistrationNumber.bind(null, record),
      },
      {
        label: '移除个案管理',
        type: 'link',
        size: 'small',
        popConfirm: {
          title: '是否执行该操作！',
          placement: 'topRight',
          confirm: handleDelIncorporateCase.bind(null, record),
        },
        ifShow: () =>
          hasPermissionAndRoute(
            ['CaseManagementHepatitis', 'CaseManagementTub'],
            RoleEnum.CASE_MG_INCORPORATE_CASE,
          ),
      },
      {
        label: '取消结案',
        type: 'link',
        size: 'small',
        popConfirm: {
          title: '是否执行该操作！',
          placement: 'topRight',
          confirm: handleCancelClose.bind(null, record),
        },
        auth: RoleEnum.CASE_MG_CANCEL_CLOSE,
        ifShow: () => record?.caseStatusCode === 3,
      },
      // 复诊提醒
      {
        label: '复诊提醒',
        type: 'link',
        size: 'small',
        onClick: handleFollowUpRemind.bind(null, record),
      },

    ];
    return actions;
  }

  function handleFollowUpRemind(record:IPatientPage) {
    console.log(record);
  }

  const [registerAssignCommunityModal, { openModal: openAssignCommunityModal }] = useModal();
  function handleAssignCommunity(record, _column) {
    openAssignCommunityModal(true, {
      pkPatientInfo: record.id,
      patientProfileRecordId: record.patientProfileRecordId,
    });
  }
  const { runAsync: runAsyncCancelClose } = useRequest(cancelCloseCase, {
    manual: true,
    onSuccess: () => {
      tableInstance.reload();
    },
  });

  function handleCancelClose(record) {
    runAsyncCancelClose(record.patientProfileRecordId);
  }

  const [registerFollowUpModal, { openModal: openFollowUpModal }] = useModal();
  function handfollowUpEdit(_record) {
    openFollowUpModal(true, {
      mode: 'edit',
      pkPatientInfo: _record.id,
      patientProfileRecordId: _record.patientProfileRecordId,
    });
  }
  function handfollowUpView(_record, _column: BasicColumn) {
    router.push({
      name: 'CaseManagementFollowUpView',
      query: {
        pkPatientInfo: _record.id,
        patientProfileRecordId: _record.patientProfileRecordId,
        treatmentFacilityName: _record.visitOrgName,
      },
    });
  }
  function handleAllocPatient(_record, _column) {
    openAllocModal(true, {
      id: _record.id,
      title: '治疗状态变更',
    });
  }

  // const { runAsync: deleteRunAsync } = useRequest(delPatient, {
  //   manual: true,
  //   showSuccessMessage: true,
  //   onSuccess() {
  //     tableInstance.reload();
  //   },
  // });
  // function onRemove(record) {
  //   deleteRunAsync(record.id);
  // }
  const route = useRoute();
  const { createInfoModal } = useMessage();
  function handFollowUpViewToWorkbench(record, _column) {
    hivFollowUpJump(record.patientProfileRecordId).then((res) => {
      const path = import.meta.env.DEV ? `http://localhost:5140/workbench` : `/crb-fwpt/workbench`;
      const remark = route.meta.remark as Recordable;
      if (res.id) {
        window.open(
          `${path}?id=${remark.appId}&menuId=${remark.menuId}&visitRecordId=${
            res.id
          }&infectionDisease=${res.infectionDisease}&token=${getToken()}`,
        );
      } else {
        createInfoModal({
          title: '提示',
          content: '此患者已完成最近就诊随访',
          onOk: () => {
            window.open(
              `${path}?id=${remark.appId}&menuId=${remark.menuId}&patientName=${
                res.patientName
              }&token=${getToken()}`,
            );
          },
        });
      }
    });
  }
  defineExpose({
    tableInstance,
  });
</script>

<template>
  <div class="w-full h-full case-mg-table">
    <BasicTable class="ft-main-table" @register="registerTable">
      <template #headerTop>
        <div class="text-16px font-500 title-label mb-1">{{ typeTitle }}</div>
        <Tabs
          v-if="hasPermissionAndRoute('CaseManagementHepatitis', RoleEnum.CASE_MG_TUB_TBS)"
          v-model:activeKey="activeKey"
          @change="tableInstance.reload()"
        >
          <Tabs.TabPane key="" tab="全部" />
          <Tabs.TabPane
            v-for="item in tabsList"
            :key="item.dictItemCode"
            :tab="item.dictItemName"
          />
        </Tabs>
        <Tabs
          v-if="hasPermissionAndRoute('CaseManagementTub', RoleEnum.CASE_MG_TUB_TBS)"
          v-model:activeKey="activeKey"
          @change="tableInstance.reload()"
        >
          <!-- 1 2 3 4 5 敏感结核 耐药结核 结核感染 肺外结核 NTM -->
          <Tabs.TabPane key="" tab="全部" />
          <Tabs.TabPane key="1" tab="敏感结核" v-if="hasPermission(RoleEnum.CASE_MG_TUB_TABS_1)" />
          <Tabs.TabPane key="2" tab="耐药结核" v-if="hasPermission(RoleEnum.CASE_MG_TUB_TABS_2)" />
          <Tabs.TabPane key="3" tab="结核感染" v-if="hasPermission(RoleEnum.CASE_MG_TUB_TABS_3)" />
          <Tabs.TabPane key="4" tab="肺外结核" v-if="hasPermission(RoleEnum.CASE_MG_TUB_TABS_4)" />
          <Tabs.TabPane key="5" tab="NTM" v-if="hasPermission(RoleEnum.CASE_MG_TUB_TABS_5)" />
        </Tabs>
      </template>
      <template #tableTitle>
        <!-- <div>
          <Button
            type="primary"
            @click="handleAddCase"
            v-if="
              hasPermissionAndRoute(
                ['CaseManagementTub', 'CaseManagementHepatitis'],
                RoleEnum.CASE_MG_ADD,
              )
            "
          >
            添加个案
          </Button>
        </div> -->
      </template>
      <template #toolbar>
        <!-- <Button :loading="templateLoading" @click="onTemplateDown"> 模板下载 </Button>
        <Upload
          accept=".xlsx,.xls"
          :max-count="1"
          :show-upload-list="false"
          :custom-request="onImportExpert"
        >
          <Button :loading="importLoading"> 批量导入 </Button>
        </Upload> -->
        <Button
          :class="[
            hasPermissionAndRoute('CaseManagementHiv', RoleEnum.CASE_MG_TUB_TBS)
              ? '-mt-5'
              : '-mt-15',
          ]"
          :loading="exportLoading"
          @click="onExportExpert"
        >
          批量导出
        </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction
            :divider="false"
            :actions="createActions(record, column)"
            :drop-down-actions="createDropDownActions(record, column)"
          >
            <template #more>
              <Button size="small" type="link"> 更多 </Button>
            </template>
          </TableAction>
        </template>
      </template>
    </BasicTable>
    <!-- <AddCaseModal @register="registerAddModal" /> -->
    <AllocPatientModal @register="registerAllocModal" @success="tableInstance.reload" />
    <!-- 编辑登记号 -->
    <IncludingCasesModel @register="registerIncludingCasesModal" @success="tableInstance.reload" />
    <!-- 编辑复诊时间 -->
    <FollowUpModal @register="registerFollowUpModal" @success="tableInstance.reload" />
    <!-- 分配社区 -->
    <AssignCommunityModal @register="registerAssignCommunityModal" />
  </div>
</template>

<style lang="less" scoped>
  .case-mg-table {
    :deep {
      .ant-tabs-nav {
        margin: 0 !important;
      }

      .ft-main-table .vben-basic-form--compact .ant-col .ant-form-item {
        margin-bottom: 8px !important;
      }
    }
  }
</style>
