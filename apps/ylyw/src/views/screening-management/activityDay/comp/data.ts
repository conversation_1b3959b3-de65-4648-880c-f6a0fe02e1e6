import type { BasicColumn } from '@ft/internal/components/Table';
import type { FormSchema } from '@ft/internal/components/Form';
import {
  DictEnum,
  getAdministrativeList,
  getDictItemList,
  queryOrganizationList,
} from '@ft/internal/api';
import { useUserStoreWithOut } from '@ft/internal/store/modules/user';
import { getTouchScreeningPatientQueryPatientProfileList } from '/@/api/screening-management/touchScreening';

const { getUserInfo } = useUserStoreWithOut();

/**
筛查任务名称
筛查日期
筛查病种
筛查地区
筛查地点
筛查机构
医务人员
预计筛查人数
实际筛查人数
 */
export const columns: BasicColumn[] = [
  {
    title: '筛查任务名称',
    dataIndex: 'screeningTaskName',
    align: 'left',
    width: 100,
  },
  {
    title: '筛查日期',
    dataIndex: 'screeningDate',
    align: 'left',
    width: 160,
  },
  {
    title: '筛查病种',
    dataIndex: 'screeningDiseaseName',
    align: 'left',
    width: 100,
  },
  {
    title: '筛查地区',
    dataIndex: 'screeningDivisionName',
    align: 'left',
    width: 100,
  },
  {
    title: '筛查地点',
    dataIndex: 'screeningPlace',
    align: 'left',
    width: 100,
  },
  {
    title: '筛查机构',
    dataIndex: 'screeningOrgName',
    align: 'left',
    width: 100,
  },
  {
    title: '医务人员',
    dataIndex: 'screeningDirector',
    align: 'left',
    width: 100,
  },
  {
    title: '预计筛查人数',
    dataIndex: 'expectedScreeningPopulation',
    align: 'left',
    width: 100,
  },
  {
    title: '实际筛查人数',
    dataIndex: 'actualScreeningPopulation',
    align: 'left',
    width: 100,
  },
];
/**
 * 筛查地区
 * 筛查年份
 * 筛查机构
 * 筛查任务
 */
export const searchSchema: FormSchema[] = [
  {
    field: 'screeningDivisionId',
    label: '筛查地区',
    component: 'ApiSelect',
    componentProps: {
      api: getAdministrativeList,
      labelField: 'name',
      valueField: 'code',
      getPopupContainer: () => document.body, // 防止弹出层被modal隐藏掉
    },
    colProps: { span: 6 },
  },
  {
    field: 'screeningYear',
    label: '筛查年份',
    component: 'DatePicker',
    componentProps: {
      picker: 'year',
      showTime: false,
      showNow: false,
      valueFormat: 'YYYY',
      placeholder: '请选择年份',
      style: {
        width: '100%',
      },
      getPopupContainer: () => document.body, // 防止弹出层被modal隐藏掉
    },
    // defaultValue: dayjs().format('YYYY'),
    colProps: { span: 6 },
    // isHandleDateDefaultValue: false,
  },
  {
    field: 'screeningOrgId',
    label: '筛查机构',
    component: 'ApiSelect',
    componentProps: () => {
      return {
        api: () => queryOrganizationList({ orgType: 1 }),
        labelField: 'orgName',
        valueField: 'id',
        showSearch: true,
        optionFilterProp: 'label',
        getPopupContainer: () => document.body, // 防止弹出层被modal隐藏掉
      };
    },
    colProps: { span: 6 },
  },
  {
    field: 'screeningTaskName',
    label: '筛查任务',
    component: 'Input',
    colProps: { span: 6 },
  },
];

/**
 * 筛查任务名称
 * 筛查日期
 * 筛查病种
 * 筛查地区
 * 筛查地点
 * 筛查机构
 * 医务人员
 * 预计筛查人数
 * 实际筛查人数
 */
export const addModal = (screeningDiseaseName: string): FormSchema[] => [
  {
    field: 'id',
    component: 'Input',
    label: '主键',
    colProps: { span: 12 },
    componentProps: {},
    show: false,
  },
  {
    field: 'screeningDiseaseId',
    component: 'Input',
    label: '目录主键',
    colProps: { span: 12 },
    componentProps: {},
    show: false,
  },
  {
    field: 'screeningTaskName',
    component: 'Input',
    label: '筛查任务名称',
    required: true,
    colProps: { span: 12 },
  },
  {
    field: 'screeningDate',
    component: 'DatePicker',
    label: '筛查日期',
    required: true,
    colProps: { span: 12 },
    componentProps: {
      picker: 'date',
      showTime: false,
      showNow: false,
      valueFormat: 'YYYY-MM-DD',
      placeholder: '请选择日期',
      style: {
        width: '100%',
      },
      getPopupContainer: () => document.body, // 防止弹出层被modal隐藏掉
    },
  },
  {
    field: 'screeningDiseaseName',
    component: 'Input',
    label: '筛查病种名称',
    colProps: { span: 12 },
    show: false,
  },
  {
    field: 'screeningDiseaseId',
    component: 'ApiSelect',
    label: '筛查病种',
    required: true,
    colProps: { span: 12 },
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.SCREENING_DISEASE),
        getPopupContainer: () => document.body,
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        // disabled: true,
        onChange(_, opt) {
          if (opt?.label) formModel.screeningDiseaseName = opt?.label;
        },
      };
    },
  },
  {
    field: 'screeningDivisionName',
    component: 'Input',
    label: '筛查病种名称',
    show: false,
  },
  {
    field: 'screeningOrgName',
    component: 'Input',
    label: '筛查机构名称',
    colProps: { span: 12 },
    defaultValue: getUserInfo?.orgName,
    show: false,
  },
  {
    field: 'screeningOrgId',
    component: 'ApiSelect',
    label: '筛查机构',
    required: true,
    componentProps: ({ formModel }) => {
      return {
        api: () => queryOrganizationList({ orgType: 1 }),
        labelField: 'orgName',
        valueField: 'id',
        showSearch: true,
        optionFilterProp: 'label',
        getPopupContainer: () => document.body, // 防止弹出层被modal隐藏掉
        onChange(_, opt) {
          if (opt?.label) formModel.screeningOrgName = opt?.label;
        },
      };
    },
    defaultValue: getUserInfo?.orgId,
    colProps: { span: 12 },
  },
  {
    field: 'screeningDivisionId',
    component: 'ApiSelect',
    label: screeningDiseaseName?.includes('结核') ? '筛查社区' : '筛查地区',
    required: true,
    colProps: {
      span: 10,
    },
    itemProps: {
      wrapperCol: {
        span: 18,
      },
    },
    componentProps: ({ formModel }) => {
      return {
        api: getAdministrativeList,
        labelField: 'name',
        valueField: 'code',
        onChange(_, opt) {
          if (opt?.label) formModel.screeningDivisionName = opt?.label;
        },
        getPopupContainer: () => document.body, // 防止弹出层被modal隐藏掉
      };
    },
  },
  {
    field: 'townName',
    label: '所属乡镇/街道名称',
    component: 'Input',
    show: false,
  },
  {
    field: 'townId',
    label: ' ',
    required: true,
    component: 'ApiSelect',
    disabledLabelWidth: true,
    componentProps: ({ formModel }) => {
      return {
        placeholder: '请选择所属乡镇/街道名称',
        api: () =>
          formModel.screeningDivisionId && getAdministrativeList(formModel.screeningDivisionId),
        labelField: 'name',
        valueField: 'code',
        showSearch: true,
        optionFilterProp: 'label',
        getPopupContainer() {
          return document.body;
        },
        onChange(_, opt) {
          if (opt?.label) formModel.townName = opt?.label;
          formModel.villageId = '';
        },
      };
    },
    colProps: {
      span: 7,
    },
    itemProps: {
      // class: 'city-code',
      wrapperCol: {
        span: 24,
      },
    },
  },
  {
    field: 'villageName',
    label: '所属社区/村名称',
    component: 'Input',
    show: false,
  },
  {
    field: 'villageId',
    label: ' ',
    component: 'ApiSelect',
    disabledLabelWidth: true,
    componentProps: ({ formModel }) => {
      return {
        api: () => formModel.townId && getAdministrativeList(formModel.townId),
        labelField: 'name',
        valueField: 'code',
        showSearch: true,
        optionFilterProp: 'label',
        getPopupContainer() {
          return document.body;
        },
        placeholder: '请选择所属社区/村名称',
        onChange(_, opt) {
          if (opt?.label) formModel.villageName = opt?.label;
        },
      };
    },
    colProps: {
      span: 7,
    },
    itemProps: {
      wrapperCol: {
        span: 24,
      },
    },
  },
  {
    field: 'taskType',
    component: 'ApiSelect',
    label: '筛查任务类型',
    required: true,
    colProps: { span: 12 },
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.SCREENING_TASK_TYPE),
        getPopupContainer: () => document.body,
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        valueToString: formModel.taskType ? true : false,
        // onChange(_, opt) {
        //   if (opt?.label) formModel.screeningDiseaseName = opt?.label;
        // },
      };
    },
  },
  {
    field: 'screeningPlace',
    component: 'Input',
    label: '筛查地点',
    rules: [
      {
        required: true,
        validator(_, value) {
          if (value?.includes('班')) {
            return Promise.resolve();
          }
          return Promise.reject('筛查任务类型为学校,筛查地点必须包含班级信息');
        },
      },
    ],
    ifShow: ({ model }) => model.taskType === '1',
    colProps: { span: 12 },
  },
  {
    field: 'screeningPlace',
    component: 'Input',
    label: '筛查地点',
    required: true,
    colProps: { span: 12 },
    ifShow: ({ model }) => model.taskType !== '1',
  },
  {
    field: 'screeningDirector',
    component: 'Input',
    label: '医务人员',
    required: true,
    colProps: { span: 12 },
  },
  {
    field: 'expectedScreeningPopulation',
    component: 'InputNumber',
    label: '预计筛查人数',
    // required: true,
    componentProps: {
      min: 0,
    },
    colProps: { span: 12 },
  },
  {
    field: 'actualScreeningPopulation',
    component: 'InputNumber',
    label: '实际筛查人数',
    componentProps: {
      min: 0,
    },
    colProps: { span: 12 },
  },
  {
    field: 'patientIdList',
    component: 'ApiSelect',
    label: '密接患者名称',
    // required: true,
    colProps: { span: 12 },
    componentProps: ({ formModel }) => {
      return {
        //多选
        mode: 'multiple',
        api: () =>
          getTouchScreeningPatientQueryPatientProfileList({
            touchScreeningType: formModel.taskType,
            isCheck: false,
          }),
        getPopupContainer: () => document.body,
        labelField: 'touchPatientName',
        valueField: 'touchPatientId',
        showSearch: true,
        optionFilterProp: 'label',
        // onChange(_, opt) {
        //   if (opt?.label) formModel.screeningDiseaseName = opt?.label;
        // },
      };
    },
  },
];
