<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { addModal } from './data';
  import {
    addActiveDayScreeningTask,
    detailActiveDayScreeningTask,
    editActiveDayScreeningTask,
  } from '/@/api/screening-management/activityDay';

  const emit = defineEmits(['register', 'success']);
  const screeningDiseaseName = ref('');
  const [registerForm, formAction] = useForm({
    schemas: computed(() => addModal(screeningDiseaseName.value)),
    labelWidth: 120,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });
  const mode = ref<'add' | 'edit'>('add');
  const title = computed(() => {
    return mode.value === 'edit' ? '编辑筛查任务' : '新增筛查任务';
  });
  const { runAsync: detailRunAsync } = useRequest(detailActiveDayScreeningTask, {
    manual: true,
    onSuccess: (res) => {
      formAction.setFieldsValue({
        ...res,
        patientIdList: res?.patientList?.map((item) => item.touchPatientId),
      });
    },
  });
  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data?.mode;
    formAction.setFieldsValue({ ...data.data });
    screeningDiseaseName.value = data?.data?.screeningDiseaseName;
    if (mode.value === 'edit' && data?.data?.id) {
      detailRunAsync(data.data.id);
    }
  });
  const { loading, runAsync: drugSaveRunAsync } = useRequest(
    (params) =>
      mode.value === 'add' ? addActiveDayScreeningTask(params) : editActiveDayScreeningTask(params),
    {
      showSuccessMessage: true,
      manual: true,
    },
  );
  async function handleOk() {
    formAction.validate().then((values) => {
      drugSaveRunAsync({
        ...values,
      }).then(() => {
        emit('success');
        closeModal();
      });
    });
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="title"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    width="1000px"
    @register="register"
    @ok="handleOk"
    centered
  >
    <BasicForm class="activity-day-form" @register="registerForm" />
  </BasicModal>
</template>
<style lang="less" scoped>
  .activity-day-form {
    :deep {
      .ant-form-item-label > label[for='form_item_townId'],
      .ant-form-item-label > label[for='form_item_villageId'] {
        width: 0 !important;
        margin-left: 8px;

        &::after,
        &::before {
          content: '';
        }
      }
    }
  }
</style>
