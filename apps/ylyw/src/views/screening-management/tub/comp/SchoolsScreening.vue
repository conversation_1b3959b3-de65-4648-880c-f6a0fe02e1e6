<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import { Input, Upload } from 'ant-design-vue';
  import type { ActionItem, BasicColumn } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { StyledList } from '@ft/components';
  import { useRequest } from '@ft/request';
  import { useModal } from '@ft/internal/components/Modal';
  import { exportUtil } from '@ft/internal/utils';
  import print from 'vue3-print-nb';
  import type { UploadRequestOption } from 'ant-design-vue/lib/vc-upload/interface';
  import AddModal from '../AddModal.vue';
  import { columns } from '../data';
  import type { IGetActiveDayScreeningTaskQueryTaskList } from '/@/api/screening-management/touchScreening';
  import {
    delTouchScreening,
    downloadTemplate,
    getActiveDayScreeningTaskQueryTaskList,
    getTouchQueryPage,
    touchScreeningBatchImport,
    touchScreeningExport,
  } from '/@/api/screening-management/touchScreening';
  const props = defineProps({
    activeKey: {
      type: String,
      default: '2',
    },
    formValues: {
      type: Object,
      default: () => {},
    },
  });
  defineOptions({
    directives: {
      print,
    },
  });
  const activeId = ref<string>();
  const activeItem = ref<Partial<IGetActiveDayScreeningTaskQueryTaskList>>({});
  const queryName = ref('');
  const [registerAddModal, { openModal: openAddModal }] = useModal();

  const { data: treeList, loading } = useRequest(
    () =>
      getActiveDayScreeningTaskQueryTaskList({
        // 任务类型：1 学校密接者筛查 2 新生入学体检 3 活动日筛查
        taskType: '1',
        screeningTaskName: queryName.value,
        //筛查病种编码 5 结核病
        screeningDiseaseId: '5',
      }),
    {
      onSuccess: (data) => {
        activeId.value = data.length > 0 ? data[0].id : '';
        activeItem.value = data[0] || {};
      },
    },
  );
  const items = computed(() => {
    return treeList.value?.filter((item) => {
      return item?.screeningTaskName?.includes(queryName.value);
    });
  });

  const [registerTable, tableInstance] = useTable({
    fetchSetting: {
      sizeField: 'pageSize',
      pageField: 'pageNum',
      listField: 'data',
    },
    api: getTouchQueryPage,
    columns: computed(() => {
      return columns(props.activeKey);
    }),
    useSearchForm: false,
    bordered: true,
    immediate: false,
    size: 'small',
    beforeFetch: (params) => ({
      touchScreeningType: props.activeKey,
      taskId: activeId.value,
      ...params,
      ...props.formValues,
    }),
    afterFetch: (res) => {
      return res.list;
    },
    resizeHeightOffset: 5,
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
    },
  });

  function handleAddCase() {
    openAddModal(true, {
      mode: 'add',
      touchScreeningType: props.activeKey,
      taskId: activeId.value,
    });
  }
  watch(
    () => activeId.value,
    (val) => {
      val && tableInstance.reload();
    },
  );

  function handleEdit(record, column) {
    console.log(record, column);
    openAddModal(true, {
      mode: 'edit',
      ...record,
      taskId: activeId.value,
      touchScreeningType: props.activeKey,
    });
  }

  function createActions(record, column: BasicColumn): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '编辑',
        type: 'link',
        size: 'small',
        onClick: handleEdit.bind(null, record, column),
      },
      {
        label: '删除',
        type: 'link',
        size: 'small',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: handleDelete.bind(null, record),
          okButtonProps: {
            danger: true,
          },
        },
      },
    ];
    return actions;
  }
  const { runAsync: runAsyncDel } = useRequest(delTouchScreening, {
    manual: true,
    showSuccessMessage: true,
    onSuccess() {
      tableInstance.reload();
    },
  });
  function handleDelete(record) {
    runAsyncDel(record.id);
  }
  const { loading: downExpertTemplate, runAsync: runAsyncDownTemplate } = useRequest(
    downloadTemplate,
    {
      manual: true,
    },
  );

  function onDownloadTemplate() {
    exportUtil(runAsyncDownTemplate());
  }

  // const go = useGo();
  // function handlePrint() {
  //   go({
  //     name: 'ScreeningManagementTubPrintPreview',
  //     query: {
  //       formValues: JSON.stringify({
  //         ...props.formValues,
  //         taskId: activeId.value,
  //         touchScreeningType: props.activeKey,
  //       }),
  //     },
  //   });
  // }
  function refreshTable() {
    activeId.value && tableInstance.reload();
  }
  const { loading: importLoading, runAsync: runAsyncBatchImportImport } = useRequest(
    touchScreeningBatchImport,
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess() {
        tableInstance.reload();
      },
    },
  );
  function onImport(e: UploadRequestOption<any>) {
    const { file } = e;
    runAsyncBatchImportImport({
      // @ts-ignore
      file,
      /**touchScreeningType	密接筛查类型：1 联系人 2 学校 3 社区*/
      touchScreeningType: '2',
      taskId: activeId.value,
    });
  }
  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(
    touchScreeningExport,
    {
      manual: true,
      showSuccessMessage: true,
    },
  );
  function onExportExpert() {
    exportUtil(
      exportExpertRunAsync({
        /**touchScreeningType	密接筛查类型：1 联系人 2 学校 3 社区*/
        touchScreeningType: '2',
        taskId: activeId.value,
        ...props.formValues,
      }),
    );
  }

  defineExpose({ refreshTable });
</script>

<template>
  <div class="flex gap-2 w-full">
    <div
      class="flex flex-col gap-3 w-228px bg-#fff p-4 w-230px b-r-1px b-#E4E4E4 rounded h-[calc(100vh-346px)]"
    >
      <Input v-model:value="queryName" placeholder="筛查任务名称" />
      <div class="of-y-auto of-x-hidden min-w-0">
        <StyledList
          :items="items || []"
          v-model="activeId"
          v-model:value="activeItem"
          valueField="id"
          label-field="screeningTaskName"
          class="flex-1"
          :width="216"
          :loading="loading"
        />
      </div>
    </div>
    <!-- <div class="w-1px bg-coolGray h-full"></div> -->
    <div class="min-w-0 of-y-auto of-x-hidden">
      <BasicTable class="ft-main-table" @register="registerTable">
        <template #headerTop>
          <h2 class="text-center"
            >湖北省宜昌市{{ activeItem?.screeningDivisionName
            }}{{ activeItem?.screeningPlace }}肺结核患者接触者筛查一览表</h2
          >
        </template>
        <template #tableTitle>
          <Button class="mr-2" type="primary" @click="handleAddCase"> 新增筛查 </Button>
        </template>
        <template #toolbar>
          <!-- <Button @click="handlePrint"> 打印预览 </Button> -->
          <div class="flex gap-2">
            <Button :loading="downExpertTemplate" @click="onDownloadTemplate"> 下载模板 </Button>
            <Upload :max-count="1" :show-upload-list="false" :custom-request="onImport">
              <Button :loading="importLoading"> 批量导入 </Button>
            </Upload>
            <Button :loading="exportLoading" @click="onExportExpert"> 批量导出 </Button>
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :actions="createActions(record, column)" :divider="false" />
          </template>
        </template>
      </BasicTable>
    </div>
    <!-- 新增筛查 -->
    <AddModal
      :touchScreeningType="activeKey"
      @register="registerAddModal"
      @success="tableInstance.reload()"
    />
  </div>
</template>

<style lang="less" scoped></style>
