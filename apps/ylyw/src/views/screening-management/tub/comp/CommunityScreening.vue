<script setup lang="ts">
  import { computed, ref } from 'vue';
  import type { ActionItem, BasicColumn } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Upload } from 'ant-design-vue';
  import ApiSelect from '@ft/internal/components/Form/src/components/ApiSelect.vue';
  import { Button } from '@ft/internal/components/Button';
  import { useRequest } from '@ft/request';
  import { useModal } from '@ft/internal/components/Modal';
  import { exportUtil } from '@ft/internal/utils';
  import type { UploadRequestOption } from 'ant-design-vue/lib/vc-upload/interface';
  import { queryOrganizationList } from '@ft/internal/api';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import AddModal from '../AddModal.vue';
  import { columns } from '../data';
  import {
    communityPublishResult,
    delTouchScreening,
    downloadTemplate,
    getTouchQueryPage,
    touchScreeningBatchImport,
    touchScreeningExport,
  } from '/@/api/screening-management/touchScreening';
  const props = defineProps({
    activeKey: {
      type: String,
      default: '3',
    },
    formValues: {
      type: Object,
      default: () => {},
    },
  });

  const [registerAddModal, { openModal: openAddModal }] = useModal();

  const totalCount = ref(0);

  const [registerTable, tableInstance] = useTable({
    fetchSetting: {
      sizeField: 'pageSize',
      pageField: 'pageNum',
      listField: 'data',
      totalField: 'data.total',
    },
    api: getTouchQueryPage,
    columns: computed(() => {
      return columns(props.activeKey);
    }),
    useSearchForm: false,
    bordered: true,
    immediate: false,
    size: 'small',
    beforeFetch: (params) => {
      return {
        touchScreeningType: props.activeKey,
        ...params,
        ...props.formValues,
        screeningCommunity: screeningCommunity.value,
      };
    },
    afterFetch: (res) => {
      totalCount.value = res.total;
      return res.list;
    },
    resizeHeightOffset: 5,
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
    },
  });

  function handleAddCase() {
    openAddModal(true, {
      mode: 'add',
      touchScreeningType: props.activeKey,
      screeningCommunity: screeningCommunity.value,
    });
  }

  function handleEdit(record, column) {
    console.log(record, column);
    openAddModal(true, {
      mode: 'edit',
      ...record,
      touchScreeningType: props.activeKey,
    });
  }

  function createActions(record, column: BasicColumn): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '编辑',
        type: 'link',
        size: 'small',
        onClick: handleEdit.bind(null, record, column),
      },
      {
        label: '删除',
        type: 'link',
        size: 'small',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: handleDelete.bind(null, record),
          okButtonProps: {
            danger: true,
          },
        },
      },
    ];
    return actions;
  }
  const { runAsync: runAsyncDel } = useRequest(delTouchScreening, {
    manual: true,
    onSuccess: () => {
      tableInstance.reload();
    },
  });
  function handleDelete(record) {
    runAsyncDel(record.id);
  }

  const screeningCommunity = ref();
  const { loading: downExpertTemplate, runAsync: runAsyncDownTemplate } = useRequest(
    downloadTemplate,
    {
      manual: true,
    },
  );
  function onDownloadTemplate() {
    exportUtil(runAsyncDownTemplate());
  }

  const { loading: importLoading, runAsync: importExpertRunAsync } = useRequest(
    touchScreeningBatchImport,
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess: () => {
        // tableIns.reload();
      },
    },
  );

  function onImportExpert(e: UploadRequestOption<any>) {
    const { file } = e;
    importExpertRunAsync({
      // @ts-ignore
      file,
    });
  }

  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(
    touchScreeningExport,
    {
      manual: true,
      showSuccessMessage: true,
    },
  );
  function onExportExpert() {
    exportUtil(
      exportExpertRunAsync({
        touchScreeningType: props.activeKey,
        ...props.formValues,
        screeningCommunity: screeningCommunity.value,
      }),
    );
  }
  function handlePrint() {}
  const communityName = ref('');
  async function _getCommunityList() {
    const communityList = await queryOrganizationList({
      orgType: 2,
      orgCategory: '6',
      enableFlag: 0,
    });
    screeningCommunity.value = communityList.length > 0 ? communityList[0].orgCode : '';
    communityName.value = communityList.length > 0 ? communityList[0].orgName : '';
    tableInstance.reload();
    return communityList;
  }
  function onCommunityChange(opt) {
    communityName.value = opt?.label;
    tableInstance.reload();
  }

  const { runAsync: publishAsync } = useRequest(communityPublishResult, {
    manual: true,
    showSuccessMessage: true,
  });
  const { createConfirm } = useMessage();
  function handlePublish() {
    createConfirm({
      iconType: 'warning',
      title: '发布结果',
      content: `确定发布查询的 <strong class="text-red">${totalCount.value}</strong> 个社区的一个筛查者结果吗？`,
      onOk: () => {
        publishAsync({
          touchScreeningType: props.activeKey,
          ...props.formValues,
          screeningCommunity: screeningCommunity.value,
        });
      },
    });
  }

  function refreshTable() {
    screeningCommunity.value && tableInstance.reload();
  }
  defineExpose({ refreshTable });
</script>

<template>
  <div class="w-full h-full">
    <BasicTable class="ft-main-table" @register="registerTable">
      <template #headerTop>
        <div>
          <div>
            社区名称：
            <ApiSelect
              :api="_getCommunityList"
              class="!w-220px"
              v-model:value="screeningCommunity"
              labelField="orgName"
              valueField="orgCode"
              showSearch
              optionFilterProp="label"
              placeholder="请选择社区名称"
              @change="(_, opt) => onCommunityChange(opt)"
            />
          </div>
          <h2 class="text-center">{{ communityName }}肺结核患者接触者筛查一览表</h2>
        </div>
      </template>
      <template #tableTitle>
        <Button class="mr-2" type="primary" @click="handleAddCase"> 新增筛查 </Button>
      </template>
      <template #toolbar>
        <Button @click="handlePublish" type="primary"> 发布 </Button>
        <Button v-if="activeKey === '2'" @click="handlePrint"> 打印 </Button>
        <div v-if="activeKey === '3'" class="flex gap-2">
          <Button :loading="downExpertTemplate" @click="onDownloadTemplate"> 下载模板 </Button>
          <Upload
            accept=".xlsx,.xls"
            :max-count="1"
            :show-upload-list="false"
            :custom-request="onImportExpert"
          >
            <Button :loading="importLoading"> 批量导入 </Button>
          </Upload>
          <Button :loading="exportLoading" @click="onExportExpert"> 批量导出 </Button>
        </div>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record, column)" :divider="false" />
        </template>
      </template>
    </BasicTable>
    <!-- 新增筛查 -->
    <AddModal
      :touchScreeningType="activeKey"
      @register="registerAddModal"
      @success="tableInstance.reload()"
    />
  </div>
</template>

<style lang="less" scoped></style>
