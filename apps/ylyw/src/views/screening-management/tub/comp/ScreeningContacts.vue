<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { Input, Tabs } from 'ant-design-vue';
  import { Button } from '@ft/internal/components/Button';
  import { StyledList } from '@ft/components';
  import { useRequest } from '@ft/request';
  import { useModal } from '@ft/internal/components/Modal';
  import { useLoading } from '@ft/internal/components/Loading';
  import AddModal from '../AddModal.vue';
  import FollowTable from './FollowTable/index.vue';
  import DelModal from '../DeleModal.vue';
  import { isDiseaseControl } from '/@/enums/roleEnum';
  import type { IPatientList, IQueryPageList } from '/@/api/screening-management/touchScreening';
  import {
    getEtiologyResult,
    getTouchQueryList,
    getTouchScreeningPatientQueryExistList,
  } from '/@/api/screening-management/touchScreening';
  const props = defineProps({
    activeKey: {
      type: String,
      default: '2',
    },
    formValues: {
      type: Object,
      default: () => {},
    },
  });
  const activeId = ref<string>();
  const activeItem = ref<Partial<IPatientList>>({});
  const queryName = ref('');
  /**密接筛查状态 1-未密接筛查 2-有密接筛查 (全部不传) */
  const screeningStatus = ref<string>('');
  const [registerAddModal, { openModal: openAddModal }] = useModal();

  const TableRef = ref();
  const [openLoading, closeLoading] = useLoading({
    target: TableRef.value,
    props: {
      tip: '加载中...',
      absolute: true,
    },
  });
  const { data: tableData = ref([]), runAsync: getTouchQueryListRunAsync } = useRequest(
    () =>
      getTouchQueryList({
        touchScreeningType: props.activeKey,
        touchPatientId: activeItem.value?.touchPatientId,
        ...props.formValues,
      }),
    {
      manual: true,
      onBefore() {
        openLoading();
      },
      onSuccess() {
        closeLoading();
      },
    },
  );

  const {
    data: treeList,
    loading,
    runAsync: getTreelistRunAsync,
  } = useRequest(
    () =>
      getTouchScreeningPatientQueryExistList({
        isCheck: true,
        queryName: queryName.value,
        touchScreeningType: props.activeKey,
        screeningStatus: screeningStatus.value,
        startTime: props.formValues?.startTime,
        endTime: props.formValues?.endTime,
      }),
    {
      onSuccess: (data) => {
        activeId.value = data.length > 0 ? data[0].id : '';
        activeItem.value = data[0] || {};
        if (data && data.length > 0) {
          getTouchQueryListRunAsync();
        } else {
          tableData.value = [];
        }
      },
    },
  );
  const items = computed(() => {
    return treeList.value
      ?.map((item) => ({
        ...item,
        label: item.recordNo + ' ' + item.touchPatientName,
      }))
      .filter((item) => {
        return (
          item?.touchPatientName?.includes(queryName.value) ||
          item?.recordNo?.includes(queryName.value)
        );
      });
  });
  // const [registerAddPatientModal, { openModal: openAddPatientModal }] = useModal();

  // function handleAddPatient() {
  //   openAddPatientModal(true, {
  //     mode: 'add',
  //     touchScreeningType: props.activeKey,
  //   });
  // }

  const { runAsync: getEtiologyResultRunAsync } = useRequest(getEtiologyResult, {
    showSuccessMessage: false,
    manual: true,
    onSuccess: (res) => {
      openAddModal(true, {
        mode: 'add',
        touchPatientId: activeItem.value?.touchPatientId,
        touchPatientName: activeItem.value?.touchPatientName,
        etiologyResult: res,
      });
    },
  });
  function handleAddCase() {
    getEtiologyResultRunAsync({
      touchScreeningType: '1',
      touchPatientId: activeItem.value?.touchPatientId,
    });
  }
  // const { runAsync: delRunAsync } = useRequest(delTouchScreeningPatient, {
  //   showSuccessMessage: true,
  //   manual: true,
  //   onSuccess: () => {
  //     getTreelistRunAsync();
  //   },
  // });
  // const { createConfirm } = useMessage();
  // function handleDel(item) {
  //   console.log(item);
  //   createConfirm({
  //     iconType: 'warning',
  //     title: '移除患者',
  //     content: `确定移除该患者吗？`,
  //     onOk: () => {
  //       delRunAsync(item.id);
  //     },
  //   });
  // }
  const FollowTableRef = ref<InstanceType<typeof FollowTable>>();

  async function refreshTable() {
    getTreelistRunAsync();
  }

  function onTdClick(item: Partial<IQueryPageList>) {
    if (isDiseaseControl()) return;
    openAddModal(true, {
      mode: 'edit',
      ...item,
      touchScreeningType: props.activeKey,
    });
  }

  const [registerDelModal, { openModal }] = useModal();
  function onDelClick(item: IQueryPageList) {
    if (isDiseaseControl()) return;
    openModal(true, {
      ...item,
    });
  }

  defineExpose({ refreshTable });
</script>

<template>
  <div class="flex gap-2 w-full">
    <div class="flex flex-col bg-#fff p-4 pt-0 h-[calc(100vh-346px)]">
      <h3 class="mb-0">个案列表</h3>
      <div>
        <Tabs v-model:activeKey="screeningStatus" @change="refreshTable" size="small">
          <Tabs.TabPane key="" tab="全部" />
          <Tabs.TabPane key="1" tab="未密接" />
          <Tabs.TabPane key="2" tab="有密接" />
        </Tabs>
      </div>
      <div
        class="flex flex-col gap-3 w-228px bg-#fff w-230px b-r-1px b-#E4E4E4 flex-1 max-h-[calc(100vh-436px)]"
      >
        <div class="pr-4">
          <Input v-model:value="queryName" placeholder="登记号或姓名搜索" />
        </div>
        <!-- <div>
        <Button type="primary" @click="handleAddPatient"> 添加患者 </Button>
      </div> -->
        <div class="of-y-auto of-x-hidden min-w-0">
          <StyledList
            :items="items || []"
            value-field="id"
            v-model="activeId"
            v-model:value="activeItem"
            class="flex-1"
            :width="216"
            :loading="loading"
            @change="
              () => {
                getTouchQueryListRunAsync();
              }
            "
          >
            <!-- <template #default="item">
            <div class="flex justify-between items-center w-full relative">
              <div
                :title="item?.recordNo + ' ' + item?.touchPatientName"
                class="w-[calc(100%-40px)] overflow-hidden truncate"
                >{{ item?.recordNo + ' ' + item?.touchPatientName }}
              </div>
              <Dropdown placement="bottom" :class="[activeId === item?.id ? '!block' : '!hidden']">
                <div
                  class="hover:bg-#f5f5f5 absolute right-16px rounded-2px flex items-center px-4px py-2px pt-5px"
                >
                  <Icon @click.prevent color="#303133" :size="18" icon="ant-design:more-outlined" />
                </div>
                <template #overlay>
                  <div
                    class="bg-#fff color-#333 w-fit min-w-80px rounded-3px text-left cursor-pointer shadow-md"
                  >
                    <span class="py-2 px-3 block hover:text-primary-color" @click="handleDel(item)">
                      <Icon icon="ant-design:delete-outlined" class="mr-1" />
                      移除
                    </span>
                  </div>
                </template>
              </Dropdown>
            </div>
          </template> -->
          </StyledList>
        </div>
      </div>
    </div>
    <div
      class="flex-1 flex flex-col gap-2 bg-#fff rounded-2 pb-3 px-3 h-[calc(100vh-346px)] min-w-0"
    >
      <h3 class="mb-0">{{ activeItem?.touchPatientName }}密接者列表</h3>
      <div class="flex gap-10 items-end">
        <Button type="primary" @click="handleAddCase" v-if="!isDiseaseControl()">
          新增筛查者
        </Button>
        <div class="text-#999 line-height-loose">
          注：鼠标点击可进行编辑操作，右键可进行删除操作
        </div>
      </div>
      <div class="power-data flex-1 overflow-auto">
        <FollowTable
          class="h-full"
          :table-data="tableData"
          ref="FollowTableRef"
          @col-click="onTdClick"
          @col-contextmenu="onDelClick"
        >
          <template #objectFlag="{ item }">
            {{ item }}
          </template>
        </FollowTable>
      </div>
    </div>
    <!-- 新增筛查 -->
    <AddModal
      :touchScreeningType="activeKey"
      @register="registerAddModal"
      @success="refreshTable"
    />
    <!-- 新增患者 -->
    <!-- <AddPatientModal @register="registerAddPatientModal" @success="getTreelistRunAsync" /> -->
    <!-- 删除筛查 -->
    <DelModal @register="registerDelModal" @success="refreshTable" />
  </div>
</template>

<style lang="less" scoped></style>
