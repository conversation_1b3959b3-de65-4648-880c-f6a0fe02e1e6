<script setup lang="ts">
  import { computed, nextTick, ref } from 'vue';
  import { Col, FormItemRest, Input, InputGroup, Row, Tabs } from 'ant-design-vue';
  import { BasicForm, useForm } from '@ft/internal/components/Form';

  import { formSchema } from './data';
  import ScreeningContacts from './comp/ScreeningContacts.vue';
  import SchoolsScreening from './comp/SchoolsScreening.vue';
  import CommunityScreening from './comp/CommunityScreening.vue';

  const activeKey = ref('1');
  const componentMap = {
    1: ScreeningContacts,
    2: SchoolsScreening,
    3: CommunityScreening,
  };
  const ScreeningContactsRef = ref<InstanceType<typeof ScreeningContacts>>();
  const SchoolsScreeningRef = ref<InstanceType<typeof SchoolsScreening>>();
  const CommunityScreeningRef = ref<InstanceType<typeof CommunityScreening>>();
  const componentMapRef = {
    1: ScreeningContactsRef,
    2: SchoolsScreeningRef,
    3: CommunityScreeningRef,
  };
  const formValues = ref({});

  const [registerForm, ActionForm] = useForm({
    schemas: computed(() => formSchema(activeKey.value)),
    labelWidth: 100,
    showAdvancedButton: false,
    //@ts-ignore
    actionColOptions: { span: computed(() => (activeKey.value == '3' ? 24 : 6)) },
    fieldMapToTime: [['preliminaryScreeningDate', ['startTime', 'endTime'], 'YYYY-MM-DD']],
    autoSubmitOnEnter: false,
    showActionButtonGroup: true,
    submitFunc: async () => {
      await handleFormChange();
    },
    submitOnReset: false,
    resetFunc: async () => {
      formValues.value = {};
      await refreshComponentTable();
    },
  });
  async function refreshComponentTable() {
    console.log('Refreshing table for tab:', activeKey.value);
    console.log('Component ref:', componentMapRef[activeKey.value].value);
    await nextTick();
    await componentMapRef[activeKey.value].value?.refreshTable();
  }

  async function handleFormChange() {
    formValues.value = ActionForm.getFieldsValue();
    await refreshComponentTable();
  }
</script>

<template>
  <div class="w-full h-full pr-4">
    <div class="bg-white px-4 pt-4 m-0 rounded rounded-lt-none">
      <BasicForm
        @register="registerForm"
        @formValuesChange="refreshComponentTable"
        class="bg-white"
      >
        <template #recordNo="{ model, field }">
          <InputGroup>
            <Row :gutter="24" class="flex items-center">
              <Col :span="10">
                <Input v-model:value="model[field]" />
              </Col>
              <Col :span="4">
                <div class="h-1px bg-#E4E4E4"></div>
              </Col>
              <Col :span="10">
                <FormItemRest>
                  <Input v-model:value="model.recordNoEnd" />
                </FormItemRest>
              </Col>
            </Row>
          </InputGroup>
        </template>
      </BasicForm>
    </div>
    <div class="mt-4 bg-white rounded flex flex-col">
      <div class="pt-4 px-4">
        <div class="text-16px font-500 title-label">结核病筛查</div>
        <Tabs v-model:activeKey="activeKey" class="pl-4" @change="handleFormChange">
          <!-- 联系人密接者筛查 学学校密接者筛杳 社区筛查 -->
          <Tabs.TabPane key="1" tab="联系人密接者筛查" />
          <Tabs.TabPane key="2" tab="学校密接者筛查" />
          <Tabs.TabPane key="3" tab="社区筛查" />
        </Tabs>
      </div>
      <component
        :is="componentMap[activeKey]"
        :activeKey="activeKey"
        :formValues="formValues"
        :ref="componentMapRef[activeKey]"
      />
    </div>
  </div>
</template>

<style lang="less" scoped></style>
