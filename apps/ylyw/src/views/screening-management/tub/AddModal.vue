<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { computed, ref } from 'vue';
  import { useRequest } from '@ft/request';
  import { getBirthDateAndGender } from '@ft/internal/utils';
  import { addFormSchema, communityScreeningAddFormSchema, screeningAddFormSchema } from './data';
  import {
    detailTouchScreening,
    saveTouchScreening,
    updateTouchScreening,
  } from '/@/api/screening-management/touchScreening';

  const emit = defineEmits(['register', 'success']);
  const props = defineProps({
    /**
     *  1联系人密接者筛查 2学学校密接者筛杳 3社区筛查
     */
    touchScreeningType: {
      type: String,
      default: '1',
    },
  });
  const [registerFrom, formAction] = useForm({
    labelWidth: 150,
    colon: false,
    showActionButtonGroup: false,
    schemas: computed(
      () =>
        ({
          1: screeningAddFormSchema,
          2: addFormSchema('2'),
          3: communityScreeningAddFormSchema,
        }[props.touchScreeningType]),
    ),
  });
  const mode = ref('add');
  const getTitle = computed(() => {
    return mode.value === 'add' ? '新增筛查' : '编辑筛查';
  });
  const { runAsync: getDetailRunAsync } = useRequest(detailTouchScreening, {
    manual: true,
    onSuccess: async (data) => {
      const result = getBirthDateAndGender(data?.idCard || '');
      await formAction.setFieldsValue({
        ...data,
        suspiciousResult: data?.suspiciousResult?.split(','),
        phlegmCheck: data?.phlegmCheck?.split(','),
        sex: data?.sex ? data?.sex : result.gender,
        sexName: data?.sexName ? data?.sexName : result.gender?.toString() === '1' ? '男' : '女',
        age: data?.age ? data?.age : result.age,
      });
      formAction.clearValidate();
    },
  });

  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data.mode;
    if (data.mode === 'edit') {
      getDetailRunAsync(data.id);
    } else {
      formAction.setFieldsValue({
        ...data,
      });
      formAction.clearValidate();
    }
  });
  const { runAsync, loading } = useRequest(
    (params) => (mode.value === 'edit' ? updateTouchScreening(params) : saveTouchScreening(params)),
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess: (data) => {
        emit('success', data);
        closeModal();
      },
    },
  );

  async function handleOk() {
    formAction.validate().then((values) => {
      runAsync({
        ...values,
        suspiciousResult: values?.suspiciousResult?.join(',') || null,
        phlegmCheck: values?.phlegmCheck?.join(',') || null,
        touchScreeningType: props.touchScreeningType,
      });
    });
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="getTitle"
    :can-fullscreen="false"
    :minHeight="150"
    width="80%"
    :ok-button-props="{
      loading,
    }"
    @register="register"
    @ok="handleOk"
  >
    <BasicForm @register="registerFrom">
      <template #textname="{ field }">
        <div class="flex items-center gap-2">
          <span class="inline-block bg-primary-color w-3px h-1em"></span>
          <span>{{ field }}</span>
        </div>
      </template>
      <template #secondarytitle="{ field }">
        <div class="ml-10"> {{ field }} </div>
      </template>
    </BasicForm>
  </BasicModal>
</template>

<style lang="less" scoped>
  :deep {
    .ant-form-item-label {
      padding-right: 0 !important;
    }

    .ant-form-item-label > label[for='form_item_townId'],
    .ant-form-item-label > label[for='form_item_villageId'] {
      width: 0 !important;
      margin-left: 8px;
    }
  }
</style>
