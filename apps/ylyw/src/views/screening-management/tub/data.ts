import {
  DictEnum,
  getAdministrativeList,
  getDictItemList,
  queryOrganizationList,
} from '@ft/internal/api';
import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { getBirthDateAndGender } from '@ft/internal/utils';
import dayjs from 'dayjs';
import { debounce } from 'lodash-es';

const debounceValidator = debounce((rule, value, callback) => {
  value?.includes('班')
    ? callback()
    : callback(new Error('筛查任务类型为学校,筛查地点必须包含班级信息'));
}, 300);
export const addFormSchema = (touchScreeningType: string): FormSchema[] => {
  return [
    {
      field: 'touchScreeningType',
      component: 'Input',
      label: '',
      show: false,
    },
    {
      field: 'taskId',
      component: 'Input',
      label: '',
      show: false,
    },
    {
      field: 'touchPatientId',
      component: 'Input',
      label: '',
      show: false,
    },
    {
      field: 'touchPatientName',
      component: 'Input',
      label: '',
      show: false,
    },
    {
      field: 'id',
      component: 'Input',
      label: '',
      show: false,
    },
    {
      field: '筛查信息',
      component: 'ApiSelect',
      label: ' ',
      slot: 'textname',
      colProps: { span: 2 },
      disabledLabelWidth: true,
    },
    {
      field: 'screeningTime',
      component: 'DatePicker',
      required: true,
      label: '筛查日期',
      colProps: { span: 8 },
      itemProps: { wrapperCol: { span: 14 } },
      componentProps: {
        valueFormat: 'YYYY-MM-DD',
        format: 'YYYY-MM-DD',
        showTime: false,
        style: {
          width: '100%',
        },
        getPopupContainer: () => document.body,
      },
      isHandleDateDefaultValue: false,
      defaultValue: dayjs().format('YYYY-MM-DD'),
    },
    {
      field: 'screeningDisease',
      component: 'ApiSelect',
      label: '筛查病种',
      componentProps: ({ formModel }) => {
        return {
          api: () => getDictItemList(DictEnum.SCREENING_DISEASE),
          labelField: 'dictItemName',
          valueToString: formModel?.screeningDisease ? true : false,
          valueField: 'dictItemCode',
          disabled: true,
        };
      },
      defaultValue: '5',
      colProps: { span: 7 },
      itemProps: { wrapperCol: { span: 14 } },
    },
    {
      field: 'divisionName',
      component: 'Input',
      label: '',
      show: false,
    },
    {
      field: 'divisionId',
      component: 'ApiSelect',
      required: true,
      label: '筛查地区',
      componentProps: ({ formModel }) => {
        return {
          api: getAdministrativeList,
          labelField: 'name',
          valueField: 'code',
          onChange: (_, opt) => {
            if (opt?.label) formModel.divisionName = opt?.label;
          },
        };
      },
      colProps: { span: 7 },
      itemProps: { wrapperCol: { span: 14 } },
    },
    {
      field: 'screeningSchool',
      component: 'Input',
      label: '筛查学校',
      labelWidth: 245,
      // required: true,
      rules: [
        {
          required: true,
          validator: debounceValidator,
          trigger: 'blur',
        },
      ],
      ifShow: () => touchScreeningType === '2',
      colProps: { span: 24 },
      itemProps: { wrapperCol: { span: 6 } },
    },
    {
      field: 'screeningSchool',
      component: 'Input',
      label: '筛查地点',
      labelWidth: 245,
      // required: true,
      colProps: { span: 24 },
      itemProps: { wrapperCol: { span: 6 } },
      ifShow: () => touchScreeningType === '3',
    },
    // {
    //   field: 'screeningSchool',
    //   component: 'Input',
    //   label: '筛查学校',
    //   // required: true,
    //   ifShow: () => touchScreeningType === '2',
    //   colProps: { span: 12 },
    // },
    // {
    //   field: 'screeningCommunity',
    //   label: '所属社区',
    //   component: 'ApiSelect',
    //   colProps: { span: 6 },
    //   componentProps: ({}) => {
    //     return {
    //       api: () =>
    //         queryOrganizationList({
    //           orgType: 2,
    //           orgCategory: '6',
    //           enableFlag: 0,
    //           // divisionId: formModel.areaCode,
    //         }),
    //       labelField: 'orgName',
    //       valueField: 'orgCode',
    //       showSearch: true,
    //       optionFilterProp: 'label',
    //       getPopupContainer() {
    //         return document.body;
    //       },
    //     };
    //   },
    //   ifShow: () => touchScreeningType === '3',
    // },
    {
      field: '筛查者信息',
      component: 'Input',
      label: ' ',
      slot: 'textname',
      colProps: { span: 2 },
      disabledLabelWidth: true,
    },
    {
      field: 'screeningName',
      component: 'Input',
      required: true,
      label: '筛查者姓名',
      colProps: { span: touchScreeningType === '1' ? 8 : 10 },
      itemProps: { wrapperCol: { span: 14 } },
    },
    {
      field: 'idCard',
      component: 'Input',
      required: true,
      label: '身份证号',
      rules: [
        {
          pattern:
            /^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
          message: '请输入正确的身份证号',
        },
      ],
      componentProps: ({ formModel }) => {
        return {
          maxLength: 18,
          minLength: 16,
          onChange(e) {
            if (e?.target?.value) {
              const result = getBirthDateAndGender(e.target.value);
              formModel.sex = result.gender;
              formModel.age = result.age;
            }
          },
        };
      },
      colProps: { span: 10 },
      itemProps: { wrapperCol: { span: 14 } },
    },
    {
      field: 'sexName',
      label: '性别',
      component: 'Input',
      show: false,
    },
    {
      field: 'sex',
      component: 'RadioGroup',
      labelWidth: 245,
      required: true,
      label: '性别',
      colProps: { span: 8 },
      itemProps: { wrapperCol: { span: 14 } },
      componentProps: ({ formModel }) => {
        return {
          options: [
            { label: '男', value: 1 },
            { label: '女', value: 2 },
          ],
          onChange: (_, opt) => {
            if (opt?.label) formModel.sexName = opt?.label;
          },
        };
      },
    },
    {
      field: 'age',
      component: 'InputNumber',
      label: '年龄',
      required: true,
      colProps: { span: 7 },
      itemProps: { wrapperCol: { span: 14 } },
      componentProps: {
        min: 0,
        style: {
          width: '100%',
        },
      },
    },
    {
      field: 'phone',
      component: 'Input',
      required: true,
      label: '联系方式',
      rules: [
        {
          pattern: /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
          message: '请输入正确的手机号码',
        },
      ],
      componentProps: {
        style: {
          width: '100%',
        },
      },
      colProps: { span: 7 },
      itemProps: { wrapperCol: { span: 14 } },
    },
    {
      field: '',
      component: 'Divider',
      label: ' ',
      componentProps: {
        style: {
          display: 'none',
        },
      },
      colProps: { span: 24 },
      itemProps: { wrapperCol: { span: 24 } },
    },
    {
      field: 'areaCode',
      label: '现住址所属区划',
      labelWidth: 245,
      // required: true,
      component: 'Select',
      defaultValue: '湖北省',
      componentProps: {
        options: [
          {
            label: '湖北省',
            value: '湖北省',
          },
        ],
      },
      // componentProps: ({ formModel }) => {
      //   return {
      //     api: getAdministrativeList,
      //     placeholder: '请选择现住址所属区划',
      //     labelField: 'name',
      //     valueField: 'code',
      //     showSearch: true,
      //     optionFilterProp: 'label',
      //     getPopupContainer() {
      //       return document.body;
      //     },
      //     onChange(_, opt) {
      //       if (opt?.label) formModel.areaName = opt?.label;
      //       formModel.townId = undefined;
      //       formModel.villageId = undefined;
      //     },
      //   };
      // },
      colProps: {
        span: 8,
      },
      itemProps: {
        wrapperCol: {
          span: 19,
        },
      },
    },
    {
      field: 'townName',
      label: '所属乡镇/街道名称',
      component: 'Input',
      show: false,
    },
    {
      field: 'townId',
      label: ' ',
      // required: true,
      component: 'Select',
      defaultValue: '宜昌市',
      componentProps: {
        options: [
          {
            label: '宜昌市',
            value: '宜昌市',
          },
        ],
      },
      disabledLabelWidth: true,
      // componentProps: ({ formModel }) => {
      //   return {
      //     placeholder: '请选择所属乡镇/街道名称',
      //     api: () => formModel.areaCode && getAdministrativeList(formModel.areaCode),
      //     labelField: 'name',
      //     valueField: 'code',
      //     showSearch: true,
      //     optionFilterProp: 'label',
      //     getPopupContainer() {
      //       return document.body;
      //     },
      //     onChange(_, opt) {
      //       if (opt?.label) formModel.townName = opt?.label;
      //       formModel.villageId = '';
      //     },
      //   };
      // },
      colProps: {
        span: 7,
      },
      itemProps: {
        // class: 'city-code',
        wrapperCol: {
          span: 24,
        },
      },
    },
    {
      field: 'regionName',
      label: '所属区划名称',
      component: 'Input',
      show: false,
    },
    {
      field: 'regionId',
      label: ' ',
      // required: true,
      component: 'ApiSelect',
      disabledLabelWidth: true,
      componentProps: ({ formModel }) => {
        return {
          api: getAdministrativeList,
          labelField: 'name',
          valueField: 'code',
          getPopupContainer: (trigger: Element) => trigger.parentNode,
          onChange: (_, opt) => {
            if (opt?.label) formModel.regionName = opt?.label;
          },
        };
      },
      colProps: {
        span: 7,
      },
      itemProps: {
        wrapperCol: {
          span: 24,
        },
      },
    },
    {
      field: 'address',
      component: 'InputTextArea',
      label: '详细地址',
      labelWidth: 245,
      componentProps: {
        rows: 4,
        autoSize: { minRows: 4, maxRows: 4 },
      },
      colProps: { span: 24 },
      itemProps: { wrapperCol: { span: 19 } },
    },
    {
      field: '症状筛查',
      component: 'Input',
      label: ' ',
      slot: 'textname',
      colProps: { span: 2 },
      disabledLabelWidth: true,
    },
    {
      field: 'suspiciousResultName',
      label: '肺结核可疑症状',
      component: 'Input',
      show: false,
    },
    {
      field: 'suspiciousResult',
      component: 'ApiSelect',
      label: '肺结核可疑症状',
      componentProps: ({ formModel }) => {
        return {
          api: () => getDictItemList(DictEnum.SUSPICIOUS_RESULT),
          labelField: 'dictItemName',
          valueField: 'dictItemCode',
          maxTagCount: 2,
          //多选
          mode: 'multiple',
          placeholder: '请选择肺结核可疑症状',
          multiple: true,
          getPopupContainer: () => document.body,
          onChange: (_, opt) => {
            if (opt && opt.length > 0) {
              formModel.suspiciousResultName = opt.map((item: any) => item.label).join(',');
            }
          },
        };
      },
      colProps: { span: 22 },
      itemProps: { wrapperCol: { span: 9 } },
    },
    {
      field: '感染监测',
      component: 'Input',
      label: ' ',
      slot: 'textname',
      colProps: { span: 2 },
      disabledLabelWidth: true,
    },
    {
      field: 'TST检测',
      component: 'Input',
      label: '',
      slot: 'secondarytitle',
      colProps: { span: 22 },
      disabledLabelWidth: true,
    },
    //首次检测日期
    {
      field: 'tstFirstDate',
      component: 'DatePicker',
      labelWidth: 245,
      label: '首次检测日期',
      componentProps: {
        style: { width: '100%' },
        showTime: true,
        valueFormat: 'YYYY-MM-DD',
        format: 'YYYY-MM-DD',
        getPopupContainer: () => document.body,
      },
      colProps: { span: 12 },
      itemProps: { wrapperCol: { span: 14 } },
      isHandleDateDefaultValue: false,
      defaultValue: dayjs().format('YYYY-MM-DD'),
    },
    //首次横径*纵径(mm)
    {
      field: 'tstFirstValue',
      component: 'Input',
      label: '首次横径*纵径(mm)',
      colProps: { span: 12 },
      itemProps: { wrapperCol: { span: 14 } },
    },
    //二次检测日期
    {
      field: 'tstSecondDate',
      component: 'DatePicker',
      label: '二次检测日期',
      labelWidth: 245,
      componentProps: {
        style: { width: '100%' },
        showTime: true,
        valueFormat: 'YYYY-MM-DD',
        format: 'YYYY-MM-DD',
        getPopupContainer: () => document.body,
      },
      colProps: { span: 12 },
      itemProps: { wrapperCol: { span: 14 } },
    },
    //二次横径*纵径(mm)
    {
      field: 'tstSecondValue',
      component: 'Input',
      label: '二次横径*纵径(mm)',
      colProps: { span: 12 },
      itemProps: { wrapperCol: { span: 14 } },
    },
    {
      field: '',
      component: 'Divider',
      label: ' ',
      componentProps: {
        style: {
          display: 'none',
        },
      },
      colProps: { span: 2 },
    },
    {
      field: 'IGRA检测',
      component: 'Input',
      label: '',
      slot: 'secondarytitle',
      colProps: { span: 22 },
      itemProps: { wrapperCol: { span: 24 } },
      disabledLabelWidth: true,
    },
    //检查日期
    {
      field: 'igraDate',
      component: 'DatePicker',
      label: '检查日期',
      labelWidth: 245,
      componentProps: {
        style: { width: '100%' },
        showTime: true,
        valueFormat: 'YYYY-MM-DD',
        format: 'YYYY-MM-DD',
        getPopupContainer: () => document.body,
      },
      colProps: { span: 12 },
      itemProps: { wrapperCol: { span: 14 } },
    },
    {
      field: 'igraResultName',
      component: 'Input',
      label: '',
      show: false,
    },
    //检查结果
    {
      field: 'igraResult',
      component: 'ApiSelect',
      label: '检查结果',
      componentProps: ({ formModel }) => {
        return {
          api: () => getDictItemList(DictEnum.IGRA_RESULT),
          labelField: 'dictItemName',
          valueField: 'dictItemCode',
          valueToString: formModel?.igraResult ? true : false,
          onChange: (_, opt) => {
            if (opt?.label) formModel.igraResultName = opt.label;
          },
        };
      },
      colProps: { span: 12 },
      itemProps: { wrapperCol: { span: 14 } },
    },
    //胸部X光片检查
    {
      field: '胸部X光片检查',
      component: 'Input',
      label: ' ',
      slot: 'textname',
      colProps: { span: 3 },
      disabledLabelWidth: true,
    },
    //检查日期
    {
      field: 'ctCheckDate',
      component: 'DatePicker',
      labelWidth: 100,
      label: '检查日期',
      componentProps: {
        style: { width: '100%' },
        showTime: true,
        valueFormat: 'YYYY-MM-DD',
        format: 'YYYY-MM-DD',
        getPopupContainer: () => document.body,
      },
      colProps: { span: 7 },
      itemProps: { wrapperCol: { span: 14 } },
    },
    //检查结果
    {
      field: 'ctResultName',
      component: 'Input',
      label: '',
      show: false,
    },
    //检查结果
    {
      field: 'ctResult',
      component: 'ApiSelect',
      label: '检查结果',
      labelWidth: 100,
      componentProps: ({ formModel }) => {
        return {
          api: () => getDictItemList(DictEnum.CT_RESULT),
          labelField: 'dictItemName',
          valueField: 'dictItemCode',
          valueToString: formModel?.ctResult ? true : false,
          onChange: (_, opt) => {
            if (opt?.label) formModel.ctResultName = opt.label;
          },
        };
      },
      colProps: { span: 8 },
      itemProps: { wrapperCol: { span: 12 } },
    },
    {
      field: '',
      component: 'Divider',
      label: ' ',
      componentProps: {
        style: {
          display: 'none',
        },
      },
      colProps: { span: 24 },
      itemProps: { wrapperCol: { span: 24 } },
    },
    //痰检
    {
      field: '痰检',
      component: 'Input',
      labelWidth: 245,
      label: ' ',
      slot: 'textname',
      colProps: { span: 2 },
      disabledLabelWidth: true,
    },
    //留痰日期
    {
      field: 'phlegmDate',
      component: 'DatePicker',
      label: '留痰日期',
      componentProps: {
        style: { width: '100%' },
        showTime: true,
        valueFormat: 'YYYY-MM-DD',
        format: 'YYYY-MM-DD',
        getPopupContainer: () => document.body,
      },
      colProps: { span: 7 },
      itemProps: { wrapperCol: { span: 14 } },
    },
    //检查方法
    {
      field: 'phlegmCheckName',
      component: 'Input',
      label: '',
      show: false,
    },
    //检查结果
    {
      field: 'phlegmCheck',
      component: 'ApiSelect',
      label: '检查方法',
      componentProps: ({ formModel }) => {
        return {
          api: () => getDictItemList(DictEnum.PHLEGM_CHECK),
          labelField: 'dictItemName',
          valueField: 'dictItemCode',
          mode: 'multiple',

          onChange: (_, opt) => {
            if (opt && opt.length > 0) {
              formModel.phlegmCheckName = opt.map((item: any) => item.label).join(',');
            }
          },
        };
      },
      colProps: { span: 7 },
      itemProps: { wrapperCol: { span: 14 } },
    },
    //检查结果
    {
      field: 'phlegmResultName',
      component: 'Input',
      label: '',
      show: false,
    },
    //检查结果
    {
      field: 'phlegmResult',
      component: 'ApiSelect',
      label: '检查结果',
      componentProps: ({ formModel }) => {
        return {
          api: () => getDictItemList(DictEnum.PHLEGM_RESULT),
          labelField: 'dictItemName',
          valueField: 'dictItemCode',
          valueToString: formModel?.phlegmResult ? true : false,
          onChange: (_, opt) => {
            if (opt?.label) formModel.phlegmResultName = opt.label;
          },
        };
      },
      colProps: { span: 7 },
      itemProps: { wrapperCol: { span: 14 } },
    },

    {
      field: '筛查结果',
      component: 'Input',
      label: ' ',
      slot: 'textname',
      colProps: { span: 2 },
      disabledLabelWidth: true,
    },
    //筛查结果
    {
      field: 'screeningResult',
      component: 'Input',
      label: '',
      show: false,
    },
    //筛查结果 InputTextarea
    {
      field: 'screeningResultValue',
      component: 'ApiRadioGroup',
      label: '筛查结果',

      componentProps: ({ formModel }) => {
        return {
          api: () => getDictItemList(DictEnum.SCREENING_RESULT),
          labelField: 'dictItemName',
          valueField: 'dictItemCode',
          valueToString: true,
          onChange: (_, opt) => {
            if (opt?.label) formModel.screeningResult = opt.label;
            if (opt?.value === '5') formModel.screeningResult = '';
          },
          // valueToString: true,
        };
      },
      colProps: { span: 22 },
      itemProps: { wrapperCol: { span: 14 } },
    },
    {
      field: 'screeningResult',
      component: 'Input',
      label: '其他筛查结果',
      labelWidth: 245,
      required: true,
      colProps: { span: 24 },
      itemProps: { wrapperCol: { span: 7 } },
      ifShow: ({ model }) => model.screeningResultValue === '5',
    },
    //是否为预防性治疗对象 RadioGroup
    {
      field: 'objectFlag',
      component: 'RadioGroup',
      label: '是否为预防性治疗对象',
      labelWidth: 245,
      componentProps: {
        options: [
          { label: '是', value: 0 },
          { label: '否', value: 1 },
        ],
      },
      colProps: { span: 24 },
      itemProps: { wrapperCol: { span: 20 } },
    },
    //是否接受预防性治疗 RadioGroup
    {
      field: 'acceptFlag',
      component: 'RadioGroup',
      label: '是否接受预防性治疗',
      labelWidth: 245,
      colProps: { span: 24 },
      itemProps: { wrapperCol: { span: 20 } },
      componentProps: {
        options: [
          { label: '是', value: 0 },
          { label: '否', value: 1 },
        ],
      },
    },
    //是否完成预防性治疗 RadioGroup
    {
      field: 'finishFlag',
      component: 'RadioGroup',
      label: '是否完成预防性治疗',
      labelWidth: 245,
      colProps: { span: 24 },
      itemProps: { wrapperCol: { span: 20 } },
      componentProps: {
        options: [
          { label: '完成', value: 0 },
          { label: '未完成', value: 1 },
        ],
      },
    },
    //备注
    {
      field: 'remark',
      component: 'InputTextArea',
      label: '备注',
      labelWidth: 245,
      colProps: { span: 24 },
      itemProps: { wrapperCol: { span: 20 } },
      componentProps: {
        rows: 4,
        autoSize: { minRows: 3, maxRows: 3 },
      },
    },
  ];
};

export const screeningAddFormSchema: FormSchema[] = [
  {
    field: 'screeningDisease',
    component: 'Input',
    label: '筛查病种',
    defaultValue: '5',
    show: false,
  },
  {
    field: 'touchScreeningType',
    component: 'Input',
    label: '',
    show: false,
  },
  {
    field: 'taskId',
    component: 'Input',
    label: '',
    show: false,
  },
  {
    field: 'touchPatientId',
    component: 'Input',
    label: '',
    show: false,
  },
  {
    field: 'touchPatientName',
    component: 'Input',
    label: '',
    show: false,
  },
  {
    field: 'id',
    component: 'Input',
    label: '',
    show: false,
  },
  {
    field: '筛查信息',
    component: 'ApiSelect',
    label: ' ',
    slot: 'textname',
    colProps: { span: 2 },
    disabledLabelWidth: true,
  },
  {
    field: 'screeningTime',
    component: 'DatePicker',
    required: true,
    label: '筛查日期',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      showTime: false,
      style: {
        width: '100%',
      },
      getPopupContainer: () => document.body,
    },
    isHandleDateDefaultValue: false,
    defaultValue: dayjs().format('YYYY-MM-DD'),
  },
  {
    field: 'etiologyResultName',
    component: 'ApiSelect',
    label: '患者病原性结果名称',
    show: false,
  },
  {
    field: 'etiologyResult',
    component: 'ApiSelect',
    required: true,
    label: '患者病原性结果',
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.ETIOLOGY_RESULT),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        valueToString: formModel?.etiologyResult ? true : false,
        onChange: (_, opt) => {
          if (opt?.label) formModel.etiologyResultName = opt.label;
        },
      };
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: '',
    component: 'Divider',
    label: ' ',
    componentProps: {
      style: {
        display: 'none',
      },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: 'divisionName',
    component: 'Input',
    label: '',
    show: false,
  },
  {
    field: '筛查者信息',
    component: 'Input',
    label: ' ',
    slot: 'textname',
    colProps: { span: 2 },
    disabledLabelWidth: true,
  },
  {
    field: 'screeningName',
    component: 'Input',
    required: true,
    label: '筛查者姓名',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'idCard',
    component: 'Input',
    required: true,
    label: '身份证号',
    rules: [
      {
        pattern:
          /^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
        message: '请输入正确的身份证号',
      },
    ],
    componentProps: ({ formModel }) => {
      return {
        maxLength: 18,
        minLength: 16,
        onChange(e) {
          if (e?.target?.value) {
            const result = getBirthDateAndGender(e.target.value);
            formModel.sex = result.gender;
            formModel.age = result.age;
          }
        },
      };
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'sexName',
    label: '性别',
    component: 'Input',
    show: false,
  },
  {
    field: 'sex',
    component: 'RadioGroup',
    required: true,
    label: '性别',
    labelWidth: 120,
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
    componentProps: ({ formModel }) => {
      return {
        options: [
          { label: '男', value: 1 },
          { label: '女', value: 2 },
        ],
        onChange: (_, opt) => {
          if (opt?.label) formModel.sexName = opt?.label;
        },
      };
    },
  },
  {
    field: 'age',
    component: 'InputNumber',
    label: '年龄',
    labelWidth: 245,
    required: true,
    colProps: { span: 10 },
    itemProps: { wrapperCol: { span: 11 } },
    componentProps: {
      min: 0,
      style: {
        width: '100%',
      },
    },
  },
  {
    field: 'regionName',
    label: '所属区划名称',
    component: 'Input',
    show: false,
  },
  {
    field: 'regionId',
    label: '区划',
    required: true,
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: getAdministrativeList,
        labelField: 'name',
        valueField: 'code',
        getPopupContainer: (trigger: Element) => trigger.parentNode,
        onChange: (_, opt) => {
          if (opt?.label) formModel.regionName = opt?.label;
        },
      };
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'phone',
    component: 'Input',
    required: true,
    label: '联系方式',
    labelWidth: 120,
    rules: [
      {
        pattern: /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
        message: '请输入正确的手机号码',
      },
    ],
    componentProps: {
      style: {
        width: '100%',
      },
    },
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 20 } },
  },
  {
    field: '接触者情况',
    component: 'Input',
    label: ' ',
    slot: 'textname',
    colProps: { span: 2 },
    disabledLabelWidth: true,
  },
  {
    field: 'contactTypeName',
    component: 'ApiSelect',
    label: '接触类型名称',
    show: false,
  },
  {
    field: 'contactType',
    component: 'ApiSelect',
    label: '接触类型',
    required: true,
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.CONTACT_TYPE),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        valueToString: formModel?.contactType ? true : false,
        onChange: (_, opt) => {
          if (opt?.label) formModel.contactTypeName = opt.label;
        },
        getPopupContainer: (trigger: Element) => trigger.parentNode as HTMLElement,
      };
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'contactPlaceName',
    component: 'ApiSelect',
    label: '接触场所名称',
    show: false,
  },
  {
    field: 'contactPlace',
    component: 'ApiSelect',
    label: '接触场所',
    required: true,
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.CONTACT_PLACE),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        valueToString: formModel?.contactPlace ? true : false,
        onChange: (_, opt) => {
          if (opt?.label) {
            formModel.contactPlaceName = opt.label;
          } else {
            formModel.contactPlaceRemark = undefined;
            formModel.contactPlaceName = undefined;
          }
        },
        getPopupContainer: (trigger: Element) => trigger.parentNode as HTMLElement,
      };
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'contactPlaceRemark',
    label: '其他场所',
    required: true,
    labelWidth: 120,
    component: 'Input',
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 20 } },
    ifShow: ({ model }) => model?.contactPlace == '8',
  },
  {
    field: '',
    component: 'Divider',
    label: ' ',
    componentProps: {
      style: {
        display: 'none',
      },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: '症状筛查',
    component: 'Input',
    label: ' ',
    slot: 'textname',
    colProps: { span: 2 },
    disabledLabelWidth: true,
  },
  {
    field: 'suspiciousResultName',
    label: '肺结核可疑症状',
    component: 'Input',
    show: false,
  },
  {
    field: 'suspiciousResult',
    component: 'ApiSelect',
    required: true,
    label: '肺结核可疑症状',
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.SUSPICIOUS_RESULT),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        maxTagCount: 2,
        //多选
        mode: 'multiple',
        placeholder: '请选择肺结核可疑症状',
        multiple: true,
        getPopupContainer: (trigger: Element) => trigger.parentNode,
        onChange: (_, opt) => {
          if (opt && opt.length > 0) {
            formModel.suspiciousResultName = opt.map((item: any) => item.label).join(',');
          } else {
            formModel.suspiciousResultName = undefined;
            formModel.suspiciousResultRemark = undefined;
          }
        },
      };
    },
    colProps: { span: 10 },
    itemProps: { wrapperCol: { span: 20 } },
  },
  {
    field: 'suspiciousResultRemark',
    component: 'Input',
    label: '其他症状',
    required: true,
    labelWidth: 100,
    ifShow: ({ model }) => {
      return model?.suspiciousResult?.indexOf('10') > -1;
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    field: '',
    component: 'Divider',
    label: ' ',
    componentProps: {
      style: {
        display: 'none',
      },
    },
    colProps: { span: 24 },
  },
  {
    field: '感染监测',
    component: 'Input',
    label: ' ',
    slot: 'textname',
    colProps: { span: 2 },
    disabledLabelWidth: true,
  },
  {
    field: 'TST检测',
    component: 'Input',
    label: '',
    slot: 'secondarytitle',
    colProps: { span: 22 },
    disabledLabelWidth: true,
  },
  //首次检测日期
  {
    field: 'tstFirstDate',
    component: 'DatePicker',
    labelWidth: 245,
    label: '首次检测日期',
    componentProps: {
      style: { width: '100%' },
      showTime: true,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      getPopupContainer: (trigger: Element) => trigger.parentNode as HTMLElement,
    },
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 14 } },
    isHandleDateDefaultValue: false,
    defaultValue: dayjs().format('YYYY-MM-DD'),
  },
  //首次横径*纵径(mm)
  {
    field: 'tstFirstValue',
    component: 'Input',
    label: '首次横径*纵径(mm)',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  //二次检测日期
  {
    field: 'tstSecondDate',
    component: 'DatePicker',
    label: '二次检测日期',
    labelWidth: 245,
    componentProps: {
      style: { width: '100%' },
      showTime: true,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      getPopupContainer: () => document.body,
    },
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 14 } },
    defaultValue: dayjs().format('YYYY-MM-DD'),
  },
  //二次横径*纵径(mm)
  {
    field: 'tstSecondValue',
    component: 'Input',
    label: '二次横径*纵径(mm)',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: '',
    component: 'Divider',
    label: ' ',
    componentProps: {
      style: {
        display: 'none',
      },
    },
    colProps: { span: 2 },
  },
  {
    field: 'IGRA检测',
    component: 'Input',
    label: '',
    slot: 'secondarytitle',
    colProps: { span: 22 },
    itemProps: { wrapperCol: { span: 24 } },
    disabledLabelWidth: true,
  },
  //检查日期
  {
    field: 'igraDate',
    component: 'DatePicker',
    label: '检查日期',
    labelWidth: 245,
    componentProps: {
      style: { width: '100%' },
      showTime: true,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      getPopupContainer: () => document.body,
    },
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 14 } },
    defaultValue: dayjs().format('YYYY-MM-DD'),
  },
  {
    field: 'igraResultName',
    component: 'Input',
    label: '',
    show: false,
  },
  //检查结果
  {
    field: 'igraResult',
    component: 'ApiSelect',
    label: '检查结果',
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.IGRA_RESULT),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        valueToString: formModel?.igraResult ? true : false,
        getPopupContainer: (trigger: Element) => trigger.parentNode as HTMLElement,
        onChange: (_, opt) => {
          if (opt?.label) formModel.igraResultName = opt.label;
        },
      };
    },
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  //胸部影像学检查
  {
    field: '胸部影像学检查',
    component: 'Input',
    label: ' ',
    slot: 'textname',
    colProps: { span: 3 },
    disabledLabelWidth: true,
  },
  //检查日期
  {
    field: 'ctCheckDate',
    component: 'DatePicker',
    labelWidth: 100,
    label: '检查日期',
    componentProps: {
      style: { width: '100%' },
      showTime: true,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      getPopupContainer: () => document.body,
    },
    colProps: { span: 7 },
    itemProps: { wrapperCol: { span: 14 } },
    defaultValue: dayjs().format('YYYY-MM-DD'),
  },
  {
    field: 'inspectTypeName',
    component: 'ApiSelect',
    label: '检查类型名称',
    show: false,
  },
  {
    field: 'inspectType',
    component: 'ApiSelect',
    label: '检查类型',
    labelWidth: 100,
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.INSPECT_TYPE),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        valueToString: formModel?.inspectType ? true : false,
        getPopupContainer: (trigger: Element) => trigger.parentNode as HTMLElement,
        onChange: (_, opt) => {
          if (opt?.label) {
            formModel.inspectTypeName = opt.label;
          } else {
            formModel.inspectTypeRemark = undefined;
            formModel.inspectTypeName = undefined;
          }
        },
      };
    },
    colProps: { span: 7 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'inspectTypeRemark',
    label: '其他类型',
    required: true,
    labelWidth: 100,
    component: 'Input',
    colProps: { span: 7 },
    itemProps: { wrapperCol: { span: 14 } },
    ifShow: ({ model }) => model?.inspectType == '3',
  },

  {
    field: '',
    component: 'Divider',
    label: ' ',
    componentProps: {
      style: {
        display: 'none',
      },
    },
    colProps: { span: 24 },
  },
  //检查结果
  {
    field: 'ctResultName',
    component: 'Input',
    label: '',
    show: false,
  },
  //检查结果
  {
    field: 'ctResult',
    component: 'ApiSelect',
    label: '检查结果',
    labelWidth: 245,
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.CT_RESULT),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        valueToString: formModel?.ctResult ? true : false,
        getPopupContainer: (trigger: Element) => trigger.parentNode as HTMLElement,
        onChange: (_, opt) => {
          if (opt?.label) {
            formModel.ctResultName = opt.label;
          } else {
            formModel.imagingResultRemark = undefined;
            formModel.ctResultName = undefined;
          }
        },
      };
    },
    colProps: { span: 10 },
    itemProps: { wrapperCol: { span: 10 } },
  },
  {
    field: 'imagingResultRemark',
    label: '其他结果',
    required: true,
    labelWidth: 100,
    component: 'Input',
    colProps: { span: 7 },
    itemProps: { wrapperCol: { span: 14 } },
    ifShow: ({ model }) => model?.ctResult == '3',
  },
  {
    field: '',
    component: 'Divider',
    label: ' ',
    componentProps: {
      style: {
        display: 'none',
      },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  //痰检
  {
    field: '痰检',
    component: 'Input',
    labelWidth: 245,
    label: ' ',
    slot: 'textname',
    colProps: { span: 2 },
    disabledLabelWidth: true,
  },
  //留痰日期
  {
    field: 'phlegmDate',
    component: 'DatePicker',
    label: '留痰日期',
    componentProps: {
      style: { width: '100%' },
      showTime: true,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      getPopupContainer: () => document.body,
    },
    colProps: { span: 7 },
    itemProps: { wrapperCol: { span: 14 } },
    defaultValue: dayjs().format('YYYY-MM-DD'),
  },
  //检查方法
  {
    field: 'phlegmCheckName',
    component: 'Input',
    label: '',
    show: false,
  },
  //检查结果
  {
    field: 'phlegmCheck',
    component: 'ApiSelect',
    label: '检查方法',
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.PHLEGM_CHECK),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        maxTagCount: 1,
        //多选
        mode: 'multiple',
        maxTagTextLength: 2,
        multiple: true,
        getPopupContainer: (trigger: Element) => trigger.parentNode,
        onChange: (_, opt) => {
          if (opt && opt.length > 0) {
            formModel.phlegmCheckName = opt.map((item: any) => item.label).join(',');
          }
        },
      };
    },
    colProps: { span: 7 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  //检查结果
  {
    field: 'phlegmResultName',
    component: 'Input',
    label: '',
    show: false,
  },
  //检查结果
  {
    field: 'phlegmResult',
    component: 'ApiSelect',
    label: '检查结果',
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.PHLEGM_RESULT),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        valueToString: formModel?.phlegmResult ? true : false,
        getPopupContainer: (trigger: Element) => trigger.parentNode,
        onChange: (_, opt) => {
          if (opt?.label) formModel.phlegmResultName = opt.label;
        },
      };
    },
    colProps: { span: 7 },
    itemProps: { wrapperCol: { span: 14 } },
  },

  {
    field: '筛查结果',
    component: 'Input',
    label: ' ',
    slot: 'textname',
    colProps: { span: 2 },
    disabledLabelWidth: true,
  },
  //筛查结果
  {
    field: 'screeningResult',
    component: 'Input',
    label: '',
    show: false,
  },
  {
    field: 'screeningResultValue',
    component: 'ApiRadioGroup',
    label: '诊断',
    required: true,
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.SCREENING_RESULT),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        valueToString: true,
        getPopupContainer: (trigger: Element) => trigger.parentNode,
        onChange: (_, opt) => {
          if (opt?.label) {
            formModel.screeningResult = opt.label;
          } else {
            formModel.screeningResultRemark = undefined;
            formModel.screeningResult = undefined;
          }
        },
        // valueToString: true,
      };
    },
    colProps: { span: 22 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'screeningResultRemark',
    component: 'Input',
    label: '其他诊断结果',
    labelWidth: 245,
    required: true,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 7 } },
    ifShow: ({ model }) => model.screeningResultValue == '4',
  },
  //是否为预防性治疗对象 RadioGroup
  {
    field: 'objectFlag',
    component: 'RadioGroup',
    label: '是否符合预防性治疗',
    required: true,
    labelWidth: 245,
    componentProps: ({ formModel }) => {
      return {
        options: [
          { label: '是', value: 0 },
          { label: '否', value: 1 },
        ],
        onChange: () => {
          if (formModel.objectFlag == 1 && formModel.acceptFlag == 1) {
            formModel.preventiveTreatmentPlan = undefined;
            formModel.finishFlag = undefined;
            formModel.preventiveTreatmentReason = undefined;
            formModel.preventiveTreatmentReasonName = undefined;
            formModel.preventiveTreatmentRemark = undefined;
            formModel.remark = undefined;
          }
        },
      };
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  //是否接受预防性治疗 RadioGroup
  {
    field: 'acceptFlag',
    component: 'RadioGroup',
    label: '是否开始预防性治疗',
    labelWidth: 245,
    required: true,
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
    componentProps: ({ formModel }) => {
      return {
        options: [
          { label: '是', value: 0 },
          { label: '否', value: 1 },
        ],
        onChange: () => {
          if (formModel.objectFlag == 1 && formModel.acceptFlag == 1) {
            formModel.preventiveTreatmentPlan = undefined;
            formModel.finishFlag = undefined;
            formModel.preventiveTreatmentReason = undefined;
            formModel.preventiveTreatmentReasonName = undefined;
            formModel.preventiveTreatmentRemark = undefined;
            formModel.remark = undefined;
          }
        },
      };
    },
  },
  //是否完成预防性治疗 RadioGroup
  {
    field: 'finishFlag',
    component: 'RadioGroup',
    label: '是否完成预防性治疗',
    labelWidth: 245,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 20 } },
    componentProps: {
      options: [
        { label: '完成', value: 0 },
        { label: '未完成', value: 1 },
      ],
    },
    ifShow: ({ model }) => model.acceptFlag == 0 || model.objectFlag == 0,
  },
  {
    field: 'preventiveTreatmentPlan',
    component: 'InputTextArea',
    label: '预防性治疗方案',
    required: true,
    labelWidth: 245,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 20 } },
    componentProps: {
      rows: 4,
      autoSize: { minRows: 3, maxRows: 3 },
    },
    ifShow: ({ model }) => model.acceptFlag == 0 || model.objectFlag == 0,
  },
  {
    field: 'preventiveTreatmentReasonName',
    component: 'Input',
    label: '预防性治疗原因名称',
    show: false,
  },
  {
    field: 'preventiveTreatmentReason',
    component: 'ApiSelect',
    label: '预防性治疗原因',
    labelWidth: 245,
    required: true,
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.PREVENTIVE_TREATMENT_REASON),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        valueToString: formModel?.preventiveTreatmentReason ? true : false,
        getPopupContainer: (trigger: Element) => trigger.parentNode,
        onChange: (_, opt) => {
          if (opt?.label) {
            formModel.preventiveTreatmentReasonName = opt.label;
          } else {
            formModel.preventiveTreatmentRemark = undefined;
            formModel.preventiveTreatmentReasonName = undefined;
          }
        },
      };
    },
    colProps: { span: 10 },
    itemProps: { wrapperCol: { span: 14 } },
    ifShow: ({ model }) => model.acceptFlag == 0 || model.objectFlag == 0,
  },
  {
    field: 'preventiveTreatmentRemark',
    label: '其他原因',
    required: true,
    labelWidth: 120,
    component: 'Input',
    colProps: { span: 10 },
    itemProps: { wrapperCol: { span: 20 } },
    ifShow: ({ model }) =>
      model?.preventiveTreatmentReason == '5' && (model.acceptFlag == 0 || model.objectFlag == 0),
  },
  //备注
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '备注',
    labelWidth: 245,
    required: true,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 20 } },
    componentProps: {
      rows: 4,
      autoSize: { minRows: 3, maxRows: 3 },
    },
    ifShow: ({ model }) => model.acceptFlag == 0 || model.objectFlag == 0,
  },
];
export const communityScreeningAddFormSchema: FormSchema[] = [
  {
    field: 'screeningDisease',
    component: 'Input',
    label: '筛查病种',
    defaultValue: '5',
    show: false,
  },
  {
    field: 'screeningCommunity',
    component: 'Input',
    label: '所属社区',
    show: false,
  },
  {
    field: 'touchScreeningType',
    component: 'Input',
    label: '',
    show: false,
  },
  {
    field: 'taskId',
    component: 'Input',
    label: '',
    show: false,
  },
  {
    field: 'touchPatientId',
    component: 'Input',
    label: '',
    show: false,
  },
  {
    field: 'touchPatientName',
    component: 'Input',
    label: '',
    show: false,
  },
  {
    field: 'id',
    component: 'Input',
    label: '',
    show: false,
  },
  {
    field: '筛查信息',
    component: 'ApiSelect',
    label: ' ',
    slot: 'textname',
    colProps: { span: 2 },
    disabledLabelWidth: true,
  },
  {
    field: 'screeningTime',
    component: 'DatePicker',
    label: '筛查日期',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      showTime: false,
      style: {
        width: '100%',
      },
      getPopupContainer: () => document.body,
    },
    isHandleDateDefaultValue: false,
    defaultValue: dayjs().format('YYYY-MM-DD'),
  },
  {
    field: '',
    component: 'Divider',
    label: ' ',
    componentProps: {
      style: {
        display: 'none',
      },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: 'divisionName',
    component: 'Input',
    label: '',
    show: false,
  },
  {
    field: '筛查者信息',
    component: 'Input',
    label: ' ',
    slot: 'textname',
    colProps: { span: 2 },
    disabledLabelWidth: true,
  },
  {
    field: 'screeningName',
    component: 'Input',
    label: '筛查者姓名',
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'idCard',
    component: 'Input',
    label: '身份证号',
    rules: [
      {
        pattern:
          /^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
        message: '请输入正确的身份证号',
      },
    ],
    componentProps: ({ formModel }) => {
      return {
        maxLength: 18,
        minLength: 16,
        onChange(e) {
          if (e?.target?.value) {
            const result = getBirthDateAndGender(e.target.value);
            formModel.sex = result.gender;
            formModel.age = result.age;
          }
        },
      };
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'sexName',
    label: '性别',
    component: 'Input',
    show: false,
  },
  {
    field: 'sex',
    component: 'RadioGroup',
    label: '性别',
    labelWidth: 120,
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 14 } },
    componentProps: ({ formModel }) => {
      return {
        options: [
          { label: '男', value: 1 },
          { label: '女', value: 2 },
        ],
        onChange: (_, opt) => {
          if (opt?.label) formModel.sexName = opt?.label;
        },
      };
    },
  },
  {
    field: 'age',
    component: 'InputNumber',
    label: '年龄',
    labelWidth: 245,
    colProps: { span: 10 },
    itemProps: { wrapperCol: { span: 11 } },
    componentProps: {
      min: 0,
      style: {
        width: '100%',
      },
    },
  },
  {
    field: 'regionName',
    label: '所属区划名称',
    component: 'Input',
    show: false,
  },
  {
    field: 'regionId',
    label: '区划',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: getAdministrativeList,
        labelField: 'name',
        valueField: 'code',
        getPopupContainer: (trigger: Element) => trigger.parentNode,
        onChange: (_, opt) => {
          if (opt?.label) formModel.regionName = opt?.label;
        },
      };
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'phone',
    component: 'Input',
    label: '联系方式',
    labelWidth: 120,
    rules: [
      {
        pattern: /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
        message: '请输入正确的手机号码',
      },
    ],
    componentProps: {
      style: {
        width: '100%',
      },
    },
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 20 } },
  },
  {
    field: '',
    component: 'Divider',
    label: ' ',
    componentProps: {
      style: {
        display: 'none',
      },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: '症状筛查',
    component: 'Input',
    label: ' ',
    slot: 'textname',
    colProps: { span: 2 },
    disabledLabelWidth: true,
  },
  {
    field: 'suspiciousResultName',
    label: '肺结核可疑症状',
    component: 'Input',
    show: false,
  },
  {
    field: 'suspiciousResult',
    component: 'ApiSelect',
    label: '肺结核可疑症状',
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.SUSPICIOUS_RESULT),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        maxTagCount: 2,
        //多选
        mode: 'multiple',
        placeholder: '请选择肺结核可疑症状',
        multiple: true,
        getPopupContainer: (trigger: Element) => trigger.parentNode,
        onChange: (_, opt) => {
          if (opt && opt.length > 0) {
            formModel.suspiciousResultName = opt.map((item: any) => item.label).join(',');
          } else {
            formModel.suspiciousResultName = undefined;
            formModel.suspiciousResultRemark = undefined;
          }
        },
      };
    },
    colProps: { span: 10 },
    itemProps: { wrapperCol: { span: 20 } },
  },
  {
    field: 'suspiciousResultRemark',
    component: 'Input',
    label: '其他症状',
    labelWidth: 100,
    ifShow: ({ model }) => {
      return model?.suspiciousResult?.indexOf('10') > -1;
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    field: '',
    component: 'Divider',
    label: ' ',
    componentProps: {
      style: {
        display: 'none',
      },
    },
    colProps: { span: 24 },
  },
  {
    field: '感染监测',
    component: 'Input',
    label: ' ',
    slot: 'textname',
    colProps: { span: 2 },
    disabledLabelWidth: true,
  },
  {
    field: 'TST检测',
    component: 'Input',
    label: '',
    slot: 'secondarytitle',
    colProps: { span: 22 },
    disabledLabelWidth: true,
  },
  //首次检测日期
  {
    field: 'tstFirstDate',
    component: 'DatePicker',
    labelWidth: 245,
    label: '首次检测日期',
    componentProps: {
      style: { width: '100%' },
      showTime: true,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      getPopupContainer: (trigger: Element) => trigger.parentNode as HTMLElement,
    },
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 14 } },
    isHandleDateDefaultValue: false,
    defaultValue: dayjs().format('YYYY-MM-DD'),
  },
  //首次横径*纵径(mm)
  {
    field: 'tstFirstValue',
    component: 'Input',
    label: '首次横径*纵径(mm)',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  //二次检测日期
  {
    field: 'tstSecondDate',
    component: 'DatePicker',
    label: '二次检测日期',
    labelWidth: 245,
    componentProps: {
      style: { width: '100%' },
      showTime: true,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      getPopupContainer: () => document.body,
    },
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 14 } },
    defaultValue: dayjs().format('YYYY-MM-DD'),
  },
  //二次横径*纵径(mm)
  {
    field: 'tstSecondValue',
    component: 'Input',
    label: '二次横径*纵径(mm)',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: '',
    component: 'Divider',
    label: ' ',
    componentProps: {
      style: {
        display: 'none',
      },
    },
    colProps: { span: 2 },
  },
  {
    field: 'IGRA检测',
    component: 'Input',
    label: '',
    slot: 'secondarytitle',
    colProps: { span: 22 },
    itemProps: { wrapperCol: { span: 24 } },
    disabledLabelWidth: true,
  },
  //检查日期
  {
    field: 'igraDate',
    component: 'DatePicker',
    label: '检查日期',
    labelWidth: 245,
    componentProps: {
      style: { width: '100%' },
      showTime: true,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      getPopupContainer: () => document.body,
    },
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 14 } },
    defaultValue: dayjs().format('YYYY-MM-DD'),
  },
  {
    field: 'igraResultName',
    component: 'Input',
    label: '',
    show: false,
  },
  //检查结果
  {
    field: 'igraResult',
    component: 'ApiSelect',
    label: '检查结果',
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.IGRA_RESULT),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        valueToString: formModel?.igraResult ? true : false,
        getPopupContainer: (trigger: Element) => trigger.parentNode as HTMLElement,
        onChange: (_, opt) => {
          if (opt?.label) formModel.igraResultName = opt.label;
        },
      };
    },
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: '',
    component: 'Divider',
    label: ' ',
    componentProps: {
      style: {
        display: 'none',
      },
    },
    colProps: { span: 24 },
  },
  //舌试子
  {
    field: '舌试子',
    component: 'Input',
    label: ' ',
    slot: 'textname',
    colProps: { span: 3 },
    disabledLabelWidth: true,
  },
  {
    field: 'tongueTestResultName',
    component: 'Input',
    label: '检查结果',
    show: false,
  },
  {
    field: 'tongueTestResult',
    label: '检查结果',
    labelWidth: 100,
    component: 'Select',
    componentProps: ({ formModel }) => {
      return {
        style: { width: '100%' },
        options: [
          {
            label: '阳性',
            value: 2,
          },
          {
            label: '阴性',
            value: 1,
          },
        ],
        onChange: (_, opt) => {
          console.log(opt, '234');
          //@ts-ignore
          if (opt?.label) formModel.tongueTestResultName = opt?.label;
        },
      };
    },
    colProps: { span: 7 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: '',
    component: 'Divider',
    label: ' ',
    componentProps: {
      style: {
        display: 'none',
      },
    },
    colProps: { span: 24 },
  },
  //胸部影像学检查
  {
    field: '胸部影像学检查',
    component: 'Input',
    label: ' ',
    slot: 'textname',
    colProps: { span: 3 },
    disabledLabelWidth: true,
  },
  //检查日期
  {
    field: 'ctCheckDate',
    component: 'DatePicker',
    labelWidth: 100,
    label: '检查日期',
    componentProps: {
      style: { width: '100%' },
      showTime: true,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      getPopupContainer: () => document.body,
    },
    colProps: { span: 7 },
    itemProps: { wrapperCol: { span: 14 } },
    defaultValue: dayjs().format('YYYY-MM-DD'),
  },
  {
    field: 'inspectTypeName',
    component: 'ApiSelect',
    label: '检查类型名称',
    show: false,
  },
  {
    field: 'inspectType',
    component: 'ApiSelect',
    label: '检查类型',
    labelWidth: 100,
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.INSPECT_TYPE),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        valueToString: formModel?.inspectType ? true : false,
        getPopupContainer: (trigger: Element) => trigger.parentNode as HTMLElement,
        onChange: (_, opt) => {
          if (opt?.label) {
            formModel.inspectTypeName = opt.label;
          } else {
            formModel.inspectTypeRemark = undefined;
            formModel.inspectTypeName = undefined;
          }
        },
      };
    },
    colProps: { span: 7 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'inspectTypeRemark',
    label: '其他类型',
    labelWidth: 100,
    component: 'Input',
    colProps: { span: 7 },
    itemProps: { wrapperCol: { span: 14 } },
    ifShow: ({ model }) => model?.inspectType == '3',
  },

  {
    field: '',
    component: 'Divider',
    label: ' ',
    componentProps: {
      style: {
        display: 'none',
      },
    },
    colProps: { span: 24 },
  },
  //检查结果
  {
    field: 'ctResultName',
    component: 'Input',
    label: '',
    show: false,
  },
  //检查结果
  {
    field: 'ctResult',
    component: 'ApiSelect',
    label: '检查结果',
    labelWidth: 245,
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.CT_RESULT),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        valueToString: formModel?.ctResult ? true : false,
        getPopupContainer: (trigger: Element) => trigger.parentNode as HTMLElement,
        onChange: (_, opt) => {
          if (opt?.label) {
            formModel.ctResultName = opt.label;
          } else {
            formModel.imagingResultRemark = undefined;
            formModel.ctResultName = undefined;
          }
        },
      };
    },
    colProps: { span: 10 },
    itemProps: { wrapperCol: { span: 10 } },
  },
  {
    field: 'imagingResultRemark',
    label: '其他结果',
    labelWidth: 100,
    component: 'Input',
    colProps: { span: 7 },
    itemProps: { wrapperCol: { span: 14 } },
    ifShow: ({ model }) => model?.ctResult == '3',
  },
  {
    field: '',
    component: 'Divider',
    label: ' ',
    componentProps: {
      style: {
        display: 'none',
      },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  //痰检
  {
    field: '痰检',
    component: 'Input',
    labelWidth: 245,
    label: ' ',
    slot: 'textname',
    colProps: { span: 2 },
    disabledLabelWidth: true,
  },
  //留痰日期
  {
    field: 'phlegmDate',
    component: 'DatePicker',
    label: '留痰日期',
    componentProps: {
      style: { width: '100%' },
      showTime: true,
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD',
      getPopupContainer: () => document.body,
    },
    colProps: { span: 7 },
    itemProps: { wrapperCol: { span: 14 } },
    defaultValue: dayjs().format('YYYY-MM-DD'),
  },
  //检查方法
  {
    field: 'phlegmCheckName',
    component: 'Input',
    label: '',
    show: false,
  },
  //检查结果
  {
    field: 'phlegmCheck',
    component: 'ApiSelect',
    label: '检查方法',
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.PHLEGM_CHECK),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        maxTagCount: 1,
        //多选
        mode: 'multiple',
        maxTagTextLength: 2,
        multiple: true,
        getPopupContainer: (trigger: Element) => trigger.parentNode,
        onChange: (_, opt) => {
          if (opt && opt.length > 0) {
            formModel.phlegmCheckName = opt.map((item: any) => item.label).join(',');
          }
        },
      };
    },
    colProps: { span: 7 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  //检查结果
  {
    field: 'phlegmResultName',
    component: 'Input',
    label: '',
    show: false,
  },
  //检查结果
  {
    field: 'phlegmResult',
    component: 'ApiSelect',
    label: '检查结果',
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.PHLEGM_RESULT),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        valueToString: formModel?.phlegmResult ? true : false,
        getPopupContainer: (trigger: Element) => trigger.parentNode,
        onChange: (_, opt) => {
          if (opt?.label) formModel.phlegmResultName = opt.label;
        },
      };
    },
    colProps: { span: 7 },
    itemProps: { wrapperCol: { span: 14 } },
  },

  {
    field: '筛查结果',
    component: 'Input',
    label: ' ',
    slot: 'textname',
    colProps: { span: 2 },
    disabledLabelWidth: true,
  },
  //筛查结果
  {
    field: 'screeningResult',
    component: 'Input',
    label: '',
    show: false,
  },
  {
    field: 'screeningResultValue',
    component: 'ApiRadioGroup',
    label: '诊断',
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.SCREENING_RESULT),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        valueToString: true,
        getPopupContainer: (trigger: Element) => trigger.parentNode,
        onChange: (_, opt) => {
          if (opt?.label) {
            formModel.screeningResult = opt.label;
          } else {
            formModel.screeningResultRemark = undefined;
            formModel.screeningResult = undefined;
          }
        },
        // valueToString: true,
      };
    },
    colProps: { span: 22 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  {
    field: 'screeningResultRemark',
    component: 'Input',
    label: '其他诊断结果',
    labelWidth: 245,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 7 } },
    ifShow: ({ model }) => model.screeningResultValue == '4',
  },
  //是否为预防性治疗对象 RadioGroup
  {
    field: 'objectFlag',
    component: 'RadioGroup',
    label: '是否符合预防性治疗',
    labelWidth: 245,
    componentProps: ({ formModel }) => {
      return {
        options: [
          { label: '是', value: 0 },
          { label: '否', value: 1 },
        ],
        onChange: () => {
          if (formModel.objectFlag == 1 && formModel.acceptFlag == 1) {
            formModel.preventiveTreatmentPlan = undefined;
            formModel.finishFlag = undefined;
            formModel.preventiveTreatmentReason = undefined;
            formModel.preventiveTreatmentReasonName = undefined;
            formModel.preventiveTreatmentRemark = undefined;
            formModel.remark = undefined;
          }
        },
      };
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
  },
  //是否接受预防性治疗 RadioGroup
  {
    field: 'acceptFlag',
    component: 'RadioGroup',
    label: '是否开始预防性治疗',
    labelWidth: 245,
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 14 } },
    componentProps: ({ formModel }) => {
      return {
        options: [
          { label: '是', value: 0 },
          { label: '否', value: 1 },
        ],
        onChange: () => {
          if (formModel.objectFlag == 1 && formModel.acceptFlag == 1) {
            formModel.preventiveTreatmentPlan = undefined;
            formModel.finishFlag = undefined;
            formModel.preventiveTreatmentReason = undefined;
            formModel.preventiveTreatmentReasonName = undefined;
            formModel.preventiveTreatmentRemark = undefined;
            formModel.remark = undefined;
          }
        },
      };
    },
  },
  //是否完成预防性治疗 RadioGroup
  {
    field: 'finishFlag',
    component: 'RadioGroup',
    label: '是否完成预防性治疗',
    labelWidth: 245,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 20 } },
    componentProps: {
      options: [
        { label: '完成', value: 0 },
        { label: '未完成', value: 1 },
      ],
    },
    ifShow: ({ model }) => model.acceptFlag == 0 || model.objectFlag == 0,
  },
  {
    field: 'preventiveTreatmentPlan',
    component: 'InputTextArea',
    label: '预防性治疗方案',
    labelWidth: 245,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 20 } },
    componentProps: {
      rows: 4,
      autoSize: { minRows: 3, maxRows: 3 },
    },
    ifShow: ({ model }) => model.acceptFlag == 0 || model.objectFlag == 0,
  },
  {
    field: 'preventiveTreatmentReasonName',
    component: 'Input',
    label: '预防性治疗原因名称',
    show: false,
  },
  {
    field: 'preventiveTreatmentReason',
    component: 'ApiSelect',
    label: '预防性治疗原因',
    labelWidth: 245,
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.PREVENTIVE_TREATMENT_REASON),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        valueToString: formModel?.preventiveTreatmentReason ? true : false,
        getPopupContainer: (trigger: Element) => trigger.parentNode,
        onChange: (_, opt) => {
          if (opt?.label) {
            formModel.preventiveTreatmentReasonName = opt.label;
          } else {
            formModel.preventiveTreatmentRemark = undefined;
            formModel.preventiveTreatmentReasonName = undefined;
          }
        },
      };
    },
    colProps: { span: 10 },
    itemProps: { wrapperCol: { span: 14 } },
    ifShow: ({ model }) => model.acceptFlag == 0 || model.objectFlag == 0,
  },
  {
    field: 'preventiveTreatmentRemark',
    label: '其他原因',
    labelWidth: 120,
    component: 'Input',
    colProps: { span: 10 },
    itemProps: { wrapperCol: { span: 20 } },
    ifShow: ({ model }) =>
      model?.preventiveTreatmentReason == '5' && (model.acceptFlag == 0 || model.objectFlag == 0),
  },
  //备注
  {
    field: 'remark',
    component: 'InputTextArea',
    label: '备注',
    labelWidth: 245,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 20 } },
    componentProps: {
      rows: 4,
      autoSize: { minRows: 3, maxRows: 3 },
    },
    ifShow: ({ model }) => model.acceptFlag == 0 || model.objectFlag == 0,
  },
];

export const formSchema = (activeKey): FormSchema[] => {
  return [
    {
      field: 'screeningYear',
      label: '筛查年份',
      component: 'DatePicker',
      componentProps: {
        picker: 'year',
        showTime: false,
        showNow: false,
        valueFormat: 'YYYY',
        format: 'YYYY',
        placeholder: '请选择年份',
        style: {
          width: '100%',
        },
        getPopupContainer: () => document.body, // 防止弹出层被modal隐藏掉
      },
      defaultValue: dayjs().format('YYYY'),
      isHandleDateDefaultValue: false,
      colProps: { span: 6 },
      ifShow: () => activeKey !== '1',
    },
    {
      field: 'preliminaryScreeningDate',
      label: '筛查日期',
      colProps: { span: 6 },
      component: 'RangePicker',
      componentProps: {
        options: [],
        style: 'width: 100%',
        // format: '',
        getPopupContainer() {
          return document.body;
        },
        placeholder: ['开始时间', '结束时间'],
      },
      ifShow: () => activeKey !== '2',
    },
    {
      field: 'divisionId',
      label: '筛查地区',
      component: 'ApiSelect',
      componentProps: {
        api: getAdministrativeList,
        labelField: 'name',
        valueField: 'code',
        getPopupContainer: () => document.body, // 防止弹出层被modal隐藏掉
      },
      colProps: { span: 6 },
    },
    {
      field: 'orgId',
      label: '筛查机构',
      component: 'ApiSelect',
      componentProps: () => {
        return {
          api: () => queryOrganizationList({ orgType: 1 }),
          labelField: 'orgName',
          valueField: 'id',
          showSearch: true,
          optionFilterProp: 'label',
          getPopupContainer: () => document.body, // 防止弹出层被modal隐藏掉
        };
      },
      colProps: { span: 6 },
    },
    {
      field: 'screeningResultValue',
      component: 'ApiSelect',
      label: '筛查结果',
      colProps: { span: 6 },
      componentProps: () => {
        return {
          api: () => getDictItemList(DictEnum.SCREENING_RESULT),
          getPopupContainer: () => document.body,
          labelField: 'dictItemName',
          valueField: 'dictItemCode',
        };
      },
    },

    // {
    //   field: 'touchPatientName',
    //   component: 'Input',
    //   label: '患者姓名',
    //   colProps: { span: 6 },
    // },
    {
      field: 'screeningName',
      component: 'Input',
      label: '密接者姓名',
      colProps: { span: 6 },
    },
    // 主管医生 chiefPhysician
    {
      field: 'chiefPhysician',
      component: 'Input',
      label: '主管医生',
      colProps: { span: 6 },
    },
    {
      field: 'recordNoStart',
      label: '个案登记号',
      slot: 'recordNo',
      component: 'Input',
      colProps: { span: 6 },
    },
    {
      field: 'recordNoEnd',
      label: '个案登记号',
      component: 'Input',
      show: false,
    },
  ];
};

/**
 * @description: 表格列
 */

export const columns = (activeKey): BasicColumn[] => {
  return [
    {
      title: '患者姓名',
      dataIndex: 'touchPatientName',
      width: 150,
      ifShow: () => activeKey === '2',
      align: 'left',
    },
    {
      title: '接触者姓名',
      dataIndex: 'screeningName',
      width: 110,
      ifShow: () => activeKey !== '3',
      align: 'left',
    },
    {
      title: '筛查者姓名',
      dataIndex: 'screeningName',
      width: 110,
      ifShow: () => activeKey === '3',
      align: 'left',
    },
    {
      title: '性别',
      dataIndex: 'sexName',
      width: 50,
      align: 'left',
    },
    {
      title: '年龄',
      dataIndex: 'age',
      width: 60,
    },
    {
      title: '现详细住址',
      dataIndex: 'address',
      width: 120,
      align: 'left',
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      width: 120,
      align: 'left',
    },
    {
      title: '症状筛查',
      // 筛查日期 肺结核可疑症状
      children: [
        {
          title: '筛查日期',
          dataIndex: 'screeningTime',
          width: 110,
          align: 'left',
        },
        {
          title: '肺结核可疑症状',
          dataIndex: 'suspiciousResultName',
          width: 160,
          align: 'left',
        },
      ],
    },
    {
      title: '感染监测',
      // TST检测 IGRA检测
      children: [
        {
          title: 'TST检测',
          children: [
            // 首次检测日期 首次横径*纵径(mm) 二次检测日期 二次横径*纵径(mm)
            {
              title: '首次检测日期',
              dataIndex: 'tstFirstDate',
              width: 110,
              align: 'left',
            },
            {
              title: '首次横径*纵径(mm)',
              dataIndex: 'tstFirstValue',
              width: 160,
              align: 'left',
            },
            {
              title: '二次检测日期',
              dataIndex: 'tstSecondDate',
              width: 110,
              align: 'left',
            },
            {
              title: '二次横径*纵径(mm)',
              dataIndex: 'tstSecondValue',
              width: 160,
              align: 'left',
            },
          ],
        },
        {
          title: 'IGRA检测',
          children: [
            //检测日期 检测结果
            {
              title: '检测日期',
              dataIndex: 'igraDate',
              width: 110,
              align: 'left',
            },
            {
              title: '检测结果',
              dataIndex: 'igraResultName',
              width: 110,
              align: 'left',
            },
          ],
        },
      ],
    },
    {
      title: '胸部X光片检查',
      children: [
        //检查日期 检查结果
        {
          title: '检查日期',
          dataIndex: 'ctCheckDate',
          width: 110,
          align: 'left',
        },
        {
          title: '检查结果',
          dataIndex: 'ctResultName',
          width: 150,
          align: 'left',
        },
      ],
    },
    {
      title: '舌试子',
      dataIndex: 'tongueTestResultName',
      width: 80,
      align: 'left',
    },
    {
      title: '痰检',
      children: [
        //留痰日期 检查方法 检查结果
        {
          title: '留痰日期',
          dataIndex: 'phlegmDate',
          width: 110,
          align: 'left',
        },
        {
          title: '检查方法',
          dataIndex: 'phlegmCheckName',
          width: 150,
          align: 'left',
        },
        {
          title: '检查结果',
          dataIndex: 'phlegmResultName',
          width: 110,
          align: 'left',
        },
      ],
    },
    {
      title: '筛查结果',
      dataIndex: 'screeningResult',
      width: 110,
      align: 'left',
    },
    {
      title: '是否为预防性治疗对象',
      dataIndex: 'objectFlag',
      customRender: ({ text }) => (text === 0 ? '是' : text === 1 ? '否' : ''),
      width: 110,
      align: 'left',
    },
    {
      title: '是否接受预防性治疗',
      dataIndex: 'acceptFlag',
      width: 110,
      customRender: ({ text }) => (text === 0 ? '是' : text === 1 ? '否' : ''),
      align: 'left',
    },
    {
      title: '是否完成预防性治疗',
      dataIndex: 'finishFlag',
      width: 110,
      align: 'left',
      customRender: ({ text }) => (text === 0 ? '是' : text === 1 ? '否' : ''),
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 110,
      align: 'left',
    },
  ];
};
