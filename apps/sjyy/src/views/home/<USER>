<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import { <PERSON><PERSON>, DatePicker } from 'ant-design-vue';
  import { ApiSelect } from '@ft/internal/components/Form';
  import { useRequest } from '@ft/request';
  import Pie<PERSON>hart from './components/PieChart.vue';
  import Bar<PERSON>hart from './components/BarChart.vue';
  import {
    querySpecificDiseasePatientOrgStatistics,
    querySpecificDiseaseStatistics,
    querySpecificDiseaseStatisticsAreaList,
    querySpecificDiseaseStatisticsOrgList,
  } from '/@/api/home';

  /**
   * 各专病患者辖区分布统计
   */

  // 日期选择
  const dateRange1 = ref<[string, string]>(['', '']);
  const dateRange2 = ref<[string, string]>(['', '']);
  const selectedRegion1 = ref<string>();
  const selectedRegion2 = ref<string>();

  const {
    data: pieData,
    run: runPieData,
    loading: pieLoading,
  } = useRequest(querySpecificDiseaseStatistics, {
    manual: true,
  });
  const diseaseTypeMap = {
    1: '病毒性肝炎患者数',
    2: '艾滋病患者数',
    3: '结核病患者数',
  };
  const statData = computed(() => {
    const sortData = [...(pieData.value || [])]?.sort((a, b) => a.sort - b.sort);
    return sortData?.map((item) => ({
      title: diseaseTypeMap[item.diseaseType],
      data:
        item.areaPatientDistribution?.map((item) => ({
          name: item.item,
          value: item.count,
        })) || [],
    }));
  });

  const {
    data: orgData,
    run: runOrgData,
    loading: orgLoading,
  } = useRequest(querySpecificDiseasePatientOrgStatistics, {
    manual: true,
  });

  const orgStatData = computed(() => {
    return {
      tub:
        orgData.value
          ?.find((item) => item.diseaseType === 3)
          ?.areaPatientDistribution?.map((item) => ({
            name: item.item,
            value: item.count,
          })) || [],
      hepatitis:
        orgData.value
          ?.find((item) => item.diseaseType === 1)
          ?.areaPatientDistribution?.map((item) => ({
            name: item.item,
            value: item.count,
          })) || [],
      aids:
        orgData.value
          ?.find((item) => item.diseaseType === 2)
          ?.areaPatientDistribution?.map((item) => ({
            name: item.item,
            value: item.count,
          })) || [],
    };
  });

  onMounted(() => {
    runPieData({
      areaCode: selectedRegion1.value || '',
      startDate: dateRange1.value[0],
      endDate: dateRange1.value[1],
    });
    runOrgData({
      orgCode: selectedRegion2.value || '',
      startDate: dateRange2.value[0],
      endDate: dateRange2.value[1],
    });
  });

  // 重置
  const handleReset1 = () => {
    dateRange1.value = ['', ''];
    selectedRegion1.value = '';
    handleSearch1();
  };

  const handleReset2 = () => {
    dateRange2.value = ['', ''];
    selectedRegion2.value = '';
    handleSearch2();
  };

  // 查询
  const handleSearch1 = () => {
    runPieData({
      areaCode: selectedRegion1.value || '',
      startDate: dateRange1.value?.[0] ? dateRange1.value?.[0] : '',
      endDate: dateRange1.value?.[1] ? dateRange1.value?.[1] : '',
    });
  };

  const handleSearch2 = () => {
    runOrgData({
      orgCode: selectedRegion2.value || '',
      startDate: dateRange2.value?.[0] ? dateRange2.value?.[0] : '',
      endDate: dateRange2.value?.[1] ? dateRange2.value?.[1] : '',
    });
  };
</script>

<template>
  <div class="w-full h-full flex flex-col gap-4 pr-4 pb-4">
    <!-- 上部分 -->
    <div class="flex-1 bg-white p-4 rounded-lg rounded-tl-none flex flex-col gap-4">
      <!-- 查询条件 -->
      <div class="rounded-lg flex flex-col gap-2">
        <div class="text-sm font-bold">各专病患者辖区分布统计</div>
        <div class="flex items-center gap-4">
          <div class="flex items-center">
            <span class="mr-2">统计日期：</span>
            <DatePicker.RangePicker value-format="YYYY-MM-DD" v-model:value="dateRange1" />
          </div>
          <div class="flex items-center">
            <span class="mr-2">统计辖区：</span>
            <ApiSelect
              :api="querySpecificDiseaseStatisticsAreaList"
              label-field="name"
              value-field="code"
              v-model:value="selectedRegion1"
              placeholder="请选择"
              style="width: 200px"
              show-search
              option-filter-prop="label"
            />
          </div>
          <Button type="primary" @click="handleSearch1" :loading="pieLoading">查询</Button>
          <Button type="default" @click="handleReset1">重置</Button>
        </div>
      </div>

      <!-- 图表展示区域 -->
      <div class="grid grid-cols-3 gap-4">
        <div
          class="p-4 rounded-lg h-80 border-1 border-[#DCDFE6]"
          v-for="(item, index) in statData"
          :key="index"
        >
          <PieChart :title="item.title" :data="item.data" />
        </div>
      </div>
    </div>

    <!-- 下部分 -->
    <div class="flex-1 bg-white p-4 rounded-lg flex flex-col gap-4">
      <!-- 查询条件 -->
      <div class="rounded-lg flex flex-col gap-2">
        <div class="text-sm font-bold">各专病患者就诊机构分布统计</div>
        <div class="flex items-center gap-4">
          <div class="flex items-center">
            <span class="mr-2">统计日期：</span>
            <DatePicker.RangePicker value-format="YYYY-MM-DD" v-model:value="dateRange2" />
          </div>
          <div class="flex items-center">
            <span class="mr-2">统计机构：</span>
            <ApiSelect
              :api="querySpecificDiseaseStatisticsOrgList"
              label-field="orgName"
              value-field="orgCode"
              v-model:value="selectedRegion2"
              placeholder="请选择，默认前五"
              style="width: 200px"
              show-search
              option-filter-prop="label"
            />
          </div>
          <Button type="primary" @click="handleSearch2" :loading="orgLoading">查询</Button>
          <Button type="default" @click="handleReset2">重置</Button>
        </div>
      </div>

      <!-- 图表展示区域 -->
      <div class="grid grid-cols-3 gap-4">
        <div class="p-4 rounded-lg h-80 border-1 border-[#DCDFE6]">
          <BarChart name="患者数" title="结核病就诊患者数" :data="orgStatData.tub" />
        </div>
        <div class="p-4 rounded-lg h-80 border-1 border-[#DCDFE6]">
          <BarChart name="患者数" title="病毒性肝炎就诊患者数" :data="orgStatData.hepatitis" />
        </div>
        <div class="p-4 rounded-lg h-80 border-1 border-[#DCDFE6]">
          <BarChart name="患者数" title="艾滋病就诊患者数" :data="orgStatData.aids" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
  :deep(.ant-select-selection-placeholder) {
    color: #999;
  }
</style>
