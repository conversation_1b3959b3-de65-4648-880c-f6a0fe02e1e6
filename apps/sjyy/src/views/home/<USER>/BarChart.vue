<script setup lang="ts">
  import { onMounted, ref, watch } from 'vue';
  import { useECharts } from '@ft/internal/hooks/web/useECharts';

  interface ChartData {
    name: string;
    value: number;
  }

  const props = defineProps<{
    title: string;
    data: ChartData[];
    name: string;
  }>();

  const chartRef = ref<HTMLDivElement>();
  const { setOptions } = useECharts(chartRef as any);

  const initChart = () => {
    setOptions({
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        left: '3%',
        right: '5%',
        bottom: '3%',
        top: '5%',
        containLabel: true,
      },
      legend: {
        show: true,
        left: 'center',
        top: 'top',
        itemWidth: 10,
        itemHeight: 10,
      },
      xAxis: {
        type: 'value',
      },
      yAxis: {
        type: 'category',
        data: props.data.map((item) => item.name),
        axisLabel: {
          width: 100,
          overflow: 'truncate',
          rotate: 30,
          color: '#B0B1B4',
        },
      },
      series: [
        {
          name: props.name,
          type: 'bar',
          barWidth: 15,
          data: props.data.map((item) => item.value),
          itemStyle: {
            color: '#3D85FF',
          },
        },
      ],
    });
  };

  watch(
    () => props.data,
    () => {
      initChart();
    },
    { deep: true },
  );

  onMounted(() => {
    initChart();
  });
</script>

<template>
  <div class="h-full w-full">
    <div class="chart-title mb-4">
      <span class="title-text">{{ title }}</span>
    </div>
    <div ref="chartRef" class="h-[calc(100%-32px)]"></div>
  </div>
</template>

<style scoped>
  .chart-title {
    position: relative;
    padding-left: 12px;
    font-size: 16px;
    color: #333;
  }

  .chart-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 2px;
    height: 16px;
    background-color: #4752e6;
  }
</style>
