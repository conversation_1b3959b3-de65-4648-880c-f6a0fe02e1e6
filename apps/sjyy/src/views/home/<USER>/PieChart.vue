<script setup lang="ts">
  import { onMounted, ref, watch } from 'vue';
  import { useECharts } from '@ft/internal/hooks/web/useECharts';
  import type { EChartsOption } from 'echarts';

  interface ChartData {
    name: string;
    value: number;
  }

  const props = defineProps<{
    title: string;
    data: ChartData[];
  }>();

  const chartRef = ref<HTMLDivElement>();
  const { setOptions } = useECharts(chartRef as any, null);
  const color = [
    '#0440A5',
    '#1A61D7',
    '#3074E7',
    '#3D85FF',
    '#5795FF',
    '#679FFF',
    '#83B1FF',
    '#A1C4FF',
    '#B8D3FF',
    '#D3E4FF',
    '#E2EDFF',
    '#8CB7FF',
  ];

  function getRichRectConfig() {
    return color.reduce((acc, item, index) => {
      acc[`rect${index}`] = {
        width: 12,
        height: 12,
        backgroundColor: item,
      };
      return acc;
    }, {});
  }

  const initChart = () => {
    const option = {
      title: {
        show: false,
      },
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c}人',
      },
      legend: {
        show: false,
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '50%'],
          data: props.data,
          label: {
            show: true,
            position: 'outside',
            // borderType: [0, 0],
            // borderWidth: 12,
            // borderColor: 'inherit',

            formatter: function (params) {
              return [
                `{rect${params.dataIndex}|} {name|${params.name}}`,
                `{value|${params.value}人}`,
              ].join('\n');
            },
            rich: {
              ...getRichRectConfig(),
              name: {
                fontSize: 12,
                color: '#5C5F66',
              },
              value: {
                fontSize: 12,
                color: '#252931',
                align: 'left',
                // padding: [8, 0, 0, 20],
              },
            },
            fontSize: 12,
            lineHeight: 16,
            color: '#333',
          },

          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
            label: {
              show: true,
              fontWeight: 'bold',
            },
          },
        },
      ],
      color,
    };
    setOptions(option as EChartsOption);
  };

  watch(
    () => props.data,
    () => {
      initChart();
    },
    { deep: true },
  );

  onMounted(() => {
    initChart();
  });
</script>

<template>
  <div class="h-full w-full">
    <div class="chart-title mb-4">
      <span class="title-text">{{ title }}</span>
    </div>
    <div ref="chartRef" class="h-[calc(100%-32px)]"></div>
  </div>
</template>

<style scoped>
  .chart-title {
    position: relative;
    padding-left: 12px;
    font-size: 16px;
    color: #333;
  }

  .chart-title::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 2px;
    height: 16px;
    background-color: #4752e6;
  }
</style>
