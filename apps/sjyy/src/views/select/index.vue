<script setup lang="ts">
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { setPatientInfo } from '@ft/internal/utils/auth';
  import { useRequest } from '@ft/request';
  import { useModal } from '@ft/internal/components/Modal';
  import { onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';

  import SelectModel from './SelectModel.vue';
  import { getPatient } from '/@/api/select';

  const go = useGo();
  const router = useRouter();
  const patientId = useRouteQuery('patientId', '', { transform: String });
  //判断地址栏中patientId是否存在

  const { runAsync: runGetPatient } = useRequest(getPatient, {
    manual: true,
    showSuccessMessage: false,
  });
  const [registerModal, { openModal }] = useModal();

  onMounted(() => {
    if (patientId.value !== '') {
      handleOk(patientId.value);
    } else {
      openModal(true, {
        isCanClose: false,
      });
    }
  });
  function handleOk(data: string) {
    runGetPatient(data).then((res) => {
      setPatientInfo(res);
      if (router.hasRoute('PatientManagement')) {
        go({ name: 'PatientManagement' });
      } else if (router.hasRoute('DoctorsView')) {
        go({ name: 'DoctorsView' });
      } else if (router.hasRoute('PublicHealth')) {
        go({ name: 'PublicHealth' });
      }
    });
  }
</script>

<template>
  <div class="select-patient">
    <SelectModel @register="registerModal" @success="handleOk" :defaultVisible="patientId === ''" />
  </div>
</template>
<style scoped></style>
