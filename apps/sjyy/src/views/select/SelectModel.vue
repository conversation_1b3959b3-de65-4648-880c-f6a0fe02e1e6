<script setup lang="ts">
  import { computed, nextTick, ref } from 'vue';
  // import { patientPage } from '@ft/internal/api/patient.ts';
  import { Tabs } from 'ant-design-vue';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import type { TableRowSelection } from 'ant-design-vue/lib/table/interface';
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { useUserStore } from '@ft/internal/store/modules/user';
  import { columns, patientStatusApiMap, schemas, treatmentStatusApiMap } from './data';
  import { patientPage } from '/@/api/select';

  const emit = defineEmits(['register', 'success']);
  const props = defineProps({
    defaultVisible: {
      type: Boolean,
      default: true,
    },
  });
  const visible = ref(props.defaultVisible);
  const isCanClose = ref(false);
  const activeKey = ref('2');
  const userStore = useUserStore();
  const isDesensitization = computed(() => {
    return userStore?.getUserInfo?.isDesensitization;
  });

  const [register, tableInstance] = useTable({
    columns,
    api: patientPage,
    useSearchForm: true,
    inset: true,
    formConfig: {
      schemas,
      labelWidth: 100,
      showAdvancedButton: false,
      actionColOptions: {
        span: 8,
        style: {
          textAlign: 'right',
        },
      },
      fieldMapToTime: [['visitTime', ['visitTimeStart', 'visitTimeEnd'], 'YYYY-MM-DD']],
    },
    afterFetch: (data) => {
      data?.forEach((item) => {
        item._idCardNo = item.idCardNo
          ? isDesensitization.value === 1
            ? item.idCardNo?.replace(/(\d{6})(\d{8})(\w{4})/, '$1********$3')
            : item.idCardNo
          : '';
      });
      return data;
    },
    canResize: false,
    scroll: { y: '40vh' },

    beforeFetch: (params) => {
      params.diseaseCode = activeKey.value;
      return params;
    },
    rowKey: 'id',
  });

  const [registerModel, { closeModal }] = useModalInner((data) => {
    isCanClose.value = data.isCanClose;
  });

  const selectedRowKeys = ref<any[]>([]);
  const rowSelection = computed<TableRowSelection>(() => {
    return {
      type: 'radio',
      selectedRowKeys: selectedRowKeys.value,
      onChange: (keys: any, _) => {
        if (keys.length === 0) {
          return;
        }
        selectedRowKeys.value = keys;
      },
    };
  });
  function handleOk() {
    emit('success', selectedRowKeys?.value[0]);

    closeModal();
  }
  function reloadTable() {
    selectedRowKeys.value = [];
    const form = tableInstance.getForm();
    form.updateSchema({
      field: 'patientStatus',
      componentProps: {
        api: patientStatusApiMap[activeKey.value],
      },
    });
    form.updateSchema({
      field: 'treatmentStatus',
      componentProps: {
        api: treatmentStatusApiMap[activeKey.value],
      },
    });
    form.setFieldsValue({
      patientStatus: '',
      treatmentStatus: '',
    });
    nextTick(() => {
      setTimeout(() => {
        console.log(107, form.getFieldsValue());
        tableInstance.reload();
      }, 166);
    });
  }
</script>

<template>
  <BasicModal
    wrapClassName="select-model"
    :closable="isCanClose"
    v-model:visible="visible"
    :destroy-on-close="true"
    title="选择患者"
    @register="registerModel"
    :can-fullscreen="false"
    width="1160px"
    @ok="handleOk"
    :show-cancel-btn="isCanClose"
    :keyboard="isCanClose"
    :maskClosable="isCanClose"
    :ok-button-props="{ disabled: selectedRowKeys?.length === 0 }"
    centered
  >
    <BasicTable class="select-model" :row-selection="rowSelection" @register="register">
      <template #headerTop>
        <Tabs v-model:activeKey="activeKey" @change="reloadTable()">
          <Tabs.TabPane key="2" tab="艾滋病" />
          <Tabs.TabPane key="1" tab="病毒性肝炎" />
          <Tabs.TabPane key="3" tab="结核病" />
        </Tabs>
      </template>
    </BasicTable>
  </BasicModal>
</template>

<style lang="less">
  .select-model {
    .ant-table-body {
      height: calc(100% - 250px) !important;
    }

    .ant-modal-body {
      .scrollbar {
        padding: 4px 24px !important;
        // overflow: hidden;
      }
    }

    .vben-basic-table-form-container .ant-form {
      margin-bottom: 0;
    }

    .ft-main-table .ant-table-wrapper {
      padding: 0 12px !important;
    }

    .vben-basic-table--inset .ant-form {
      border-bottom: none;
      padding: 12px 0 6px !important;
    }

    .ant-tabs-tab {
      padding-top: 0;
    }

    .ant-modal-footer {
      text-align: center;
    }

    .vben-basic-table .ant-table-wrapper .ant-table-title {
      padding: 0 !important;

      > div > div {
        margin: 0 !important;
      }
    }
  }
</style>
