import { DictEnum, getDictItemList } from '@ft/internal/api';
import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
// import { idCardMask } from '@ft/internal/utils';

export const patientStatusApiMap = {
  '2': () => getDictItemList(DictEnum.AZB_PATIENT_STATUS),
  '1': () => getDictItemList(DictEnum.GY_PATIENT_STATUS),
  '3': () => getDictItemList(DictEnum.JHB_PATIENT_STATUS),
};

export const treatmentStatusApiMap = {
  '2': () => getDictItemList(DictEnum.AZB_TREATMENT_STATUS),
  '1': () => getDictItemList(DictEnum.GY_TREATMENT_STATUS),
  '3': () => getDictItemList(DictEnum.JHB_TREATMENT_STATUS),
};

/**
 * 患者编号 患者姓名 性别 年龄 身份证号 电话 诊断结果 所属科室 所属机构
 */
export const columns: BasicColumn[] = [
  {
    title: '患者编号',
    dataIndex: 'patientId',
    width: 150,
    align: 'left',
  },
  {
    title: '患者姓名',
    dataIndex: 'name',
    width: 150,
    align: 'left',
  },
  {
    title: '诊断名称',
    dataIndex: 'diagnosticResults',
    width: 150,
    align: 'left',
  },
  {
    title: '就诊机构',
    dataIndex: 'visitOrgName',
    width: 150,
    align: 'left',
  },
  {
    title: '性别',
    dataIndex: 'sexName',
    width: 100,
  },
  {
    title: '年龄',
    dataIndex: 'age',
    width: 100,
  },
  {
    title: '身份证号',
    dataIndex: '_idCardNo',
    width: 200,
  },
  {
    title: '电话',
    dataIndex: 'telephone',
    width: 150,
  },
  // {
  //   title: '患者状态',
  //   dataIndex: 'patientStatusDesc',
  //   width: 100,
  //   align: 'left',
  // },
  {
    title: '治疗状态',
    dataIndex: 'treatmentStatusDesc',
    width: 100,
    align: 'left',
  },
  // {
  //   title: '最近就诊时间',
  //   dataIndex: 'lastVisitTime',
  //   width: 180,
  // },
  // {
  //   title: '所属科室',
  //   dataIndex: 'department',
  //   width: 150,
  // },
  // {
  //   title: '所属机构',
  //   dataIndex: 'organization',
  //   width: 150,
  // },
];

/**
 * 患者姓名
 * 就诊时间
 * 就诊机构
 * 身份证号
 */
export const schemas: FormSchema[] = [
  {
    field: 'name',
    label: '患者姓名',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'visitTime',
    label: '就诊时间',
    colProps: { span: 8 },
    component: 'RangePicker',
    componentProps: {
      options: [],
      style: 'width: 100%',
      // format: '',
      getPopupContainer() {
        return document.body;
      },
      placeholder: ['开始时间', '结束时间'],
    },
  },
  {
    field: 'diagnosticResults',
    label: '诊断名称',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'patientStatus',
    component: 'ApiSelect',
    label: '患者状态',
    colProps: { span: 8 },
    componentProps: {
      api: patientStatusApiMap['2'],
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
  },
  {
    field: 'treatmentStatus',
    component: 'ApiSelect',
    label: '治疗状态',
    colProps: { span: 8 },
    componentProps: {
      api: treatmentStatusApiMap['2'],
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
    },
  },
];
