import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { getDeptList, queryOrganizationList } from '/@/api/sys';
import { DictEnum, getDictItemList } from '@ft/internal/api';

/**
 * 专家姓名
 * 专家机构
 * 专家科室
 * 专家职称
 * 擅长
 * 专家分级
 */
export const schemas: FormSchema[] = [
  {
    field: 'expertName',
    label: '专家姓名',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'orgId',
    label: '专家机构',
    component: 'ApiSelect',
    componentProps: {
      api: () => queryOrganizationList(),
      labelField: 'orgName',
      valueField: 'id',
      showSearch: true,
      optionFilterProp: 'label',
    },
    colProps: { span: 6 },
  },
  {
    field: 'deptId',
    label: '专家科室',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDeptList(),
      labelField: 'deptName',
      valueField: 'deptId',
      showSearch: true,
      optionFilterProp: 'label',
    },
    colProps: { span: 6 },
  },
  {
    field: 'jobTitleCode',
    label: '专家职称',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.JOB_TITLE),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
      optionFilterProp: 'label',
    },
    colProps: { span: 6 },
  },
  {
    field: 'servicesRange',
    label: '服务领域',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'expertLevel',
    label: '专家分级',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.EXPERT_LEVEL),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      showSearch: true,
      optionFilterProp: 'label',
    },
    colProps: { span: 6 },
  },
];

export const columns: BasicColumn[] = [
  {
    title: '专家姓名',
    dataIndex: 'expertName',
    width: 100,
    align: 'center',
  },
  {
    title: '性别',
    dataIndex: 'expertSexName',
    width: 80,
  },
  {
    title: '年龄',
    dataIndex: 'expertAge',
    width: 80,
  },
  {
    title: '民族',
    dataIndex: 'nation',
    width: 80,
  },
  {
    title: '职称',
    dataIndex: 'jobTitleName',
    width: 120,
    align: 'center',
  },
  {
    title: '联系方式',
    dataIndex: 'expertPhone',
    width: 120,
    align: 'center',
  },
  {
    title: '主要成就',
    dataIndex: 'majorAchievement',
    width: 240,
    align: 'center',
    ellipsis: false,
  },
  {
    title: '服务领域',
    dataIndex: 'servicesRange',
    width: 240,
    align: 'center',
    ellipsis: false,
  },
  {
    title: '资格证书',
    dataIndex: 'expertCertificate',
    width: 240,
    align: 'center',
  },
  {
    title: '绑定专家组',
    dataIndex: 'expertGroupName',
    width: 120,
    align: 'center',
  },
  {
    title: '专家分级',
    dataIndex: 'expertLevelName',
    width: 120,
    align: 'center',
  },
  {
    title: '专家所属科室',
    dataIndex: 'deptName',
    width: 120,
    align: 'center',
  },
  {
    title: '专家机构',
    dataIndex: 'orgName',
    width: 160,
    align: 'center',
  },
];
