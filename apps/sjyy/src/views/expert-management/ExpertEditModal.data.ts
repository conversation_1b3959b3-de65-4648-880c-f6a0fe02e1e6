import type { FormSchema } from '@ft/internal/components/Table';
import { getDeptList, queryOrganizationList } from '/@/api/sys';
import { getExpertGroupList } from '/@/api/expert-group';
import type { IExpert } from '/@/api/expert';
import { UPLOAD_URL } from '/@/api/expert';
import { DictEnum, getDictItemList } from '@ft/internal/api';

export interface ExpertEditModalDT {
  mode: 'edit' | 'add' | 'view';
  record?: IExpert;
}

export const EditSchemas: FormSchema[] = [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'expertName',
    label: '专家姓名',
    component: 'Input',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
    required: true,
    rules: [
      {
        message: '超出字符限制，请控制在1-255个字符之间',
        trigger: 'change',
        max: 255,
      },
    ],
  },
  {
    field: 'expertSex',
    label: '性别',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '男', value: 1 },
        { label: '女', value: 2 },
      ],
    },
    defaultValue: '1',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
    required: true,
  },
  {
    field: 'expertAge',
    label: '年龄',
    component: 'InputNumber',
    componentProps: {
      min: 0,
    },
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
    required: true,
  },
  {
    field: 'nation',
    label: '民族',
    component: 'Input',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
    rules: [
      {
        message: '超出字符限制，请控制在1-255个字符之间',
        trigger: 'change',
        max: 255,
      },
    ],
  },
  {
    field: 'jobTitleCode',
    label: '职称',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.JOB_TITLE),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        showSearch: true,
        optionFilterProp: 'label',
        getPopupContainer: () => document.body,
        onChange: (_, opt) => {
          formModel.jobTitleName = opt?.label;
        },
      };
    },
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
    required: true,
  },
  {
    field: 'jobTitleName',
    label: '职称名称',
    component: 'Input',
    show: false,
  },
  {
    field: 'expertLevel',
    label: '专家分级',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.EXPERT_LEVEL),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        showSearch: true,
        optionFilterProp: 'label',
        getPopupContainer: () => document.body,
        onChange: (_, opt) => {
          formModel.expertLevelName = opt?.label;
        },
      };
    },
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    field: 'expertLevelName',
    label: '专家分级名称',
    component: 'Input',
    show: false,
  },
  {
    field: 'orgId',
    label: '专家机构',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => queryOrganizationList(),
        labelField: 'orgName',
        valueField: 'id',
        showSearch: true,
        optionFilterProp: 'label',
        getPopupContainer: () => document.body,
        onChange: (_, opt) => {
          formModel.orgName = opt?.label;
          formModel.deptId = undefined;
          formModel.deptName = undefined;
        },
      };
    },
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
    required: true,
  },
  {
    field: 'orgName',
    label: '专家机构名称',
    component: 'Input',
    show: false,
  },
  {
    field: 'deptId',
    label: '所属科室',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => (formModel.orgId ? getDeptList({ orgId: formModel.orgId }) : []),
        labelField: 'deptName',
        valueField: 'deptId',
        showSearch: true,
        optionFilterProp: 'label',
        getPopupContainer: () => document.body,
        onChange: (_, opt) => {
          formModel.deptName = opt?.label;
        },
      };
    },
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
    required: true,
  },
  {
    field: 'deptName',
    label: '所属科室名称',
    component: 'Input',
    show: false,
  },
  {
    field: 'expertPhone',
    label: '联系方式',
    component: 'Input',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
    required: true,
    rules: [
      {
        pattern: /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
        message: '请输入正确的手机号码',
      },
    ],
  },
  {
    field: 'expertCertificate',
    label: '资格证书',
    component: 'Input',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
    rules: [
      {
        message: '超出字符限制，请控制在1-255个字符之间',
        trigger: 'change',
        max: 255,
      },
    ],
  },
  {
    field: 'expertGroupId',
    label: '绑定专家组',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => getExpertGroupList(''),
        labelField: 'groupName',
        valueField: 'id',
        showSearch: true,
        optionFilterProp: 'label',
        getPopupContainer: () => document.body,
        onChange: (_, opt) => {
          formModel.expertGroupName = opt?.label;
        },
      };
    },
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 16 } },
  },
  {
    field: 'expertGroupName',
    label: '专家分组名称',
    component: 'Input',
    show: false,
  },
  {
    field: 'expertImg',
    label: '登记照',
    component: 'UploadAsset',
    componentProps: {
      uploadType: 'image',
      resultField: 'data.url',
      maxCount: 1,
      action: UPLOAD_URL,
    },
    colProps: { span: 24 },
  },
  {
    field: 'servicesRange',
    label: '服务领域',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 20 } },
    required: true,
    rules: [
      {
        message: '超出字符限制，请控制在1-255个字符之间',
        trigger: 'change',
        max: 255,
      },
    ],
  },
  {
    field: 'majorAchievement',
    label: '主要成就',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 20 } },
    rules: [
      {
        message: '超出字符限制，请控制在1-255个字符之间',
        trigger: 'change',
        max: 255,
      },
    ],
  },
];
