<script setup lang="ts">
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { computed, ref } from 'vue';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import type { ExpertEditModalDT } from './ExpertEditModal.data';
  import { EditSchemas } from './ExpertEditModal.data';
  import { useRequest } from '@ft/request';
  import { getExpertDetail, saveExpert, updateExpert } from '/@/api/expert';

  const emit = defineEmits(['register', 'success']);
  const mode = ref('add');

  const getTitle = computed(() => {
    return mode.value === 'add' ? '新增专家' : '编辑专家';
  });

  const disabled = computed(() => mode.value === 'view');

  const [registerForm, formAction] = useForm({
    schemas: EditSchemas,
    labelWidth: 100,
    colon: true,
    showActionButtonGroup: false,
  });

  const [register, { closeModal }] = useModalInner<ExpertEditModalDT>((data) => {
    mode.value = data.mode;
    if (!data?.record) return;

    getExpertDetail(data.record.id).then((res) => {
      formAction.setFieldsValue(res);
    });
  });

  const { runAsync: saveExpertRunAsync, loading } = useRequest(
    (params) => (mode.value === 'add' ? saveExpert(params) : updateExpert(params)),
    {
      manual: true,
      showSuccessMessage: true,
    },
  );
  async function handleOk() {
    formAction.validate().then((values) => {
      saveExpertRunAsync(values).then(() => {
        emit('success');
        closeModal();
      });
    });
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="getTitle"
    :can-fullscreen="false"
    :minHeight="150"
    width="780px"
    @register="register"
    @ok="handleOk"
    ok-text="保存"
    :ok-button-props="{
      loading,
    }"
    :footer="disabled ? null : undefined"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
