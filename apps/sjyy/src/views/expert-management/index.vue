<script setup lang="ts">
  import { Tabs, Upload } from 'ant-design-vue';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { onMounted, ref } from 'vue';
  import { Button } from '@ft/internal/components/Button';
  import { useModal } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import type { UploadRequestOption } from 'ant-design-vue/lib/vc-upload/interface';
  import { columns, schemas } from './data';
  import ExpertEditModal from './ExpertEditModal.vue';
  import type { ExpertEditModalDT } from './ExpertEditModal.data';
  import type { IExpert } from '/@/api/expert';
  import {
    deleteExpert,
    downloadExpertTemplate,
    exportExpert,
    getExpertPage,
    importExpert,
  } from '/@/api/expert';
  import type { IExpertGroup } from '/@/api/expert-group';
  import { getExpertGroupList } from '/@/api/expert-group';

  const activeExpertKey = ref('');

  const [registerTable, tableIns] = useTable({
    api: getExpertPage,
    columns,
    useSearchForm: true,
    formConfig: {
      schemas,
      labelWidth: 120,
    },
    actionColumn: {
      title: '操作',
      dataIndex: 'action',
      width: 100,
    },
    beforeFetch: (params) => {
      return {
        ...params,
        expertGroupId: activeExpertKey.value || undefined,
      };
    },
    resizeHeightOffset: 5,
  });

  const expertGroupList = ref<IExpertGroup[]>([]);
  onMounted(() => {
    getExpertGroupList('').then((data) => {
      expertGroupList.value = data;
    });
  });

  const [registerModal, { openModal }] = useModal();

  function onAddExpert() {
    openModal<ExpertEditModalDT>(true, {
      mode: 'add',
    });
  }

  function onEditExpert(record: IExpert) {
    openModal<ExpertEditModalDT>(true, {
      mode: 'edit',
      record,
    });
  }
  // deleteExpert
  const { runAsync: deleteExpertRunAsync } = useRequest(deleteExpert, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      tableIns.reload();
    },
  });
  function onDeleteExpert(record: IExpert) {
    deleteExpertRunAsync(record.id);
  }

  function createActions(record: any, _column: any): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: onEditExpert.bind(null, record),
      },
      {
        label: '删除',
        danger: true,
        popConfirm: {
          title: '确定删除吗？',
          placement: 'topRight',
          okButtonProps: {
            danger: true,
          },
          confirm: onDeleteExpert.bind(null, record),
        },
      },
    ];
  }

  const { loading: downExpertTemplate, runAsync: runAsyncDownTemplate } = useRequest(
    downloadExpertTemplate,
    {
      manual: true,
    },
  );
  function onDownloadTemplate() {
    exportUtil(runAsyncDownTemplate());
  }

  const { loading: importLoading, runAsync: importExpertRunAsync } = useRequest(importExpert, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      tableIns.reload();
    },
  });

  function onImportExpert(e: UploadRequestOption<any>) {
    const { file } = e;
    importExpertRunAsync({
      // @ts-ignore
      file,
    });
  }

  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(exportExpert, {
    manual: true,
    showSuccessMessage: true,
  });
  function onExportExpert() {
    exportUtil(exportExpertRunAsync(tableIns.getForm().getFieldsValue()));
  }
</script>

<template>
  <div class="h-full pr-2.5 pb-2.5">
    <div class="h-full">
      <BasicTable class="ft-main-table" @register="registerTable">
        <template #headerTop>
          <div class="text-16px font-500 basic-title">专家列表</div>
          <Tabs v-model:activeKey="activeExpertKey" @change="() => tableIns.reload()">
            <Tabs.TabPane key="" tab="全部" />
            <Tabs.TabPane
              v-for="expertGroup in expertGroupList"
              :key="expertGroup.id"
              :tab="expertGroup.groupName"
            />
          </Tabs>
        </template>
        <template #tableTitle>
          <Button type="primary" @click="onAddExpert"> 新增专家 </Button>
        </template>
        <template #toolbar>
          <Button :loading="downExpertTemplate" @click="onDownloadTemplate"> 下载模板 </Button>

          <Upload
            accept=".xlsx,.xls"
            :max-count="1"
            :show-upload-list="false"
            :custom-request="onImportExpert"
          >
            <Button :loading="importLoading"> 批量导入 </Button>
          </Upload>
          <Button :loading="exportLoading" @click="onExportExpert"> 批量导出 </Button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :divider="false" :actions="createActions(record, column)" />
          </template>
        </template>
      </BasicTable>
    </div>
    <ExpertEditModal @register="registerModal" @success="tableIns.reload" />
  </div>
</template>
<style scoped></style>
