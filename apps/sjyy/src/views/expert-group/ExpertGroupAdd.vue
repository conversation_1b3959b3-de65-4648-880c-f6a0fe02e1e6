<script setup lang="ts">
  import { Form, Input, InputNumber, Select } from 'ant-design-vue';
  import { Icon } from '@ft/internal/components/Icon';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { computed, ref } from 'vue';
  import { useRequest } from '@ft/request';
  import type { ExpertGroupEditModalDT } from './data';
  import { schemas } from './data';
  import type { PositionItem } from '/@/api/expert-group';
  import { getBodyPositionList, saveExpertGroup, updateExpertGroup } from '/@/api/expert-group';

  const props = defineProps({
    groupCategory: {
      type: String,
      default: '',
    },
    groupCategoryName: {
      type: String,
      default: '',
    },
  });
  const mode = ref<'add' | 'edit'>('add');
  const emit = defineEmits(['register', 'success']);

  const getTitle = computed(() => {
    return mode.value === 'add' ? '新增专组' : '编辑专组';
  });

  const [registerForm, formAction] = useForm({
    labelWidth: 150,
    showActionButtonGroup: false,
    schemas,
  });

  const positionList = ref<PositionItem[]>([]);

  const [register, { closeModal }] = useModalInner<ExpertGroupEditModalDT>(async (_data) => {
    const data = JSON.parse(JSON.stringify(_data));
    mode.value = data.mode;
    await getBodyPositionList({ pageSize: 0, pageNum: 0, type: 1 }).then((res) => {
      positionList.value = res;
    });
    if (!data?.record) {
      formAction.setFieldsValue({
        groupCategory: props.groupCategory,
        groupCategoryName: props.groupCategoryName,
      });
    } else {
      formAction.setFieldsValue(data.record);
    }
  });

  const { runAsync: runSaveExpertGroup } = useRequest(
    (params) => (mode.value === 'add' ? saveExpertGroup(params) : updateExpertGroup(params)),
    {
      manual: true,
      showSuccessMessage: true,
    },
  );

  function onSelectChange(item, value) {
    const selected = positionList.value.find((item) => item.id === value);
    if (selected) {
      item.name = selected.name;
      item.id = selected.id;
      item.childList = (selected.childList || []).map((sub) => ({ name: sub.name, id: sub.id }));
    }
  }

  function handleAdd(model) {
    if (!model.tree) model.tree = [];
    model.tree.push({
      name: '',
      childList: [],
    });
  }

  function handleRemove(model, idx: number) {
    model.tree.splice(idx, 1);
  }

  function handleAddChild(item) {
    if (!item.childList) item.childList = [];
    item.childList.push({
      name: '',
    });
  }

  function handleRemoveChild(item, idx: number) {
    item.childList.splice(idx, 1);
  }

  function handleOk() {
    console.log('ok');
    formAction.validate().then((values) => {
      if (!values.receiveSex) {
        values.receiveSex = '';
      }
      if (!values.receiveEndAge) {
        values.receiveEndAge = '';
      }
      if (!values.receiveStartAge) {
        values.receiveStartAge = '';
      }
      runSaveExpertGroup(values).then(() => {
        emit('success');
        closeModal();
      });
    });
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="getTitle"
    :can-fullscreen="false"
    width="782px"
    centered
    :min-height="120"
    @register="register"
    @ok="handleOk"
    ok-text="保存"
  >
    <BasicForm @register="registerForm">
      <template #receiveAge="{ model }">
        <div class="flex items-center gap-2 w-182px">
          <InputNumber
            style="width: 80px !important"
            :min="0"
            v-model:value="model.receiveStartAge"
          />
          <div>至</div>
          <InputNumber
            style="width: 80px !important"
            class="w-80px"
            :min="0"
            v-model:value="model.receiveEndAge"
          />
        </div>
      </template>
      <template #tree="{ model }">
        <div class="h-240px of-y-auto w-400px">
          <div
            v-for="(item, index) in model.tree"
            :key="index"
            class="border-1 border-[#EDEEF0] p-4 w-320px mb-2"
          >
            <div class="flex items-center gap-2 mb-4">
              <Form.Item
                label="专组接诊部位"
                :name="['tree', index, 'id']"
                style="width: 254px; margin-bottom: 0"
                required
              >
                <Select
                  :value="item.id"
                  :options="positionList.map((item) => ({ label: item.name, value: item.id }))"
                  @change="onSelectChange(item, $event)"
                />
              </Form.Item>
              <Icon
                :size="24"
                class="cursor-pointer"
                icon="ant-design:minus-circle-outlined"
                @click="handleRemove(model, index)"
              />
            </div>

            <Form.Item label="部位症状" :name="['tree', index, 'childList']" required>
              <div
                v-for="(child, childIdx) in item.childList"
                :key="childIdx"
                class="flex items-center gap-2 mb-4"
              >
                <Form.Item
                  style="margin-bottom: 0"
                  :name="['tree', index, 'childList', childIdx, 'name']"
                  :rules="[{ required: true, message: '请输入部位症状' }]"
                >
                  <Input v-model:value="child.name" />
                </Form.Item>
                <Icon
                  :size="24"
                  class="cursor-pointer"
                  icon="ant-design:minus-circle-outlined"
                  @click="handleRemoveChild(item, childIdx)"
                />
              </div>
              <Icon
                :size="24"
                class="cursor-pointer"
                style="vertical-align: middle"
                icon="ant-design:plus-circle-outlined"
                @click="handleAddChild(item)"
              />
            </Form.Item>
          </div>
          <div class="h-32px flex items-center cursor-pointer" @click="handleAdd(model)">
            <Icon icon="ant-design:plus-circle-outlined" :size="24" />
          </div>
        </div>
      </template>
    </BasicForm>
  </BasicModal>
</template>
<style scoped></style>
