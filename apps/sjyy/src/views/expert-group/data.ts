import type { FormSchema } from '@ft/internal/components/Form';
import type { BasicColumn } from '@ft/internal/components/Table';
import type { IExpertGroup } from '/@/api/expert-group';

export interface ExpertGroupEditModalDT {
  mode: 'edit' | 'add';
  record?: IExpertGroup;
}

/**
 * 专组应用
 * 专组名称
 * 专组介绍
 */
export const schemas: FormSchema[] = [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'groupCategory',
    label: 'groupCategory',
    component: 'Input',
    show: false,
  },
  {
    field: 'groupCategoryName',
    label: '专组应用',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 6 } },
    required: true,
    rules: [
      {
        message: '超出字符限制，请控制在1-255个字符之间',
        trigger: 'change',
        max: 255,
      },
    ],
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'groupName',
    label: '专组名称',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 6 } },
    required: true,
    rules: [
      {
        message: '超出字符限制，请控制在1-255个字符之间',
        trigger: 'change',
        max: 255,
      },
    ],
  },
  {
    field: 'groupDescription',
    label: '专组介绍',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 18 } },
    required: true,
    rules: [
      {
        message: '超出字符限制，请控制在1-255个字符之间',
        trigger: 'change',
        max: 255,
      },
    ],
  },
  {
    field: 'receiveSex',
    label: '专组接诊性别',
    component: 'Select',
    componentProps: {
      options: [
        { label: '男', value: '1' },
        { label: '女', value: '2' },
      ],
    },
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 6 } },
  },
  {
    field: 'receiveAge',
    label: '专组接诊年龄',
    component: 'Input',
    slot: 'receiveAge',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 6 } },
  },
  {
    field: 'receiveStartAge',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'receiveEndAge',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'tree',
    label: '专病接诊部位及症状',
    component: 'Input',
    slot: 'tree',
    defaultValue: [],
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 6 } },
  },
];

/**
 * 专家列表
 */
export const ExpertColumns: BasicColumn[] = [
  {
    title: '专家姓名',
    dataIndex: 'expertName',
    width: 100,
  },
  {
    title: '性别',
    dataIndex: 'expertSexName',
    width: 100,
  },
  {
    title: '年龄',
    dataIndex: 'expertAge',
    width: 100,
  },
  {
    title: '民族',
    dataIndex: 'nation',
    width: 100,
  },
  {
    title: '职称',
    dataIndex: 'jobTitleName',
    width: 100,
  },
  {
    title: '主要成就',
    dataIndex: 'majorAchievement',
    width: 200,
  },
  {
    title: '服务领域',
    dataIndex: 'servicesRange',
    width: 200,
  },
  {
    title: '绑定专家组',
    dataIndex: 'expertGroupName',
    width: 140,
  },
  {
    title: '专家分级',
    dataIndex: 'expertLevelName',
    width: 100,
  },
  {
    title: '专家机构',
    dataIndex: 'orgName',
    width: 140,
  },
  {
    title: '所属科室',
    dataIndex: 'deptName',
    width: 140,
  },
];
