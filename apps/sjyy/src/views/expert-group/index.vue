<script setup lang="ts">
  import { StyledList } from '@ft/components';
  import { Button } from '@ft/internal/components/Button';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { computed, nextTick, ref, watch } from 'vue';
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { useModal } from '@ft/internal/components/Modal';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import { DictEnum, getDictItemList } from '@ft/internal/api';
  import type { ExpertGroupEditModalDT } from './data';
  import { ExpertColumns, schemas } from './data';
  import ExpertGroupAdd from './ExpertGroupAdd.vue';
  import MembersAdd from './MembersAdd.vue';
  import type { IExpertGroup } from '/@/api/expert-group';
  import {
    getExistExpertList,
    getExpertGroupList,
    getPositionTreeById,
    removeExpertGroup,
  } from '/@/api/expert-group';
  import { exportExpert } from '/@/api/expert';

  const activeGroupCategory = ref('');
  const activeGroupCategoryName = computed(() => {
    return groupCategoryItems.value.find((item) => item.value === activeGroupCategory.value)?.label;
  });
  const groupCategoryItems = ref<any>([]);
  useRequest(() => getDictItemList(DictEnum.GROUP_CATEGORY), {
    onSuccess(data) {
      groupCategoryItems.value =
        data?.map((item) => {
          return {
            label: item.dictItemName,
            value: item.dictItemCode,
          };
        }) || [];
      activeGroupCategory.value = data[0]?.dictItemCode || '';
    },
  });
  watch(activeGroupCategory, (val, oldVal) => {
    if (val === oldVal) return;
    nextTick(() => {
      getGroupListAsync(val);
    });
  });
  const activeGroupId = ref('');
  const mode = ref('view');
  const [register, formAction] = useForm({
    showActionButtonGroup: false,
    schemas,
    labelWidth: 150,
    disabled: computed(() => mode.value === 'view'),
  });
  const {
    data: groupList,
    refresh: refreshGroupList,
    runAsync: getGroupListAsync,
  } = useRequest(getExpertGroupList, {
    onSuccess(data) {
      activeGroupId.value = data[0]?.id || '';
      if (data.length === 0) formAction.resetFields();
      onGroupClick(activeGroupId.value);
    },
    manual: true,
  });

  const [registerGroupAdd, { openModal }] = useModal();
  const [registerMember, { openModal: openMember }] = useModal();

  function onAddGroup() {
    openModal(true, {
      mode: 'add',
    });
  }

  function onEdit() {
    const record = formAction.getFieldsValue() as IExpertGroup;
    openModal<ExpertGroupEditModalDT>(true, {
      mode: 'edit',
      record,
    });
  }

  const { createConfirm } = useMessage();

  const { runAsync: removeExpertRun } = useRequest(removeExpertGroup, {
    manual: true,
    showSuccessMessage: true,
  });
  function onDelete() {
    console.log('删除');
    createConfirm({
      title: '删除专组',
      iconType: 'warning',
      content: '确定删除该专组吗？',
      onOk: () => {
        removeExpertRun(activeGroupId.value).then(() => {
          refreshGroupList();
        });
      },
    });
  }

  const [registerTable, tableIns] = useTable({
    api: getExistExpertList,
    beforeFetch: () => {
      return {
        expertGroupId: activeGroupId.value,
      };
    },
    columns: ExpertColumns,
    resizeHeightOffset: 10,
    immediate: false,
  });

  watch(activeGroupId, (val, oldVal) => {
    if (val === oldVal) return;
    tableIns.reload();
  });
  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(exportExpert, {
    manual: true,
    showSuccessMessage: true,
  });
  function onExportExpert() {
    exportUtil(exportExpertRunAsync({ expertGroupId: activeGroupId.value }));
  }

  function onAddMember() {
    openMember(true);
  }

  async function onGroupClick(id: string) {
    activeGroupId.value = id;
    const values = groupList.value?.find((item) => item.id === activeGroupId.value);
    const tree = await getPositionTreeById({
      groupId: activeGroupId.value,
    });
    if (values) {
      values.tree = tree;
      formAction.setFieldsValue(values);
    }
  }
</script>

<template>
  <div class="h-full pr-2.5 pb-2.5">
    <div class="h-full flex gap-2.5">
      <div class="w-228px h-full flex flex-col bg-white rounded-lg rounded-lt-none">
        <div class="module-title flex justify-between items-center px-3 py-4">
          <span class="basic-title">专组应用</span>
        </div>
        <div class="px-3 pb-3 flex-1 basis-0 of-y-auto of-x-hidden min-h-0">
          <StyledList
            :width="204"
            v-model:model-value="activeGroupCategory"
            :items="groupCategoryItems || []"
          />
        </div>
      </div>
      <div class="flex-1 of-hidden flex flex-col gap-2.5">
        <div class="flex flex-col rounded-lg bg-white p-4 gap-4">
          <div class="flex items-center gap-6.25">
            <span class="basic-title">专家分组</span>
            <Button
              size="small"
              type="link"
              pre-icon="ant-design:plus-circle-outlined"
              @click="onAddGroup"
            >
              新增分组
            </Button>
          </div>
          <div class="tabs flex flex-wrap gap-4">
            <div
              class="tab-item min-w-130px truncate bg-[#F5F7FA] text-center py-2 cursor-pointer text-secondary-text-color hover:text-primary-color"
              v-for="(item, idx) in groupList"
              :class="[
                activeGroupId === item.id ? '!bg-[#ECF3FF] rounded !text-primary-color' : '',
              ]"
              :key="item.id + idx"
              @click="onGroupClick(item.id)"
            >
              {{ item.groupName }}
            </div>
          </div>
        </div>
        <div class="flex flex-col rounded-lg bg-white p-4">
          <div class="flex items-center gap-6.25">
            <span class="basic-title">专组详情</span>
            <div>
              <Button size="small" type="link" pre-icon="ant-design:edit-outlined" @click="onEdit">
                编辑
              </Button>
              <Button
                size="small"
                type="link"
                danger
                pre-icon="ant-design:delete-outlined"
                @click="onDelete"
              >
                删除
              </Button>
            </div>
          </div>
          <div class="mt-4">
            <BasicForm @register="register">
              <template #receiveAge="{ model }">
                <div v-if="model.receiveStartAge || model.receiveEndAge">
                  {{ model.receiveStartAge }}至{{ model.receiveEndAge }}岁
                </div>
              </template>
              <template #tree="{ model }">
                <div>{{
                  (model.tree || [])
                    .map((item) => item.childList.map((sub) => `${item.name}-${sub.name}`))
                    .join(',')
                }}</div>
              </template>
            </BasicForm>
          </div>
          <div class="">
            <BasicTable @register="registerTable">
              <template #headerTop>
                <div class="pb-2">
                  <span class="basic-title">专组介绍</span>
                </div>
              </template>
              <template #tableTitle>
                <Button type="primary" @click="onAddMember">导入成员</Button>
              </template>
              <template #toolbar>
                <Button :loading="exportLoading" @click="onExportExpert">导出</Button>
              </template>
            </BasicTable>
          </div>
        </div>
      </div>
    </div>
    <ExpertGroupAdd
      :groupCategory="activeGroupCategory"
      :groupCategoryName="activeGroupCategoryName"
      @register="registerGroupAdd"
      @success="refreshGroupList"
    />
    <MembersAdd
      :expert-group-id="activeGroupId"
      @register="registerMember"
      @success="tableIns.reload"
    />
  </div>
</template>
<style scoped></style>
