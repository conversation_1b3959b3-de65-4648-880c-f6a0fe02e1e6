<script setup lang="ts">
  import { Button } from '@ft/internal/components/Button';
  import { Icon } from '@ft/internal/components/Icon';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { Checkbox, CheckboxGroup, Input } from 'ant-design-vue';
  import type { CheckboxChangeEvent } from 'ant-design-vue/lib/checkbox/interface';
  import { computed, reactive, ref, watch } from 'vue';
  import type { IExpert } from '/@/api/expert';
  import { getExpertList } from '/@/api/expert';
  import { expertGroupBatchUpdate, getExistExpertList } from '/@/api/expert-group';
  import { useRequest } from '@ft/request';
  import { remove } from 'lodash-es';

  const props = defineProps<{
    expertGroupId: string;
  }>();

  const emit = defineEmits(['register', 'success']);

  // 生成一个长度为 10 的数组，内容是 label ，value 为 index
  const leftOptions = ref<IExpert[]>([]);
  const leftOptionsBackup = ref<IExpert[]>([]);

  const [register, { closeModal }] = useModalInner();

  function onVisibleChange(visible: boolean) {
    resetAll();
    if (!visible) return;
    getExpertList().then((expertList) => {
      getExistExpertList({
        expertGroupId: props.expertGroupId,
      }).then((currentExpertList) => {
        rightOptions.value = currentExpertList;
        leftOptions.value = expertList.filter(
          (item) => !currentExpertList.map((i) => i.id).includes(item.id),
        );
        bakOptions();
      });
    });
  }

  const leftSearchState = reactive({
    expertName: '',
    orgName: '',
    servicesRange: '',
  });

  // 添加生效的搜索状态，只有点击搜索按钮时才会更新
  const leftActiveSearchState = reactive({
    expertName: '',
    orgName: '',
    servicesRange: '',
  });

  const rightSearchState = reactive({
    expertName: '',
    orgName: '',
    servicesRange: '',
  });

  // 添加生效的搜索状态，只有点击搜索按钮时才会更新
  const rightActiveSearchState = reactive({
    expertName: '',
    orgName: '',
    servicesRange: '',
  });

  const leftFilterOptions = computed(() => {
    // 基于生效的搜索状态过滤数据，保留原有的点击搜索按钮逻辑
    return leftOptions.value.filter((item) => {
      return (
        item.expertName.includes(leftActiveSearchState.expertName) &&
        item.orgName.includes(leftActiveSearchState.orgName) &&
        item.servicesRange.includes(leftActiveSearchState.servicesRange)
      );
    });
  });
  const leftState = reactive({
    indeterminate: false,
    checkAll: false,
    checkedList: [] as string[],
  });

  function onLeftSearch() {
    // 点击搜索按钮时，将当前搜索条件应用到生效搜索状态
    leftActiveSearchState.expertName = leftSearchState.expertName;
    leftActiveSearchState.orgName = leftSearchState.orgName;
    leftActiveSearchState.servicesRange = leftSearchState.servicesRange;
  }

  function onCheckAllLeftChange(e) {
    // 基于过滤后的数据进行全选操作
    leftState.checkedList = e.target.checked ? leftFilterOptions.value.map((item) => item.id) : [];
    leftState.indeterminate = false;
  }

  const hookCheckedLeftList = computed({
    get() {
      return leftState.checkedList;
    },
    set() {
      return;
    },
  });

  function handleCheckLeftChange(e: CheckboxChangeEvent) {
    const checked = e.target.checked;
    if (checked) {
      leftState.checkedList.push(e.target.value);
    } else {
      leftState.checkedList = leftState.checkedList.filter((item) => item !== e.target.value);
    }
  }

  watch(
    () => leftState.checkedList,
    (val) => {
      // 基于过滤后的数据计算全选状态
      leftState.checkAll = val?.length
        ? leftFilterOptions.value.every((item) => val.includes(item.id))
        : leftState.checkAll;
      leftState.checkAll = leftFilterOptions.value.length === 0 ? false : leftState.checkAll;

      leftState.indeterminate = val.length === 0 ? false : !leftState.checkAll;
    },
    {
      deep: true,
    },
  );

  const rightState = reactive({
    indeterminate: false,
    checkAll: false,
    checkedList: [] as string[],
  });

  const rightOptions = ref<IExpert[]>([]);
  const rightOptionsBackup = ref<IExpert[]>([]);

  function onRightSearch() {
    // 点击搜索按钮时，将当前搜索条件应用到生效搜索状态
    rightActiveSearchState.expertName = rightSearchState.expertName;
    rightActiveSearchState.orgName = rightSearchState.orgName;
    rightActiveSearchState.servicesRange = rightSearchState.servicesRange;
  }

  function onCheckAllRightChange(e) {
    // 基于过滤后的数据进行全选操作
    rightState.checkedList = e.target.checked
      ? rightFilterOptions.value.map((item) => item.id)
      : [];
    rightState.indeterminate = false;
  }

  const rightFilterOptions = computed(() => {
    // 基于生效的搜索状态过滤数据，保留原有的点击搜索按钮逻辑
    return rightOptions.value.filter((item) => {
      return (
        item.expertName.includes(rightActiveSearchState.expertName) &&
        item.orgName.includes(rightActiveSearchState.orgName) &&
        item.servicesRange.includes(rightActiveSearchState.servicesRange)
      );
    });
  });

  const hookCheckedRightList = computed({
    get() {
      return rightState.checkedList;
    },
    set() {
      return;
    },
  });

  function handleCheckRightChange(e: CheckboxChangeEvent) {
    const checked = e.target.checked;
    if (checked) {
      rightState.checkedList.push(e.target.value);
    } else {
      rightState.checkedList = rightState.checkedList.filter((item) => item !== e.target.value);
    }
  }

  watch(
    () => rightState.checkedList,
    (val) => {
      // 基于过滤后的数据计算全选状态
      rightState.checkAll = val?.length === rightFilterOptions.value.length;
      rightState.checkAll = rightFilterOptions.value.length === 0 ? false : rightState.checkAll;
      rightState.indeterminate = val.length === 0 ? false : !rightState.checkAll;
    },
    {
      deep: true,
    },
  );

  function bakOptions() {
    leftOptionsBackup.value = leftOptions.value;
    rightOptionsBackup.value = rightOptions.value;
  }
  function handleLeftToRight() {
    const transferData = remove(leftOptions.value, (item) =>
      leftState.checkedList.includes(item.id),
    );
    rightOptions.value = rightOptions.value.concat(transferData);
    leftState.checkedList = [];
    bakOptions();
  }

  function handleRightToLeft() {
    const transferData = remove(rightOptions.value, (item) =>
      rightState.checkedList.includes(item.id),
    );
    leftOptions.value = leftOptions.value.concat(transferData);
    rightState.checkedList = [];

    bakOptions();
  }

  const { runAsync: updateGroupMembers } = useRequest(expertGroupBatchUpdate, {
    manual: true,
    showSuccessMessage: true,
  });

  function resetAll() {
    leftOptions.value = [];
    rightOptions.value = [];
    leftOptionsBackup.value = [];
    rightOptionsBackup.value = [];

    leftState.checkAll = false;
    leftState.indeterminate = false;
    leftState.checkedList = [];

    rightState.checkAll = false;
    rightState.indeterminate = false;
    rightState.checkedList = [];

    // 重置搜索条件和生效搜索状态
    leftSearchState.expertName = '';
    leftSearchState.orgName = '';
    leftSearchState.servicesRange = '';

    leftActiveSearchState.expertName = '';
    leftActiveSearchState.orgName = '';
    leftActiveSearchState.servicesRange = '';

    rightSearchState.expertName = '';
    rightSearchState.orgName = '';
    rightSearchState.servicesRange = '';

    rightActiveSearchState.expertName = '';
    rightActiveSearchState.orgName = '';
    rightActiveSearchState.servicesRange = '';
  }

  function handleOk() {
    updateGroupMembers({
      expertGroupId: props.expertGroupId,
      expertIdList: rightOptions.value.map((item) => item.id),
    }).then(() => {
      emit('success');
      closeModal();
    });
  }
</script>

<template>
  <BasicModal
    destroy-on-close
    v-bind="$attrs"
    title="编辑成员"
    :can-fullscreen="false"
    width="944px"
    centered
    :min-height="455"
    @register="register"
    @ok="handleOk"
    ok-text="保存"
    @visible-change="onVisibleChange"
  >
    <div class="h-455px flex">
      <div class="flex flex-col border-1 border-[#DCDFE6] flex-1 rounded of-hidden w-25.7222vw">
        <div class="header border-b-1 border-[#DCDFE6]">
          <div
            class="title flex items-center justify-between px-4 bg-[#FAFAFA] min-h-38px rounded-t border-b border-[#DCDFE6]"
          >
            <div class="left">
              <Checkbox
                :indeterminate="leftState.indeterminate"
                v-model:checked="leftState.checkAll"
                @change="onCheckAllLeftChange"
                class="check-all"
              />
              <span class="ml-2">可选成员</span>
            </div>
            <div class="right">
              <span class="text-[#999]">
                {{ leftState.checkedList.length }}/{{ leftFilterOptions.length }}
              </span>
            </div>
          </div>
          <div class="search flex flex-col gap-2 px-4 py-2">
            <div class="flex gap-4">
              <Input class="flex-1" placeholder="名称" v-model:value="leftSearchState.expertName" />
              <Input class="flex-1" placeholder="机构" v-model:value="leftSearchState.orgName" />
            </div>
            <div class="flex-1 flex gap-4">
              <div class="flex-1">
                <Input placeholder="服务领域" v-model:value="leftSearchState.servicesRange" />
              </div>
              <div class="flex-1 text-right">
                <Button type="primary" @click="onLeftSearch">查询</Button>
              </div>
            </div>
          </div>
        </div>
        <div class="transfer-body bg-white p-4 of-auto min-h-0 flex-1 !basis-0">
          <CheckboxGroup class="w-full" v-model:value="hookCheckedLeftList">
            <div
              class="transfer-item flex gap-2 rounded px-2 py-1.5 hover:bg-[#ECF3FF] cursor-pointer"
              v-for="user in leftFilterOptions"
              :key="user.id"
            >
              <Checkbox class="item-checkbox" :value="user.id" @change="handleCheckLeftChange" />
              <span
                :title="`${user.expertName} ${user.deptName} ${user.orgName} ${user.servicesRange}`"
                class="truncate"
              >
                <span>{{ user.expertName }}</span>
                <span class="mx-3">{{ user.deptName }}</span>
                <span class="mr-3">{{ user.orgName }}</span>
                <span>{{ user.servicesRange }}</span>
              </span>
            </div>
          </CheckboxGroup>
        </div>
      </div>
      <div class="w-64px grow-0 shrink-0 flex justify-center items-center">
        <div class="flex flex-col gap-6">
          <span
            :class="[
              leftState.checkedList.length ? 'cursor-pointer bg-primary-color text-white' : '',
            ]"
            class="w-32px h-32px flex items-center justify-center border-1px border-[#DCDFE6] rounded text-[##999999]"
            @click="handleLeftToRight"
          >
            <Icon class="transform -rotate-90" :size="16" icon="ant-design:down-outlined" />
          </span>
          <span
            :class="[
              rightState.checkedList.length ? 'cursor-pointer bg-primary-color text-white' : '',
            ]"
            class="w-32px h-32px flex items-center justify-center border-1px border-[#DCDFE6] rounded text-[##999999]"
            @click="handleRightToLeft"
          >
            <Icon class="transform rotate-90" :size="16" icon="ant-design:down-outlined" />
          </span>
        </div>
      </div>
      <div class="flex flex-col border-1 border-[#DCDFE6] flex-1 rounded of-hidden w-25.7222vw">
        <div class="header border-b-1 border-[#DCDFE6]">
          <div
            class="title flex items-center justify-between px-4 bg-[#FAFAFA] min-h-38px rounded-t border-b border-[#DCDFE6]"
          >
            <div class="left">
              <Checkbox
                :indeterminate="rightState.indeterminate"
                v-model:checked="rightState.checkAll"
                @change="onCheckAllRightChange"
                class="check-all"
              />
              <span class="ml-2">已选成员</span>
            </div>
            <div class="right">
              <span class="text-[#999]">
                {{ rightState.checkedList.length }}/{{ rightFilterOptions.length }}
              </span>
            </div>
          </div>
          <div class="search flex flex-col gap-2 px-4 py-2">
            <div class="flex gap-4">
              <Input placeholder="名称" v-model:value="rightSearchState.expertName" />
              <Input placeholder="机构" v-model:value="rightSearchState.orgName" />
            </div>
            <div class="flex gap-4">
              <div class="flex-1">
                <Input placeholder="服务领域" v-model:value="rightSearchState.servicesRange" />
              </div>
              <div class="flex-1 text-right">
                <Button type="primary" @click="onRightSearch">查询</Button>
              </div>
            </div>
          </div>
        </div>
        <div class="transfer-body bg-white p-4 of-auto min-h-0 flex-1 !basis-0">
          <CheckboxGroup class="w-full" v-model:value="hookCheckedRightList">
            <div
              class="transfer-item flex gap-2 rounded px-2 py-1.5 hover:bg-[#ECF3FF] cursor-pointer"
              v-for="user in rightFilterOptions"
              :key="user.id"
            >
              <Checkbox class="item-checkbox" :value="user.id" @change="handleCheckRightChange" />
              <span
                :title="`${user.expertName} ${user.deptName} ${user.orgName} ${user.servicesRange}`"
                class="truncate"
              >
                <span>{{ user.expertName }}</span>
                <span class="mx-3">{{ user.deptName }}</span>
                <span class="mr-3">{{ user.orgName }}</span>
                <span>{{ user.servicesRange }}</span>
              </span>
            </div>
          </CheckboxGroup>
        </div>
      </div>
    </div>
  </BasicModal>
</template>
<style scoped></style>
