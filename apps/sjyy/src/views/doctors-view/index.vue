<script setup lang="ts">
  import { Tabs } from 'ant-design-vue';
  import type { Ref } from 'vue';
  import { computed, ref } from 'vue';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { StyledList } from '@ft/components';
  import { useRequest } from '@ft/request';
  import { getPatientInfo } from '@ft/internal/utils/auth';
  import { useLoading } from '@ft/internal/components/Loading';
  import PatientInfo from '../patient-management/PatientInfo.vue';
  import stLabel from '/@/assets/images/st-label.png';
  import zyLabel from '/@/assets/images/zy-label.png';
  import type { VisitRecordItem } from '/@/api/doctors-view';
  import { getInspectionException, getVisitCount, getVisitRecord } from '/@/api/doctors-view';
  import { searchFormSchema } from './data';

  import VisitDetails from './VisitDetails.vue';

  const activeId = ref();
  const activeKey1 = ref();
  const patientInfo = computed(() => {
    return getPatientInfo();
  });
  const [registerForm, { getFieldsValue }] = useForm({
    labelWidth: 0,
    schemas: searchFormSchema,
    actionColOptions: {
      // @ts-ignore
      span: 24,
      //折叠
      style: {
        textAlign: 'right',
      },
    },
    showAdvancedButton: true,
    autoAdvancedLine: 1,
    showActionButtonGroup: true,
    showResetButton: false,
    submitFunc: async () => {
      getVisitRecordRefresh();
      getVisitCountRunAsync();
    },
  });
  const activeItem = ref();
  const VisitDetailsRef = ref<ElRef>(null);
  const [openWrapLoading, closeWrapLoading] = useLoading({
    target: VisitDetailsRef.value,
    props: {
      tip: '加载中...',
      absolute: true,
    },
  });

  const styledListLoading = ref(false);

  const {
    data: visitRecordList = [] as unknown as Ref<VisitRecordItem[]>,
    refresh: getVisitRecordRefresh,
  } = useRequest(
    () =>
      getVisitRecord({
        idCardNo: patientInfo.value.idCardNo,
        ...getFieldsValue(),
      }),
    {
      onBefore: () => {
        openWrapLoading();
        styledListLoading.value = true;
      },
      onSuccess: (data) => {
        styledListLoading.value = false;
        closeWrapLoading();
        activeId.value = data.length > 0 ? data[0]?.id : '';
        activeItem.value = data[0];
        getExceptionListRunAsync();
      },
    },
  );
  const { data: visitCountInfo, runAsync: getVisitCountRunAsync } = useRequest(() =>
    getVisitCount({
      idCardNo: patientInfo.value.idCardNo,
      ...getFieldsValue(),
    }),
  );
  const outInPatientNo = computed(() => {
    return activeItem.value?.visitTypeCode === '3'
      ? activeItem.value?.inpatientNo
      : activeItem.value?.outpatientNo;
  });
  const { data: exceptionList, runAsync: getExceptionListRunAsync } = useRequest(
    () => getInspectionException(outInPatientNo.value),
    {
      ready: activeId,
      manual: true,
    },
  );
  function handleChange() {
    getExceptionListRunAsync();
  }
</script>
<template>
  <div class="doctors-view w-full h-full pr-4 flex justify-between gap-10px pb-10px">
    <div class="flex flex-col gap-10px w-228px">
      <PatientInfo :medicalShow="false" />
      <div class="flex-1 bg-#fff rounded-2 p-3 flex flex-col gap-2">
        <div>患者就诊记录</div>
        <div class="rounded-2 bg-#FAFAFA p-10px flex flex-col gap-2">
          <BasicForm @register="registerForm" />
        </div>
        <Tabs v-model:activeKey="activeKey1">
          <Tabs.TabPane key="1" tab="全部" />
          <!-- <Tabs.TabPane key="2" tab="传染病诊疗">2 </Tabs.TabPane>
          <Tabs.TabPane key="3" tab="常规诊疗">3 </Tabs.TabPane> -->
        </Tabs>
        <div class="flex flex-col gap-2 flex-1">
          <div class="flex justify-evenly">
            <!-- <div>
              <span class="text-#999">急诊：</span>
              <span class="text-primary-color fw-bold">{{
                visitCountInfo?.emergencyCount || 0
              }}</span>
            </div> -->
            <div>
              <span class="text-#999">门诊：</span>
              <span class="text-primary-color fw-bold">{{
                visitCountInfo?.outpatientCount || 0
              }}</span>
            </div>
            <div>
              <span class="text-#999">住院：</span>
              <span class="text-primary-color fw-bold">{{
                visitCountInfo?.inpatientCount || 0
              }}</span>
            </div>
          </div>
          <div class="flex-1 basis-0 of-y-auto of-x-hidden">
            <StyledList
              :items="visitRecordList || []"
              v-model="activeId"
              v-model:value="activeItem"
              valueField="id"
              class="flex-1 doctors-view-styled-list"
              :width="204"
              :loading="styledListLoading"
              @change="handleChange"
            >
              <template #default="item">
                <div class="flex flex-col gap-1px flex-1 py-2 pr-2 rounded-2">
                  <div class="flex justify-between items-center">
                    <div class="w-6px h-6px rounded-50% !bg-#E7E7E7 relative -left-2px"></div>
                    <div
                      class="text-#999 text-12px line-height-24px flex flex-col justify-start gap-1 flex-1 ml-8px"
                    >
                      <div v-if="item.visitTypeCode === '3'">{{
                        item?.admitHospitalTime?.substr(0, 10) || '-'
                      }}</div>
                      <div v-else>{{ item?.visitDate?.substr(0, 10) || '-' }}</div>
                    </div>
                    <div>
                      <div
                        v-if="item.visitTypeCode === '3'"
                        class="text-#FF7D34 rounded-20% bg-#FFE6D8 px-1 py-2px text-12px fw-bold"
                      >
                        住
                      </div>
                      <div
                        v-else
                        class="text-#4c81ff rounded-20% bg-#D6E2FF px-1 text-12px fw-bold"
                      >
                        门
                      </div>
                    </div>
                  </div>
                  <div class="b-l-1px b-color-#E7E7E7 flex flex-col p-l-10px gap-1">
                    <div class="text-14px text-#EE62EE">{{ item?.diagnoseDiseaseName }}</div>
                    <div class="absolute top-0 -left-4 w-1px h-full bg-#E7E7E7"></div>
                    <!-- <div class="w-[calc(100%-20px)]">
                          【{{ item?.diagnosisDept }}】{{ item?.hospitalOrg }}
                        </div> -->
                    <div>
                      <span
                        class="border-0.5px border-#32BF2F px-2 py-2px text-#32BF2F rounded-1 bg-#E8FAE7"
                        >{{ item?.diagnosisDept }}</span
                      >
                    </div>
                    <div class="text-#666666">{{ item?.hospitalOrg }}</div>
                  </div>
                </div>
              </template>
            </StyledList>
          </div>
        </div>
      </div>
    </div>
    <div ref="VisitDetailsRef" class="flex-1 flex gap-10px flex-col min-w-0">
      <div class="rounded-2 bg-#fff p-4 relative">
        <img
          :src="activeItem?.visitTypeCode === '3' ? zyLabel : stLabel"
          class="h-50px absolute z-2 -left-20px top-50% -mt-25px"
        />
        <div class="flex justify-start gap-10 pl-53px">
          <div class="flex">
            <div class="text-#999">门诊号：</div>
            <div class="text-#333">{{ activeItem?.outpatientNo || '-' }}</div>
          </div>
          <div class="flex">
            <div class="text-#999">就诊时间：</div>
            <div class="text-#333">{{ activeItem?.visitDate || '-' }}</div>
          </div>
          <div class="flex">
            <div class="text-#999">就诊机构：</div>
            <div class="text-#333">{{ activeItem?.hospitalOrg || '-' }}</div>
          </div>
          <div class="flex">
            <div class="text-#999">科室：</div>
            <div class="text-#333">{{ activeItem?.diagnosisDept || '-' }}</div>
          </div>
          <div class="flex">
            <div class="text-#999">医生：</div>
            <div class="text-#333">{{ activeItem?.diagnosisDoctor || '-' }}</div>
          </div>
        </div>
        <div class="absolute top-0 right-4 bg-#FFF1EA rounded-[0_0_8px_8px] flex py-2 px-3">
          <div class="text-#999">检验：</div>
          <div class="text-#FF7D34">异常{{ exceptionList?.length || 0 }}</div>
        </div>
      </div>
      <div class="flex-1 bg-#fff rounded-2 p-4 flex flex-col gap-4">
        <div class="flex items-center gap-2">
          <span class="inline-block bg-primary-color w-3px h-1em"></span>
          <span class="text-#333333 fw-bold">患者就诊详情</span>
        </div>
        <VisitDetails :activeItem="activeItem" :patientInfo="patientInfo" />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
  .doctors-view {
    :deep {
      .vben-basic-form .ant-form-item:not(.ant-form-item-with-help) {
        margin-bottom: 8px;
      }

      .vben-basic-table .ant-table-wrapper {
        padding: 0;
      }

      .vben-basic-table-form-container {
        padding: 0;
      }

      .vben-basic-table-form-container .ant-form,
      .vben-basic-table .ant-table-wrapper {
        border-radius: 4px;
      }
    }

    .doctors-view-styled-list {
      :deep {
        .ant-timeline-item {
          padding-bottom: 0;
        }

        .ant-timeline-item-content {
          margin-left: 20px;
        }

        .ant-timeline-item-head {
          background-color: transparent;
        }
      }
    }
  }
</style>
