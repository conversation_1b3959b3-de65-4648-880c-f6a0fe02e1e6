<script setup lang="ts">
  import { Tabs } from 'ant-design-vue';
  import { computed, ref, watch } from 'vue';
  import OutpatientPrescriptions from './component/OutpatientPrescriptions.vue';
  import OutpatientRecord from './component/OutpatientRecord.vue';
  import CheckReport from './component/CheckReport.vue';
  import InspectionReport from './component/InspectionReport.vue';
  import InpatientAdvice from './component/InpatientAdvice.vue';
  import InpatientRecords from './component/InpatientRecords.vue';
  import NursingDocuments from './component/NursingDocuments.vue';
  import SurgicalAnesthesia from './component/SurgicalAnesthesia.vue';
  import AideDiagnosisDetails from './component/AideDiagnosisDetails.vue';
  import DiagnosisTreatment from './component/DiagnosisTreatment.vue';

  defineOptions({
    components: {
      OutpatientPrescriptions,
      OutpatientRecord,
      CheckReport,
      InspectionReport,
      InpatientAdvice,
      InpatientRecords,
      NursingDocuments,
      SurgicalAnesthesia,
      AideDiagnosisDetails,
      DiagnosisTreatment,
    },
  });

  const componentsMap = {
    1: [
      {
        tab: '门诊处方',
        key: 'OutpatientPrescriptions',
      },
      {
        tab: '门诊病历',
        key: 'OutpatientRecord',
      },
      {
        tab: '检查报告',
        key: 'CheckReport',
      },
      {
        tab: '检验报告',
        key: 'InspectionReport',
      },
    ],
    // 救治规范系统-不明原因/重点重症模块患者详情页面使用
    4: [
      {
        tab: '住院医嘱',
        key: 'InpatientAdvice',
      },
      {
        tab: '住院病历',
        key: 'InpatientRecords',
      },
      {
        tab: '检查报告',
        key: 'CheckReport',
      },
      {
        tab: '检验报告',
        key: 'InspectionReport',
      },
      {
        tab: '辅助诊断',
        key: 'AideDiagnosisDetails',
      },
    ],
    3: [
      {
        tab: '住院医嘱',
        key: 'InpatientAdvice',
      },
      {
        tab: '住院病历',
        key: 'InpatientRecords',
      },
      {
        tab: '检查报告',
        key: 'CheckReport',
      },
      {
        tab: '检验报告',
        key: 'InspectionReport',
      },
      {
        tab: '护理文书',
        key: 'NursingDocuments',
      },
      {
        tab: '诊疗情况',
        key: 'DiagnosisTreatment',
      },
      // {
      //   tab: '手术麻醉',
      //   key: 'SurgicalAnesthesia',
      // },
    ],
  };

  const props = defineProps({
    activeItem: {
      type: Object,
      default: () => {},
    },
    patientInfo: {
      type: Object,
      default: () => {},
    },
    // 监测预警数据报表传染病流调追踪使用
    simpleMode: {
      type: Boolean,
      default: false,
    },
  });
  const currentComponents = computed(() => {
    if (!props.simpleMode) {
      return componentsMap[props.activeItem?.visitTypeCode || '1'] || [];
    }

    // 监测预警数据报表传染病流调追踪使用
    const simpleComponentsMap = {
      1: [
        {
          tab: '门诊处方',
          key: 'OutpatientPrescriptions',
        },
      ],
      3: [
        {
          tab: '住院医嘱',
          key: 'InpatientAdvice',
        },
        {
          tab: '住院病历',
          key: 'InpatientRecords',
        },
      ],
    };

    return simpleComponentsMap[props.activeItem?.visitTypeCode || '1'] || [];
  });
  const visitDetailsKey = ref('OutpatientPrescriptions');

  watch(
    () => props.activeItem,
    (val) => {
      visitDetailsKey.value = componentsMap[val?.visitTypeCode || '1'][0].key;
    },
    {
      deep: true,
      immediate: true,
    },
  );
</script>

<template>
  <div class="flex-1 of-hidden flex flex-col">
    <Tabs v-model:activeKey="visitDetailsKey" :destroyInactiveTabPane="true">
      <!-- 门诊处方 门诊病历 检查报告 检验报告 -->
      <Tabs.TabPane v-for="item in currentComponents" :key="item.key" :tab="item.tab" />
    </Tabs>
    <component
      :is="visitDetailsKey"
      :idCardNo="patientInfo?.idCardNo"
      :pkVisitInfo="activeItem?.id"
      :patientInfo="patientInfo"
      :activeItemParent="activeItem"
    />
  </div>
</template>
