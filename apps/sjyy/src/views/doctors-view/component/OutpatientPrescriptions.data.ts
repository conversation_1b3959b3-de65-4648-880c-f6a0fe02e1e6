import type { BasicColumn } from '@ft/internal/components/Table';

/**
 * @description: 表格列
 * 处方类型
  处方项目
  用
  用法
  频次
  天数
  总量
  单价
  金额
 */
export const columns: BasicColumn[] = [
  {
    title: '处方类型',
    dataIndex: 'prescriptionType',
    width: 100,
    align: 'center',
  },
  {
    title: '处方项目',
    dataIndex: 'medicOrdersItem',
    width: 100,
    align: 'center',
  },
  // {
  //   title: '用法',
  //   dataIndex: 'tcmUsageMethod',
  //   width: 100,
  //   align: 'center',
  // },
  {
    title: '用量',
    dataIndex: 'yl',
    width: 100,
    align: 'center',
  },
  {
    title: '频次',
    dataIndex: 'drugUseFrequency',
    width: 100,
    align: 'center',
  },
  // {
  //   title: '天数',
  //   dataIndex: 'prescriptionEffectiveDays',
  //   width: 100,
  //   align: 'center',
  // },
  {
    title: '总量',
    dataIndex: 'totalDose',
    width: 100,
    align: 'center',
  },
  // {
  //   title: '单价',
  //   dataIndex: 'unitPrice',
  //   width: 100,
  //   align: 'center',
  // },
  {
    title: '金额',
    dataIndex: 'amount',
    width: 100,
    align: 'center',
  },
];
