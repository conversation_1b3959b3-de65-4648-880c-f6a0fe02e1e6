<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { watch } from 'vue';
  import { columns } from './InpatientAdvice.data';
  import { medicalOrders } from '/@/api/doctors-view';
  const props = defineProps({
    idCardNo: {
      type: String,
      default: '',
    },
    activeItemParent: {
      type: Object,
      default: () => {},
    },
  });
  watch(
    () => props.activeItemParent,
    () => {
      reload();
    },
    {
      deep: true,
    },
  );

  /**
   * @description 住院医嘱
   */

  const [registerTable, { reload }] = useTable({
    fetchSetting: {
      sizeField: 'pageSize',
      pageField: 'pageNum',
      listField: 'list',
    },
    columns,
    api: medicalOrders,
    useSearchForm: false,
    showIndexColumn: true,
    bordered: false,
    immediate: !!props?.activeItemParent?.inpatientNo,
    size: 'small',
    resizeHeightOffset: 20,
    beforeFetch: (params) => {
      params.idCardNo = props.idCardNo;
      params.inpatientNo = props?.activeItemParent?.inpatientNo;
      return params;
    },
  });
</script>
<template>
  <div class="h-full flex flex-col gap-4">
    <div class="flex items-center gap-2 bg-#FAFAFA">
      <span class="inline-block bg-primary-color w-2px h-8 mr-3"></span>
      <span class="text-#333333 py-5px">住院医嘱</span>
    </div>
    <div class="basis-0 of-y-auto of-x-hidden flex-1">
      <BasicTable @register="registerTable" />
    </div>
  </div>
</template>

<style lang="less" scoped>
  :deep {
    .vben-basic-table .ant-table-wrapper {
      padding: 0;
    }

    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table-form-container .ant-form,
    .vben-basic-table .ant-table-wrapper {
      border-radius: 4px;
    }
  }
</style>
