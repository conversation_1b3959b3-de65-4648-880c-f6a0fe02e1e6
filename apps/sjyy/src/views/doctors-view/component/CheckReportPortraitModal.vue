<script setup lang="ts">
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { ref } from 'vue';
  import { Empty } from 'ant-design-vue';
  const emit = defineEmits(['register', 'success']);
  const url = ref<string>('');

  const [register, { closeModal }] = useModalInner((data) => {
    url.value = data.examineReportContentUrl;
  });

  async function handleOk() {
    emit('success', '');
    closeModal();
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    title="查看影像"
    :can-fullscreen="false"
    :minHeight="600"
    :height="600"
    width="70%"
    @register="register"
    @ok="handleOk"
    :show-cancel-btn="false"
  >
    <div class="h-full flex items-center of-y-auto of-x-hidden">
      <iframe
        v-if="url && url != ''"
        :src="url"
        class="flex-1 min-h-600px"
        width="100%"
        frameborder="0"
      ></iframe>
      <Empty v-else :image="Empty.PRESENTED_IMAGE_SIMPLE" />
    </div>
  </BasicModal>
</template>
