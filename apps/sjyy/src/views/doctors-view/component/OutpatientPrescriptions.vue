<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { watch } from 'vue';
  import { columns } from './OutpatientPrescriptions.data';
  import { prescription } from '/@/api/doctors-view';

  /**
   * @description 门诊处方
   */
  const props = defineProps({
    idCardNo: {
      type: String,
      default: '',
    },
    activeItemParent: {
      type: Object,
      default: () => {},
    },
  });
  watch(
    () => props.activeItemParent,
    (val) => {
      if (!val?.outpatientNo) return setTableData([]);
      reload();
    },
    {
      deep: true,
    },
  );
  // const OutpatientPrescriptionsRef = ref<ElRef>(null);
  // const [openWrapLoading, closeWrapLoading] = useLoading({
  //   target: OutpatientPrescriptionsRef.value,
  //   props: {
  //     tip: '加载中...',
  //     absolute: true,
  //   },
  // });
  const [registerTable, { reload, setTableData }] = useTable({
    api: prescription,
    columns,
    useSearchForm: false,
    showIndexColumn: false,
    bordered: false,
    immediate: !!props?.activeItemParent?.inpatientNo,
    size: 'small',
    beforeFetch: (params) => {
      params.idCardNo = props.idCardNo;
      params.outpatientNo = props?.activeItemParent?.outpatientNo;
      return params;
    },
    resizeHeightOffset: 20,
    pagination: false,
    canResize: false,
  });

  // const { data: prescriptionList, runAsync: getPrescriptionRunAsync } = useRequest(prescription, {
  //   manual: true,
  //   onBefore: () => {
  //     openWrapLoading();
  //   },
  //   onSuccess: () => {
  //     closeWrapLoading();
  //   },
  // });
</script>
<template>
  <div ref="OutpatientPrescriptionsRef" class="h-full flex flex-col gap-4">
    <div class="flex items-center gap-2 bg-#FAFAFA">
      <span class="inline-block bg-primary-color w-2px h-8 mr-3"></span>
      <span class="text-#333333 py-5px">门诊处方</span>
    </div>
    <BasicTable @register="registerTable" size="small" />

    <!-- <div class="basis-0 of-y-auto of-x-hidden flex-1">
      <div class="flex-1 flex flex-col gap-3 of-y-auto of-x-hidden flex-1">
        <BasicTable @register="registerTable" size="small" />
        <div v-for="(item, index) in prescriptionList" :key="index" class="flex flex-col gap-3">
          <div class="flex justify-between mt-4">
            <div class="text-#666 fw-bold">{{ item?.prescriptionType }}</div>
            <div class="flex justify-start gap-10 pl-53px">
              <div class="flex">
                <div class="text-#999">处方医生：</div>
                <div class="text-#333">{{ item?.diagnosisDoctor }}</div>
              </div>
              <div class="flex">
                <div class="text-#999">处方时间：</div>
                <div class="text-#333">{{ item?.prescriptionDate }}</div>
              </div>
            </div>
          </div>
          <BasicTable
            v-if="item?.prescriptionType === '西药处方'"
            :columns="columns"
            :dataSource="item?.medicinelist || []"
            :bordered="false"
            size="small"
            :resizeHeightOffset="20"
            :pagination="false"
            :canResize="false"
          />
          <div v-else>
            <div class="p-4 border-1px border-#DCDFE6 rounded-1 mt-10px flex gap-4 flex-wrap">
              <div
                class="text-14px text-#333333 py-1 px-2 border-1px border-#DCDFE6 rounded-1"
                v-for="(i, index) in item.medicinelist"
                :key="index"
                >{{ i.medicOrdersItem }}{{ i.drugDosageUnit }}</div
              >
            </div>
            <div class="flex justify-start gap-60px py-2 px-4">
              <div class="flex">
                <div class="text-#999">共 {{ item?.chineseMedicine?.dosage || 0 }} 剂</div>
              </div>
              <div class="flex">
                <div class="text-#999">用法：</div>
                <div class="text-#333">{{ item?.chineseMedicine?.tcmUsageMethod }}</div>
              </div>
              <div class="flex">
                <div class="text-#999">一次用量：</div>
                <div class="text-#333">{{ item?.chineseMedicine?.yl }}</div>
              </div>
              <div class="flex">
                <div class="text-#999">医生：</div>
                <div class="text-#333">{{ item?.chineseMedicine?.doctor }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div> -->
  </div>
</template>

<style lang="less" scoped>
  :deep {
    .vben-basic-table .ant-table-wrapper {
      padding: 0;
    }

    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table-form-container .ant-form,
    .vben-basic-table .ant-table-wrapper {
      border-radius: 4px;
    }
  }
</style>
