<script setup lang="ts">
  import { ref } from 'vue';
  import { StyledList } from '@ft/components';
  import { Empty } from 'ant-design-vue';
  import { useRequest } from '@ft/request';
  import { surgicalAnesthesia } from '/@/api/doctors-view';

  /**
   * @description 手术麻醉
   */
  const props = defineProps({
    idCardNo: {
      type: String,
      default: '',
    },
    activeItemParent: {
      type: Object,
      default: () => {},
    },
  });
  const activeId = ref();
  const activeItem = ref();
  const typeParam = {
    '1': 'outpatientNo',
    '3': 'inpatientNo',
  };
  const { loading: loading, data } = useRequest(
    () =>
      surgicalAnesthesia({
        idCardNo: props.idCardNo,
        //visitTypeCode	就诊类型代码：1门诊，2急诊，3住院
        [typeParam[props.activeItemParent.visitTypeCode]]:
          props.activeItemParent[typeParam[props.activeItemParent.visitTypeCode]],
      }),
    {
      ready: !!props.activeItemParent?.outpatientNo,
      onSuccess: (data) => {
        activeId.value = data.length > 0 ? data[0].admitHospitalRecordTime : '';
        activeItem.value = data?.[0] || {};
      },
    },
  );
</script>
<template>
  <div class="h-full flex flex-col gap-4">
    <div class="flex items-center gap-2 bg-#FAFAFA">
      <span class="inline-block bg-primary-color w-2px h-8 mr-3"></span>
      <span class="text-#333333 py-5px">手术麻醉</span>
    </div>
    <div class="h-full basis-0 of-y-auto of-x-hidden flex-1 flex">
      <div class="w-186px pr-4 b-r-1px b-r-solid b-r-#EDEEF0 of-y-auto of-x-hidden min-w-0">
        <StyledList
          v-model="activeId"
          v-model:value="activeItem"
          valueField="admitHospitalRecordTime"
          class="flex-1"
          :items="data || []"
          :loading="loading"
        >
          <template #default="item">
            <div class="flex flex-col gap-0 py-2 py-3">
              <div class="text-#333333">{{ item.admitHospitalRecord }}</div>
              <div class="text-#999">{{ item.admitHospitalRecordTime }}</div>
            </div>
          </template>
        </StyledList>
      </div>
      <div class="px-4 flex-1 flex flex-col of-y-auto of-x-hidden min-w-0">
        <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped></style>
