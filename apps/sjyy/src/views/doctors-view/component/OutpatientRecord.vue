<script setup lang="ts">
  import { useRequest } from '@ft/request';
  import { medicalRecord } from '/@/api/doctors-view';
  import { ref } from 'vue';
  import { Descriptions, Empty } from 'ant-design-vue';
  import { StyledList } from '@ft/components';

  import { useLoading } from '@ft/internal/components/Loading';

  /**
   * @description 门诊病历
   */

  const props = defineProps({
    idCardNo: {
      type: String,
      default: '',
    },
    activeItemParent: {
      type: Object,
      default: () => {},
    },
  });
  const activeId = ref<any>(undefined);
  const activeItem = ref();
  const OutpatientRecordRef = ref<ElRef>(null);
  const [openWrapLoading, closeWrapLoading] = useLoading({
    target: OutpatientRecordRef.value,
    props: {
      tip: '加载中...',
      absolute: true,
    },
  });
  const { data: medicalRecordList } = useRequest(
    () =>
      medicalRecord({
        idCardNo: props.idCardNo,
        outpatientNo: props?.activeItemParent?.outpatientNo,
      }),
    {
      ready: !!props.activeItemParent?.outpatientNo,
      onBefore: () => {
        openWrapLoading();
      },
      onSuccess: (data) => {
        closeWrapLoading();
        // activeId.value = data.length > 0 ? data[0]?.recordId : undefined; recordId为空 暂时取时间
        activeId.value = data.length > 0 ? data[0]?.pkVisitInfo : undefined;
        activeItem.value = data[0];
      },
    },
  );

  // const { runAsync: runElectronicCaseHistoryDocDetail } = useRequest(
  //   electronicCaseHistoryDocDetail,
  //   {
  //     manual: true,
  //     showSuccessMessage: false,
  //     onSuccess: (data) => {
  //       caseDetailsRef.value.innerHTML = data?.data || '';
  //     },
  //   },
  // );
  // watch(
  //   () => activeId.value,
  //   (val) => {
  //     runElectronicCaseHistoryDocDetail({ pkElectronicCaseHistoryDoc: val });
  //   },
  // );
</script>
<template>
  <div class="h-full flex flex-col gap-4">
    <div class="flex items-center gap-2 bg-#FAFAFA">
      <span class="inline-block bg-primary-color w-2px h-8 mr-3"></span>
      <span class="text-#333333 py-5px">门诊病历</span>
    </div>
    <div class="basis-0 of-y-auto of-x-hidden flex-1">
      <div
        v-if="medicalRecordList && medicalRecordList.length > 0"
        class="h-full pr-4 flex flex-1 justify-between gap-10px pb-10px"
        ref="OutpatientRecordRef"
      >
        <div class="w-186px pr-4 b-r-1px b-r-solid b-r-#EDEEF0 of-y-auto of-x-hidden min-w-0">
          <StyledList
            v-model="activeId"
            v-model:value="activeItem"
            valueField="pkVisitInfo"
            class="flex-1"
            :items="medicalRecordList || []"
          >
            <template #default="item">
              <div class="flex flex-col gap-0 py-2 py-3">
                <div class="text-#333333">{{ item?.diagnoseDiseaseName }}</div>
                <div class="text-#999">{{ item?.visitDate }}</div>
              </div>
            </template>
          </StyledList>
        </div>
        <div ref="caseDetailsRef" class="px-4 flex-1 basis-0 of-y-auto of-x-hidden flex-1">
          <h2 class="text-center mb-4"
            >门诊电子病历{{
              activeItem?.firstVisitStatus === '1'
                ? '(初诊)'
                : activeItem?.firstVisitStatus === '2'
                ? '(复诊)'
                : ''
            }}</h2
          >
          <Descriptions
            :column="24"
            :label-style="{ color: '#333' }"
            :content-style="{ color: '#252931' }"
          >
            <Descriptions.Item :span="6" label="姓名">{{
              activeItem?.patientName
            }}</Descriptions.Item>
            <Descriptions.Item :span="5" label="性别">{{ activeItem?.sexName }}</Descriptions.Item>
            <Descriptions.Item :span="5" label="年龄">{{ activeItem?.age }}</Descriptions.Item>
            <Descriptions.Item :span="8" label="科室">{{ activeItem?.deptName }}</Descriptions.Item>
          </Descriptions>
          <div class="b-t-1px b-color-#EDEEF0 pb-4"></div>
          <Descriptions
            :label-style="{
              color: 'rgba(0, 0, 0, 0.85)',
              fontWeight: 'bold',
              fontSize: '16px',
              width: '80px',
              textAlign: 'left',
              display: 'inline-block',
            }"
            :content-style="{ color: '#252931' }"
          >
            <Descriptions.Item :span="3" label="主 诉">{{
              activeItem?.chiefComplaint
            }}</Descriptions.Item>
            <Descriptions.Item :span="3" label="现病史">{{
              activeItem?.presentMedicalHistory
            }}</Descriptions.Item>
            <Descriptions.Item :span="3" label="既往史">{{
              activeItem?.personalHistory
            }}</Descriptions.Item>
            <Descriptions.Item :span="3" label="体检">{{
              activeItem?.physicalExamination
            }}</Descriptions.Item>
            <Descriptions.Item :span="3" label="辅检结果">{{
              activeItem?.auxiliaryInspectionResult
            }}</Descriptions.Item>
            <Descriptions.Item :span="3" label="治疗意见">{{
              activeItem?.treatmentAdvice
            }}</Descriptions.Item>
          </Descriptions>
        </div>
      </div>
      <Empty v-else :image="Empty.PRESENTED_IMAGE_SIMPLE" />
    </div>
  </div>
</template>

<style lang="less" scoped></style>
