<script setup lang="ts">
  import { computed, ref, watchEffect } from 'vue';
  import { useECharts } from '@ft/internal/hooks/web/useECharts';
  import red from '/@/assets/svg/chart-legend/red.svg';
  import green from '/@/assets/svg/chart-legend/green.svg';
  import blue from '/@/assets/svg/chart-legend/blue.svg';
  import type { IIndicatorTrendData } from '/@/api/doctors-view';
  import dayjs from 'dayjs';

  const props = defineProps<{
    resultDiagnosisList: IIndicatorTrendData[] | undefined;
    checkType: string | undefined;
    options: IIndicatorTrendData[] | undefined;
  }>();

  const chartRef = ref<HTMLDivElement>();
  const { setOptions } = useECharts(chartRef as any);
  const xData = computed(() => {
    return [
      ...new Set(
        props.resultDiagnosisList?.map((item) => dayjs(item?.labReportDate).format('YYYY-MM-DD')),
      ),
    ];
  });
  const upperLimitLegendSelected = computed(() => {
    const data = props?.resultDiagnosisList?.filter((item) => item.upperLimit !== null);
    return data && data.length > 0 ? true : false;
  });
  const lowerLimitLegendSelected = computed(() => {
    const data = props?.resultDiagnosisList?.filter((item) => item.lowerLimit !== null);
    return data && data.length > 0 ? true : false;
  });
  watchEffect(() => {
    setOptions({
      tooltip: {
        trigger: 'axis',
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '10%',
      },
      legend: {
        data: [
          { name: '正常范围上限', icon: `image://${green}` },
          { name: '正常范围下限', icon: `image://${red}` },
          { name: '结果', icon: `image://${blue}` },
        ],
        selected: {
          正常范围上限: upperLimitLegendSelected.value,
          正常范围下限: lowerLimitLegendSelected.value,
        },
      },
      xAxis: {
        data: xData.value || [],
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#999999',
        },
        axisLine: {
          lineStyle: {
            color: '#E7E7E7',
          },
        },
        boundaryGap: false,
      },
      yAxis: {
        splitLine: {
          lineStyle: {
            color: '#E7E7E7',
          },
        },
        axisLabel: {
          color: '#999999',
        },
      },
      series: [
        {
          name: '正常范围上限',
          type: 'line',
          symbol: 'none',
          data: props?.resultDiagnosisList?.map((item) => item.upperLimit),
          itemStyle: {
            color: '#32BF2F',
          },
          lineStyle: {
            type: 'dashed',
          },
          silent: false,
        },
        {
          name: '正常范围下限',
          type: 'line',
          symbol: 'none',
          data: props?.resultDiagnosisList?.map((item) => item.lowerLimit),
          itemStyle: {
            color: '#FA536D',
          },
          lineStyle: {
            type: 'dashed',
          },
          silent: false,
        },
        {
          name: '结果',
          type: 'line',
          smooth: true,
          data: props?.resultDiagnosisList?.map((item) => item.labResult),
          itemStyle: {
            color: '#56A8FF',
          },
        },
      ],
    });
  });
  function handeleChart() {}
  defineExpose({ handeleChart });
</script>

<template>
  <div class="w-full h-full" ref="chartRef"> </div>
</template>
<style scoped></style>
