<script setup lang="ts">
  import { Button, Empty } from 'ant-design-vue';
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { computed, ref } from 'vue';
  import { StyledList } from '@ft/components';
  import { useModal } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { columnsCg } from '../data';
  import InspectionReportModal from './InspectionReportModal.vue';
  import Item from './item.vue';

  import type { IInspectionExceptionItem } from '/@/api/doctors-view';
  import {
    getInspectionReport,
    getInspectionReportDetail,
    indicatorTrendButton,
  } from '/@/api/doctors-view';

  /**
   * @description 检验报告
   */

  const props = defineProps({
    idCardNo: {
      type: String,
      default: '',
    },
    activeItemParent: {
      type: Object,
      default: () => {},
    },
  });
  const activeId = ref();
  const outInPatientNo = computed(() =>
    props.activeItemParent?.visitTypeCode === '3'
      ? props.activeItemParent?.inpatientNo
      : props.activeItemParent?.outpatientNo,
  );

  const activeItem = ref<Partial<IInspectionExceptionItem>>({});
  const { loading: loading, data: resultReportList } = useRequest(
    () => getInspectionReport(outInPatientNo.value),
    {
      ready: !!outInPatientNo.value,
      onSuccess: (data) => {
        activeId.value = data.length > 0 ? data[0].reportNo : '';
        activeItem.value = data?.[0] || {};
        handleChange();
      },
    },
  );
  function handleChange() {
    getReportDetailRunAsync();
    getExceptionListRunAsync();
  }

  const { runAsync: getReportDetailRunAsync } = useRequest(
    () => getInspectionReportDetail(activeId.value),
    {
      manual: true,
      ready: activeId,
      onSuccess: (res) => {
        ActionTableCg.setTableData(res?.regularReportDetailVOS || []);
        // ActionTableXj.setTableData(res?.bacterialReportDetailVOS || []);
      },
    },
  );
  const [registerModal, { openModal }] = useModal();
  function handleClick() {
    openModal(true, {
      reportNo: activeId.value,
    });
  }
  const [registerTableCg, ActionTableCg] = useTable({
    pagination: false,
    columns: columnsCg,
    useSearchForm: false,
    showIndexColumn: true,
    bordered: false,
    immediate: false,
    size: 'small',
    resizeHeightOffset: 20,
    scroll: { x: undefined, y: 300 },
  });
  // const [registerTableXj, ActionTableXj] = useTable({
  //   pagination: false,
  //   columns: columnsXj,
  //   useSearchForm: false,
  //   showIndexColumn: true,
  //   bordered: false,
  //   immediate: false,
  //   size: 'small',
  //   // resizeHeightOffset: 20,
  //   scroll: { x: undefined, y: 200 },
  // });
  const { data: isIndicatorChart, runAsync: getExceptionListRunAsync } = useRequest(
    () => indicatorTrendButton(activeId.value),
    {
      manual: true,
      ready: activeId,
    },
  );
</script>
<template>
  <div class="h-full flex flex-col gap-4">
    <div class="flex items-center gap-2 bg-#FAFAFA">
      <span class="inline-block bg-primary-color w-2px h-8 mr-3"></span>
      <span class="text-#333333 py-5px">检验报告</span>
    </div>
    <div class="basis-0 of-y-auto of-x-hidden flex-1">
      <div class="h-full flex flex-col gap-4">
        <div class="basis-0 of-y-auto of-x-hidden flex-1">
          <div
            v-if="resultReportList && resultReportList.length > 0"
            class="w-full h-full pr-4 flex justify-between gap-10px"
          >
            <div class="w-186px pr-4 flex of-y-auto of-x-hidden min-w-0">
              <StyledList
                v-model="activeId"
                v-model:value="activeItem"
                valueField="reportNo"
                class="flex-1"
                :items="resultReportList || []"
                :loading="loading"
                @change="handleChange"
              >
                <template #default="item">
                  <div class="flex flex-col gap-0 py-2 py-3">
                    <div class="text-#333333">{{ item.inspectionItemName }}</div>
                    <div class="text-#999">{{ item.inspectionTime }}</div>
                  </div>
                </template>
              </StyledList>
            </div>
            <div class="px-4 flex-1 flex flex-col min-w-0 h-[calc(100vh-22rem)] overflow-y-auto">
              <Item :info="activeItem" />
              <div class="my-4" v-if="isIndicatorChart">
                <Button class="mr-2" type="primary" @click="handleClick"> 指标趋势图 </Button>
              </div>
              <BasicTable @register="registerTableCg">
                <template #bodyCell="{ column, record }">
                  <template v-if="column.dataIndex">
                    <h4
                      v-if="
                        record[column.dataIndex] === '异常' ||
                        record[column.dataIndex] === '↑' ||
                        record[column.dataIndex] === '↓'
                      "
                      class="color-#F15226 font-bold text-18px"
                      >{{ record[column.dataIndex] }}</h4
                    >
                    <span v-else>{{ record[column.dataIndex] }}</span>
                  </template>
                </template>
              </BasicTable>
              <!-- <BasicTable @register="registerTableXj" /> -->
            </div>
            <InspectionReportModal @register="registerModal" />
          </div>
          <Empty v-else :image="Empty.PRESENTED_IMAGE_SIMPLE" />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
  :deep {
    .vben-basic-table .ant-table-wrapper {
      padding: 0;
    }

    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table-form-container .ant-form,
    .vben-basic-table .ant-table-wrapper {
      border-radius: 4px;
    }
  }
</style>
