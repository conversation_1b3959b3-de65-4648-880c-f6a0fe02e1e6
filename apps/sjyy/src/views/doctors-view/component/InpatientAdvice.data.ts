import type { BasicColumn } from '@ft/internal/components/Table';

/**
 * @description: 表格列
  类别
  开嘱时间
  停囑时间
  医嘱类型
  医𡂖项目
  用量
  规格
  用法
  频次
  总量
  单价
  金额
  科室
  医生
  护士

 */
export const columns: BasicColumn[] = [
  {
    title: '类别',
    dataIndex: 'medicOrdersType',
    width: 100,
    align: 'left',
  },
  {
    title: '开嘱时间',
    dataIndex: 'medicOrdersStartDate',
    width: 180,
    align: 'left',
  },
  // {
  //   title: '停嘱时间',
  //   dataIndex: 'medicOrdersStopDate',
  //   width: 180,
  //   align: 'left',
  // },
  {
    title: '医嘱类型',
    dataIndex: 'medicOrdersItemType',
    width: 100,
    align: 'left',
  },
  {
    title: '医𡂖项目',
    dataIndex: 'medicOrdersItem',
    width: 100,
    align: 'left',
  },
  {
    title: '用量',
    dataIndex: 'drugUsed',
    width: 100,
    align: 'left',
  },
  {
    title: '规格',
    dataIndex: 'drugSpec',
    width: 100,
    align: 'left',
  },
  // {
  //   title: '用法',
  //   dataIndex: 'drugUsage',
  //   width: 100,
  //   align: 'left',
  // },
  {
    title: '频次',
    dataIndex: 'drugUseDrequency',
    width: 100,
    align: 'left',
  },
  {
    title: '总量',
    dataIndex: 'totalSize',
    width: 100,
    align: 'left',
  },
  // {
  //   title: '单价',
  //   dataIndex: 'price',
  //   width: 100,
  //   align: 'left',
  // },
  {
    title: '金额',
    dataIndex: 'totalPrice',
    width: 100,
    align: 'left',
  },
  // {
  //   title: '科室',
  //   dataIndex: 'visitDept',
  //   width: 100,
  //   align: 'left',
  // },
  {
    title: '医生',
    dataIndex: 'doctorName',
    width: 100,
    align: 'left',
  },
  // {
  //   title: '护士',
  //   dataIndex: 'nurseName',
  //   width: 100,
  //   align: 'left',
  // },
];
