<script setup lang="ts">
  import type { ExamineResultReportListItem, IInspectionExceptionItem } from '/@/api/doctors-view';

  defineProps<{
    info?: (IInspectionExceptionItem & ExamineResultReportListItem) | any;
  }>();
</script>

<template>
  <div class="flex justify-start gap-8">
    <div class="flex">
      <div class="text-#999">检查时间：</div>
      <div class="text-#333">{{ info?.inspectionTime || info?.checkDate }}</div>
    </div>
    <div class="flex">
      <div class="text-#999">检查项目：</div>
      <div class="text-#333">{{ info?.inspectionItemName || info?.checkItem }}</div>
    </div>
    <div class="flex">
      <div class="text-#999">检查医院：</div>
      <div class="text-#333">{{ info?.inspectionOrg || info?.checkOrg }}</div>
    </div>
    <!-- <div class="flex">
      <div class="text-#999">检查科室：</div>
      <div class="text-#333">{{ info?.inspectionDept || info?.checkDept }}</div>
    </div>
    <div class="flex">
      <div class="text-#999">检查医生：</div>
      <div class="text-#333">{{ info?.inspectionDoc || info?.checkDoc }}</div>
    </div> -->
  </div>
</template>
