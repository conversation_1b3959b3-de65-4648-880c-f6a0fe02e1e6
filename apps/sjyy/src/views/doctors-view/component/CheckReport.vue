<script setup lang="ts">
  import { Empty } from 'ant-design-vue';
  import { ref } from 'vue';
  import { StyledList } from '@ft/components';
  import { useRequest } from '@ft/request';
  import Item from './item.vue';

  import { examineResultReportList } from '/@/api/doctors-view';

  /**
   * @description 检查报告
   */

  const props = defineProps({
    idCardNo: {
      type: String,
      default: '',
    },
    activeItemParent: {
      type: Object,
      default: () => {},
    },
  });
  const activeId = ref();
  const activeItem = ref();
  const typeParam = {
    '1': 'outpatientNo',
    '3': 'inpatientNo',
    '4': 'inpatientNo', //救治规范系统-不明原因/重点重症模块患者详情使用
  };
  const { loading: loading, data: resultReportList } = useRequest(
    () =>
      examineResultReportList({
        idCardNo: props.idCardNo,
        //visitTypeCode	就诊类型代码：1门诊，2急诊，3住院
        [typeParam[props.activeItemParent.visitTypeCode]]:
          props.activeItemParent[typeParam[props.activeItemParent.visitTypeCode]],
      }),
    {
      ready: !!props.activeItemParent[typeParam[props.activeItemParent.visitTypeCode]],
      onSuccess: (data) => {
        activeId.value = data.length > 0 ? data[0].id : '';
        activeItem.value = data?.[0] || {};
      },
    },
  );

  // const {
  //   data: resultReportDetail = {} as unknown as Ref<any>,
  //   runAsync: getReportDetailRunAsync,
  // } = useRequest(() => examineResultReportDetail({ pkExamineReport: activeId.value }), {
  //   manual: true,
  //   ready: activeId,
  // });
  // const [registerModal, { openModal }] = useModal();
  // const [registerCheckReportPortraitModal, { openModal: openCheckReportPortraitModal }] =
  //   useModal();
  // function handleClick() {
  //   openModal(true, {
  //     examineReportItemCode: activeItem.value.examineReportItemCode,
  //     // diagnoseName: activeItem.value.examineReportItem,
  //     idCardNo: props.idCardNo,
  //   });
  // }
  // function handlePortrait() {
  //   openCheckReportPortraitModal(true, {
  //     examineReportContentUrl: activeItem.value?.examineReportContentUrl,
  //   });
  // }
</script>
<template>
  <div class="h-full flex flex-col gap-4">
    <div class="flex items-center gap-2 bg-#FAFAFA">
      <span class="inline-block bg-primary-color w-2px h-8 mr-3"></span>
      <span class="text-#333333 py-5px">检查报告</span>
    </div>
    <div class="basis-0 of-y-auto of-x-hidden flex-1">
      <div class="h-full flex flex-col gap-4">
        <div class="basis-0 of-y-auto of-x-hidden flex-1">
          <div
            v-if="resultReportList && resultReportList.length > 0"
            class="w-full h-full pr-4 flex justify-between gap-10px pb-10px"
          >
            <div class="w-186px pr-4 b-r-1px b-r-solid b-r-#EDEEF0 of-y-auto of-x-hidden min-w-0">
              <StyledList
                v-model="activeId"
                v-model:value="activeItem"
                valueField="id"
                class="flex-1"
                :items="resultReportList || []"
                :loading="loading"
              >
                <template #default="item">
                  <div class="flex flex-col gap-0 py-2 py-3">
                    <div class="text-#333333">{{ item.reportName }}</div>
                    <div class="text-#999">{{ item.reportDate }}</div>
                  </div>
                </template>
              </StyledList>
            </div>
            <div class="px-4 flex-1 flex flex-col of-y-auto of-x-hidden min-w-0">
              <Item :info="activeItem" />
              <!-- <div class="my-4">
                <Button class="mr-2" type="primary" @click="handlePortrait"> 查看影像 </Button>
                <Button @click="handleClick" class="mr-2"> 既往报告 </Button>
              </div> -->
              <div class="flex flex-col gap-4 flex-1 bg-#FAFAFA rounded-1 p-4">
                <div class="flex gap-0">
                  <div class="text-#333333 min-w-70px">检查所见：</div>
                  <div class="text-#999">
                    {{ activeItem?.checkFind }}
                  </div>
                </div>
                <div class="flex gap-0">
                  <div class="text-#333333 min-w-70px">检查意见：</div>
                  <div class="text-#999">
                    {{ activeItem?.checkView }}
                  </div>
                </div>
              </div>
            </div>
            <!-- <CheckReportModal @register="registerModal" />
            <CheckReportPortraitModal @register="registerCheckReportPortraitModal" /> -->
          </div>
          <Empty v-else :image="Empty.PRESENTED_IMAGE_SIMPLE" />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
  .doctors-view-styled-list {
    .active-bg-color {
      background: #ebf1ff !important;

      .type-card {
        background-color: #ecf1ff;
        color: #4c81ff;
        border-color: #4c81ff;
      }
    }

    :deep {
      .ant-timeline-item {
        padding-bottom: 0;
      }

      .ant-timeline-item-head {
        background-color: transparent;
      }
    }
  }

  :deep {
    .vben-basic-table .ant-table-wrapper {
      padding: 0;
    }

    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table-form-container .ant-form,
    .vben-basic-table .ant-table-wrapper {
      border-radius: 4px;
    }
  }
</style>
