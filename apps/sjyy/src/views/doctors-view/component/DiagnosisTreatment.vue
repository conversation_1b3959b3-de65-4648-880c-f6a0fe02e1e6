<script setup lang="ts">
  import { useRequest } from '@ft/request';
  import { BasicForm, useForm } from '@ft/internal/components/Form';

  import {
    queryInspectionIndex,
    queryMedicationRecordList,
    queryRehabilitationCondition,
  } from '@ft/internal/api';

  const props = defineProps({
    patientInfo: {
      type: Object,
      default: () => ({}),
    },
    activeItemParent: {
      type: Object,
      default: () => ({}),
    },
  });

  const [registerForm, formAction] = useForm({
    schemas: [
      {
        field: 'condition',
        label: '病情状况',
        component: 'InputTextArea',
        componentProps: {
          autoSize: { minRows: 4, maxRows: 4 },
        },
        itemProps: { wrapperCol: { span: 14 } },
        colProps: { span: 14 },
      },
      {
        field: 'treatmentStatus',
        label: '治疗情况',
        component: 'InputTextArea',
        componentProps: {
          autoSize: { minRows: 4, maxRows: 4 },
        },
        itemProps: { wrapperCol: { span: 14 } },
        colProps: { span: 14 },
      },
      {
        field: 'rehabilitationStatus',
        label: '康复情况',
        component: 'InputTextArea',
        componentProps: {
          autoSize: { minRows: 4, maxRows: 4 },
        },
        itemProps: { wrapperCol: { span: 14 } },
        colProps: { span: 14 },
      },
    ],
    disabled: true,
    labelWidth: 90,
    colon: true,
    showActionButtonGroup: false,
  });

  const fetchData = async () => {
    const inpatientNo = props.patientInfo.inpatientNo || props.activeItemParent.inpatientNo;
    const [conditionRes, treatmentRes, rehabilitationRes] = await Promise.all([
      queryInspectionIndex(inpatientNo),
      queryMedicationRecordList(inpatientNo),
      queryRehabilitationCondition(inpatientNo),
    ]);
    formAction.setFieldsValue({
      //项目名称: 项目指标1 +项目结果2，项目指标2+项目结果2
      condition: conditionRes
        ?.map((item) => {
          const exceptionIndexList = item.exceptionIndexList
            .map((index) => {
              return `${index.indexName}+${index.exceptionFlag}`;
            })
            .join(',');
          return `${item.itemName}:${exceptionIndexList}`;
        })
        .join('\n'),
      // 药品名称+用量+用量单位+用法+频次
      treatmentStatus: treatmentRes
        ?.map(
          (i) =>
            `${i.drugName || ''} ${i.drugUsed || ''}${i.douseUnit || ''}  ${i.drugUsage || ''} ${
              i.drugUseDrequency || ''
            }`,
        )
        .join('\n'),
      rehabilitationStatus: rehabilitationRes,
    });
  };

  // 发起请求
  useRequest(fetchData);
</script>

<template>
  <div class="h-full flex flex-col gap-4">
    <div class="flex items-center gap-2 bg-#FAFAFA">
      <span class="inline-block bg-primary-color w-2px h-8 mr-3"></span>
      <span class="text-#333333 py-5px">诊疗情况</span>
    </div>
    <BasicForm @register="registerForm" />
  </div>
</template>

<style lang="less" scoped></style>
