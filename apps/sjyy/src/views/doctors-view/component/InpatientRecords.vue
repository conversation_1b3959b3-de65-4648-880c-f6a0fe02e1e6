<script setup lang="ts">
  import { ref } from 'vue';
  import { StyledList } from '@ft/components';
  import { inHospitalRecord } from '/@/api/doctors-view';
  import { useRequest } from '@ft/request';
  import { Empty } from 'ant-design-vue';

  /**
   * @description 住院病历
   */
  const props = defineProps({
    idCardNo: {
      type: String,
      default: '',
    },
    activeItemParent: {
      type: Object,
      default: () => {},
    },
  });
  const activeId = ref();
  const activeItem = ref();
  const caseDetailsRef = ref();

  const { loading: loading, data: inHospitalList } = useRequest(
    () =>
      inHospitalRecord({
        idCardNo: props.idCardNo,
        inpatientNo: props.activeItemParent?.inpatientNo,
      }),
    {
      ready: !!props.activeItemParent?.inpatientNo,
      onSuccess: (res) => {
        activeId.value = res.length > 0 ? res?.[0]?.admitHospitalRecordTime : '';
        activeItem.value = res?.[0] || {};
      },
    },
  );

  // const { runAsync: runAsyncElectronicCaseHistoryDocDetail } = useRequest(
  //   () => electronicCaseHistoryDocDetail({ pkElectronicCaseHistoryDoc: activeId.value }),
  //   {
  //     manual: true,
  //     onSuccess: (res) => {
  //       caseDetailsRef.value.innerHTML = res?.data || '';
  //     },
  //   },
  // );
</script>
<template>
  <div class="h-full flex flex-col gap-4">
    <div class="flex items-center gap-2 bg-#FAFAFA">
      <span class="inline-block bg-primary-color w-2px h-8 mr-3"></span>
      <span class="text-#333333 py-5px">住院病历</span>
    </div>
    <div class="basis-0 of-y-auto of-x-hidden flex-1">
      <div
        v-if="inHospitalList && inHospitalList.length > 0"
        class="h-full pr-4 flex flex-1 justify-between gap-10px pb-10px"
      >
        <div class="w-186px pr-4 b-r-1px b-r-solid b-r-#EDEEF0 of-y-auto of-x-hidden min-w-0">
          <StyledList
            v-model="activeId"
            v-model:value="activeItem"
            valueField="admitHospitalRecordTime"
            class="flex-1"
            :loading="loading"
            :items="inHospitalList || []"
          >
            <template #default="item">
              <div class="flex flex-col gap-0 py-2 py-3">
                <div class="text-#333333">{{ item.admitHospitalRecord }}</div>
                <div class="text-#999">{{ item.admitHospitalRecordTime }}</div>
              </div>
            </template>
          </StyledList>
        </div>
        <div ref="caseDetailsRef" class="px-4 flex-1 of-y-auto of-x-hidden">
          <div>{{ activeItem?.content }}</div>
          <!-- <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE" /> -->
        </div>
      </div>
      <Empty v-else :image="Empty.PRESENTED_IMAGE_SIMPLE" />
    </div>
  </div>
</template>

<style lang="less" scoped></style>
