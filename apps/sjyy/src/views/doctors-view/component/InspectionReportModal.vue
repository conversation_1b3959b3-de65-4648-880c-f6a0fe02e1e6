<script setup lang="ts">
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import type { Ref } from 'vue';
  import { computed, ref } from 'vue';
  import { Input, Select } from 'ant-design-vue';
  import { StyledList } from '@ft/components';
  import { Icon } from '@ft/internal/components/Icon';
  import { useRequest } from '@ft/request';
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { columns } from './InspectionReport.data';
  import Item from './item.vue';

  import InspectionReportChart from './InspectionReportChart.vue';
  import type { TrendChartItem } from '/@/api/doctors-view';
  import {
    getIndicatorTrendData,
    getInspectionExceptionByReportNo,
    labTestResultReportDetail,
  } from '/@/api/doctors-view';

  defineEmits(['register']);
  const searchValue = ref('');
  const activeId = ref();
  const reportNo = ref('');
  const idCardNo = ref();
  const checkType = ref();

  const [register, { closeModal }] = useModalInner((res) => {
    reportNo.value = res.reportNo;
    idCardNo.value = res.idCardNo;
    runGetTrendChartRefresh();
  });

  async function handleOk() {
    closeModal();
  }
  const activeItem = ref();
  //getHistoryDiagnosis

  const {
    loading: loading,
    data: resultDiagnosisList,
    refresh: getReportDetailRefresh,
  } = useRequest(
    () =>
      getIndicatorTrendData({
        reportNo: reportNo.value,
        indexCode: checkType.value,
      }),
    {
      manual: true,
      // onSuccess: (data) => {
      //   activeId.value = data && data.length > 0 ? data[0]?.pkLabTestResultReport : '';
      //   getReportDetailRunAsync();
      // },
    },
  );
  // const items = computed(() => {
  //   return resultDiagnosisList.value?.filter((item) => item.labResult?.includes(searchValue.value));
  // });
  const {
    data: trendChartList = [] as unknown as Ref<TrendChartItem[]>,
    run: runGetTrendChartRefresh,
  } = useRequest(() => getInspectionExceptionByReportNo(reportNo.value), {
    onSuccess: (data) => {
      if (data && data.length > 0) {
        checkType.value = data[0]?.labItemCode || undefined;
        getReportDetailRefresh();
      }
    },
    manual: true,
  });
  const options = computed(() => {
    return trendChartList.value?.reduce((uniqueArr, item) => {
      if (!uniqueArr.some((uniqueItem) => uniqueItem.labItemCode === item.labItemCode)) {
        uniqueArr.push(item);
      }
      return uniqueArr;
    }, [] as TrendChartItem[]);
  });

  const {
    data: resultReportDetail = {} as unknown as Ref<any>,
    runAsync: getReportDetailRunAsync,
  } = useRequest(() => labTestResultReportDetail({ pkLabTestResultReport: activeId.value }), {
    manual: true,
    ready: activeId,
    onSuccess: () => {
      reload();
    },
  });
  function handleChange() {
    getReportDetailRunAsync();
  }
  function handleSelectChange(item) {
    checkType.value = item;
    getReportDetailRefresh();
  }
  const [registerTable, { reload }] = useTable({
    pagination: false,
    columns,
    dataSource: [{ name: '白细胞' }],
    useSearchForm: false,
    showIndexColumn: true,
    bordered: false,
    immediate: false,
    size: 'small',
    resizeHeightOffset: 20,
    // api: labTestResultDetailList,
    beforeFetch: () => {
      return {
        pkLabTestResultReport: activeId.value,
        labReportNo: resultReportDetail.value?.labReportNo,
      };
    },
    scroll: {
      y: 200,
    },
  });
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    title="指标趋势图"
    :can-fullscreen="false"
    width="1500px"
    @register="register"
    @ok="handleOk"
  >
    <div class="h-full flex flex-col gap-4">
      <div class="title basic-title">检查项目：血常规五分类</div>
      <div>
        <Select
          style="width: 260px"
          v-model:value="checkType"
          :options="options"
          :fieldNames="{ label: 'labItemName', value: 'labItemCode' }"
          @change="handleSelectChange"
        />
      </div>
      <div class="h-224px">
        <InspectionReportChart
          :resultDiagnosisList="resultDiagnosisList"
          :options="options"
          :checkType="checkType"
        />
      </div>
      <div class="flex flex-1">
        <div class="w-186px pr-4 flex flex-col of-y-auto of-x-hidden min-w-0 gap-3">
          <Input
            v-model:value="searchValue"
            allow-clear
            size="large"
            style="height: 32px"
            placeholder="诊断检索"
          >
            <template #suffix>
              <Icon icon="ant-design:search-outlined" />
            </template>
          </Input>
          <StyledList
            v-model="activeId"
            v-model:value="activeItem"
            valueField="pkLabTestResultReport"
            class="flex-1"
            :loading="loading"
            @change="handleChange"
            :items="[]"
          >
            <template #default="item">
              <div class="flex flex-col gap-0 py-2 py-3">
                <div class="text-#333333">{{ item.diagnoseName }}</div>
                <div class="text-#999">{{ item.diagnoseDate }}</div>
              </div>
            </template>
          </StyledList>
        </div>
        <div class="px-4 flex-1 flex flex-col">
          <Item :info="activeItem" />
          <BasicTable @register="registerTable">
            <template #bodyCell="{ record, column }">
              <template v-if="column.dataIndex === 'criticalValueFlag'">
                <span v-if="record[column.dataIndex] === '高'" class="text-red-500">{{
                  record[column.dataIndex]
                }}</span>
                <span v-else>{{ record[column.dataIndex] }}</span>
              </template>
            </template>
          </BasicTable>
        </div>
      </div>
    </div>
  </BasicModal>
</template>
