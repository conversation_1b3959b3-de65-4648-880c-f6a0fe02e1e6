import type { FormSchema } from '@ft/internal/components/Form';
export const auxiliaryTreatmentSchema: FormSchema[] = [
  {
    label: '检查检验项目建议',
    field: 'jcjyxmjy',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },

  {
    label: '辅助诊断建议',
    field: 'fzzdjy',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },

  {
    label: '鉴别诊断建议',
    field: 'jbzdjy',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },

  {
    label: '治疗方案建议',
    field: 'zlfajy',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    label: '辅助报告单解读',
    field: 'fzbgdjd',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    label: '护理方案建议',
    field: 'hlfajy',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },

  {
    label: '监测反馈专家',
    field: 'jcfkzj',
    component: 'Input',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 12 } },
    componentProps: {},
  },

  {
    label: '反馈时间',
    field: 'feedbackTime',
    component: 'DatePicker',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 12 } },
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      style: { width: '100%' },
    },
  },
];
