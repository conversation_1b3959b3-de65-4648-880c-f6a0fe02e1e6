<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { FixedAction } from '@ft/components';
  import { Button } from '@ft/internal/components/Button';
  import { computed, onMounted, ref } from 'vue';
  import { useRequest } from '@ft/request';
  import { getActiveRoutePathCache } from '@ft/internal/utils/auth';
  import { Input } from 'ant-design-vue';
  import ApiSelect from '@ft/internal/components/Form/src/components/ApiSelect.vue';
  import { DictEnum, getDictItemList } from '@ft/internal/api';
  import { auxiliaryTreatmentSchema } from './AideDiagnosisDetails.data';
  import {
    addAssistedDiagnosis,
    getAssistedDiagnosisInfo,
    getMedicalKnowledgeByIcdCode,
    updateAssistedDiagnosis,
  } from '/@/api/doctors-view';
  // 允许编辑的路由路径
  const EDITABLE_ROUTES = ['/case-management', '/auxiliary-diagnosis'];
  const parentPath = getActiveRoutePathCache();
  console.log(`parentPath`, parentPath);
  const props = defineProps({
    idCardNo: {
      type: String,
      default: '',
    },
    pkVisitInfo: {
      type: String,
      default: '',
    },
    patientInfo: {
      type: Object,
      default: () => {},
    },
  });
  const patientType = computed(() => {
    return props.patientInfo.patientType;
  });
  const mode = ref('');
  const showEdit = ref(true);
  const { runAsync: runAsyncGetDiagnosisInfo } = useRequest(getAssistedDiagnosisInfo, {
    manual: true,
    onSuccess: (data) => {
      if (data) {
        mode.value = 'look';
        showEdit.value = true;
        formAction.setFieldsValue(data);
      } else {
        if (EDITABLE_ROUTES.includes(parentPath)) {
          mode.value = 'add';
          showEdit.value = false;
        } else {
          mode.value = 'look';
          showEdit.value = false;
        }
      }
    },
  });
  onMounted(async () => {
    console.log(48, props.patientInfo);
    await runAsyncGetDiagnosisInfo(props.patientInfo.id);
    EDITABLE_ROUTES.includes(parentPath) &&
      patientType.value === 2 &&
      (await runAsyncGetMedicalKnowledgeDetail(props.patientInfo.diagnoseCode));
  });
  const [register, formAction] = useForm({
    labelWidth: 120,
    showActionButtonGroup: false,
    schemas: auxiliaryTreatmentSchema,
    disabled: computed(() => mode.value === 'look'),
  });
  const divRef = ref();
  function onEdit() {
    showEdit.value = false;
    mode.value = 'edit';
  }
  const { runAsync: runAsyncSaveAssistedDiagnosis } = useRequest(
    () => {
      const params = {
        ...formAction.getFieldsValue(),
        id: props.patientInfo.id,
        // patientId: props.patientInfo.patientId,
        outInId: props.patientInfo.inpatientNo || props.patientInfo.outInId,
        patientType: props.patientInfo.patientType,
      };
      return mode.value === 'add' ? addAssistedDiagnosis(params) : updateAssistedDiagnosis(params);
    },
    {
      manual: true,
      showSuccessMessage: true,
      async onBefore() {
        try {
          await formAction.validate();
          return true;
        } catch (e) {
          return false;
        }
      },
      onSuccess: async () => {
        await runAsyncGetDiagnosisInfo(props.patientInfo.id);
      },
    },
  );
  function onCancel() {
    if (mode.value === 'add') {
      formAction.resetFields();
    } else if (mode.value === 'edit') {
      showEdit.value = true;
      mode.value = 'look';
      // runAsyncGetDiagnosisInfo(props.patientInfo.id);
    }
  }
  function getOptions() {
    return getDictItemList(DictEnum.MEDICAL_KNOWLEDGE);
  }
  const selectCode = ref();
  const content = ref('');

  const { runAsync: runAsyncGetMedicalKnowledgeDetail } = useRequest(getMedicalKnowledgeByIcdCode, {
    manual: true,
    onSuccess: (data) => {
      if (patientType.value === 1) {
        content.value = data?.knowledgeContent || '';
      } else if (patientType.value === 2) {
        content.value = `1.发病情况\n${data?.morbiditySituation || ''}\n2.传染途径\n${
          data?.contagionWay || ''
        }\n3.鉴别诊断方法\n${data?.differentialDiagnosisMethod || ''}\n4.用药推荐\n${
          data?.medicineRecommendation || ''
        }\n5.治疗建议\n${data?.diagRecommendation || ''}`;
      }
    },
  });
  function onSearch() {
    runAsyncGetMedicalKnowledgeDetail(selectCode.value);
  }
  const InputTextarea = Input.TextArea;
</script>
<template>
  <div class="h-full flex flex-col gap-4" :class="showEdit ? '' : 'pb-15'">
    <div class="flex items-center gap-2 bg-#FAFAFA">
      <span class="inline-block bg-primary-color w-2px h-8 mr-3"></span>
      <span class="text-#333333 py-5px">辅助诊断</span>
      <Button
        v-if="showEdit && EDITABLE_ROUTES.includes(parentPath)"
        class="ml-auto"
        size="small"
        type="link"
        pre-icon="ant-design:edit-outlined"
        @click="onEdit"
      >
        编辑
      </Button>
    </div>
    <div class="basis-0 of-y-auto of-x-hidden flex-1 flex gap-20">
      <BasicForm @register="register" class="flex-1" />
      <div class="flex-1">
        <div
          class="flex flex-col gap-4"
          v-if="!showEdit && EDITABLE_ROUTES.includes(parentPath) && patientType === 1"
        >
          <div class="title basic-title"
            >根据当前传染不明原因患者主诉、病程记录、初步诊断等信息查询相关传染病诊疗医学知识：</div
          >
          <div>
            <ApiSelect
              style="width: 260px"
              :api="getOptions"
              v-model:value="selectCode"
              placeholder="请选择查询的内容"
              label-field="dictItemName"
              value-field="dictItemCode"
              @change="(_, opt) => (selectCode = opt?.value)"
            />
            <Button type="primary" class="ml-2" @click="onSearch">查询</Button>
          </div>
          <div class="pr-5">
            <InputTextarea
              class=""
              :auto-size="{ minRows: 25, maxRows: 25 }"
              v-model:value="content"
              readOnly
            />
          </div>
        </div>
        <div
          class="flex flex-col gap-4"
          v-if="!showEdit && EDITABLE_ROUTES.includes(parentPath) && patientType === 2"
        >
          <div class="title basic-title"
            >根据当前传染病重点/重症患者主要诊断查询相关传染病诊疗医学知识：</div
          >
          <div class="pr-5">
            <InputTextarea
              class=""
              :auto-size="{ minRows: 25, maxRows: 25 }"
              v-model:value="content"
              readOnly
            />
          </div>
        </div>
      </div>
    </div>
    <teleport to="body">
      <FixedAction
        v-if="!showEdit && EDITABLE_ROUTES.includes(parentPath)"
        class="justify-end pr-4 !w-[calc(100%-100px)]"
        :referenceEl="divRef"
      >
        <Button @click="onCancel">取消</Button>
        <Button type="primary" @click="runAsyncSaveAssistedDiagnosis">确定</Button>
      </FixedAction>
    </teleport>
  </div>
</template>

<style lang="less" scoped></style>
