<script setup lang="ts">
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import type { Ref } from 'vue';
  import { computed, ref } from 'vue';
  import { Input } from 'ant-design-vue';
  import { StyledList } from '@ft/components';
  import { Icon } from '@ft/internal/components/Icon';
  import { examineResultReportDetail, getHistoryDiagnosis } from '/@/api/doctors-view';
  import { useRequest } from '@ft/request';
  import Item from './item.vue';
  /**
   * 既往报告
   */

  const emit = defineEmits(['register', 'success']);
  const searchValue = ref('');
  const info = ref<any>({});
  const activeId = ref();
  const activeItem = ref();

  const [register, { closeModal }] = useModalInner((data) => {
    info.value = data;
    getHistoryDiagnosisListRunAsync();
  });
  const {
    loading: loading,
    data: historyDiagnosisList,
    runAsync: getHistoryDiagnosisListRunAsync,
  } = useRequest(() => getHistoryDiagnosis(info.value), {
    onSuccess: (data) => {
      activeId.value = data.length > 0 ? data[0].pkExamineReport : '';
      activeItem.value = data?.[0] || {};
      handleChange();
    },
    manual: true,
  });
  const items = computed(() => {
    return historyDiagnosisList.value?.filter((item) =>
      item.diagnoseName.includes(searchValue.value),
    );
  });

  async function handleOk() {
    emit('success', '');
    closeModal();
  }

  function handleChange() {
    getReportDetailRunAsync();
  }
  const {
    data: resultReportDetail = {} as unknown as Ref<any>,
    runAsync: getReportDetailRunAsync,
  } = useRequest(() => examineResultReportDetail({ pkExamineReport: activeId.value }), {
    manual: true,
    ready: activeId,
  });
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    title="既往报告"
    :can-fullscreen="false"
    :minHeight="150"
    :height="750"
    width="1400px"
    @register="register"
    @ok="handleOk"
  >
    <div class="h-full flex flex-col gap-4">
      <div class="flex flex-1">
        <div class="w-186px pr-4 b-r-1px b-r-solid b-r-#EDEEF0 flex flex-col gap-4">
          <Input
            v-model:value="searchValue"
            allow-clear
            size="large"
            style="height: 32px"
            placeholder="诊断检索"
            @change="handleChange"
            @pressEnter="handleChange"
          >
            <template #suffix>
              <Icon icon="ant-design:search-outlined" />
            </template>
          </Input>
          <StyledList
            :items="items || []"
            v-model="activeId"
            v-model:value="activeItem"
            valueField="pkExamineReport"
            class="flex-1 doctors-view-styled-list"
            :loading="loading"
            @change="handleChange"
          >
            <template #default="item">
              <div class="flex flex-col gap-0 py-2 py-3">
                <div class="text-#333333">{{ item.diagnoseName }}</div>
                <div class="text-#999">{{ item.diagnoseDate }}</div>
              </div>
            </template>
          </StyledList>
        </div>
        <div class="px-4 flex-1 flex flex-col">
          <Item :info="resultReportDetail" />
          <!-- <div class="my-4">
            <Button class="mr-2" type="primary"> 查看影像 </Button>
          </div> -->
          <div class="flex flex-col gap-4 flex-1 bg-#FAFAFA rounded-1 p-4">
            <div class="flex gap-0">
              <div class="text-#333333 min-w-70px">检查所见：</div>
              <div class="text-#999">
                {{ resultReportDetail?.reptResultObjectiveOpinion }}
              </div>
            </div>
            <div class="flex gap-0">
              <div class="text-#333333 min-w-70px">检查意见：</div>
              <div class="text-#999">
                {{ resultReportDetail?.reptResultSubjectiveOpinion }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </BasicModal>
</template>
