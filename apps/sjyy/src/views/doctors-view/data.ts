import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import dayjs from 'dayjs';

/**
 * 年份
 * 机构
 * 类别
 * 科室
 * 诊断
 */
export const searchFormSchema: FormSchema[] = [
  {
    field: 'idCardNo',
    label: '',
    disabledLabelWidth: false,
    component: 'Input',
    colProps: { span: 24 },
    show: false,
  },
  {
    field: 'year',
    label: '',
    disabledLabelWidth: false,
    component: 'DatePicker',
    componentProps: {
      picker: 'year',
      showTime: false,
      showNow: false,
      valueFormat: 'YYYY',
      placeholder: '请选择年份',
      style: {
        width: '100%',
      },
      getPopupContainer: () => document.body, // 防止弹出层被modal隐藏掉
    },
    defaultValue: dayjs().format('YYYY'),
    colProps: { span: 24 },
    isHandleDateDefaultValue: false,
  },
  {
    field: 'orgName',
    label: '',
    disabledLabelWidth: false,
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入机构',
    },
  },
  // {
  //   field: 'orgId',
  //   label: '',
  //   disabledLabelWidth: false,
  //   component: 'Input',
  //   colProps: { span: 24 },
  //   show: false,
  // },
  // {
  //   field: 'orgCode',
  //   label: '',
  //   disabledLabelWidth: false,
  //   component: 'ApiSelect',
  //   componentProps: ({ formModel }) => {
  //     return {
  //       placeholder: '请选择机构',
  //       api: () => queryOrganizationList(),
  //       valueField: 'orgCode',
  //       labelField: 'orgName',
  //       showSearch: true,

  //       onChange(_, opt) {
  //         if (opt?.label) {
  //           formModel.orgId = opt?.id;
  //           formModel.deptCode = undefined;
  //         }
  //       },
  //     };
  //   },
  //   colProps: { span: 24 },
  // },
  {
    field: 'visitTypeCode',
    label: '',
    disabledLabelWidth: false,
    component: 'Select',
    componentProps: {
      placeholder: '请选择类别',
      options: [
        {
          label: '门诊',
          value: '1',
        },
        {
          label: '住院',
          value: '3',
        },
      ],
    },
    colProps: { span: 24 },
  },
  {
    field: 'deptName',
    label: '',
    disabledLabelWidth: false,
    component: 'Input',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入科室',
    },
  },
  // {
  //   field: 'deptCode',
  //   label: '',
  //   disabledLabelWidth: false,
  //   component: 'ApiSelect',
  //   componentProps: ({ formModel }) => {
  //     return {
  //       placeholder: '请选择科室',
  //       api: () => getDeptList({ orgId: formModel.orgId }),
  //       valueField: 'deptCode',
  //       labelField: 'deptName',
  //       showSearch: true,
  //     };
  //   },
  //   colProps: { span: 24 },
  // },
  {
    field: 'diagnoseName',
    label: '',
    disabledLabelWidth: false,
    component: 'Input',
    componentProps: {
      placeholder: '请输入诊断',
    },
    colProps: { span: 24 },
  },
  {
    field: 'code',
    label: '',
    disabledLabelWidth: false,
    component: 'Select',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请选择生命周期',
      /**
       * 婴期0-1岁
       * 幼儿期1-3岁
       * 学龄前期4-6岁
       * 学龄期7-12岁
       * 青年期13-18岁
       * 成年早期19-25岁
       * 成年中期26-65岁
       * 成年晚期65岁以后
       */
      options: [
        { label: '婴期(0-1岁)', value: 1 },
        { label: '幼儿期(1-3岁)', value: 2 },
        { label: '学龄前期(4-6岁)', value: 3 },
        { label: '学龄期(7-12岁)', value: 4 },
        { label: '青年期(13-18岁)', value: 5 },
        { label: '成年早期(19-25岁)', value: 6 },
        { label: '成年中期(26-65岁)', value: 7 },
        { label: '成年晚期(65岁以后)', value: 8 },
      ],
    },
  },
];
/**
 * 常规表格
指标名称
指标描述
结果
异常标识
参考范围
单位
 */
export const columnsCg: BasicColumn[] = [
  {
    title: '指标名称',
    dataIndex: 'indexName',
    width: 100,
    align: 'left',
  },
  // {
  //   title: '指标描述',
  //   dataIndex: 'indicatorDesc',
  //   width: 150,
  //   align: 'left',
  // },
  {
    title: '结果',
    dataIndex: 'result',
    width: 150,
    align: 'left',
  },
  {
    title: '异常标识',
    dataIndex: 'exceptionResult',
    width: 150,
    align: 'left',
  },
  {
    title: '参考范围',
    dataIndex: 'referenceValue',
    width: 150,
    align: 'left',
  },
  {
    title: '单位',
    dataIndex: 'unit',
    width: 150,
    align: 'left',
  },
];
/**
 * 细菌表格
细菌名称
菌落计数
菌落计数单位
培莽基
培养时间
培莽条件
检測结果
 */
export const columnsXj: BasicColumn[] = [
  {
    title: '细菌名称',
    dataIndex: 'bacterialName',
    width: 100,
    align: 'left',
  },
  {
    title: '菌落计数',
    dataIndex: 'colonyCount',
    width: 80,
    align: 'left',
  },
  {
    title: '菌落计数单位',
    dataIndex: 'colonyCountUnit',
    width: 100,
    align: 'left',
  },
  {
    title: '培养基',
    dataIndex: 'medium',
    width: 150,
    align: 'left',
  },
  {
    title: '培养时间',
    dataIndex: 'cultureTime',
    width: 150,
    align: 'left',
  },
  {
    title: '培养条件',
    dataIndex: 'cultureCondition',
    width: 150,
    align: 'left',
  },
  {
    title: '检测结果',
    dataIndex: 'detectionResult',
    width: 150,
    align: 'left',
  },
];
