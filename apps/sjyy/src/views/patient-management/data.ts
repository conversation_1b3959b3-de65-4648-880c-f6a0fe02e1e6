import type { BasicColumn } from '@ft/internal/components/Table';

/**
 * @description: 表格列
 * 检查情况
 *   治疗情况
 *   涂片
 *   培养
 *   影像学
 *   血糖
 *   血常规
 *   尿常规
 *   肝功能
 *   肾功能
 *   电解质
 *   TSH
 *   ECG
 *   听力
 *   视力视野
 * 用药情况
      H
      R
      SM
      E
      Pto
      PAS
      Km
      Am
      cm
      oxx
      Lfx
      Mfx
      Amx/Civ
      CLr
      其他
 *
 */
export const columns: BasicColumn[] = [
  {
    title: '检查情况',
    dataIndex: 'checkInfo',
    width: 100,
    align: 'center',
    children: [
      {
        title: '治疗情况',
        dataIndex: 'treatmentInfo1',
        width: 100,
        align: 'left',
      },
      {
        title: '涂片',
        dataIndex: 'treatmentInfo2',
        width: 100,
        align: 'left',
      },
      {
        title: '培养',
        dataIndex: 'treatmentInfo3',
        width: 100,
        align: 'left',
      },
      {
        title: '影像学',
        dataIndex: 'treatmentInfo4',
        width: 100,
        align: 'left',
      },
      {
        title: '血糖',
        dataIndex: 'treatmentInfo5',
        width: 100,
        align: 'left',
      },
      {
        title: '血常规',
        dataIndex: 'treatmentInfo6',
        width: 100,
        align: 'left',
      },
      {
        title: '尿常规',
        dataIndex: 'treatmentInfo7',
        width: 100,
        align: 'left',
      },
      {
        title: '肝功能',
        dataIndex: 'treatmentInfo8',
        width: 100,
        align: 'left',
      },
      {
        title: '肾功能',
        dataIndex: 'treatmentInfo9',
        width: 100,
        align: 'left',
      },
      {
        title: '电解质',
        dataIndex: 'treatmentInfo10',
        width: 100,
        align: 'left',
      },
      {
        title: 'TSH',
        dataIndex: 'treatmentInfo11',
        width: 100,
        align: 'left',
      },
      {
        title: 'ECG',
        dataIndex: 'treatmentInfo12',
        width: 100,
        align: 'left',
      },
      {
        title: '听力',
        dataIndex: 'treatmentInfo13',
        width: 100,
        align: 'left',
      },
      {
        title: '视力视野',
        dataIndex: 'treatmentInfo14',
        width: 100,
        align: 'left',
      },
    ],
  },
  {
    title: '用药情况',
    dataIndex: 'medicineInfo',
    width: 100,
    align: 'center',
    children: [
      {
        title: 'H',
        dataIndex: 'medicineInfo1',
        width: 100,
        align: 'left',
      },
      {
        title: 'R',
        dataIndex: 'medicineInfo2',
        width: 100,
        align: 'left',
      },
      {
        title: 'SM',
        dataIndex: 'medicineInfo3',
        width: 100,
        align: 'left',
      },
      {
        title: 'E',
        dataIndex: 'medicineInfo4',
        width: 100,
        align: 'left',
      },
      {
        title: 'Pto',
        dataIndex: 'medicineInfo5',
        width: 100,
        align: 'left',
      },
      {
        title: 'PAS',
        dataIndex: 'medicineInfo6',
        width: 100,
        align: 'left',
      },
      {
        title: 'Km',
        dataIndex: 'medicineInfo7',
        width: 100,
        align: 'left',
      },
      {
        title: 'Am',
        dataIndex: 'medicineInfo8',
        width: 100,
        align: 'left',
      },
      {
        title: 'cm',
        dataIndex: 'medicineInfo9',
        width: 100,
        align: 'left',
      },
      {
        title: 'oxx',
        dataIndex: 'medicineInfo10',
        width: 100,
        align: 'left',
      },
      {
        title: 'Lfx',
        dataIndex: 'medicineInfo11',
        width: 100,
        align: 'left',
      },
      {
        title: 'Mfx',
        dataIndex: 'medicineInfo12',
        width: 100,
        align: 'left',
      },
      {
        title: 'Amx/Civ',
        dataIndex: 'medicineInfo13',
        width: 100,
        align: 'left',
      },
      {
        title: 'CLr',
        dataIndex: 'medicineInfo14',
        width: 100,
        align: 'left',
      },
      {
        title: '其他',
        dataIndex: 'medicineInfo15',
        width: 100,
        align: 'left',
      },
    ],
  },
];
export const femaleBodyMap = {
  '脑、头、颅': {
    cirle: {
      x: '223',
      y: '20',
    },
    path: 'M 223 20  L 273 10',
    text: {
      x: '280',
      y: '15',
    },
  },
  眼: {
    cirle: {
      x: '190',
      y: '38',
    },
    path: 'M 190 38  L 100 39',
    text: {
      x: '52',
      y: '44',
    },
  },
  耳: {
    cirle: {
      x: '180',
      y: '55',
    },
    path: 'M 180 55  L 108 56',
    text: {
      x: '68',
      y: '60',
    },
  },
  鼻: {
    cirle: {
      x: '209',
      y: '65',
    },
    path: 'M 209 65  L 300 55',
    text: {
      x: '312',
      y: '56',
    },
  },
  '口、舌、颌': {
    cirle: {
      x: '209',
      y: '75',
    },
    path: 'M 209 75  L 300 76',
    text: {
      x: '310',
      y: '77',
    },
  },
  '颈、甲状腺、喉、气管、颈前食管、咽': {
    cirle: {
      x: '209',
      y: '85',
    },
    path: 'M 210 85  L 140 83',
    text: {
      x: '76',
      y: '88',
    },
  },
  肩: {
    cirle: {
      x: '255',
      y: '110',
    },
    path: 'M 255 110  L 310 109',
    text: {
      x: '320',
      y: '113',
    },
  },
  锁骨: {
    cirle: {
      x: '209',
      y: '120',
    },
    path: 'M 209 120  L 110 121',
    text: {
      x: '0',
      y: '123',
    },
  },
  心胸: {
    cirle: {
      x: '205',
      y: '145',
    },
    path: 'M 205 145  L 100 146',
    text: {
      x: '0',
      y: '148',
    },
  },
  肘关节: {
    cirle: {
      x: '127',
      y: '208',
    },
    path: 'M 126 208  L 70 204',
    text: {
      x: '0',
      y: '210',
    },
  },
  上肢疾病: {
    cirle: {
      x: '127',
      y: '178',
    },
    path: 'M 126 178  L 100 180',
    text: {
      x: '0',
      y: '185',
    },
  },
  肝胆: {
    cirle: {
      x: '188',
      y: '232',
    },
    path: 'M 188 232  L 100 234',
    text: {
      x: '0',
      y: '235',
    },
  },
  胆囊: {
    cirle: {
      x: '188',
      y: '242',
    },
    path: 'M 188 242  L 90 250',
    text: {
      x: '0',
      y: '255',
    },
  },
  '肾、肾上腺': {
    cirle: {
      x: '186',
      y: '255',
    },
    path: 'M 186 255  L 95 265',
    text: {
      x: '0',
      y: '270',
    },
  },
  腕和手: {
    cirle: {
      x: '106',
      y: '290',
    },
    path: 'M 106 290  L 70 295',
    text: {
      x: '0',
      y: '298',
    },
  },
  手指骨: {
    cirle: {
      x: '96',
      y: '335',
    },
    path: 'M 96 335  L 70 350',
    text: {
      x: '0',
      y: '355',
    },
  },
  大腿: {
    cirle: {
      x: '168',
      y: '420',
    },
    path: 'M 168 420  L 120 420',
    text: {
      x: '20',
      y: '425',
    },
  },
  '下肢膝关节、髌骨、腘': {
    cirle: {
      x: '170',
      y: '465',
    },
    path: 'M 168 465  L 123 481',
    text: {
      x: '25',
      y: '485',
    },
  },
  小腿: {
    cirle: {
      x: '165',
      y: '500',
    },
    path: 'M 165 500  L 110 510',
    text: {
      x: '40',
      y: '516',
    },
  },
  下肢疾病: {
    cirle: {
      x: '155',
      y: '450',
    },
    path: 'M 155 450  L 100 455',
    text: {
      x: '0',
      y: '460',
    },
  },
  足踝骨: {
    cirle: {
      x: '166',
      y: '575',
    },
    path: 'M 165 575  L 110 569',
    text: {
      x: '20',
      y: '572',
    },
  },
  '上肢肱骨、上臂、肱二头肌': {
    cirle: {
      x: '283',
      y: '131',
    },
    path: 'M 283 131  L 316 130',
    text: {
      x: '316',
      y: '135',
    },
  },
  乳腺: {
    cirle: {
      x: '243',
      y: '145',
    },
    path: 'M 243 145  L 316 143',
    text: {
      x: '320',
      y: '150',
    },
  },
  '肺、支气管': {
    cirle: {
      x: '243',
      y: '165',
    },
    path: 'M 243 165  L 316 163',
    text: {
      x: '320',
      y: '168',
    },
  },
  脾脏: {
    cirle: {
      x: '240',
      y: '205',
    },
    path: 'M 240 205  L 316 198',
    text: {
      x: '318',
      y: '202',
    },
  },
  '胃、十二指肠': {
    cirle: {
      x: '230',
      y: '225',
    },
    path: 'M 230 225  L 316 223',
    text: {
      x: '320',
      y: '228',
    },
  },
  '上肢尺骨、桡骨、前臂': {
    cirle: {
      x: '303',
      y: '245',
    },
    path: 'M 303 245  L 356 247',
    text: {
      x: '356',
      y: '250',
    },
  },
  胰腺: {
    cirle: {
      x: '216',
      y: '255',
    },
    path: 'M 216 255  L 330 261',
    text: {
      x: '332',
      y: '268',
    },
  },
  下腹腔: {
    cirle: {
      x: '235',
      y: '278',
    },
    path: 'M 235 278  L 330 293',
    text: {
      x: '332',
      y: '296',
    },
  },
  小腹部: {
    cirle: {
      x: '195',
      y: '298',
    },
    path: 'M 195 298  L 340 315',
    text: {
      x: '343',
      y: '320',
    },
  },
  '阴道、尿道口': {
    cirle: {
      x: '208',
      y: '331',
    },
    path: 'M 208 331  L 260 348',
    text: {
      x: '263',
      y: '356',
    },
  },
  跗骨: {
    cirle: {
      x: '248',
      y: '585',
    },
    path: 'M 248 585  L 290 583',
    text: {
      x: '295',
      y: '585',
    },
  },
  跖骨: {
    cirle: {
      x: '248',
      y: '596',
    },
    path: 'M 248 596  L 290 594',
    text: {
      x: '295',
      y: '598',
    },
  },
  脚趾骨: {
    cirle: {
      x: '248',
      y: '610',
    },
    path: 'M 248 610  L 290 608',
    text: {
      x: '295',
      y: '613',
    },
  },
};
export const maleBodyMap = {
  '脑、头、颅': {
    cirle: {
      x: '223',
      y: '20',
    },
    path: 'M 223 20  L 283 14',
    text: {
      x: '293',
      y: '20',
    },
  },
  眼: {
    cirle: {
      x: '190',
      y: '34',
    },
    path: 'M 190 34  L 122 29',
    text: {
      x: '82',
      y: '34',
    },
  },
  耳: {
    cirle: {
      x: '180',
      y: '45',
    },
    path: 'M 180 45  L 145 48',
    text: {
      x: '98',
      y: '53',
    },
  },
  鼻: {
    cirle: {
      x: '209',
      y: '53',
    },
    path: 'M 209 53  L 270 50',
    text: {
      x: '281',
      y: '53',
    },
  },
  '口、舌、颌': {
    cirle: {
      x: '209',
      y: '67',
    },
    path: 'M 209 67  L 280 67',
    text: {
      x: '286',
      y: '73',
    },
  },
  '颈、甲状腺、喉、气管、颈前食管、咽': {
    cirle: {
      x: '208',
      y: '85',
    },
    path: 'M 208 85  L 150 85',
    text: {
      x: '80',
      y: '90',
    },
  },
  肩: {
    cirle: {
      x: '255',
      y: '105',
    },
    path: 'M 255 105  L 310 105',
    text: {
      x: '315',
      y: '95',
    },
  },
  锁骨: {
    cirle: {
      x: '209',
      y: '120',
    },
    path: 'M 209 120  L 130 118',
    text: {
      x: '40',
      y: '120',
    },
  },
  心胸: {
    cirle: {
      x: '205',
      y: '145',
    },
    path: 'M 205 145  L 118 140',
    text: {
      x: '40',
      y: '145',
    },
  },
  肘关节: {
    cirle: {
      x: '146',
      y: '212',
    },
    path: 'M 146 212  L 90 198',
    text: {
      x: '20',
      y: '198',
    },
  },
  上肢疾病: {
    cirle: {
      x: '136',
      y: '178',
    },
    path: 'M 136 178  L 100 175',
    text: {
      x: '16',
      y: '175',
    },
  },
  肝胆: {
    cirle: {
      x: '188',
      y: '232',
    },
    path: 'M 188 232  L 94 224',
    text: {
      x: '10',
      y: '228',
    },
  },
  胆囊: {
    cirle: {
      x: '188',
      y: '245',
    },
    path: 'M 188 245  L 100 245',
    text: {
      x: '20',
      y: '248',
    },
  },
  '肾、肾上腺': {
    cirle: {
      x: '186',
      y: '255',
    },
    path: 'M 186 255  L 95 260',
    text: {
      x: '10',
      y: '265',
    },
  },
  腕和手: {
    cirle: {
      x: '106',
      y: '290',
    },
    path: 'M 106 290  L 70 295',
    text: {
      x: '0',
      y: '303',
    },
  },
  手指骨: {
    cirle: {
      x: '100',
      y: '330',
    },
    path: 'M 100 330  L 70 345',
    text: {
      x: '0',
      y: '350',
    },
  },
  大腿: {
    cirle: {
      x: '168',
      y: '400',
    },
    path: 'M 168 400  L 120 400',
    text: {
      x: '20',
      y: '405',
    },
  },
  '下肢膝关节、髌骨': {
    cirle: {
      x: '168',
      y: '450',
    },
    path: 'M 168 450  L 120 451',
    text: {
      x: '40',
      y: '455',
    },
  },
  小腿: {
    cirle: {
      x: '165',
      y: '500',
    },
    path: 'M 165 500  L 110 505',
    text: {
      x: '40',
      y: '510',
    },
  },
  下肢疾病: {
    cirle: {
      x: '155',
      y: '430',
    },
    path: 'M 155 430  L 110 428',
    text: {
      x: '20',
      y: '430',
    },
  },
  足踝骨: {
    cirle: {
      x: '165',
      y: '579',
    },
    path: 'M 165 579  L 120 589',
    text: {
      x: '40',
      y: '599',
    },
  },
  '上肢肱骨、上臂、肱二头肌': {
    cirle: {
      x: '283',
      y: '131',
    },
    path: 'M 283 131  L 316 122',
    text: {
      x: '316',
      y: '125',
    },
  },
  乳腺: {
    cirle: {
      x: '243',
      y: '145',
    },
    path: 'M 243 145  L 316 143',
    text: {
      x: '320',
      y: '150',
    },
  },
  '肺、支气管': {
    cirle: {
      x: '243',
      y: '165',
    },
    path: 'M 243 165  L 320 163',
    text: {
      x: '325',
      y: '168',
    },
  },
  脾脏: {
    cirle: {
      x: '240',
      y: '205',
    },
    path: 'M 240 205  L 310 193',
    text: {
      x: '320',
      y: '192',
    },
  },
  '胃、十二指肠': {
    cirle: {
      x: '230',
      y: '225',
    },
    path: 'M 230 225  L 316 213',
    text: {
      x: '320',
      y: '218',
    },
  },
  '上肢尺骨、桡骨、前臂': {
    cirle: {
      x: '303',
      y: '245',
    },
    path: 'M 303 245  L 336 235',
    text: {
      x: '340',
      y: '238',
    },
  },
  胰腺: {
    cirle: {
      x: '216',
      y: '258',
    },
    path: 'M 216 258  L 320 255',
    text: {
      x: '332',
      y: '260',
    },
  },
  下腹腔: {
    cirle: {
      x: '235',
      y: '278',
    },
    path: 'M 235 278  L 330 283',
    text: {
      x: '332',
      y: '286',
    },
  },
  小腹部: {
    cirle: {
      x: '195',
      y: '298',
    },
    path: 'M 195 298  L 340 305',
    text: {
      x: '343',
      y: '310',
    },
  },
  男性外生殖器疾患: {
    cirle: {
      x: '208',
      y: '331',
    },
    path: 'M 208 331  L 340 348',
    text: {
      x: '343',
      y: '350',
    },
  },
  跗骨: {
    cirle: {
      x: '258',
      y: '595',
    },
    path: 'M 258 595  L 290 568',
    text: {
      x: '295',
      y: '569',
    },
  },
  跖骨: {
    cirle: {
      x: '265',
      y: '600',
    },
    path: 'M 265 600  L 298 590',
    text: {
      x: '300',
      y: '595',
    },
  },
  脚趾骨: {
    cirle: {
      x: '273',
      y: '610',
    },
    path: 'M 273 610  L 300 610',
    text: {
      x: '305',
      y: '616',
    },
  },
};
