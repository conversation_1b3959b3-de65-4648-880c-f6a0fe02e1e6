<script setup lang="ts">
  import { useDrawer } from '@ft/internal/components/Drawer';
  import { Icon } from '@ft/internal/components/Icon';
  import { FileTextOutlined } from '@ant-design/icons-vue';
  import { computed } from 'vue';
  import { getPatientInfo } from '@ft/internal/utils/auth';

  import type { DiagnosisCountVo } from '/@/api/patient-management';
  import { useUserStore } from '@ft/internal/store/modules/user';
  import DrugDrawer from './Details.vue';

  defineProps<{
    medicalShow: Boolean;
    medicalInfo?: DiagnosisCountVo;
  }>();
  const patientInfo = computed(() => {
    return getPatientInfo();
  });
  const [registerDrawer, { openDrawer }] = useDrawer();
  function formatName(name) {
    if (name.length >= 3) {
      const firstChar = name[0];
      const lastChar = name[name.length - 1];
      return firstChar + '*' + lastChar;
    }
    return name;
  }
  const userStore = useUserStore();
  const isDesensitization = computed(() => {
    return userStore?.getUserInfo?.isDesensitization;
  });
  //当前登陆用户信息isDesensitization=1时，身份证脱敏
  function idCardMask(idCard) {
    if (!idCard) return '';
    return isDesensitization.value === 1
      ? idCard?.replace(/(\d{6})(\d{8})(\w{4})/, '$1********$3')
      : idCard;
  }
</script>
<template>
  <div>
    <div class="flex flex-col min-w-228px bg-#fff rounded-[0_8px_8px_8px] py-4 px-4">
      <div class="relative flex justify-between gap-3 mb-3">
        <div
          class="rounded-4 flex items-center text-20px justify-center fw-bold text-#fff bg-gradient-to-br bg-left-top from-#FF6161 to-#FF9494 from-4% to-99% h-56px w-56px"
        >
          {{ formatName(patientInfo?.name) }}
        </div>
        <div class="flex-1 flex flex-col justify-center text-14px gap-6px">
          <div class="flex gap-2 items-center">
            <span class="text-#333333 fw-bold">{{ patientInfo?.name }}</span>
            <Icon v-if="patientInfo?.sex === 2" icon="woman|svg" :size="16" />
            <Icon v-else icon="man|svg" :size="16" />
          </div>
          <div class="text-#999999">
            {{ patientInfo?.birthday || '-' }} ({{ patientInfo?.age || '-' }})
          </div>
        </div>
        <FileTextOutlined
          @click="openDrawer(true)"
          class="absolute right-0 top-0 !text-primary-color cursor-pointer"
        />
      </div>
      <div class="flex flex-col gap-2 text-#666666">
        <div> {{ patientInfo?.telephone || '-' }} </div>
        <div>{{ patientInfo?.idCardNo && idCardMask(patientInfo?.idCardNo) }}</div>
      </div>
      <div v-if="medicalShow">
        <div class="h-1px w-full bg-#E7E7E7 mt-2 mb-3"></div>
        <div class="flex justify-evenly items-center gap-10">
          <!-- <div class="flex flex-col items-center">
            <div class="text-#999999">急诊</div>
            <div class="text-primary-color fw-bold">{{ medicalInfo?.emergencyCount || 0 }}</div>
          </div> -->
          <div class="flex flex-col items-center">
            <div class="text-#999999">门诊</div>
            <div class="text-primary-color fw-bold">{{ medicalInfo?.outpatientCount || 0 }}</div>
          </div>
          <div class="flex flex-col items-center">
            <div class="text-#999999">住院</div>
            <div class="text-primary-color fw-bold">{{ medicalInfo?.inpatientCount || 0 }}</div>
          </div>
        </div>
      </div>
    </div>
    <DrugDrawer @register="registerDrawer" />
  </div>
</template>
<style></style>
