<script setup lang="ts">
  import { Button } from '@ft/internal/components/Button';
  import { Descriptions, DescriptionsItem } from 'ant-design-vue';
  import { BasicDrawer, useDrawerInner } from '@ft/internal/components/Drawer';
  import { computed } from 'vue';
  import { getPatientInfo } from '@ft/internal/utils/auth';
  import { useUserStore } from '@ft/internal/store/modules/user';

  const emit = defineEmits(['register', 'close']);

  const [register, { closeDrawer }] = useDrawerInner();
  const patientInfo = computed(() => {
    return getPatientInfo();
  });

  function _closeDrawer() {
    emit('close');
    closeDrawer();
  }

  function onClose() {
    _closeDrawer();
  }

  function onOk() {
    _closeDrawer();
  }
  const userStore = useUserStore();
  const isDesensitization = computed(() => {
    return userStore?.getUserInfo?.isDesensitization;
  });
  //当前登陆用户信息isDesensitization=1时，身份证脱敏
  function idCardMask(idCard) {
    if (!idCard) return '';
    return isDesensitization.value === 1
      ? idCard?.replace(/(\d{6})(\d{8})(\w{4})/, '$1********$3')
      : idCard;
  }
</script>

<template>
  <BasicDrawer
    :mask="false"
    v-bind="$attrs"
    :maskClosable="false"
    width="857px"
    @register="register"
    title="患者基本信息"
    destroyOnClose
    showFooter
    @close="onClose"
  >
    <Descriptions
      class="user-info-descriptions"
      :label-style="{
        color: '#333333',
        background: '#EBEEF5',
        width: '145px',
        height: '40px',
        padding: '9px 12px',
      }"
      :contentStyle="{ color: '#333333', height: '40px', padding: '9px 12px' }"
      bordered
    >
      <DescriptionsItem :span="2" label="患者姓名">{{ patientInfo?.name }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="患者编号">{{ patientInfo?.patientId }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="性别">{{ patientInfo?.sexName }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="身份证号">{{
        idCardMask(patientInfo?.idCardNo)
      }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="出生日期">{{ patientInfo?.birthday }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="年龄">{{ patientInfo?.age }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="民族">{{ patientInfo?.nation }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="证件类型">{{ patientInfo?.cardType }} </DescriptionsItem>
      <DescriptionsItem :span="2" label="本人电话">{{ patientInfo?.telephone }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="职业">{{ patientInfo?.personType }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="文化程度">{{
        patientInfo?.educationLevel
      }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="婚姻状况">{{
        patientInfo?.maritalStatus
      }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="联系人1姓名">{{
        patientInfo?.contactName1
      }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="联系人1电话">{{
        patientInfo?.contactTelephone1
      }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="联系人2姓名">{{
        patientInfo?.contactName2
      }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="联系人2电话">{{
        patientInfo?.contactTelephone2
      }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="现住址类型">{{
        patientInfo?.addressType
      }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="现住址">{{ patientInfo?.address }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="户籍地类型">{{
        patientInfo?.registeredResidenceType
      }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="户籍地址">{{
        patientInfo?.registeredResidence
      }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="所属区划">{{ patientInfo?.regionalism }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="所属社区">{{ patientInfo?.community }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="社区负责人">{{
        patientInfo?.communityLeader
      }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="社区联系方式">{{
        patientInfo?.communityTelephone
      }}</DescriptionsItem>
      <DescriptionsItem :span="3" label="医疗费用支付方式">{{
        patientInfo?.payType
      }}</DescriptionsItem>
      <DescriptionsItem :span="3" label="药物过敏史">
        {{ patientInfo?.allergyList?.map((item) => item?.medicalHistoryName)?.join('、') }}
      </DescriptionsItem>
      <DescriptionsItem :span="3" label="暴露史">{{
        patientInfo?.exposeList?.map((item) => item?.medicalHistoryName)?.join('、')
      }}</DescriptionsItem>
    </Descriptions>
    <div class="w-full h-10 line-height-10 text-center bg-#E2EDFF">既往史</div>
    <Descriptions
      class="user-info-descriptions"
      :label-style="{ color: '#333333', background: '#EBEEF5', width: '145px' }"
      bordered
    >
      <DescriptionsItem :span="3" label="疾病史">
        {{ patientInfo?.personalList?.map((item) => item?.medicalHistoryName)?.join('、') }}
      </DescriptionsItem>
      <DescriptionsItem :span="3" label="手术史">{{
        patientInfo?.operationList?.map((item) => item?.medicalHistoryName)?.join('、')
      }}</DescriptionsItem>
      <DescriptionsItem :span="3" label="输血史">{{
        patientInfo?.bloodTransfusionList?.map((item) => item?.medicalHistoryName)?.join('、')
      }}</DescriptionsItem>
    </Descriptions>
    <div class="w-full h-10 line-height-10 text-center bg-#E2EDFF">家族史</div>
    <Descriptions
      class="user-info-descriptions"
      :label-style="{ color: '#333333', background: '#EBEEF5', width: '145px' }"
      bordered
    >
      <DescriptionsItem :span="3" label="家族史">{{
        patientInfo?.familyList?.map((item) => item?.medicalHistoryName)?.join('、')
      }}</DescriptionsItem>
      <!-- <DescriptionsItem :span="2" label="父亲">{{ patientInfo?.family?.father }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="母亲">{{ patientInfo?.family?.mother }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="兄弟姐妹">{{
        patientInfo?.family?.sibling
      }}</DescriptionsItem>
      <DescriptionsItem :span="2" label="子女">{{
        patientInfo?.family?.children
      }}</DescriptionsItem>
      <DescriptionsItem :span="3" label="遗传病史">{{
        patientInfo?.family?.geneticHistory
      }}</DescriptionsItem>
      <DescriptionsItem :span="3" label="残疾情况">{{
        patientInfo?.family?.disabilitySituation
      }}</DescriptionsItem> -->
    </Descriptions>
    <template #footer>
      <Button style="margin-right: 8px" @click="onClose">取消</Button>
      <Button type="primary" @click="onOk">确定</Button>
    </template>
  </BasicDrawer>
</template>

<style lang="less" scoped>
  .user-info-descriptions {
    :deep {
      .ant-descriptions-view {
        border-radius: 0;
      }

      .ant-descriptions-bordered .ant-descriptions-item-label,
      .ant-descriptions-bordered .ant-descriptions-item-content {
        border-color: #ebeef5 !important;
        border-bottom: 1px solid #ebeef5;
        border-right: 1px solid #ebeef5;
      }
    }
  }
</style>
