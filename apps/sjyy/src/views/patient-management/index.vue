<script setup lang="ts">
  import { Icon } from '@ft/internal/components/Icon';
  import { Tabs, Timeline, TimelineItem } from 'ant-design-vue';

  import type { Ref } from 'vue';
  import { computed } from 'vue';
  import { getPatientInfo, setPatientInfo } from '@ft/internal/utils/auth';
  import { useRequest } from '@ft/request';
  import { useModal } from '@ft/internal/components/Modal';

  import SelectModel from '../select/SelectModel.vue';
  import TherapyProcessTab from '../../../../ylyw/src/views/patient-detail/TherapyProcessTab/index.vue';
  import PatientInfo from './PatientInfo.vue';
  import type { DiseaseMapping } from '/@/api/select';
  import { diseaseMappingDic, getPatient } from '/@/api/select';
  import PatientList from '/@/assets/icons/patientList.png';
  import { getPatientHistoryAndCount } from '/@/api/patient-management';
  import FemaleBodySvg from './component/FemaleBody.vue';
  import MaleBodySvg from './component/MaleBody.vue';

  //患者信息
  const patientInfo = computed(() => {
    return getPatientInfo();
  });
  const activeKey = computed(() => {
    return patientInfo.value.infectionDisease;
  });

  const dataList: any = computed(() => {
    return [
      {
        icon: 'card-1|svg',
        bgCard: 'card-bg-1|svg',
        title: '过敏史',
        list: info.value?.pastHistoryVO.allergyList || [],
      },
      {
        icon: 'card-2|svg',
        bgCard: 'card-bg-2|svg',
        title: '传染病史',
        list: info.value?.pastHistoryVO.infectiousDiseaseList || [],
      },
      {
        icon: 'card-3|svg',
        bgCard: 'card-bg-3|svg',
        title: '个人既往史',
        list: info.value?.pastHistoryVO.personalList || [],
      },
      {
        icon: 'card-4|svg',
        bgCard: 'card-bg-4|svg',
        title: '家族史',
        list: info.value?.pastHistoryVO?.familyList || [],

        // list: [
        //   {
        //     label: '父亲:',
        //     value: info.value?.pastHistoryVO.family?.father,
        //   },
        //   {
        //     label: '母亲:',
        //     value: info.value?.pastHistoryVO.family?.mother,
        //   },
        //   {
        //     label: '兄弟姐妹:',
        //     value: info.value?.pastHistoryVO.family?.sibling,
        //   },
        //   {
        //     label: '子女:',
        //     value: info.value?.pastHistoryVO.family?.children,
        //   },
        //   {
        //     label: '遗传病史:',
        //     value: info.value?.pastHistoryVO.family?.geneticHistory,
        //   },
        //   {
        //     label: '残疾情况:',
        //     value: info.value?.pastHistoryVO.family?.disabilitySituation,
        //   },
        // ],
      },
      {
        icon: 'card-5|svg',
        bgCard: 'card-bg-5|svg',
        title: '手术史',
        list: info.value?.pastHistoryVO.operationList || [],
      },
      {
        icon: 'card-6|svg',
        bgCard: 'card-bg-6|svg',
        title: '输血史',
        list: info.value?.pastHistoryVO.bloodTransfusionList || [],
      },
    ];
  });
  const { data: info } = useRequest(() => getPatientHistoryAndCount(patientInfo.value.idCardNo));
  function getClass(icon: string) {
    const iconName = icon.replace('|svg', '');
    return `cardBox-${iconName}`;
  }

  const { data: resultMappingDic = {} as unknown as Ref<DiseaseMapping> } = useRequest(() =>
    diseaseMappingDic({
      idCardNo: patientInfo.value?.idCardNo,
    }),
  );
  const [registerModal, { openModal }] = useModal();
  const { runAsync: runGetPatient } = useRequest(getPatient, {
    manual: true,
    showSuccessMessage: false,
  });

  function handleOk(data: string) {
    runGetPatient(data).then((res) => {
      setPatientInfo(res);
      setTimeout(() => {
        window.location.reload();
      });
    });
  }
</script>

<template>
  <div class="patient-management w-full h-full pr-4 flex flex-col gap-14px pb-10px">
    <div class="flex gap-10px">
      <PatientInfo :medicalShow="true" :medical-info="info?.diagnosisCountVO" />
      <div class="flex justify-between gap-10px flex-1">
        <div
          v-for="(item, index) in dataList"
          :key="index"
          class="p-3 min-w-174px flex-1 border-1px border-#fff rounded-2"
          :class="[getClass(item.icon)]"
        >
          <div class="flex justify-start items-center gap-2">
            <Icon :icon="item.icon" :size="20" />
            <div
              class="text-transparent fw-bold text-shadow-[0px_0px_10px_0px_rgba(0,0,0,0.05)] title"
              >{{ item.title }}</div
            >
          </div>
          <div class="mt-3 max-h-150px overflow-y-auto">
            <div v-if="item.list && item.list.length > 0">
              <Timeline>
                <TimelineItem v-for="(v, i) in item.list" :key="i" class="pb-0" color="#D8D8D8">
                  <template #dot>
                    <div class="w-6px h-6px rounded-50% bg-#D8D8D8"></div>
                  </template>
                  <div class="text-#999 text-12px line-height-28px">
                    {{ v?.medicalHistoryTime }}
                  </div>
                  <div class="text-#333 text-14px line-height-28px">{{ v.medicalHistoryName }}</div>
                </TimelineItem>
              </Timeline>
            </div>
            <span v-else>无</span>
          </div>
        </div>
      </div>
    </div>
    <div class="flex-1 bg-#fff rounded-2 flex justify-between">
      <div class="border-r-1px w-400px border-color-#EDEEF0 py-8">
        <FemaleBodySvg :disease="resultMappingDic" v-if="patientInfo.sex === 2" />
        <MaleBodySvg :disease="resultMappingDic" v-else />
      </div>
      <div class="flex-1 p-4 h-full overflow-hidden">
        <div class="flex items-center gap-2">
          <span class="inline-block bg-primary-color w-3px h-1em"></span>
          <span class="text-#333333 fw-bold">传染病及慢病治疗情况监测</span>
        </div>
        <!-- 传染病类型 1-病毒性肝炎 2-艾滋病 3-结核病 -->
        <Tabs v-model:activeKey="activeKey">
          <Tabs.TabPane v-if="patientInfo?.infectionDisease == '1'" key="1" tab="病毒性肝炎" />
          <Tabs.TabPane v-if="patientInfo?.infectionDisease == '2'" key="2" tab="艾滋病" />
          <Tabs.TabPane v-if="patientInfo?.infectionDisease == '3'" key="3" tab="结核病" />
        </Tabs>
        <TherapyProcessTab
          :infectionDisease="patientInfo?.infectionDisease"
          :diseaseSubCode="patientInfo?.diseaseSubCode"
          :patientProfileRecordId="patientInfo?.patientProfileRecordId"
          :patientId="patientInfo?.id"
          systemType="sjyy"
        />
      </div>
    </div>
    <div
      class="absolute top-190px right-0 z-2 w-168px h-86px cursor-pointer"
      @click="openModal(true, { isCanClose: true })"
    >
      <img :src="PatientList" class="w-full h-full" />
    </div>
    <SelectModel @register="registerModal" @success="handleOk" :default-visible="false" />
  </div>
</template>

<style lang="less" scoped>
  :deep {
    .vben-basic-table .ant-table-wrapper {
      padding: 0;
    }

    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table-form-container .ant-form,
    .vben-basic-table .ant-table-wrapper {
      border-radius: 4px;
    }
  }

  .patient-management {
    .cardBox-card-1 {
      background: url('/@/assets/icons/card-bg-1.png') no-repeat 90% 95%/30% 30%,
        linear-gradient(320deg, #f8f5ff 1%, #fff 100%);

      .title {
        background: linear-gradient(121deg, #60f 0%, #ae98ff 100%);
        background-clip: text;
      }
    }

    .cardBox-card-2 {
      background: url('/@/assets/icons/card-bg-2.png') no-repeat 90% 95%/30% 30%,
        linear-gradient(320deg, #fff9f4 1%, #fff 100%);

      .title {
        background: linear-gradient(115deg, #fd8f27 0%, #f33 100%);
        background-clip: text;
      }
    }

    .cardBox-card-3 {
      background: url('/@/assets/icons/card-bg-3.png') no-repeat 90% 95%/30% 30%,
        linear-gradient(320deg, #f4fff5 1%, #fff 100%);

      .title {
        background: linear-gradient(110deg, #60d314 0%, #2ca14b 100%);
        background-clip: text;
      }
    }

    .cardBox-card-4 {
      background: url('/@/assets/icons/card-bg-4.png') no-repeat 90% 95%/30% 30%,
        linear-gradient(320deg, #f5f6ff 1%, #fff 100%);

      .title {
        background: linear-gradient(121deg, #7196fa 0%, #2841e4 100%);
        background-clip: text;
      }
    }

    .cardBox-card-5 {
      background: url('/@/assets/icons/card-bg-5.png') no-repeat 90% 95%/30% 30%,
        linear-gradient(320deg, #f4feff 1%, #fff 100%);

      .title {
        background: linear-gradient(121deg, #64e1ea 0%, #10b2c4 100%);
        background-clip: text;
      }
    }

    .cardBox-card-6 {
      background: url('/@/assets/icons/card-bg-6.png') no-repeat 90% 95%/30% 30%,
        linear-gradient(320deg, #fff5f7 1%, #fff 100%);

      .title {
        background: linear-gradient(121deg, #fa7183 0%, #e42851 100%);
        background-clip: text;
      }
    }

    .ant-timeline-item {
      padding-bottom: 0;
    }
  }
</style>
