<template>
  <!-- width: 450px; -->
  <div class="h-full">
    <svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="100%" height="100%">
      <image
        sodipodi:absref="/@/assets/images/female_body_new.png"
        xlink:href="/@/assets/images/female_body_new.png"
        x="80"
        y="0"
        id="image2993"
        height="100%"
      />
      <g v-for="(val, key) in disease" :key="key">
        <path :d="femaleBodyMap[key]?.path" fill="rgb(61, 61, 61)" stroke="rgb(61, 61, 61)" />
        <circle
          id="svg_1"
          :cx="femaleBodyMap[key]?.cirle?.x"
          :cy="femaleBodyMap[key]?.cirle?.y"
          r="3"
          fill="rgb(61, 61, 61)"
        />
        <!-- <foreignObject
          :x="femaleBodyMap[key].text.x"
          :y="femaleBodyMap[key].text.y"
          width="100px"
          height="100px"
        > -->
        <g>
          <text
            v-for="(item, index) in val"
            :key="item.diagnosisCode"
            :x="femaleBodyMap[key]?.text?.x"
            :y="Number(femaleBodyMap[key]?.text?.y) + index * 15"
            class="svg_text"
            style="color: #3d3d3d; font-size: 12px"
          >
            {{
              item.diagnosisName?.length > 6
                ? item.diagnosisName?.substring(0, 6) + '...'
                : item.diagnosisName
            }}
            <title>{{ item.diagnosisName }}</title>
          </text>
        </g>
        <!-- </foreignObject> -->
      </g>
    </svg>
  </div>
</template>

<script lang="ts" setup>
  import { femaleBodyMap } from '../data';
  import type { DiseaseMapping } from '/@/api/select';
  defineProps<{
    disease?: DiseaseMapping;
  }>();
</script>
<style lang="less" scoped>
  #svg-3:hover #svg-1 {
    stroke: #d70000;
    stroke-width: 10px;
    fill: #30db30;
  }

  .svg_text {
    display: block;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    z-index: 1000;
  }
</style>
