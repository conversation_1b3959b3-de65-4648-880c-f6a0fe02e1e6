<template>
  <div class="h-full">
    <svg xmlns="http://www.w3.org/2000/svg" version="1.1" width="100%" height="100%">
      <image
        sodipodi:absref="/@/assets/images/male_body_new.png"
        xlink:href="/@/assets/images/male_body_new.png"
        id="image2993"
        x="80"
        y="0"
        height="100%"
      />
      <g v-for="(val, key) in disease" :key="key">
        <path :d="maleBodyMap[key].path" fill="rgb(61, 61, 61)" stroke="rgb(61, 61, 61)" />
        <circle
          id="svg_1"
          :cx="maleBodyMap[key].cirle.x"
          :cy="maleBodyMap[key].cirle.y"
          r="3"
          fill="rgb(61, 61, 61)"
        />
        <!-- <foreignObject
          :x="maleBodyMap[key].text.x"
          :y="maleBodyMap[key].text.y"
          width="110px"
          height="100px"
        > -->
        <g>
          <text
            v-for="(item, index) in val"
            :key="item?.diagnosisCode"
            :x="maleBodyMap[key].text.x"
            :y="Number(maleBodyMap[key].text.y) + index * 15"
            class="svg_text"
            :title="item?.diagnosisName"
            style="color: #3d3d3d; font-size: 12px; z-index: 100"
          >
            {{
              item?.diagnosisName?.length > 6
                ? item?.diagnosisName?.substring(0, 6) + '...'
                : item?.diagnosisName
            }}
            <title>{{ item?.diagnosisName }}</title>
          </text>
        </g>
      </g>
    </svg>
  </div>
</template>
<script lang="ts" setup>
  import { maleBodyMap } from '../data';
  import type { DiseaseMapping } from '/@/api/select';

  defineProps<{
    disease?: DiseaseMapping;
  }>();
</script>
<style lang="less" scoped>
  #svg-3:hover + #svg-1 {
    stroke: #479e96;
    stroke-width: 10px;
    fill: #fff;
  }

  .svg_text {
    display: block;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
</style>
