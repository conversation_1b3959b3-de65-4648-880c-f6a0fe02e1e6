<script setup lang="ts">
  import { Tabs } from 'ant-design-vue';
  import { computed, ref } from 'vue';
  import type { ActionItem, BasicColumn } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { StyledList } from '@ft/components';
  import { useRequest } from '@ft/request';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { useDrawer } from '@ft/internal/components/Drawer';
  import { getPatientInfo } from '@ft/internal/utils/auth';
  import PatientInfo from '../patient-management/PatientInfo.vue';
  import DrugDrawer from './Details.vue';
  import { columns1, columns2, columns3, formSchemas } from './data';
  import {
    getFollowupSituation,
    getInfectionPatientBasicInfo,
    getMedicationSituation,
    getPatientVaccinationInfo,
  } from '/@/api/public-health';

  useRequest(() => getInfectionPatientBasicInfo(patientInfo.value.id), {
    onSuccess: (data) => {
      formAction.setFieldsValue(data);
    },
  });
  const activeKeyType = ref('1');
  //患者信息
  const patientInfo = computed(() => {
    return getPatientInfo();
  });
  const infectionDiseaseMap = {
    '1': '病毒性肝炎',
    '2': '艾滋病',
    '3': '结核病',
  };
  const items = computed(() => {
    return [
      {
        label: infectionDiseaseMap[patientInfo.value.infectionDisease],
        value: patientInfo.value.infectionDisease,
      },
    ];
  });
  const activeItem = ref(patientInfo.value.infectionDisease);
  // 表单
  const [registerForm, formAction] = useForm({
    schemas: formSchemas,
    disabled: true,
    labelWidth: 80,
    baseColProps: { span: 6 },
    actionColOptions: { span: 24 },
    autoSubmitOnEnter: true,
    showResetButton: false,
    showSubmitButton: false,
    showActionButtonGroup: false,
  });
  const [registerTable1] = useTable({
    api: () => getMedicationSituation(patientInfo.value.idCardNo),
    pagination: false,
    columns: columns1,
    // dataSource: [{}],
    useSearchForm: false,
    showIndexColumn: true,
    bordered: false,
    size: 'small',
    // resizeHeightOffset: 20,
    scroll: { x: undefined, y: 200 },
  });
  const [registerTable2] = useTable({
    api: () =>
      getPatientVaccinationInfo(patientInfo.value.idCardNo, patientInfo.value.infectionDisease),
    pagination: false,
    columns: columns2,
    // dataSource: [{}],
    useSearchForm: false,
    showIndexColumn: true,
    bordered: false,
    indexColumnProps: {
      width: 50,
    },
    size: 'small',
    // resizeHeightOffset: 20,
    scroll: { x: undefined, y: 200 },
  });
  const [registerTable3] = useTable({
    api: () => getFollowupSituation(patientInfo.value.idCardNo),
    pagination: false,
    columns: columns3,
    // dataSource: [{}],
    useSearchForm: false,
    showIndexColumn: true,
    indexColumnProps: {
      width: 50,
    },
    bordered: false,
    size: 'small',
    // resizeHeightOffset: 20,
    scroll: { x: undefined, y: 200 },
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
  });
  function createActions(record, column: BasicColumn): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '详情',
        type: 'link',
        size: 'small',
        onClick: handleDetail.bind(null, record, column),
      },
    ];
    return actions;
  }

  const [registerDrawer, { openDrawer }] = useDrawer();
  function handleDetail(record, column) {
    console.log(record, column);
    openDrawer(true, {
      record,
      infectionDiseaseName: infectionDiseaseMap[patientInfo.value.infectionDisease],
    });
  }
</script>
<template>
  <div class="w-full h-full pr-4 flex justify-between gap-10px pb-10px">
    <div class="flex flex-col gap-10px">
      <PatientInfo :medicalShow="false" />
      <div class="flex-1 bg-#fff rounded-2 p-3 flex flex-col gap-2">
        <div>公共卫生管理</div>
        <Tabs v-model:activeKey="activeKeyType">
          <Tabs.TabPane key="1" class="flex-1" tab="传染病管理">
            <StyledList
              :items="items"
              v-model="activeItem"
              class="flex-1 doctors-view-styled-list"
              :width="216"
            >
              <template #default="item">
                <div class="flex gap-2 p-6px -ml-3">
                  {{ item.label }}
                </div>
              </template>
            </StyledList>
          </Tabs.TabPane>
          <!-- <Tabs.TabPane key="2" class="flex-1" tab="慢病管理">
            <StyledList
              :items="items"
              v-model="activeItem"
              class="flex-1 doctors-view-styled-list"
              :width="216"
            >
              <template #default="item">
                <div class="flex gap-2 p-6px -ml-3">
                  {{ item.label }}
                </div>
              </template>
            </StyledList>
          </Tabs.TabPane> -->
        </Tabs>
      </div>
    </div>
    <div
      class="flex-1 flex gap-4 flex-col bg-#fff rounded-2 p-4 h-[calc(100vh-7rem)] overflow-y-auto"
    >
      <div class="flex items-center gap-2">
        <span class="inline-block bg-primary-color w-3px h-1em"></span>
        <span class="text-#333333 fw-bold"
          >{{ infectionDiseaseMap[patientInfo.infectionDisease] }}管理</span
        >
      </div>
      <div class="flex-1 flex flex-col gap-4">
        <div class="flex items-center gap-2 bg-#FAFAFA">
          <span class="inline-block bg-primary-color w-2px h-8 mr-3"></span>
          <span class="text-#333333 py-5px">基本情况</span>
        </div>
        <BasicForm @register="registerForm" />
      </div>
      <div class="flex-1 flex flex-col gap-4">
        <div class="flex items-center gap-2 bg-#FAFAFA">
          <span class="inline-block bg-primary-color w-2px h-8 mr-3"></span>
          <span class="text-#333333 py-5px">服药情况</span>
        </div>
        <BasicTable @register="registerTable1" />
      </div>
      <div class="flex-1 flex flex-col gap-4">
        <div class="flex items-center gap-2 bg-#FAFAFA">
          <span class="inline-block bg-primary-color w-2px h-8 mr-3"></span>
          <span class="text-#333333 py-5px">疫苗接种情况</span>
        </div>
        <BasicTable @register="registerTable2" />
      </div>
      <div class="flex-1 flex flex-col gap-4">
        <div class="flex items-center gap-2 bg-#FAFAFA">
          <span class="inline-block bg-primary-color w-2px h-8 mr-3"></span>
          <span class="text-#333333 py-5px">随访情况</span>
        </div>
        <BasicTable @register="registerTable3">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
              <TableAction :actions="createActions(record, column)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <DrugDrawer @register="registerDrawer" />
  </div>
</template>

<style lang="less" scoped>
  .public-health-table {
    :deep {
      .vben-basic-table .ant-table-wrapper {
        padding: 0;
      }

      .vben-basic-table-form-container {
        padding: 0;
      }

      .vben-basic-table-form-container .ant-form,
      .vben-basic-table .ant-table-wrapper {
        border-radius: 4px;
      }
    }
  }
</style>
