<script setup lang="ts">
  import { ref } from 'vue';
  import { Button } from '@ft/internal/components/Button';
  import { BasicDrawer, useDrawerInner } from '@ft/internal/components/Drawer';
  import { getFollowupSituationDetail } from '/@/api/public-health';
  import { useRequest } from '@ft/request';
  const emit = defineEmits(['register', 'close']);
  const title = ref('');
  const [register, { closeDrawer }] = useDrawerInner((data) => {
    console.log(9, data);
    title.value = data.infectionDiseaseName;
    run(data.record.id);
  });
  const { data: detailsInfo, run } = useRequest(getFollowupSituationDetail, {
    manual: true,
  });
  function _closeDrawer() {
    emit('close');
    closeDrawer();
  }

  function onClose() {
    _closeDrawer();
  }

  function onOk() {
    _closeDrawer();
  }

  function onSubmit() {
    _closeDrawer();
  }
</script>

<template>
  <BasicDrawer
    :mask="false"
    v-bind="$attrs"
    :maskClosable="false"
    width="857px"
    @register="register"
    title="随访详情"
    destroyOnClose
    showFooter
    @ok="onOk"
    @close="onClose"
    class="public-health-details"
    zIndex="1001"
  >
    <div class="power-data">
      <div class="text-#333 text-24px text-center mb-4">{{ title }}患者随访服务记录表</div>
      <table class="table2 table-all" cellpadding="0" cellspacing="0">
        <tr align="center">
          <td class="thBack tdLine" colspan="2">随访时间</td>
          <td class="tdLine">{{ detailsInfo?.followUpDate }}</td>
        </tr>
        <tr align="center">
          <td class="thBack tdLine" colspan="2">治疗月序</td>
          <td class="tdLine"></td>
        </tr>
        <tr align="center">
          <td class="thBack tdLine" colspan="2">督导人员</td>
          <td class="tdLine"></td>
        </tr>
        <tr align="center">
          <td class="thBack tdLine" colspan="2">随访方式</td>
          <td class="tdLine">{{ detailsInfo?.followUpMethod }}</td>
        </tr>
        <tr align="center">
          <td class="thBack tdLine" colspan="2">症状及体征</td>
          <td class="tdLine">{{ detailsInfo?.physSigns }}</td>
        </tr>
        <tr align="center">
          <td class="thBack tdLine" rowspan="3"> 生活方式指导 </td>
        </tr>
        <tr align="center">
          <td class="thBack tdLine">吸烟</td>
          <td class="tdLine">{{ detailsInfo?.smokingNum }}</td>
        </tr>
        <tr align="center">
          <td class="thBack tdLine">饮酒</td>
          <td class="tdLine">{{ detailsInfo?.drinkNum }}</td>
        </tr>
        <tr align="center">
          <td class="thBack tdLine" rowspan="5">
            <div class="">用药</div>
          </td>
        </tr>
        <tr align="center">
          <td class="thBack tdLine">化疗方案</td>
          <td class="tdLine">{{ detailsInfo?.chemoPlan }}</td>
        </tr>
        <tr align="center">
          <td class="thBack tdLine">用法</td>
          <td class="tdLine"></td>
        </tr>
        <tr align="center">
          <td class="thBack tdLine">药品剂型</td>
          <td class="tdLine"></td>
        </tr>
        <tr align="center">
          <td class="thBack tdLine">漏服药次数</td>
          <td class="tdLine">{{ detailsInfo?.tbMissedDosage }}</td>
        </tr>
        <tr align="center">
          <td class="thBack tdLine" colspan="2">药物不良反应</td>
          <td class="tdLine">{{ detailsInfo?.tbAdverseEffects }}</td>
        </tr>
        <tr align="center">
          <td class="thBack tdLine" colspan="2">并发症或合并症</td>
          <td class="tdLine">{{ detailsInfo?.tbComorbidityName }}</td>
        </tr>
        <tr align="center">
          <td class="thBack tdLine" rowspan="4">
            <div class="">转诊</div>
          </td>
        </tr>
        <tr align="center">
          <td class="thBack tdLine">科别</td>
          <td class="tdLine"></td>
        </tr>
        <tr align="center">
          <td class="thBack tdLine">原因</td>
          <td class="tdLine"></td>
        </tr>
        <tr align="center">
          <td class="thBack tdLine">2周内随访，随访结果</td>
          <td class="tdLine"></td>
        </tr>
        <tr align="center">
          <td class="thBack tdLine" colspan="2">处理意见</td>
          <td class="tdLine">1</td>
        </tr>
        <tr align="center">
          <td class="thBack tdLine" colspan="2">下次随访时间</td>
          <td class="tdLine"></td>
        </tr>
        <tr align="center">
          <td class="thBack tdLine" colspan="2">随访医生</td>
          <td class="tdLine">{{ detailsInfo?.followUpDoc }}</td>
        </tr>
      </table>
    </div>

    <template #footer>
      <Button style="margin-right: 8px" @click="onClose">取消</Button>
      <Button type="primary" @click="onSubmit">确定</Button>
    </template>
  </BasicDrawer>
</template>

<style lang="less" scoped>
  .power-data {
    width: 100%;

    .table-all {
      color: black;
      border: 1px solid #ebeef5;
      border-radius: 2px 0 0;
      width: 100%;

      .thBack {
        background: #f5f7fa;
        border: 1px solid #ebeef5;
        text-align: center;
      }

      .tdLine {
        width: 122px;
      }

      .tdBtn {
        width: 99px;
      }

      tr {
        text-align: left;
        font-size: 14px;
        color: #666;
      }

      th,
      td {
        border: 1px solid #ebeef5;
        border-radius: 2px 0 0;
        padding: 9px 12px;
        text-align: left;
        font-size: 14px;
        color: #666;
      }
    }

    .table1 {
      margin-bottom: 10px;
    }

    .table3 {
      th,
      td {
        border-radius: 2px 0 0;
        padding: 4px;
        text-align: center;
        font-size: 14px;
        color: #666;
      }
    }
  }

  .vertical-text {
    writing-mode: vertical-rl;
    text-orientation: upright;
    text-align: center;
    padding: 0 42px;
  }
</style>
