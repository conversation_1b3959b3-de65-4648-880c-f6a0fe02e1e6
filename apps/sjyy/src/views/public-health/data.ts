import type { FormSchema } from '@ft/internal/components/Form';
import type { BasicColumn } from '@ft/internal/components/Table';
export const columns: BasicColumn[] = [
  {
    title: '随访日期',
    dataIndex: 'index1',
    width: 100,
    fixed: 'left',
    customCell: (_, index: any) => {
      if (index === 0) return { colSpan: 2 };
      if (index === 1) return { colSpan: 2 };
      if (index === 2) return { rowSpan: 6 };
      if (index > 2 && index <= 7) return { rowSpan: 0 };
      if (index === 8) return { rowSpan: 7 };
      if (index > 8 && index <= 14) return { rowSpan: 0 };
      if (index === 15) return { colSpan: 2 };
      if (index === 16) return { colSpan: 2 };
      if (index === 17) return { colSpan: 2 };
      if (index === 18) return { rowSpan: 6 };
      if (index > 18 && index < 24) return { rowSpan: 0 };
      if (index === 24) return { rowSpan: 2 };
      if (index > 24 && index < 26) return { rowSpan: 0 };
      if (index === 26) return { colSpan: 2 };
      if (index === 27) return { colSpan: 2 };
      if (index === 28) return { colSpan: 2 };
      if (index === 29) return { colSpan: 2 };
      return { rowSpan: 1 };
    },
  },
  {
    title: '随访方式',
    dataIndex: 'index2',
    width: 100,
    fixed: 'left',
  },
  // {
  //   title: '查体',
  //   dataIndex: 'index3',
  //   width: 100,
  //   fixed: 'left',
  // },
];
/**
 * @description: form schemas
 * 患者编号
 * 患者姓名
 * 责任医师
 * 管辖机构
 * 确诊时间
 * 确诊机构
 * 就诊机构
 * 并发症
 * 治疗状态
 */
export const formSchemas: FormSchema[] = [
  {
    field: 'patientId',
    label: '患者编号',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'name',
    label: '患者姓名',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'responsiblePhysician',
    label: '责任医师',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'administerOrgName',
    label: '管辖机构',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'diagnosisTime',
    label: '确诊时间',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'diagnosisOrgName',
    label: '确诊机构',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'visitOrgName',
    label: '就诊机构',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'comorbidities',
    label: '并发症',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'treatmentStatusDesc',
    label: '治疗状态',
    component: 'Input',
    colProps: { span: 6 },
  },
];

/**
随访日期
应服药次数（次）
实际服药次数（次）
漏服药次数（次）
药物不良反应
 */
export const columns1: BasicColumn[] = [
  {
    title: '随访日期',
    dataIndex: 'followUpDate',
    width: 100,
  },
  {
    title: '应服药次数（次）',
    dataIndex: 'tbDosage',
    width: 100,
  },
  {
    title: '实际服药次数（次）',
    dataIndex: 'tbActualDosage',
    width: 100,
  },
  {
    title: '漏服药次数（次）',
    dataIndex: 'tbMissedDosage',
    width: 100,
  },
  {
    title: '药物不良反应',
    dataIndex: 'tbAdverseEffects',
    width: 100,
  },
];

/**
疫苗名称
是否接种
 */
export const columns2: BasicColumn[] = [
  {
    title: '疫苗名称',
    dataIndex: 'vaccinationName',
    width: 100,
  },
  {
    title: '是否接种',
    dataIndex: 'isVaccinated',
    width: 100,
  },
];

/**
随访日期
随访方式
随访类型
体征描述
是否单独居室名称
通风情况描述
密切接触者有无相同症状名称
本次随访结果
随访医务人员
 */
export const columns3: BasicColumn[] = [
  {
    title: '随访日期',
    dataIndex: 'followUpDate',
    width: 100,
  },
  {
    title: '随访方式',
    dataIndex: 'followUpMethod',
    width: 100,
  },
  {
    title: '随访类型',
    dataIndex: 'followUpType',
    width: 100,
  },
  {
    title: '体征描述',
    dataIndex: 'physSigns',
    width: 100,
  },
  {
    title: '是否单独居室名称',
    dataIndex: 'isAloneRoom',
    width: 100,
  },
  {
    title: '通风情况描述',
    dataIndex: 'ventilation',
    width: 100,
  },
  {
    title: '密切接触者有无相同症状名称',
    dataIndex: 'contactSymptoms',
    width: 100,
  },
  {
    title: '本次随访结果',
    dataIndex: 'followUpResult',
    width: 100,
  },
  {
    title: '随访医务人员',
    dataIndex: 'followUpDoc',
    width: 100,
  },
];
