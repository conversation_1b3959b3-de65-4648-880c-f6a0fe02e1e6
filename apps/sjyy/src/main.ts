import { LAYOUT } from '@ft/internal/router/constant';
import './design/global.less';
import { basicRoutes } from '@ft/internal/router/routes';
import { PageEnum } from '@ft/internal/enums/pageEnum';

basicRoutes.push({
  path: '',
  name: 'sjyySelectParent',
  meta: {
    title: '选择患者',
  },
  component: LAYOUT,
  redirect: PageEnum.BASE_HOME,
  children: [
    {
      path: 'sjyy-select',
      name: 'sjyySelect',
      meta: {
        title: '选择患者',
        hideTab: true,
      },
      component: () => import('./views/select/index.vue'),
    },
  ],
});
window['DYNAMIC_VIEW_MODULES'] = Object.fromEntries(
  Object.entries(import.meta.glob('./views/**/*.{vue,tsx}')).map(([k, v]) => [
    k.replace('./views', '/sjyy'),
    v,
  ]),
);
Promise.resolve().then(() => {
  import('@ft/internal/bootstrap');
});

Promise.resolve().then(() => import('@ft/internal/bootstrap'));
