body {
  background-image: url('/@/assets/svg/body-bg-image.svg');
}

:root {
  // var
  --primary-color: @primary-color;
  --main-text-color: @text-color;
  --secondary-text-color: @text-color-secondary;
  --info-text-color: @text-color-third;
  --active-bg-color: @active-bg-color;

  background: linear-gradient(270deg, #eef4fd 0%, #eef2fe 100%) !important;

  .active-bg-color {
    background-color: var(--active-bg-color);
  }
}

.basic-title {
  padding-left: 8px;
  position: relative;
  // 左侧边框
  &::before {
    content: '';
    display: block;
    width: 4px;
    height: 16px;
    background: var(--primary-color);
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}
