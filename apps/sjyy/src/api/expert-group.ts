/**
 * /expertGroup/list
 */

import { defHttp } from '@ft/request';

export interface IExpertGroup {
  id: string;
  groupName: string;
  groupDescription: string;
  groupCategory: string;
  groupCategoryName: string;
  tree: PositionItem[];
}

/**
 * @description 获取专家分组列表
 */
export function getExpertGroupList(groupCategory: string) {
  return defHttp.get<IExpertGroup[]>({
    url: `/expertGroup/list?groupCategory=${groupCategory}`,
  });
}

/**
 * @description 保存专家分组
 * /expertGroup/save
 */
export function saveExpertGroup(data: Omit<IExpertGroup, 'id'>) {
  return defHttp.post({
    url: '/expertGroup/save',
    data,
  });
}

/**
 * @description 修改专家分组
 * /expertGroup/update
 */
export function updateExpertGroup(data: IExpertGroup) {
  return defHttp.post({
    url: '/expertGroup/update',
    data,
  });
}

/**
 * @description 删除专家分组
 * /expertGroup/remove
 */

export function removeExpertGroup(id: string) {
  return defHttp.post(
    {
      url: '/expertGroup/remove',
      params: {
        id,
      },
    },
    { joinParamsToUrl: true },
  );
}

/**
 * @description 获取专组成员列表
 * /expertGroup/existExpertList
 */
export function getExistExpertList(params: { expertGroupId: string }) {
  return defHttp.get({
    url: '/expertGroup/existExpertList',
    params,
  });
}

/**
 * @description 批量添加专家
 * /expertGroup/batchUpdate
 */
export function expertGroupBatchUpdate(data: { expertGroupId: string; expertIdList: string[] }) {
  return defHttp.post({
    url: '/expertGroup/batchUpdate',
    data,
  });
}

export type PositionItem = {
  id: string;
  name: string;
  type: '1' | '2';
  isDelete: number;
  createBy: string;
  lastModifiedBy: string;
  createTime: string;
  updateTime: string;
  status: number;
  childList: PositionItem[];
};

/**
 * 查询导诊身体部位列表（树结构）
 * /bodyPosition/queryList
 */
export function getBodyPositionList(data) {
  return defHttp.post<PositionItem[]>(
    {
      url: '/infection-zhyl/bodyPosition/queryList',
      data,
    },
    {
      joinPrefix: false,
    },
  );
}

/**
 * 根据专家分组id返回部位树
 * /expertGroup/getTreeById
 */
export function getPositionTreeById(params) {
  return defHttp.get<PositionItem[]>({
    url: '/expertGroup/getTreeById',
    params,
  });
}
