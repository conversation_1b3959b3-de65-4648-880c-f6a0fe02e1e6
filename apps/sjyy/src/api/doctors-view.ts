import { defHttp } from '@ft/request';

const defHttpPost = <T>(old) =>
  defHttp.post<T>(
    {
      ...old,
      url: `/infection-sjyy${old.url}`,
    },
    {
      joinPrefix: false,
    },
  );

const defHttpGet = <T>(old, oldOption = {}) =>
  defHttp.get<T>(
    {
      ...old,
      url: `/infection-sjyy${old.url}`,
    },
    {
      ...oldOption,
      joinPrefix: false,
    },
  );

export interface VisitRecordItem {
  admitHospitalTime: string;
  diagnoseDiseaseName: string;
  diagnosisDept: string;
  diagnosisDoctor: string;
  hospitalOrg: string;
  hospitalOrgCode: string;
  id: string;
  inpatientNo: string;
  leaveTime: string;
  outpatientNo: string;
  patientId: string;
  pkPatientInfo: string;
  visitDate: string;
  visitTimes: string;
  visitType: string;
  visitTypeCode: string;
}
export interface VisitRecordParams {
  deptCode?: string;
  diagnoseName?: string;
  idCardNo?: string;
  orderBy?: string;
  orgCode?: string;
  pageNum?: number;
  pageSize?: number;
  visitTypeCode?: string;
  year?: string;
}

/**
 * @description 就诊视图-获取就诊记录
 */
export function getVisitRecord(data?: VisitRecordParams) {
  return defHttpPost<VisitRecordItem[]>({
    url: '/lifeCycleArchives/getVisitRecord',
    data,
  });
}
export interface VisitCountItem {
  emergencyCount: number;
  inpatientCount: number;
  outpatientCount: number;
  totalCount: number;
}
/**
 * @description 就诊视图-获取就诊数
 */
export function getVisitCount(data?: VisitRecordParams) {
  return defHttpPost<VisitCountItem>({
    url: '/lifeCycleArchives/getVisitCount',
    data,
  });
}
export interface MedicalRecordItem {
  diagnoseDiseaseName: string;
  diagnosisDept: string;
  diagnosisDoctor: string;
  hospitalOrg: string;
  pkVisitInfo: string;
  recordId: string;
  visitDate: string;
}

/**
 * @description 就诊视图-获取门诊病历
 */
export function medicalRecord(data?: any) {
  return defHttpPost<MedicalRecordItem[]>({
    url: '/lifeCycleArchives/medicalRecord',
    data,
  });
}
export interface PrescriptionItem {
  chineseMedicine: ChineseMedicine;
  diagnosisDoctor: string;
  medicinelist: Medicinelist[];
  prescriptionDate: string;
  prescriptionType: string;
}
export interface Medicinelist {
  prescriptionId: string;
  prescriptionTypeCode: string;
  prescriptionType: string;
  medicOrdersItem: string;
  yl: string;
  specifications: string;
  tcmUsageMethod: string;
  drugUseFrequency: string;
  prescriptionEffectiveDays: number;
  totalDose: string;
  unitPrice: any;
  amount: any;
  drugDosageUnit: string;
}

export interface ChineseMedicine {
  doctor: string;
  dosage: string;
  tcmUsageMethod: string;
  yl: string;
}
/**
 * @description 就诊视图-获取门诊病历
 */
export function prescription(data?: any) {
  return defHttpPost<PrescriptionItem[]>({
    url: '/lifeCycleArchives/prescriptionList',
    data,
  });
}

/**
 * @description 就诊视图-获取门诊病历
 */
export function electronicCaseHistoryDocDetail(params: { pkElectronicCaseHistoryDoc: string }) {
  return defHttpGet(
    {
      url: '/electronicCaseHistoryDoc/detail',
      params,
      responseType: 'text',
    },
    {
      isReturnNativeResponse: true,
    },
  );
}
export interface ExceptionListItem {
  labClassify: string;
  labResult: string;
  labResultCode: string;
  labTestIndexes: string;
}
/**
 * @description 就诊视图-获取检验报告异常
 */
export function getExceptionList(data?: any) {
  return defHttpPost<ExceptionListItem[]>({
    url: '/labTestResultDetail/exceptionList',
    data,
  });
}

export interface HistoryDiagnosisItem {
  pkExamineReport: string;
  diagnoseDate: string;
  diagnoseName: string;
}
/**
 * @description 就诊视图-获取检验指标历史诊断
 */
export function getHistoryDiagnosis(data?: any) {
  return defHttpPost<HistoryDiagnosisItem[]>({
    url: '/examineResultReport/getHistoryDiagnosis',
    data,
  });
}
export interface TrendChartItem {
  labQuantitativeResult: string;
  labReportDate: string;
  labTestIndexes: string;
  referenceValueLower: string;
  referenceValueToplimit: string;
}
/**
 * @description 就诊视图-获取检验报告指标趋势图
 */
export function getTrendChart(data?: any) {
  return defHttpPost<TrendChartItem[]>({
    url: '/labTestResultDetail/trendChart',
    data,
  });
}
export interface MedicalOrdersItem {
  days: string;
  diagnoseName: string;
  doctorName: string;
  drugName: string;
  drugSpec: string;
  drugUsage: string;
  drugUseDrequency: string;
  drugUsed: string;
  hospitalOrg: string;
  medicOrdersExcuteDate: string;
  medicOrdersItem: string;
  medicOrdersItemCode: string;
  medicOrdersItemType: string;
  medicOrdersStartDate: string;
  medicOrdersStopDate: string;
  medicOrdersType: string;
  medicOrdersTypeCode: string;
  nurseName: string;
  price: string;
  totalPrice: string;
  totalSize: string;
  visitDept: string;
}

/**
 * @description 就诊视图-获取住院医嘱
 */
export function medicalOrders(data?: any) {
  return defHttpPost<MedicalOrdersItem[]>({
    url: '/lifeCycleArchives/medicalOrders',
    data,
  });
}
export interface InHospitalRecordItem {
  admitHospitalRecord: string;
  admitHospitalRecordTime: string;
  admitHospitalRecordUser: string;
  deptName: string;
  diagnosisInfo: string;
  hospitalOrg: string;
  recordId: string;
}
/**
 * @description 就诊视图-查询住院病历
 */
export function inHospitalRecord(data?: any) {
  return defHttpPost<InHospitalRecordItem[]>({
    url: '/lifeCycleArchives/inHospitalRecord',
    data,
  });
}
export interface ExamineResultReportListItem {
  checkDate: string;
  checkDept: string;
  checkDoc: string;
  checkFind: string;
  checkItem: string;
  checkOrg: string;
  checkView: string;
  id: string;
  reportDate: string;
  reportName: string;
}

/**
 * @description 就诊视图-获取检查项目
 */
export function examineResultReportList(data?: any) {
  return defHttpPost<ExamineResultReportListItem[]>({
    url: '/lifeCycleArchives/getCheckReport',
    data,
  });
}

/**
 * @description 就诊视图-获取检查报告异常
 */
export function examineResultReportExceptionList(data?: any) {
  return defHttpPost({
    url: '/examineResultReport/exceptionList',
    data,
  });
}
export interface ResultReportDetailInfo {
  examineReportNo: string;
  examineReportDate: string;
  examineReportItem: string;
  examineReportDept: string;
  examineReportDoctor: string;
  reptResultObjectiveOpinion: string;
  reptResultSubjectiveOpinion: string;
  examineReportNote: any;
  examineReportContentPdf: any;
  systemCode: any;
  examineReportOrg: string;
}
/**
 * @description 就诊视图-获取检查项目检查结果
 */
export function examineResultReportDetail(params: { pkExamineReport: string }) {
  return defHttpGet<ResultReportDetailInfo>({
    url: '/examineResultReport/detail',
    params,
  });
}
export interface LabTestResultReportListItem {
  pkExamineReport: string;
  labReportDate: string;
  labReportItem: string;
  pkLabTestResultReport: string;
  pkVisitInfo: string;
}
/**
 * @description 就诊视图-获取检验项目列表
 */
export function labTestResultReportList(data?: any) {
  return defHttpPost<LabTestResultReportListItem[]>({
    url: '/labTestResultReport/list',
    data,
  });
}
export interface LabTestResultReportDetailItem {
  labReportDate: string;
  labReportDept: string;
  labReportDoctor: string;
  labReportItem: string;
  labReportNo: string;
  labReportOrg: string;
}

/**
 * @description 就诊视图-获取检验项目明细
 */
export function labTestResultReportDetail(params: { pkLabTestResultReport: string }) {
  return defHttpGet<LabTestResultReportDetailItem>({
    url: '/labTestResultReport/detail',
    params,
  });
}

export interface LabTestResultDetailListItem {
  labReportDate: string;
  labReportItem: string;
  pkLabTestResultReport: string;
  pkVisitInfo: string;
}

/**
 * @description 就诊视图-获取检验结果明细列表
 */
export function labTestResultDetailList(params: {
  pkLabTestResultReport: string;
  labReportNo: string;
}) {
  return defHttpGet<LabTestResultDetailListItem[]>({
    url: '/labTestResultDetail/list',
    params,
  });
}
export interface labGetHistoryDiagnosisItem {
  pkLabTestResultReport: string;
  labReportNo: string;
  diagnoseDate: string;
  diagnoseName: string;
}

/**
 * @description 就诊视图-获取检查报告历史诊断
 */
export function labGetHistoryDiagnosis(data: {
  pkLabTestResultReport: string;
  idCardNo: string;
  labTestIndexes: string;
}) {
  return defHttpPost<labGetHistoryDiagnosisItem[]>({
    url: '/labTestResultDetail/getHistoryDiagnosis',
    data,
  });
}

/**
 * @description  辅助诊断-查看详情
 */
export const getAssistedDiagnosisInfo = (id: string) => {
  return defHttp.get(
    {
      url: `/infection-jzgf/assistedDiagnosisInfo/detail/${id}`,
    },
    { joinPrefix: false },
  );
};

/**
 * @description  辅助诊断-新增
 */
export const addAssistedDiagnosis = (data: any) => {
  return defHttp.post(
    { url: '/infection-jzgf/assistedDiagnosisInfo/save', data },
    { joinPrefix: false },
  );
};

/**
 * @description  辅助诊断-更新
 */
export const updateAssistedDiagnosis = (data: any) => {
  return defHttp.post(
    { url: '/infection-jzgf/assistedDiagnosisInfo/update', data },
    { joinPrefix: false },
  );
};
export interface IStringifyOptions {
  admitHospitalRecord: '';
  admitHospitalRecordTime: '';
  admitHospitalRecordUser: '';
  deptName: '';
  diagnosisInfo: '';
  hospitalOrg: '';
  id: '';
  recordId: '';
}

/**
 * @description  生命周期档案服务-手术麻醉
 */
export const surgicalAnesthesia = (data: any) => {
  return defHttp.post<IStringifyOptions[]>({ url: '/lifeCycleArchives/surgicalAnesthesia', data });
};

/**
 * @description  生命周期档案服务-指标趋势图数据
 */
export interface IIndicatorTrendData {
  labReportDate: string;
  labResult: string;
  lowerLimit: string;
  upperLimit: string;
}
export const getIndicatorTrendData = (data: { indexCode?: string; reportNo: string }) => {
  return defHttpPost<IIndicatorTrendData[]>({
    url: '/lifeCycleArchives/getIndicatorTrendData',
    data,
  });
};

/**
 * @description  生命周期档案服务-获取检验异常数据
 */
export const getInspectionException = (outInPatientNo: string) => {
  return defHttp.post({
    url: `/lifeCycleArchives/getInspectionException/${outInPatientNo}`,
  });
};

export interface IInspectionExceptionItem {
  inspectionDept: string;
  inspectionDoc: string;
  inspectionItemName: string;
  inspectionOrg: string;
  inspectionTime: string;
  reportNo: string;
}

/**
 * @description  生命周期档案服务-根据门急诊-住院号获取检验单列表
 */
export const getInspectionReport = (outInPatientNo: string) => {
  return defHttpPost<IInspectionExceptionItem[]>({
    url: `/lifeCycleArchives/getInspectionReport/${outInPatientNo}`,
  });
};
export interface IInspectionReportDetail {
  bacterialReportDetailVOS: IBacterialReportDetailVOS[];
  regularReportDetailVOS: IRegularReportDetailVOS[];
}
export interface IRegularReportDetailVOS {
  exceptionResult: string;
  id: string;
  indexDesc: string;
  indexName: string;
  referenceValue: string;
  result: string;
  unit: string;
}
export interface IBacterialReportDetailVOS {
  bacterialName: string;
  colonyCount: string;
  colonyCountUnit: string;
  cultureCondition: string;
  cultureTime: string;
  detectionResult: string;
  id: string;
  medium: string;
}

/**
 * @description  生命周期档案服务-根据报告单号获取检验列表详情
 */
export const getInspectionReportDetail = (reportNo: string) => {
  return defHttpPost<IInspectionReportDetail>({
    url: `/lifeCycleArchives/getInspectionReportDetail/${reportNo}`,
  });
};

/**
 * @description  生命周期档案服务-指标趋势图按钮状态 true显示
 */
export const indicatorTrendButton = (reportNo: string) => {
  return defHttpPost({
    url: `/lifeCycleArchives/indicatorTrendButton/${reportNo}`,
  });
};

/**
 * @description  生命周期档案服务-指标趋势图按钮状态 true显示
 */
export const getInspectionExceptionByReportNo = (reportNo: string) => {
  return defHttpPost({
    url: `/lifeCycleArchives/getInspectionExceptionByReportNo/${reportNo}`,
  });
};
export interface INursingDocuments {
  content: string;
  ftype: string;
  pkGennurInfo: string;
  recordTime: string;
}

/**
 * @description  生命周期档案服务-获取护理文书
 */
export const getNursingDocuments = (inpatientNo: string) => {
  return defHttp.post<INursingDocuments[]>(
    {
      url: `/infection-sjyy/lifeCycleArchives/getNursingDocuments/${inpatientNo}`,
    },
    { joinPrefix: false },
  );
};

/**
 * @description 辅助诊断-根据诊断编码查看详情
 * /medicalKnowledge/getByIcdCode/{icdCode}
 * */
export const getMedicalKnowledgeByIcdCode = (icdCode?: string) => {
  return defHttp.get(
    {
      url: `/infection-jzgf/medicalKnowledge/getByIcdCode/${icdCode}`,
    },
    { joinPrefix: false },
  );
};
