import { ContentTypeEnum } from '@ft/internal/enums/httpEnum';
import { defHttp } from '@ft/request';
import type { RcFile } from 'ant-design-vue/lib/vc-upload/interface';
import { useGlobSetting } from '@ft/internal/hooks/setting';
interface IExpertQueryParams {
  deptId: string;
  expertGroupId: string;
  expertLevel: string;
  expertName: string;
  jobTitleCode: string;
  orderBy: string;
  orgId: string;
  servicesRange: string;
}

/**
 * @description 获取专家分页
 * /expert/queryPage
 */
export function getExpertPage(data?: Partial<IExpertQueryParams>) {
  return defHttp.post({
    url: '/expert/queryPage',
    data,
  });
}

/**
 * @description 获取专家列表
 * /expert/queryList
 */

export function getExpertList(data?: Partial<IExpertQueryParams>) {
  return defHttp.post({
    url: '/expert/queryList',
    data,
  });
}

export interface IExpert {
  /**
   * 专家所属科室id
   */
  deptId: string;
  /**
   * 专家所属科室名称
   */
  deptName: string;
  /**
   * 专家年龄
   */
  expertAge: number;
  /**
   * 专家所属分组id
   */
  expertGroupId: string;
  /**
   * 专家所属分组名称
   */
  expertGroupName: string;
  /**
   * 专家级别
   */
  expertLevel: string;
  /**
   * 专家级别名称
   */
  expertLevelName: string;
  /**
   * 专家姓名
   */
  expertName: string;
  /**
   * 专家性别
   */
  expertSex: number;
  /**
   * 专家性别名称
   */
  expertSexName: string;
  id: string;
  /**
   * 专家职称代码
   */
  jobTitleCode: string;
  /**
   * 专家职称名称
   */
  jobTitleName: string;
  /**
   * 	主要成就
   */
  majorAchievement: string;
  /**
   * 民族
   */
  nation: string;
  /**
   * 机构
   */
  orgId: string;
  /**
   * 机构名称
   */
  orgName: string;
  /**
   * 服务领域
   */
  servicesRange: string;
  /**
   * 专家登记照
   * */
  expertImg: string;
}

/**
 * @description 新增专家
 * /expert/save
 */
export function saveExpert(data: Omit<IExpert, 'id'>) {
  return defHttp.post({
    url: '/expert/save',
    data,
  });
}

/**
 * @description 修改专家
 * /expert/update
 */
export function updateExpert(data: IExpert) {
  return defHttp.post({
    url: '/expert/update',
    data,
  });
}

/**
 * @description 删除专家
 * /expert/delete
 */
export function deleteExpert(id: string) {
  return defHttp.post({
    url: '/expert/delete?id=' + id,
  });
}

/**
 * @description 获取专家详情
 * /expert/detail
 */

export function getExpertDetail(id: string) {
  return defHttp.get({
    url: '/expert/detail?id=' + id,
  });
}

/**
 * /expert/batchImport
 */

export function importExpert(data: { file: RcFile }) {
  const formData = new FormData();
  formData.append('file', data.file);
  return defHttp.post({
    url: '/expert/batchImport',
    headers: {
      'Content-Type': ContentTypeEnum.FORM_DATA,
    },
    data: formData,
  });
}

/**
 * @description 导出专家
 * /expert/export
 */
export function exportExpert(data?: Partial<IExpertQueryParams>) {
  return defHttp.post(
    {
      url: '/expert/export',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );
}

/**
 * @description 下载导入模板
 * /expert/downloadTemplate
 */
export function downloadExpertTemplate() {
  return defHttp.post(
    {
      url: '/expert/downloadTemplate',
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
}
const { apiUrl, urlPrefix } = useGlobSetting();
export const UPLOAD_URL = `${apiUrl}${urlPrefix}/expert/uploadImg`;
