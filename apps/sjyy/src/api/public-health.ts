import { defHttp } from '@ft/request';

/**
 * @description 获取患者疫苗接种信息
 * /vaccination/getPatientVaccinationInfo/{idCardNo}/{type}
 */
export function getPatientVaccinationInfo(idCardNo: string, type: string) {
  return defHttp.get(
    {
      url: `/infection-jcyj/vaccination/getPatientVaccinationInfo/${idCardNo}/${type}`,
    },
    {
      joinPrefix: false,
    },
  );
}

/**
 * @description 获取服药情况
 * /lifeCycleArchives/getMedicationSituation/{idCardNo}
 * */
export function getMedicationSituation(idCardNo: string) {
  return defHttp.post({
    url: `/lifeCycleArchives/getMedicationSituation/${idCardNo}`,
  });
}

/**
 * @description 获取随访情况
 * /lifeCycleArchives/getFollowupSituation/{idCardNo}
 * */
export function getFollowupSituation(idCardNo: string) {
  return defHttp.post({
    url: `/lifeCycleArchives/getFollowupSituation/${idCardNo}`,
  });
}

/**
 * @description 公共卫生患者信息
 * /patient/getInfectionPatientBasicInfo/{id}
 * */
export function getInfectionPatientBasicInfo(id: string) {
  return defHttp.post(
    {
      url: `/infection-ywxt/patient/getInfectionPatientBasicInfo/${id}`,
    },
    {
      joinPrefix: false,
    },
  );
}

/**
 * @description 获取随访情况详情
 * /lifeCycleArchives/getFollowupSituationDetail/{id}
 * */
export function getFollowupSituationDetail(id: string) {
  return defHttp.post({
    url: `/lifeCycleArchives/getFollowupSituationDetail/${id}`,
  });
}
