import { queryOrganizationList } from '@ft/internal/api';
import { defHttp } from '@ft/request';

interface BaseStatParams {
  /** 开始日期 */
  startDate?: string;
  /** 结束日期 */
  endDate?: string;
}

interface QuerySpecificDiseaseStatParams extends BaseStatParams {
  /** 辖区编码 */
  areaCode: string;
}

interface QuerySpecificDiseaseStatResponse {
  areaPatientDistribution: {
    count: number;
    item: string;
  }[];
  /** 图表类型 1:病毒性肝炎 2:艾滋 3:结核 */
  diseaseType: 1 | 2 | 3;
  /** 排序 */
  sort: number;
}
/**
 * 各专病患者辖区分布统计
 * GET /patientProfileRecord/querySpecificDiseasePatientAreaStatistics
 */
export const querySpecificDiseaseStatistics = (params: QuerySpecificDiseaseStatParams) => {
  return defHttp.get<QuerySpecificDiseaseStatResponse[]>(
    {
      url: '/infection-ywxt/patientProfileRecord/querySpecificDiseasePatientAreaStatistics',
      params,
    },
    { joinTime: false, joinPrefix: false },
  );
};

interface QuerySpecificDiseaseOrgStatParams extends BaseStatParams {
  /** 医疗机构编码 */
  orgCode: string;
}

/**
 * 各专病患者就诊机构分布统计
 * GET /patientProfileRecord/querySpecificDiseasePatientOrgStatisticsDTO
 */
export const querySpecificDiseasePatientOrgStatistics = (
  params: QuerySpecificDiseaseOrgStatParams,
) => {
  return defHttp.get<QuerySpecificDiseaseStatResponse[]>(
    {
      url: '/infection-ywxt/patientProfileRecord/querySpecificDiseasePatientOrgStatisticsDTO',
      params,
    },
    { joinTime: false, joinPrefix: false },
  );
};

interface AreaResponse {
  code: string;
  id: string;
  name: string;
  pcode: string;
  pname: string;
  sortNumber: number;
}
/**
 * 各专病患者分布统计-区划下拉框
 * GET /patientProfileRecord/querySpecificDiseaseStatisticsAreaList
 */
export const querySpecificDiseaseStatisticsAreaList = () => {
  return defHttp.get<AreaResponse[]>(
    {
      url: '/infection-ywxt/patientProfileRecord/querySpecificDiseaseStatisticsAreaList',
    },
    { joinTime: false, joinPrefix: false },
  );
};

/**
 * 各专病患者分布统计-医疗机构下拉框
 * GET /patientProfileRecord/querySpecificDiseaseStatisticsOrgList
 */
export const querySpecificDiseaseStatisticsOrgList = (args: any) => {
  return queryOrganizationList({
    orgType: 1,
    ...args,
  });
};
