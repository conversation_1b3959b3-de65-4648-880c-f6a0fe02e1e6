import type { PatientInfItem } from '@ft/internal/utils/auth';
import { defHttp } from '@ft/request';

export interface PatientPageItem {
  id: string;
  idCardNo: string;
  name: string;
  patientId: string;
  sex: number;
  age: number;
  sexName: string;
  telephone: string;
}
export interface AllHistoryItem {
  allergyList: AllergyList[];
  bloodTransfusionList: BloodTransfusionList[];
  exposeList: ExposeList[];
  family: Family;
  infectiousDiseaseList: InfectiousDiseaseList[];
  operationList: OperationList[];
  personalList: PersonalList[];
}

export interface AllergyList {
  medicalHistoryName: string;
  medicalHistoryTime: string;
}

export interface BloodTransfusionList {
  medicalHistoryName: string;
  medicalHistoryTime: string;
}

export interface ExposeList {
  medicalHistoryName: string;
  medicalHistoryTime: string;
}

export interface Family {
  children: string;
  disabilitySituation: string;
  father: string;
  geneticHistory: string;
  mother: string;
  sibling: string;
}

export interface InfectiousDiseaseList {
  medicalHistoryName: string;
  medicalHistoryTime: string;
}

export interface OperationList {
  medicalHistoryName: string;
  medicalHistoryTime: string;
}

export interface PersonalList {
  medicalHistoryName: string;
  medicalHistoryTime: string;
}

/**
 * @description 患者信息-分页
 */
export interface IPatientPage {
  age: number;
  id: string;
  idCardNo: string;
  name: string;
  patientId: string;
  patientProfileRecordId: string;
  sex: number;
  sexName: string;
  telephone: string;
}
export function patientPage(data?: any) {
  return defHttp.post<IPatientPage[]>(
    {
      url: '/infection-ywxt/patient/pageList',
      data,
    },
    { joinPrefix: false },
  );
}
/**
 * @description 患者信息-获取所有既往史
 */
export function getAllHistory(data?: any) {
  return defHttp.post<AllHistoryItem[]>({
    url: '/patient/getAllHistory',
    data,
  });
}

export interface DiseaseMapping {
  [k: string]: AdditionalProperties1[];
}

export interface AdditionalProperties1 {
  diagnosisArea: string;
  diagnosisCode: string;
  diagnosisName: string;
  sexCode: string;
}
/**
 * @description 患者信息-获取诊断疾病图谱信息
 */
export function diseaseMappingDic(data?: any) {
  return defHttp.post<DiseaseMapping>({
    url: '/patient/diseaseMappingDic',
    data,
  });
}

/**
 * @description 患者信息-获取诊断疾病图谱信息
 */
export function getPatient(id: string) {
  return defHttp.post<PatientInfItem>(
    {
      url: `/infection-ywxt/patient/getPatientInfoOverview/${id}`,
    },
    { joinPrefix: false },
  );
}
