import { defHttp } from '@ft/request';

/**
 * @description 就诊视图-获取就诊记录
 */
export interface IPatientHistoryAndCount {
  diagnosisCountVO: DiagnosisCountVo;
  pastHistoryVO: PastHistoryVo;
}

export interface DiagnosisCountVo {
  emergencyCount: number;
  inpatientCount: number;
  outpatientCount: number;
  totalCount: number;
}

export interface PastHistoryVo {
  allergyList: IPastHistoryVoList[];
  bloodTransfusionList: IPastHistoryVoList[];
  exposeList: IPastHistoryVoList[];
  familyList: IPastHistoryVoList[];
  infectiousDiseaseList: IPastHistoryVoList[];
  operationList: IPastHistoryVoList[];
  personalList: IPastHistoryVoList[];
}

export interface IPastHistoryVoList {
  medicalHistoryName: string;
  medicalHistoryTime: string;
}

export function getPatientHistoryAndCount(idCard: string) {
  return defHttp.post<IPatientHistoryAndCount>({
    url: `/lifeCycleArchives/patientHistoryAndCount/${idCard}`,
  });
}
