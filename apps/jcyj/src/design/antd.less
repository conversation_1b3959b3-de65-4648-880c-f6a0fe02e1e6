body {
  .ant-pagination-jump-prev {
    background-color: white;
  }

  .ant-pagination-jump-next {
    background-color: white;
  }

  .ant-pagination-item-active a {
    color: white;
    background: linear-gradient(180deg, #1590ea 0%, rgb(21 144 234 / 31%) 100%);
    border-radius: 3px;

    &:hover {
      color: white;
    }
  }

  .ant-pagination-item {
    a {
      &:hover {
        color: white;
      }
    }
  }

  .ant-pagination-item-active {
    background: linear-gradient(180deg, #23befa 0%, rgb(255 255 255 / 0%) 100%);
    background-clip: content-box, border-box;
    box-shadow: -2px 0 4px 0 rgb(0 0 0 / 30%);
  }

  .ant-message-notice-content {
    box-shadow: 0 3px 6px -4px rgb(0 0 0 / 12%), 0 6px 16px 0 rgb(0 0 0 / 8%),
      0 9px 28px 8px rgb(0 0 0 / 5%);
  }

  .ant-table-tbody > tr > td {
    border-bottom: 1px solid #f0f0f0 !important;
  }
}
