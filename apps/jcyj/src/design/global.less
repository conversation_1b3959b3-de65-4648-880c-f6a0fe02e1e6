@import './antd.less';

:root {
  // var
  --primary-color: @primary-color;
  --main-text-color: @text-color;
  --secondary-text-color: @text-color-secondary;
  --info-text-color: @text-color-third;
  --active-bg-color: @active-bg-color;
  //
  --topbar-bg: #151515;
  --topbar-text-color: #fff;
  //
  --modal-table-th-color: #fff;
  --modal-table-base-text-color: #afd3ff;
  --modal-table-border-color: #0c2a4d;
  --modal-table-odd-bg-color: #071831;
}

#app {
  background: linear-gradient(270deg, #eef4fd 0%, #eef2fe 100%) !important;

  .active-bg-color {
    background-color: var(--active-bg-color);
  }
}

.view-modal-table {
  width: 100%;

  .modal-table-btn {
    color: #fff;
    background: linear-gradient(180deg, #1590ea 0%, rgb(21 144 234 / 31%) 100%);
    box-sizing: border-box;
    border: 1px solid;
    border-image: linear-gradient(180deg, #23befa 0%, rgb(255 255 255 / 0%) 100%) 1;
    border-radius: 4px;
    box-shadow: -2px 0 4px 0 rgb(0 0 0 / 30%);
  }

  thead {
    color: var(--modal-table-th-color);
    background-color: var(--modal-table-odd-bg-color);
  }

  tbody {
    color: var(--modal-table-base-text-color) !important;
  }

  th,
  td {
    height: 32px;
    padding: 0 8px;
  }

  tr:nth-child(even) {
    background-color: var(--modal-table-odd-bg-color);
  }
}

.jcyj-tooltip {
  background: url('/@/assets/images/data-view/tooltip/bg.png') no-repeat;
  padding: 14px 20px;
  background-size: 100% 100%;
}
