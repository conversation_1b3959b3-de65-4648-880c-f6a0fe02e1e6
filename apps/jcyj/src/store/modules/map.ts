import { defineStore } from 'pinia';

export type Point = {
  divisionName: string;
  peopleCount: number;
  peopleRate: string;
  tubCount: number;
  hepatitisCount: number;
  reportCount: number;
  heatAmount?: number;
};

type Position = 'center' | 'center-2' | 'center-3' | 'center-4' | 'center-5' | 'center-6';

interface MapState {
  points: Point[];
  cityModalKey: string;
  position: Position;
  onClickPointFn: (data: any) => void;
  onClickCityFn: (data: any) => void;
  onClickMapFn?: (data: any) => void;
}

export const useMapStore = defineStore({
  id: 'map-state',
  state: (): MapState => ({
    points: [],
    position: 'center',
    cityModalKey: '0',
    onClickPointFn: () => {},
    onClickCityFn: () => {},
    onClickMapFn: () => {},
  }),
  actions: {
    register({
      points,
      onClickPointFn,
      cityModalKey,
      onClickCityFn,
      position,
      onClickMapFn,
    }: {
      points?: Point[];
      onClickPointFn?: (data: any) => void;
      cityModalKey?: string;
      onClickCityFn?: (data: any) => void;
      position?: Position;
      onClickMapFn?: (data: any) => void;
    } = {}) {
      this.points = points || [];
      this.cityModalKey = cityModalKey || '0';
      this.onClickPointFn = onClickPointFn || (() => {});
      this.onClickCityFn = onClickCityFn || (() => {});
      this.onClickMapFn = onClickMapFn;
      this.position = position || 'center';
    },
  },
});
