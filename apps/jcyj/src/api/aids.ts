import { defHttp } from '@ft/request';

/**
 * 查询艾滋病监测中心地图与上下侧统计数据
 * /aidsMonitor/queryCenter
 */
export const queryCenter = () =>
  defHttp.get<Record<string, any>>({
    url: '/aidsMonitor/queryCenter',
  });

/**
 * 新增治疗基线CD4-VL统计——柱状图
 * /aidsMonitor/queryCD4VLCount
 */
export const queryCD4VLCount = () =>
  defHttp.get<Record<string, any>>({
    url: '/aidsMonitor/queryCD4VLCount',
  });

/**
 * 低病毒血症人数统计——柱状图
 * /aidsMonitor/queryDbdxzCount
 */
export const queryDbdxzCount = () =>
  defHttp.get<Record<string, any>>({
    url: '/aidsMonitor/queryDbdxzCount',
  });

/**
 * 艾滋病检测率监测统计——柱状图
 * /aidsMonitor/queryDetectionCount
 */
export const queryDetectionCount = () =>
  defHttp.get<Record<string, any>>({
    url: '/aidsMonitor/queryDetectionCount',
  });

/**
 * 艾滋病用药顺位统计——柱状图
 * /aidsMonitor/queryDrugCount
 */
export const queryDrugCount = () =>
  defHttp.get<Record<string, any>>({
    url: '/aidsMonitor/queryDrugCount',
  });

/**
 * 抗病毒失败人数统计——柱状图
 * /aidsMonitor/queryKbdsbCount
 */
export const queryKbdsbCount = () =>
  defHttp.get<Record<string, any>>({
    url: '/aidsMonitor/queryKbdsbCount',
  });

/**
 * 艾滋病病人管理统计——柱状图
 * /aidsMonitor/queryPatientCount
 */
export const queryPatientCount = (queryType) =>
  defHttp.get<Record<string, any>>({
    url: '/aidsMonitor/queryPatientCount?queryType=' + queryType,
  });

/**
 * 艾滋病病人辖区分布——下钻柱状图统计
 * /aidsMonitor/queryPatientLine
 */
export const queryPatientLine = (data) =>
  defHttp.post<Record<string, any>>({
    url: '/aidsMonitor/queryPatientLine',
    data,
  });

/**
 * 分页查询艾滋病患者列表——下钻
 * /aidsMonitor/queryPatientPage
 */
export const queryPatientPage = (data) =>
  defHttp.post<Record<string, any>>({
    url: '/aidsMonitor/queryPatientPage',
    data,
  });

/**
 * 艾滋病扩大筛查人数辖区分布——下钻柱状图统计
 * /aidsMonitor/queryScreeningLine
 */
export const queryScreeningLine = (data) =>
  defHttp.post<Record<string, any>>({
    url: '/aidsMonitor/queryScreeningLine',
    data,
  });

/**
 * 分页查询扩大筛查艾滋病病人列表——下钻
 * /aidsMonitor/queryScreeningPage
 */
export const queryScreeningPage = (data) =>
  defHttp.post<Record<string, any>>({
    url: '/aidsMonitor/queryScreeningPage',
    data,
  });

/**
 * 艾滋病确诊存活人数辖区分布——下钻柱状图统计
 * /aidsMonitor/querySurvivalLine
 */
export const querySurvivalLine = (data) =>
  defHttp.post<Record<string, any>>({
    url: '/aidsMonitor/querySurvivalLine',
    data,
  });

/**
 * 分页查询艾滋病确诊存活病人列表——下钻
 * /aidsMonitor/querySurvivalPage
 */
export const querySurvivalPage = (data) =>
  defHttp.post<Record<string, any>>({
    url: '/aidsMonitor/querySurvivalPage',
    data,
  });

/**
 * 艾滋病新确诊人数辖区分布——下钻柱状图统计
 * /aidsMonitor/queryNewDiagnosisLine
 * */
export const queryNewDiagnosisLine = (data) =>
  defHttp.post<Record<string, any>>({
    url: '/aidsMonitor/queryNewDiagnosisLine',
    data,
  });

/**
 * 分页查询艾滋病新确诊病人列表——下钻
 * /aidsMonitor/queryNewDiagnosisPage
 * */
export const queryNewDiagnosisPage = (data) =>
  defHttp.post<Record<string, any>>({
    url: '/aidsMonitor/queryNewDiagnosisPage',
    data,
  });

/**
 * 艾滋病用药顺位列表——下钻
 * /aidsMonitor/queryDrugList
 * */
export const queryDrugList = () =>
  defHttp.get<Record<string, any>>({
    url: '/aidsMonitor/queryDrugList',
  });
