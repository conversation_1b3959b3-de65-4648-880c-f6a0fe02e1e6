import { defHttp } from '@ft/request';

/**
 * 感染科各类传染病住院平均住院日
 * /medicalBurden/getAverageHospitalDays
 */
export function getAverageHospitalDays() {
  return defHttp.post({
    url: '/medicalBurden/getAverageHospitalDays',
  });
}

/**
 * 感染科医患比统计
 * /medicalBurden/getDoctorPatientRate
 */
export function getDoctorPatientRate() {
  return defHttp.post({
    url: '/medicalBurden/getDoctorPatientRate',
  });
}

/**
 * 感染科门诊各医生接诊量
 * /medicalBurden/getDoctorReceptionVolume
 */
export function getDoctorReceptionVolume() {
  return defHttp.post({
    url: '/medicalBurden/getDoctorReceptionVolume',
  });
}

/**
 * 传染病死亡率
 * /medicalBurden/getInfectionDeathRate
 */
export function getInfectionDeathRate(data) {
  return defHttp.post({
    url: '/medicalBurden/getInfectionDeathRate',
    data,
  });
}

/**
 * 感染科门诊疾病
 * /medicalBurden/getInfectiousDiseaseRecords
 */
export function getInfectiousDiseaseRecords() {
  return defHttp.post({
    url: '/medicalBurden/getInfectiousDiseaseRecords',
  });
}

/**
 * 大屏中上卡片数据
 * /medicalBurden/getMedicalBurdenNum
 */
export function getMedicalBurdenNum() {
  return defHttp.post({
    url: '/medicalBurden/getMedicalBurdenNum',
  });
}

/**
 * 感染科护患比统计
 * /medicalBurden/getNursePatientRate
 */
export function getNursePatientRate() {
  return defHttp.post({
    url: '/medicalBurden/getNursePatientRate',
  });
}

/**
 * 发热门诊疾病
 * /medicalBurden/getOutpatientDiseases
 */
export function getOutpatientDiseases() {
  return defHttp.post({
    url: '/medicalBurden/getOutpatientDiseases',
  });
}

/**
 * 感染科手术分级管理数据
 * /medicalBurden/getSurgicalGradeData
 */
export function getSurgicalGradeData() {
  return defHttp.post({
    url: '/medicalBurden/getSurgicalGradeData',
  });
}
