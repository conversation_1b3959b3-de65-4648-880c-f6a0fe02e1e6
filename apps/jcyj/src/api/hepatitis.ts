import { defHttp } from '@ft/request';

/** 丙肝患者基因分型检测率-折线图 */
export const getLeftBottomLineChart = (statisticType: string) =>
  defHttp.get<Record<string, any>>({
    url: `/viralHepatitisMonitor/getBghzjyfxjcl/${statisticType}`,
  });

/** 全年丙肝抗体阳性患者核酸检测率-折线图 */
export const getLeftMiddleLineChart = (statisticType: string) =>
  defHttp.get<Record<string, any>>({
    url: `/viralHepatitisMonitor/getQnbgktyxhzhsjcl/${statisticType}`,
  });

/** 抗病毒治疗人数统计-柱状图 */
export const getLeftTopLineChart = (statisticType: string) =>
  defHttp.get<Record<string, any>>({
    url: `/viralHepatitisMonitor/getKbdzlrstj/${statisticType}`,
  });

/** 宜昌市各辖区患病人数、患病人数占比 */
export const getMiddleBottomMapData = (year: number) =>
  defHttp.get<Record<string, any>>({
    url: `/viralHepatitisMonitor/getMiddleBottomMapData/${year}`,
  });

/** 全年收治总人数、甲型肝炎收治人数、乙型肝炎收治人数、丙型肝炎收治人数、戊型肝炎收治人数 */
export const getMiddleTopCardData = (year: number) =>
  defHttp.get<Record<string, any>>({
    url: `/viralHepatitisMonitor/getMiddleTopCardData/${year}`,
  });

/** 符合治疗条件的HCV-RNA阳性患者-折线图 */
export const getRightBottomLineChart = (statisticType: string) =>
  defHttp.get<Record<string, any>>({
    url: `/viralHepatitisMonitor/getFhzltjdhcvrnayxhz/${statisticType}`,
  });

/** 丙肝RNA阳性治疗率-折线图 */
export const getRightMiddleLineChart = (statisticType: string) =>
  defHttp.get<Record<string, any>>({
    url: `/viralHepatitisMonitor/getRightMiddleLineChart/${statisticType}`,
  });

/** 全年筛查HBSAg阳性率-折线图 */
export const getRightTopLineChart = (statisticType: string) =>
  defHttp.get<Record<string, any>>({
    url: `/viralHepatitisMonitor/getQnschbsagyxl/${statisticType}`,
  });

/** 新发HCV抗体阳性患者数-柱状图 */
export const getXfhcvktyxhzs = (statisticType: string) =>
  defHttp.get<Record<string, any>>({
    url: `/viralHepatitisMonitor/getXfhcvktyxhzs/${statisticType}`,
  });

/** 宜昌市各辖区甲、乙、丙、戊肝炎收治人数-柱状图 */
export const getGxqszrsData = ({ year, hepatitisType }: { year: number; hepatitisType: number }) =>
  defHttp.get<Record<string, any>>({
    url: `/viralHepatitisMonitor/getGxqszrs`,
    params: {
      year,
      hepatitisType,
    },
  });

/** 地图下钻-获取所属辖区患者列表
 * /viralHepatitisMonitor/queryPatientList
 */
export const getPatientList = (data) =>
  defHttp.post<Record<string, any>>(
    {
      url: `/viralHepatitisMonitor/queryPatientList`,
      data,
    },
    // {
    //   joinPrefix: false,
    // },
  );
