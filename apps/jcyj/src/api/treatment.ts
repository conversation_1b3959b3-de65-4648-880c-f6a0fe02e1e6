import { defHttp } from '@ft/request';

/**
 * 结核病治疗监测
 * /therapeuticIndexManageFacade/getTbTreatmentMonitor
 */
export const getTbTreatmentMonitor = () =>
  defHttp.get<Record<string, any>>({
    url: '/therapeuticIndexManageFacade/getTbTreatmentMonitor',
  });
/**
 * 法定传染病报告发病率
 * /therapeuticIndexManageFacade/getNNIDReportedIncidence
 */
export const getNNIDReportedIncidence = () =>
  defHttp.get<Record<string, any>>({
    url: '/therapeuticIndexManageFacade/getNNIDReportedIncidence',
  });

/**
 * 结核病患者就诊统计
 * /therapeuticIndexManageFacade/getTbPatientVisitStatistics
 */
export const getTbPatientVisitStatistics = () =>
  defHttp.get<Record<string, any>>({
    url: '/therapeuticIndexManageFacade/getTbPatientVisitStatistics',
  });

/**
 * 传染病患者县域内就诊率及相关病例组合指数
 * /therapeuticIndexManageFacade/getConsultRateCombinationIndex
 */
export const getConsultRateCombinationIndex = () =>
  defHttp.get<Record<string, any>>({
    url: '/therapeuticIndexManageFacade/getConsultRateCombinationIndex',
  });

/**
 * 病毒性肝炎治疗监测
 * /therapeuticIndexManageFacade/getHepatitisTreatmentMonitor
 */
export const getHepatitisTreatmentMonitor = () =>
  defHttp.get<Record<string, any>>({
    url: '/therapeuticIndexManageFacade/getHepatitisTreatmentMonitor',
  });

/**
 * 病毒性肝炎患者就诊统计
 * /therapeuticIndexManageFacade/getHepatitisPatientVisitStatistics
 */
export const getHepatitisPatientVisitStatistics = () =>
  defHttp.get<Record<string, any>>({
    url: '/therapeuticIndexManageFacade/getHepatitisPatientVisitStatistics',
  });

/**
 * HIV治疗监测
 * /therapeuticIndexManageFacade/getHivTreatmentMonitor
 */
export const getHivTreatmentMonitor = () =>
  defHttp.get<Record<string, any>>({
    url: '/therapeuticIndexManageFacade/getHivTreatmentMonitor',
  });

/**
 * 艾滋病患者就诊统计
 * /therapeuticIndexManageFacade/getHivPatientVisitStatistics
 */
export const getHivPatientVisitStatistics = () =>
  defHttp.get<Record<string, any>>({
    url: '/therapeuticIndexManageFacade/getHivPatientVisitStatistics',
  });

/**
 * 孕产妇传染病检出率
 * /therapeuticIndexManageFacade/getPregnantIDDRateVO
 */
export const getPregnantIDDRateVO = () =>
  defHttp.get<Record<string, any>>({
    url: '/therapeuticIndexManageFacade/getPregnantIDDRateVO',
  });
