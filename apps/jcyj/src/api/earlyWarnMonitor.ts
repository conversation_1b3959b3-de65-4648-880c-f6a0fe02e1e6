import { useGlobSetting } from '@ft/internal/hooks/setting';
import { defHttp as _defHttp } from '@ft/request';

const defHttp = {
  post: <T>(args: any) => {
    const config = useGlobSetting();
    return _defHttp.post<T>(args, {
      urlPrefix: window.location.pathname.includes('/analysis/')
        ? config.urlPrefix
        : config.urlPrefixSjyy,
      // urlPrefix: config.urlPrefixSjyy,
    });
  },
};

export const getInfectiousDiseaseList = (data?: any) =>
  _defHttp.post<any[]>(
    {
      url: '/infection-sysmgt/earlyWarnIndex/getEarlyWarnInfection',
      data,
    },
    {
      joinPrefix: false,
    },
  );

/**
 * 获取散点图数据
 * /earlyWarnMonitor/getScatterPlot/{infectionCode}
 */
export const getScatterPlot = (infectionCode: string) =>
  defHttp.post<any[]>({
    url: `/earlyWarnMonitor/getScatterPlot/${infectionCode}`,
  });

/**
 * 近7例传染病发病监测
 * /earlyWarnMonitor/getUnder7DiseasesMonitor
 */
export const getUnder7DiseasesMonitor = () =>
  defHttp.post<any[]>({
    url: '/earlyWarnMonitor/getUnder7DiseasesMonitor',
  });

/**
 * 近7日传染病顺位
 * /earlyWarnMonitor/getUnder7DiseasesRankMonitor
 */
export const getUnder7DiseasesRankMonitor = () =>
  defHttp.post<any[]>({
    url: '/earlyWarnMonitor/getUnder7DiseasesRankMonitor',
  });

/**
 * 患者列表
 * /earlyWarnMonitor/getPatientList
 */
export const getPatientList = (data: any) =>
  defHttp.post<any[]>({
    url: '/earlyWarnMonitor/getPatientList',
    data,
  });
/**
 * 根据传染病编码查询近7日传染病顺位患者分布情况
 * /earlyWarnMonitor/getUnder7DiseasesRankPatientDistributionByInfectionCode/{infectionCode}
 * */
export const getUnder7DiseasesRankPatientDistributionByInfectionCode = (infectionCode: string) =>
  defHttp.post<any[]>({
    url: `/earlyWarnMonitor/getUnder7DiseasesRankPatientDistributionByInfectionCode/${infectionCode}`,
  });

/**
 * 根据传染病编码查询近7日传染病顺位患者机构分布情况
 * /earlyWarnMonitor/getUnder7DiseasesRankPatientDistributionOrgByInfectionCode/{infectionCode}/{areaCode}
 * */
export const getUnder7DiseasesRankPatientDistributionByInfectionCodeAreaCode = (
  infectionCode: string,
  areaCode: string,
) =>
  defHttp.post<any[]>({
    url: `/earlyWarnMonitor/getUnder7DiseasesRankPatientDistributionOrgByInfectionCode/${infectionCode}/${areaCode}`,
  });

/**
 * 统计详情-获取传染病统计表头
 * /earlyWarnMonitor/getStatisticTableHeader
 */
export const getStatisticTableHeader = () =>
  defHttp.post<Recordable<string>>({
    url: '/earlyWarnMonitor/getStatisticTableHeader',
  });

interface JurisdictionStatisticQuery {
  areaCode: string;
  startDate: string;
  endDate: string;
}
/**
 * 获取辖区传染病统计数据
 * /earlyWarnMonitor/getJurisdictionStatisticData
 */
export const getJurisdictionStatisticData = (data: Partial<JurisdictionStatisticQuery>) =>
  defHttp.post<Recordable<string>[]>({
    url: '/earlyWarnMonitor/getJurisdictionStatisticData',
    data,
  });

interface OrgStatisticQuery {
  orgName: string;
  startDate: string;
  endDate: string;
}
/**
 * 获取医疗机构传染病统计数据
 * /earlyWarnMonitor/getOrgStatisticData
 */
export const getOrgStatisticData = (data: Partial<OrgStatisticQuery>) =>
  defHttp.post<Recordable<string>[]>({
    url: '/earlyWarnMonitor/getOrgStatisticData',
    data,
  });
