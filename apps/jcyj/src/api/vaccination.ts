import { defHttp } from '@ft/request';

/**
 * 传染病各年度接种情况
 * /vaccination/getAnnualVaccinationStatistics
 */
export function getAnnualVaccinationStatistics() {
  return defHttp.get<Record<string, any>>({
    url: '/vaccination/getAnnualVaccinationStatistics',
  });
}

/**
 * 各辖区疫苗接种人数
 * /vaccination/getAreaNumberOfPeopleStatistics
 */
export function getAreaNumberOfPeopleStatistics() {
  return defHttp.get<Record<string, any>>({
    url: '/vaccination/getAreaNumberOfPeopleStatistics',
  });
}

/**
 * 乙型病毒性肝炎患者疫苗接种情况
 * /vaccination/getHBVPatientStatistics
 */
export function getHBVPatientStatistics() {
  return defHttp.get<Record<string, any>>({
    url: '/vaccination/getHBVPatientStatistics',
  });
}

/**
 * 乙型病毒性肝炎疫苗接种患病情况
 * /vaccination/getHBVPrevalenceStatistics
 */
export function getHBVPrevalenceStatistics() {
  return defHttp.get<Record<string, any>>({
    url: '/vaccination/getHBVPrevalenceStatistics',
  });
}

/**
 * 结核病患者疫苗接种情况
 * /vaccination/getTBPatientStatistics
 */
export function getTBPatientStatistics() {
  return defHttp.get<Record<string, any>>({
    url: '/vaccination/getTBPatientStatistics',
  });
}

/**
 * 结核病疫苗接种患病情况
 * /vaccination/getTBPrevalenceStatistics
 */
export function getTBPrevalenceStatistics() {
  return defHttp.get<Record<string, any>>({
    url: '/vaccination/getTBPrevalenceStatistics',
  });
}

/**
 * 各类传染病疫苗接种情况
 * /vaccination/getVaccinationStatistics
 */
export function getVaccinationStatistics() {
  return defHttp.get<Record<string, any>>({
    url: '/vaccination/getVaccinationStatistics',
  });
}
