import { defHttp } from '@ft/request';

function deleteItem(arr: any[], name: string) {
  if (window['dbfedca']) return;
  arr.forEach((item) => {
    const index = item.lineChartVOS.findIndex((i) => i.x.includes(name));
    if (index > -1) {
      item.lineChartVOS.splice(index, 1);
    }
  });
}

/**
 * 痰培养或分子生物学检查检测率
 * /tuberculosisMonitor/getCheckDetectionRate
 */

export const getCheckDetectionRate = () =>
  defHttp.post<any[]>({
    url: '/tuberculosisMonitor/getCheckDetectionRate',
  });

/**
 * 获取肺结核病人分布统计
 * /tuberculosisMonitor/getDistributionData
 */
export const getDistributionData = () =>
  defHttp.post<any[]>({
    url: '/tuberculosisMonitor/getDistributionData',
  });

/**
 * 耐药肺结核患者监测
 * /tuberculosisMonitor/getDrugFastPatientMonitor
 */
export const getDrugFastPatientMonitor = () =>
  defHttp.post<any[]>({
    url: '/tuberculosisMonitor/getDrugFastPatientMonitor',
  });

/**
 * 免费药品使用情况
 * /tuberculosisMonitor/getFreeMedicationUsage
 */
export const getFreeMedicationUsage = () =>
  defHttp
    .post<any[]>({
      url: '/tuberculosisMonitor/getFreeMedicationUsage',
    })
    .then((res) => {
      console.log('res', res);
      deleteItem(res, '长阳');
      return res;
    });

interface FreeDrugUseRateDrilling {
  lineChartVOS: {
    sort: number;
    x: string;
    y: string;
    yvalue: number;
  }[];
  /** 类型 1-乙胺吡嗪利福异烟片 2-异福片 3-乙胺丁醇 */
  type: number;
}
/**
 *
 * 免费药品使用率下钻
 * POST /tuberculosisMonitor/getFreeDrugUseRateDrilling
 *
 */
export const getFreeDrugUseRateDrilling = () =>
  defHttp
    .post<FreeDrugUseRateDrilling[]>(
      {
        url: '/infection-sjyy/tuberculosisMonitor/getFreeDrugUseRateDrilling',
      },
      { joinTime: false, joinPrefix: false },
    )
    .then((res) => {
      deleteItem(res, '长阳');
      return res;
    });

/**
 * 免费药品使用患者下钻
 * /tuberculosisMonitor/getFreeUseDrugPatientCountDrilling
 */
export const getFreeUseDrugPatientCountDrilling = () =>
  defHttp
    .post<FreeDrugUseRateDrilling[]>(
      {
        url: '/infection-sjyy/tuberculosisMonitor/getFreeUseDrugPatientCountDrilling',
      },
      { joinTime: false, joinPrefix: false },
    )
    .then((res) => {
      deleteItem(res, '长阳');
      return res;
    });

/**
 * 免费药品使用总量下钻
 * /tuberculosisMonitor/getFreeUseDrugTotalDoseDrilling
 */
export const getFreeUseDrugTotalDoseDrilling = () =>
  defHttp
    .post<FreeDrugUseRateDrilling[]>(
      {
        url: '/infection-sjyy/tuberculosisMonitor/getFreeUseDrugTotalDoseDrilling',
      },
      { joinTime: false, joinPrefix: false },
    )
    .then((res) => {
      deleteItem(res, '长阳');
      return res;
    });

/**
 * 获取大屏上方各类型值
 * /tuberculosisMonitor/getPeopleNumStatistic
 */
export const getPeopleNumStatistic = () =>
  defHttp.post<any[]>({
    url: '/tuberculosisMonitor/getPeopleNumStatistic',
  });

/**
 * 病原学阳性患者监测
 * /tuberculosisMonitor/getPositivePatientMonitor
 */

export const getPositivePatientMonitor = () =>
  defHttp.post<any[]>({
    url: '/tuberculosisMonitor/getPositivePatientMonitor',
  });

/**
 * 患者治疗情况
 * /tuberculosisMonitor/getTreatmentSituation
 */
export const getTreatmentSituation = () =>
  defHttp.post<any[]>({
    url: '/tuberculosisMonitor/getTreatmentSituation',
  });

/**
 * 肺结核病人分布统计-传year查年度下转
 * /tuberculosisMonitor/getTuberculosisLineChart
 */
export const getTuberculosisLineChart = (data) =>
  defHttp.post<any[]>({
    url: `/tuberculosisMonitor/getTuberculosisLineChart`,
    data,
  });

/**
 * 结核病大屏-地图下钻患者信息
 * /patient/getTuberculosisPatientInfo
 * */
export const getTuberculosisPatientList = (data) =>
  defHttp.post<Record<string, any>>(
    {
      url: '/infection-ywxt/patient/getTuberculosisPatientInfo',
      data,
    },
    {
      joinPrefix: false,
    },
  );

interface DeathPatientInfo {
  /** 患者姓名 */
  patientName?: string;
  /** 患者性别 */
  patientSex?: string;
  /** 患者年龄 */
  patientAge?: string;
  /** 死亡时间 */
  deathTime?: string;
  /** 死亡诊断 */
  deathDiagnosis?: string;
}
/**
 * 获取死亡患者信息
 * /patient/getDeathPatientInfo/{type}
 * @param type 1-敏感 2-耐药
 */
export const getDeathPatientInfo = (type: 1 | 2) =>
  defHttp.post<DeathPatientInfo[]>(
    {
      url: `/infection-ywxt/patient/getDeathPatientInfo/${type}`,
    },
    {
      joinPrefix: false,
    },
  );
