import { defHttp } from '@ft/request';

/**
 * 不明原因发热患者单位分布-条形图
 * /infectiousDiseaseEarlyMonitor/getLeftBottomBarChart
 */
export const getLeftBottomBarChart = () =>
  defHttp.get<Record<string, any>>({
    url: '/infectiousDiseaseEarlyMonitor/getLeftBottomBarChart',
  });

/**
 * 不明原因发热患者社区分布-条形图
 * /infectiousDiseaseEarlyMonitor/getLeftMiddleBarChart
 */
export const getLeftMiddleBarChart = () =>
  defHttp.get<Record<string, any>>(
    {
      url: '/infectiousDiseaseEarlyMonitor/getLeftMiddleBarChart',
    },
    {
      joinTime: false,
    },
  );

/**
 * 不明原因发热近七日患者监测-折线图
 * /infectiousDiseaseEarlyMonitor/getLeftTopLineChart/{statisticType}
 */
export const getLeftTopLineChart = (statisticType: string) =>
  defHttp.get<Record<string, any>>({
    url: `/infectiousDiseaseEarlyMonitor/getLeftTopLineChart/${statisticType}`,
  });

/**
 * 季节性传染病统计分析-柱状图
 * /infectiousDiseaseEarlyMonitor/getMiddleLeftBottomLineChart
 */
export const getMiddleLeftBottomLineChart = () =>
  defHttp.get<Record<string, any>>({
    url: '/infectiousDiseaseEarlyMonitor/getMiddleLeftBottomLineChart',
  });

/**
 * 聚集性传染病患者学校分布-条形图
 * /infectiousDiseaseEarlyMonitor/getMiddleRightBottomLineChart
 */
export const getMiddleRightBottomLineChart = () =>
  defHttp.get<Record<string, any>>({
    url: '/infectiousDiseaseEarlyMonitor/getMiddleRightBottomLineChart',
  });

/**
 * 聚集性传染病患者单位分布-条形图
 * /infectiousDiseaseEarlyMonitor/getRightBottomBarChart
 */
export const getRightBottomBarChart = () =>
  defHttp.get<Record<string, any>>({
    url: '/infectiousDiseaseEarlyMonitor/getRightBottomBarChart',
  });

/**
 * 聚集性传染病患者社区分布-条形图
 * /infectiousDiseaseEarlyMonitor/getRightMiddleBarChart
 */
export const getRightMiddleBarChart = () =>
  defHttp.get<Record<string, any>>({
    url: '/infectiousDiseaseEarlyMonitor/getRightMiddleBarChart',
  });

/**
 * 聚集性传染病近七日患者监测-折线图
 * /infectiousDiseaseEarlyMonitor/getRightTopLineChart/{statisticType}
 */
export const getRightTopLineChart = (statisticType: string) =>
  defHttp.get<Record<string, any>>({
    url: `/infectiousDiseaseEarlyMonitor/getRightTopLineChart/${statisticType}`,
  });
