import { defHttp } from '@ft/request';

/**
 * 根据行政区划获取各类传染病发病人数统计数据
 * /infectiousDiseaseEarlyMonitorDataReport/queryIncidencePatientCountByDivision
 */
export const queryIncidencePatientCountByDivision = (data) =>
  defHttp.post<any[]>({
    url: '/infectiousDiseaseEarlyMonitorDataReport/queryIncidencePatientCountByDivision',
    data,
  });

/**
 * 根据医疗机构获取各类传染病发病人数统计数据
 * /infectiousDiseaseEarlyMonitorDataReport/queryIncidencePatientCountByHospital
 */
export const queryIncidencePatientCountByHospital = (data) =>
  defHttp.post<any[]>({
    url: '/infectiousDiseaseEarlyMonitorDataReport/queryIncidencePatientCountByHospital',
    data,
  });

/**
 * 根据发病患者获取各类传染病发病人数统计数据
 * /infectiousDiseaseEarlyMonitorDataReport/queryIncidencePatientList
 */
export const queryIncidencePatientList = (data) =>
  defHttp.post<any[]>({
    url: '/infectiousDiseaseEarlyMonitorDataReport/queryIncidencePatientList',
    data,
  });

/**
 * 导出各类传染病发病人数统计结果Excel-行政区划
 * /infectiousDiseaseEarlyMonitorDataReport/exportIncidencePatientCountDataByDivision
 */
export const exportIncidencePatientCountDataByDivision = (data) =>
  defHttp.post<any>(
    {
      url: '/infectiousDiseaseEarlyMonitorDataReport/exportIncidencePatientCountDataByDivision',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );

/**
 * 导出各类传染病发病人数统计结果Excel-医疗机构
 * /infectiousDiseaseEarlyMonitorDataReport/exportIncidencePatientCountDataByHospital
 * */
export const exportIncidencePatientCountDataByHospital = (data) =>
  defHttp.post<any>(
    {
      url: '/infectiousDiseaseEarlyMonitorDataReport/exportIncidencePatientCountDataByHospital',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );

/**
 * 导出各类传染病发病人数统计结果Excel-发病患者
 * /infectiousDiseaseEarlyMonitorDataReport/exportIncidencePatientList
 * */
export const exportIncidencePatientList = (data) =>
  defHttp.post<any>(
    {
      url: '/infectiousDiseaseEarlyMonitorDataReport/exportIncidencePatientList',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );

/**
 * 获取传染病用药统计数据
 * /infectiousDiseaseEarlyMonitorDataReport/queryMedicationStatisticsData
 */
export const queryMedicationStatisticsData = (data) =>
  defHttp.post<any[]>({
    url: '/infectiousDiseaseEarlyMonitorDataReport/queryMedicationStatisticsData',
    data,
  });

/**
 * 导出传染病用药统计数据Excel
 * /infectiousDiseaseEarlyMonitorDataReport/exportMedicationStatisticsData
 */
export const exportMedicationStatisticsData = (data) =>
  defHttp.post<any>(
    {
      url: '/infectiousDiseaseEarlyMonitorDataReport/exportMedicationStatisticsData',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );

/**
 * 获取不明原因发热传染病统计数据
 * /infectiousDiseaseEarlyMonitorDataReport/queryFeverOfUnknownStatisticsData
 */
export const queryFeverOfUnknownStatisticsData = (data) =>
  defHttp.post<any[]>({
    url: '/infectiousDiseaseEarlyMonitorDataReport/queryFeverOfUnknownStatisticsData',
    data,
  });

/**
 * 导出不明原因发热传染病统计数据Excel
 * /infectiousDiseaseEarlyMonitorDataReport/exportFeverOfUnknownStatisticsData
 */
export const exportFeverOfUnknownStatisticsData = (data) =>
  defHttp.post<any>(
    {
      url: '/infectiousDiseaseEarlyMonitorDataReport/exportFeverOfUnknownStatisticsData',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );

/**
 * 获取聚集性传染病确诊患者统计数据表列集合
 * /infectiousDiseaseEarlyMonitorDataReport/getAggregatedInfectiousDiseaseStatisticsTableColumns
 */
export const getAggregatedInfectiousDiseaseStatisticsTableColumns = () =>
  defHttp.get<any[]>({
    url: '/infectiousDiseaseEarlyMonitorDataReport/getAggregatedInfectiousDiseaseStatisticsTableColumns',
  });

/**
 * 获取聚集性传染病确诊患者统计数据
 * /infectiousDiseaseEarlyMonitorDataReport/queryAggregatedInfectiousDiseaseStatisticsData/{year}
 */
export const queryAggregatedInfectiousDiseaseStatisticsData = (year: number) =>
  defHttp.get<any[]>({
    url: `/infectiousDiseaseEarlyMonitorDataReport/queryAggregatedInfectiousDiseaseStatisticsData/${year}`,
  });

/**
 * 导出聚集性传染病确诊患者统计数据Excel
 * /infectiousDiseaseEarlyMonitorDataReport/exportAggregatedInfectiousDiseaseStatisticsData/{year}
 */
export const exportAggregatedInfectiousDiseaseStatisticsData = (year: number) =>
  defHttp.get<any>(
    {
      url: `/infectiousDiseaseEarlyMonitorDataReport/exportAggregatedInfectiousDiseaseStatisticsData/${year}`,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );

/**
 * 获取季节性传染病统计数据表列集合
 * /infectiousDiseaseEarlyMonitorDataReport/getSeasonalInfectiousDiseaseStatisticsTableColumns
 */
export const getSeasonalInfectiousDiseaseStatisticsTableColumns = () =>
  defHttp.get<any[]>({
    url: '/infectiousDiseaseEarlyMonitorDataReport/getSeasonalInfectiousDiseaseStatisticsTableColumns',
  });

/**
 * 获取季节性传染病统计数据
 * /infectiousDiseaseEarlyMonitorDataReport/querySeasonalInfectiousDiseaseStatisticsData/{year}
 */
export const querySeasonalInfectiousDiseaseStatisticsData = (year: number) =>
  defHttp.get<any[]>(
    {
      url: `/infectiousDiseaseEarlyMonitorDataReport/querySeasonalInfectiousDiseaseStatisticsData/${year}`,
    },
    {
      joinTime: false,
    },
  );

/**
 * 导出季节性传染病统计数据Excel
 * /infectiousDiseaseEarlyMonitorDataReport/exportSeasonalInfectiousDiseaseStatisticsData/{year}
 */
export const exportSeasonalInfectiousDiseaseStatisticsData = (year: number) =>
  defHttp.get<any>(
    {
      url: `/infectiousDiseaseEarlyMonitorDataReport/exportSeasonalInfectiousDiseaseStatisticsData/${year}`,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );

/**
 * 各类传染病发病人数统计服务-获取传染病发病人数统计低代码免登地址
 */
export function getLowCodeUrl() {
  return defHttp.get({ url: `/infectionStatistics/queryLowCodeUrl` });
}
