import { defHttp } from '@ft/request';

/**
 * 查询患者传染源流调追踪列表,1 居住地 2 确诊工作地 3 确诊经过地
 * /flowTrace/queryList
 * */
export const queryFlowTraceList = (data: any) =>
  defHttp.get<any[]>({
    url: '/flowTrace/queryList',
    params: data,
  });

/**
 * 分页查询患者传染源流调追踪列表,1 相同社区 2 工作单位 3 相同职业
 * /flowTrace/queryPage
 * */
export const queryFlowTracePage = (data: any) =>
  defHttp.post<any>({
    url: '/flowTrace/queryPage',
    data,
  });

/**
 * 分页查询患者信息列表
 * /flowTrace/queryPatientPage
 * */
export const queryPatientPage = (data: any) =>
  defHttp.post<any>({
    url: '/flowTrace/queryPatientPage',
    data,
  });

/**
 * 查询患者最近一次诊断信息
 * /flowTrace/queryDiagnosisInfo
 * */
export const queryDiagnosisInfo = (data: any) =>
  defHttp.post<any>({
    url: '/flowTrace/queryDiagnosisInfo',
    data,
  });

/**
 * 导出患者传染源流调追踪列表
 * /flowTrace/exportExcel
 * */
export const exportExcel = (data: any) =>
  defHttp.post<any>(
    {
      url: '/flowTrace/exportExcel',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );
