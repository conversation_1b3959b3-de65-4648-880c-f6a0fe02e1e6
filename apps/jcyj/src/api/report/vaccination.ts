import { defHttp } from '@ft/request';

/**
 * 获取统计列表
 * /vaccination/getVaccinationStatisticsReport
 */
export const queryList = (areaCode: string, year) =>
  defHttp.get<any[]>({
    url: `/vaccination/getVaccinationStatisticsReport`,
    params: { areaCode, year },
  });

/**
 * 导出Excel
 * /vaccination/export
 */
export const exportVaccination = (areaCode = '', year) =>
  defHttp.post<any>(
    {
      url: `/vaccination/export`,
      params: { areaCode, year },
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
      joinParamsToUrl: true,
    },
  );
