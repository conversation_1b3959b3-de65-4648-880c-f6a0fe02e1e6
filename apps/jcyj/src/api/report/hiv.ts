import { defHttp } from '@ft/request';

/**
 * 获取统计列表
 * /aidsMonitor/queryHIVIndexCount
 */
export const queryList = (data: any) =>
  defHttp.post<any[]>({
    url: '/aidsMonitor/queryHIVIndexCount',
    data,
  });

/**
 * 导出Excel
 * /aidsMonitor/export
 */
export const exportAidsMonitor = (data: any) =>
  defHttp.post<any>(
    {
      url: '/aidsMonitor/export',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );
