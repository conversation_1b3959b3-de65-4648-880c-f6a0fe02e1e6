import { defHttp } from '@ft/request';

/**
 * 获取统计列表
 * /therapeuticIndexManageFacade/queryTherapeuticIndexManageStatistics
 */
export const queryList = (data: any) =>
  defHttp.post<any[]>({
    url: '/therapeuticIndexManageFacade/queryTherapeuticIndexManageStatistics',
    data,
  });

/**
 * 导出Excel
 * /therapeuticIndexManageFacade/therapeuticIndexManageStatisticsExport
 */
export const exportTherapeuticIndexManageStatistics = (data: any) =>
  defHttp.post<any>(
    {
      url: '/therapeuticIndexManageFacade/therapeuticIndexManageStatisticsExport',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );
