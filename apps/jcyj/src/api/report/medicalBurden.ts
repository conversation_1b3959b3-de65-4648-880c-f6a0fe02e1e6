import { defHttp } from '@ft/request';

/**
 * 统计报表
 * /medicalBurden/statisticalReport
 */
export const queryList = (data: any) =>
  defHttp.post<any[]>({
    url: '/medicalBurden/statisticalReport',
    data,
  });
/**
 * 导出
 * /medicalBurden/export
 */
export const exportMedicalBurden = (data: any) =>
  defHttp.post<any>(
    {
      url: '/medicalBurden/export',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );

/**
 * 统计图表
 * /medicalBurden/getMedicalBurdenStatistic
 */
export const getMedicalBurdenStatistic = (data: any) =>
  defHttp.post<any>({
    url: '/medicalBurden/getMedicalBurdenStatistic',
    data,
  });
