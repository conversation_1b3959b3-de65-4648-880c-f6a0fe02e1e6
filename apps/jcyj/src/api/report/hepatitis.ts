import { defHttp } from '@ft/request';
import { ContentTypeEnum } from '@ft/internal/enums/httpEnum';
import type { RcFile } from 'ant-design-vue/lib/vc-upload/interface';

/**
 * 导出病毒性肝炎专病防治中心各项指标统计结果Excel
 * /viralHepatitisDataReport/export
 */
export const exportViralHepatitisDataReport = (data: any) =>
  defHttp.post<any>(
    {
      url: '/viralHepatitisDataReport/export',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );

/**
 * 导出病毒性肝炎专病防治中心各项指标月统计结果Excel
 * /viralHepatitisDataReport/exportDetail
 */
export const exportViralHepatitisDataReportDetail = (data: any) =>
  defHttp.post<any>(
    {
      url: '/viralHepatitisDataReport/exportDetail',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );

/**
 * 获取统计年度列表
 * /viralHepatitisDataReport/getStatisticYearList
 */
export const getStatisticYearList = () =>
  defHttp
    .get<any[]>({
      url: '/viralHepatitisDataReport/getStatisticYearList',
    })
    .then((res) => res.map((item) => ({ label: item, value: item })));

/**
 * 获取病毒性肝炎专病防治中心各项指标月统计列表
 * /viralHepatitisDataReport/queryDetailList
 */
export const queryDetailList = (data: any) =>
  defHttp.post<any[]>({
    url: '/viralHepatitisDataReport/queryDetailList',
    data,
  });

/**
 * 获取病毒性肝炎专病防治中心各项指标统计列表
 * /viralHepatitisDataReport/queryList
 */
export const queryList = (data: any) =>
  defHttp.post<any[]>({
    url: '/viralHepatitisDataReport/queryList',
    data,
    timeout: 20 * 1000,
  });

/**
 * 丙型肝炎病毒RNA检测 分页查询
 * /hcvRnaDetectionRecord/page
 * */
export const hcvRnaDetectionRecordPage = (data: any) =>
  defHttp.post<any>({
    url: '/hcvRnaDetectionRecord/page',
    data,
  });

/**
 * 丙型肝炎病毒RNA检测 下载模版
 * /hcvRnaDetectionRecord/downloadTemplate
 * */
export const hcvRnaDetectionRecordDownloadTemplate = () =>
  defHttp.get(
    {
      url: '/hcvRnaDetectionRecord/downloadTemplate',
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );

/**
 * 丙型肝炎病毒RNA检测 导入excel
 * /hcvRnaDetectionRecord/importExcel
 * */
export const hcvRnaDetectionRecordImportExcel = (data: { file: RcFile }) => {
  const formData = new FormData();
  formData.append('file', data.file);
  return defHttp.post<any>({
    url: '/hcvRnaDetectionRecord/importExcel',
    data: formData,
    headers: {
      'Content-Type': ContentTypeEnum.FORM_DATA,
    },
  });
};

/**
 * 丙型肝炎病毒RNA检测 删除
 * /hcvRnaDetectionRecord/delete/{id}
 * */
export const hcvRnaDetectionRecordDelete = (id: string) =>
  defHttp.delete({
    url: `/hcvRnaDetectionRecord/delete/${id}`,
  });
