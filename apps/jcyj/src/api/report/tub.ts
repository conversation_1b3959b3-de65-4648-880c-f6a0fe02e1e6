import { ContentTypeEnum } from '@ft/internal/enums/httpEnum';
import { defHttp } from '@ft/request';
import type { RcFile } from 'ant-design-vue/lib/vc-upload/interface';

/**
 * 获取统计列表
 * /tuberculosisMonitor/statisticalReport
 */
export const queryList = (data: any) =>
  defHttp.post<any[]>({
    url: '/tuberculosisMonitor/statisticalReport',
    data,
  });

/**
 * 导出Excel
 * /tuberculosisMonitor/export
 */
export const exportTuberculosisMonitor = (data: any) =>
  defHttp.post<any>(
    {
      url: '/tuberculosisMonitor/export',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );

/**
 * 结核病-全市结核病密接者筛查情况表-分页查询
 * /tbCloseContactScreeningRecord/page
 */
export const tbCloseContactScreeningRecordPage = (data: any) =>
  defHttp.post<any>({
    url: '/tbCloseContactScreeningRecord/page',
    data,
  });

/**
 * 结核病-全市结核病密接者筛查情况表-下载导入模板
 * /tbCloseContactScreeningRecord/downloadTemplate
 */
export const tbCloseContactScreeningRecordDownloadTemplate = () =>
  defHttp.get(
    {
      url: '/tbCloseContactScreeningRecord/downloadTemplate',
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );

/**
 * 结核病-全市结核病密接者筛查情况表-批量导入
 * /tbCloseContactScreeningRecord/importExcel
 */
export const tbCloseContactScreeningRecordImportExcel = (data: { file: RcFile }) => {
  const formData = new FormData();
  formData.append('file', data.file);
  return defHttp.post<any>({
    url: '/tbCloseContactScreeningRecord/importExcel',
    data: formData,
    headers: {
      'Content-Type': ContentTypeEnum.FORM_DATA,
    },
  });
};
