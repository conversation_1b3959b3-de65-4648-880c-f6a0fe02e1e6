import { defHttp } from '@ft/request';

/**
 * 传染病实时报告情况
 * /regionalInfectiousDiseaseMonitor/getCrbssbgqk
 */
export const getCrbssbgqk = () =>
  defHttp.get<Record<string, any>>({
    url: '/regionalInfectiousDiseaseMonitor/getCrbssbgqk',
  });

/**
 * 发病顺位
 * /regionalInfectiousDiseaseMonitor/getFbsw
 */
export const getFbsw = () =>
  defHttp.get<Record<string, any>>({
    url: '/regionalInfectiousDiseaseMonitor/getFbsw',
  });

/**
 * 人数分布
 * /regionalInfectiousDiseaseMonitor/getRqfb
 */
export const getRqfb = () =>
  defHttp.get<Record<string, any>>({
    url: '/regionalInfectiousDiseaseMonitor/getRqfb',
  });

/**
 * 报告机构top10
 * /regionalInfectiousDiseaseMonitor/getTop10bgjg
 */
export const getTop10bgjg = () =>
  defHttp.get<Record<string, any>>({
    url: '/regionalInfectiousDiseaseMonitor/getTop10bgjg',
  });

/**
 * 各类型传染病报告人数、死亡人数
 * /regionalInfectiousDiseaseMonitor/getTopCard
 */
export const getTopCard = () =>
  defHttp.get<Record<string, any>>({
    url: '/regionalInfectiousDiseaseMonitor/getTopCard',
  });

/**
 * 各辖区报告人数
 * /regionalInfectiousDiseaseMonitor/getXqbgrs
 */
export const getXqbgrs = () =>
  defHttp.get<Record<string, any>>({
    url: '/regionalInfectiousDiseaseMonitor/getXqbgrs',
  });

/**
 * 各区应急车辆
 * /regionalInfectiousDiseaseMonitor/getGqyjcl
 */
export const getGqyjcl = () =>
  defHttp.get<Record<string, any>>({
    url: '/regionalInfectiousDiseaseMonitor/getGqyjcl',
  });

/**
 * 重点药品库存情况
 * /regionalInfectiousDiseaseMonitor/getZdypkcqk
 */
export const getZdypkcqk = () =>
  defHttp.get<Record<string, any>>({
    url: '/regionalInfectiousDiseaseMonitor/getZdypkcqk',
  });

/**
 * 医疗防护资源库存存储情况
 * /regionalInfectiousDiseaseMonitor/getYlfhzykcccqk
 */
export const getYlfhzykcccqk = () =>
  defHttp.get<Record<string, any>>({
    url: '/regionalInfectiousDiseaseMonitor/getYlfhzykcccqk',
  });

/**
 * 获取死亡人数列表
 * /regionalInfectiousDiseaseMonitor/getDeathPatientList
 * */
export const getDeathPatientList = (infectionCategoryCode: number) =>
  defHttp.get<Record<string, any>>({
    url: '/regionalInfectiousDiseaseMonitor/getDeathPatientList',
    params: {
      infectionCategoryCode,
    },
  });

/**
 * 获取报告人数列表
 * /regionalInfectiousDiseaseMonitor/getReportPatientList
 * */
export const getReportPatientList = (data) =>
  defHttp.post<Record<string, any>>({
    url: '/regionalInfectiousDiseaseMonitor/getReportPatientList',
    data,
  });
