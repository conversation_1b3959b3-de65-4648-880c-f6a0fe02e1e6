<script setup lang="ts">
  import { computed, nextTick, onMounted, ref } from 'vue';
  import { useFullscreen } from '@vueuse/core';
  import { Icon } from '@ft/internal/components/Icon';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { useRoute } from 'vue-router';
  import { register } from '../helper/chart';
  import Clock from './clock.vue';
  import viewTitle from '/@/assets/svg/data-view/layout/data-view-title.svg';
  import { AnalysisRoute, AnalyzeRoute } from '/@/main';
  import { autoScaleFn } from './auto-scale';
  const fullscreenEl = ref<any>(null);
  const isIFrame = sessionStorage.getItem('iframe');
  const { isFullscreen, enter, exit } = useFullscreen(fullscreenEl);

  register();

  // const router = useRouter();
  const route = useRoute();
  const go = useGo();

  const activeTab = computed(() => route.matched[2].name);
  const subActiveTab = computed(() => route.name);

  const allTabs = computed(() =>
    [{ clock: true } as any].concat(
      route.path.startsWith('/analysis') ? AnalysisRoute : AnalyzeRoute,
    ),
  );
  const subTabs = computed(
    () => allTabs.value.find((tab) => tab.name === activeTab.value)?.children || [],
  );

  function goRouteName(name) {
    go({ name });
  }

  const fn = () => {
    nextTick(() => {
      autoScaleFn(!!isIFrame || isFullscreen.value);
    });
  };
  onMounted(() => {
    autoScaleFn(!!isIFrame || isFullscreen.value);
    fullscreenEl.value = document.querySelector('main.ant-layout-content');
    window.addEventListener('resize', fn);
  });
</script>

<template>
  <div id="data-view">
    <div class="absolute right-12 top-6 color-#5A86C2 z-2" v-if="!isIFrame">
      <Icon
        :size="32"
        v-if="!isFullscreen"
        class="cursor-pointer"
        icon="ant-design:fullscreen-outlined"
        @click="enter"
      />
      <Icon
        :size="32"
        v-if="isFullscreen"
        class="cursor-pointer"
        icon="ant-design:fullscreen-exit-outlined"
        @click="exit"
      />
    </div>
    <div class="wrap-view w-full h-full overflow-y-hidden">
      <div class="relative flex items-end h-88px">
        <img :src="viewTitle" class="absolute left-1/2 top-5 -translate-x-1/2 h-40px" />
        <div class="tabs-wrapper w-full">
          <template v-for="(tab, idx) in allTabs" :key="tab.name">
            <Clock v-if="tab.clock" />
            <div
              v-else
              @click="goRouteName(tab.name)"
              class="tab"
              :class="[
                activeTab === tab.name ? 'active' : '',
                idx < allTabs.length / 2 ? 'left' : 'right',
              ]"
            >
              <span class="text"> {{ tab.meta.title }}</span>
            </div>
            <div v-if="idx == allTabs.length / 2 - 1" class="flex-1"></div>
          </template>
        </div>
        <div class="sub-tabs absolute z-2 left-1/2 -translate-x-1/2 -bottom-20 z-81">
          <template v-for="tab in subTabs" :key="tab.name">
            <div
              @click="goRouteName(tab.name)"
              class="sub-tab relative"
              :class="[subActiveTab === tab.name ? 'active' : '']"
            >
              <i
                class="sub-icon"
                :class="[`${tab.name} ${subActiveTab === tab.name ? 'active' : ''}`]"
              ></i>
              <span class="sub-text text-white"> {{ tab.meta.title }}</span>
            </div>
          </template>
        </div>
      </div>
      <div
        class="w-full flex justify-between mt-20px pl-60px pr-60px relative"
        style="height: calc(100% - 90px)"
      >
        <router-view />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
  #data-view {
    height: 100%;
    width: 100%;
    background: url('/@/assets/images/data-view/layout/index-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
  }

  .tabs-wrapper {
    display: flex;
    // gap: 18px;
    color: white;
    transform: translateY(36px);

    .tab {
      height: 100px;
      width: 236px;
      display: flex;
      align-items: center;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      text-align: center;
      justify-content: center;

      &.left:nth-child(1) {
        transform: translateX(20px);
      }

      &.left:nth-child(2) {
        transform: translateX(-30px);
      }

      &.left:nth-child(3) {
        transform: translateX(-80px);
      }

      &.right:nth-child(5) {
        transform: translateX(80px);
      }

      &.right:nth-child(6) {
        transform: translateX(30px);
      }

      &.right:nth-child(7) {
        transform: translateX(-20px);
      }

      &.left {
        background-image: url('/@/assets/images/data-view/layout/tab-bg-left.png');
      }

      &.right {
        background-image: url('/@/assets/images/data-view/layout/tab-bg-right.png');
      }

      .text {
        height: 40px;
        // width: 160px;
        cursor: pointer;
        font-size: 16px;
        font-weight: bold;
        line-height: 40px;
        letter-spacing: 0;
        background: linear-gradient(180deg, #fff 0%, #17abff 100%);
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }

      &.active {
        &.left {
          background-image: url('/@/assets/images/data-view/layout/tab-bg-active-left.png');
        }

        &.right {
          background-image: url('/@/assets/images/data-view/layout/tab-bg-active-right.png');
        }

        .text {
          background: linear-gradient(180deg, #fff 0%, #fff 100%);
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
      }
    }
  }

  .sub-tabs {
    display: flex;
    gap: 32px;

    .sub-tab {
      display: flex;
      width: fit-content;
      background-image: url('/@/assets/images/data-view/layout/sub-tab-bg.png');
      background-size: 100% 100%;

      &.active {
        background-image: url('/@/assets/images/data-view/layout/sub-tab-bg-active.png');

        .sub-text {
          background: linear-gradient(180deg, #fff 0%, #d8bb6c 100%);
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }
      }

      .sub-text {
        cursor: pointer;
        padding: 0 4px;
        display: block;
        flex: 1;
        font-size: 16px;
        font-weight: bold;
        font-style: italic;
        background: linear-gradient(180deg, #fff 0%, #17abff 100%);
        -webkit-text-fill-color: transparent;
        background-clip: text;
        transform: translateY(-5px);
      }

      .sub-icon {
        display: inline-block;
        width: 32px;
        height: 32px;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-left: 12px;
        transform: translateY(-10px);

        &.overview {
          background-image: url('/@/assets/svg/data-view/layout/tabs/overview.svg');

          &.active {
            background-image: url('/@/assets/svg/data-view/layout/tabs/overview-active.svg');
          }
        }

        &.heating {
          background-image: url('/@/assets/svg/data-view/layout/tabs/heating.svg');

          &.active {
            background-image: url('/@/assets/svg/data-view/layout/tabs/heating-active.svg');
          }
        }

        &.prevention {
          background-image: url('/@/assets/svg/data-view/layout/tabs/prevention.svg');

          &.active {
            background-image: url('/@/assets/svg/data-view/layout/tabs/prevention-active.svg');
          }
        }

        &.infectiousDiseaseEarlyWarning {
          background-image: url('/@/assets/svg/data-view/layout/tabs/infectious-disease-early-warning.svg');

          &.active {
            background-image: url('/@/assets/svg/data-view/layout/tabs/infectious-disease-early-warning-active.svg');
          }
        }

        &.infectiousDisease {
          background-image: url('/@/assets/svg/data-view/layout/tabs/infectious-disease.svg');

          &.active {
            background-image: url('/@/assets/svg/data-view/layout/tabs/infectious-disease-active.svg');
          }
        }

        &.treatment {
          background-image: url('/@/assets/svg/data-view/layout/tabs/prevention.svg');

          &.active {
            background-image: url('/@/assets/svg/data-view/layout/tabs/prevention-active.svg');
          }
        }

        &.aids {
          background-image: url('/@/assets/svg/data-view/layout/tabs/aids.svg');

          &.active {
            background-image: url('/@/assets/svg/data-view/layout/tabs/aids-active.svg');
          }
        }

        &.tuberculosis {
          background-image: url('/@/assets/svg/data-view/layout/tabs/tuberculosis.svg');

          &.active {
            background-image: url('/@/assets/svg/data-view/layout/tabs/tuberculosis-active.svg');
          }
        }

        &.hepatitis {
          background-image: url('/@/assets/svg/data-view/layout/tabs/hepatitis.svg');

          &.active {
            background-image: url('/@/assets/svg/data-view/layout/tabs/hepatitis-active.svg');
          }
        }

        &.otherInfectiousDisease {
          background-image: url('/@/assets/svg/data-view/layout/tabs/other-infectious-diseases.svg');

          &.active {
            background-image: url('/@/assets/svg/data-view/layout/tabs/other-infectious-diseases-active.svg');
          }
        }
      }
    }
  }
</style>

<style lang="less">
  #data-view,
  .data-view-modal {
    --color-2: #afd3ff;
    --color-3: #5a86c2;
    //
    --color-blue: #00a6ff;
    --color-5: #23f1f1;
    --color-6: #ff9e37;

    .color-1 {
      color: #fff;
    }

    .color-2 {
      color: var(--color-2);
    }

    .color-3 {
      color: var(--color-3);
    }

    .color-4 {
      color: var(--color-4);
    }

    .color-5 {
      color: var(--color-5);
    }

    .color-6 {
      color: var(--color-6);
    }

    .color-7 {
      color: var(--color-7);
    }
  }
</style>
