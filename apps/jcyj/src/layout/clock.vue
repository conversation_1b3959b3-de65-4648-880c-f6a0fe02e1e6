<script setup lang="ts">
  import dayjs from 'dayjs';
  import { ref } from 'vue';
  const year = ref(dayjs().format('YYYY'));
  // const day = ref(dayjs().format('YYYY-MM-DD'));
  // const time = ref(dayjs().format('HH:mm:ss'));
  // const timer = setInterval(() => {
  //   time.value = dayjs().format('HH:mm:ss');
  // }, 1000);
  // onBeforeUnmount(() => {
  //   clearInterval(timer);
  // });
</script>

<template>
  <div class="clock-wrapper">
    <div class="color-#5A86C2 text-24px ml-4">{{ year }}年</div>
    <!-- <div class="color-#5A86C2 text-16px ml-4">{{ day }}</div>
    <div class="color-#00A6FF text-24px ml-4">{{ time }}</div> -->
  </div>
</template>

<style lang="less" scoped>
  .clock-wrapper {
    margin-top: 12px;
    margin-left: 54px;
    margin-right: 32px;
    width: 150px;
    height: 64px;
    background: url('/@/assets/svg/data-view/layout/clock-wrapper.svg');
    background-size: 150px 64px;
  }
</style>
