<template>
  <div class="overflow-hidden">
    <Topbar />
    <Layout style="height: calc(100vh - 56px); margin-top: 56px" class="jcyj">
      <Layout.Sider v-model:collapsed="collapsed" collapsible>
        <Menu
          v-model:selectedKeys="selectedKeys"
          v-model:openKeys="openKeys"
          theme="dark"
          mode="inline"
          @click="handleMenuClick"
        >
          <template v-for="item in menuList" :key="item.path">
            <Menu.SubMenu :key="item.path" v-if="item.children">
              <template #title>
                <span>
                  <Icon v-if="item.icon" :icon="item.icon" style="vertical-align: -3px" />
                  <span :title="item.title">{{ item.title }}</span>
                </span>
              </template>
              <template v-for="child in item.children" :key="child.path">
                <Menu.Item :key="child.path" v-if="!child.children">
                  <span :title="child.title">{{ child.title }}</span>
                </Menu.Item>
                <Menu.SubMenu :key="child.path" v-else>
                  <template #title>
                    <span :title="child.title">{{ child.title }}</span>
                  </template>
                  <Menu.Item v-for="grandChild in child.children" :key="grandChild.path">
                    <span :title="grandChild.title">{{ grandChild.title }}</span>
                  </Menu.Item>
                </Menu.SubMenu>
              </template>
            </Menu.SubMenu>
            <Menu.Item :key="item.path" v-else>
              <Icon v-if="item.icon" :icon="item.icon" style="vertical-align: -3px" />
              <span :title="item.title">{{ item.title }}</span>
            </Menu.Item>
          </template>
        </Menu>
      </Layout.Sider>
      <Layout>
        <Layout.Content>
          <RouterView />
          <FrameLayout />
        </Layout.Content>
      </Layout>
    </Layout>
  </div>
</template>
<script lang="ts" setup>
  import { computed, nextTick, ref, watchEffect } from 'vue';
  import Topbar from '@ft/internal/layouts/default/topbar/index.vue';
  import { Icon } from '@ft/internal/components/Icon';
  import { Layout, Menu } from 'ant-design-vue';
  import { useRouter } from 'vue-router';
  import { useMenuState } from '@ft/internal/layouts/default/menu/useLayoutMenu';
  import FrameLayout from '@ft/internal/layouts/iframe/index.vue';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { autoScaleFn } from './auto-scale';

  const router = useRouter();
  const collapsed = ref<boolean>(false);
  const currentPath = router.currentRoute.value.path;
  const initdSelectedKeys = currentPath.startsWith('/analysis')
    ? ['/analysis']
    : currentPath.startsWith('/analyze')
    ? ['/analyze']
    : [currentPath];
  const temp = currentPath.split('/');
  const initdOpenKeys = currentPath.startsWith('/data-report')
    ? [temp.slice(0, 2).join('/'), temp.slice(0, 3).join('/')]
    : [];
  const selectedKeys = ref<string[]>(initdSelectedKeys);
  const openKeys = ref<string[]>(initdOpenKeys);

  const menuStore = useMenuState();
  const menuList = computed(() => menuStore.topMenus);
  watchEffect(() => {
    window['siderWidth'] = collapsed.value ? 64 : 200;
    nextTick(() => {
      autoScaleFn();
    });
  });
  const go = useGo();
  function handleMenuClick({ key }) {
    go({
      path: key,
    });
  }
</script>
<style lang="less">
  .jcyj {
    .ant-layout-sider-children {
      overflow-y: auto;
    }

    // 隐藏滚动条
    .ant-layout-sider-children::-webkit-scrollbar {
      display: none;
    }

    ul {
      li:first-child {
        margin-top: 0;
      }
    }

    .ant-form-item-label > label {
      color: #333;
    }

    .vben-basic-table .ant-pagination {
      margin: 10px 12px;
    }
  }
</style>
