export function autoScaleFn(full = false) {
  const designWidth = 1920;
  const designHeight = 1080;
  const viewElement = document.querySelector('#data-view') as HTMLElement;
  if (!viewElement) return;
  const scaleX =
    (document.documentElement.clientWidth - (!full ? window['siderWidth'] || 200 : 0)) /
    designWidth;
  const scaleY = (document.documentElement.clientHeight - (!full ? 56 : 0)) / designHeight;
  viewElement.style.width = `${designWidth}px`;
  viewElement.style.height = `${designHeight}px`;
  viewElement.style.transform = `scale(${scaleX},${scaleY})`;
  viewElement.style.transformOrigin = '0 0';
}

export function autoScaleMapFn() {
  const designWidth = 1920;
  const designHeight = 1080;
  if (
    document.documentElement.clientWidth === 1920 &&
    document.documentElement.clientHeight === 1080
  )
    return;
  const viewElement = document.querySelector('#ycMap')! as HTMLElement;
  const scaleX = document.documentElement.clientWidth / designWidth;
  const scaleY = document.documentElement.clientHeight / designHeight;
  viewElement.style.transform = `scale(${1 / scaleX},${1 / scaleY})`;
  viewElement.style.transformOrigin = '0 0';
}
