import type { AppRouteRecordRaw } from '@ft/internal/router/types';
import './design/global.less';
import { basicRoutes } from '@ft/internal/router/routes';
import CryptoJS from 'crypto-js';
import { encrypt } from '@ft/internal/utils/rsa';
import { setToken } from '@ft/internal/utils/auth';
import { RouterView } from 'vue-router';

export const AnalysisRoute = [
  {
    path: 'infectiousDiseaseEarlyWarning',
    name: 'AnalysisInfectiousDiseaseEarlyWarning',
    component: () => import('./views/infectiousDiseaseMonitoring/early-warning/index.vue'),
    meta: {
      title: '传染病聚集/突发预警',
      ignoreAuth: true,
    },
  },
  {
    path: 'overview',
    name: 'AnalysisOverview',
    component: () => import('./views/overview/index.vue'),
    meta: {
      title: '区域传染病监测总览',
      ignoreAuth: true,
    },
  },
  {
    path: 'aids',
    name: 'AnalysisAids',
    component: () => import('./views/treatmentIndexManagement/aids/index.vue'),
    meta: {
      title: '艾滋病监测',
      ignoreAuth: true,
    },
  },
  {
    path: 'tuberculosis',
    name: 'AnalysisTuberculosis',
    component: () => import('./views/treatmentIndexManagement/tuberculosis/index.vue'),
    meta: {
      title: '结核病监测',
    },
  },
  {
    path: 'hepatitis',
    name: 'AnalysisHepatitis',
    component: () => import('./views/treatmentIndexManagement/hepatitis/index.vue'),
    meta: {
      title: '病毒性肝炎监测',
    },
  },
];

export const AnalyzeRoute = [
  {
    path: 'infectiousDiseaseEarlyWarning',
    name: 'AnalyzeInfectiousDiseaseEarlyWarning',
    component: () => import('./views/infectiousDiseaseMonitoring/early-warning/index.vue'),
    meta: {
      title: '区域传染病临床诊断监测',
      ignoreAuth: true,
    },
  },
  {
    path: 'overview',
    name: 'AnalyzeOverview',
    component: () => import('./views/overview/index.vue'),
    meta: {
      title: '区域传染病网报监测',
      ignoreAuth: true,
    },
  },
  {
    path: 'aids',
    name: 'AnalyzeAids',
    component: () => import('./views/treatmentIndexManagement/aids/index.vue'),
    meta: {
      title: '艾滋病监测',
      ignoreAuth: true,
    },
  },
  {
    path: 'tuberculosis',
    name: 'AnalyzeTuberculosis',
    component: () => import('./views/treatmentIndexManagement/tuberculosis/index.vue'),
    meta: {
      title: '结核病监测',
    },
  },
  {
    path: 'hepatitis',
    name: 'AnalyzeHepatitis',
    component: () => import('./views/treatmentIndexManagement/hepatitis/index.vue'),
    meta: {
      title: '病毒性肝炎监测',
    },
  },
];
const route: AppRouteRecordRaw[] = [
  {
    path: '/',
    name: 'Index',
    meta: {},
    redirect: '/home',
    component: sessionStorage.getItem('iframe') ? RouterView : () => import('/@/layout/layout.vue'),
    children: [
      {
        path: '/analysis',
        name: 'analysisHome',
        component: () => import('/@/layout/index.vue'),
        redirect: '/analysis/infectiousDiseaseEarlyWarning',
        meta: {
          title: '数据大屏',
          ignoreAuth: true,
          hideTab: true,
        },
        children: AnalysisRoute,
      },
      {
        path: '/analyze',
        name: 'analyzeHome',
        component: () => import('/@/layout/index.vue'),
        redirect: '/analyze/infectiousDiseaseEarlyWarning',
        meta: {
          title: '数据大屏',
          ignoreAuth: true,
          hideTab: true,
        },
        children: AnalyzeRoute,
      },
    ],
  },
];

basicRoutes.push(...route);
window['DYNAMIC_VIEW_MODULES'] = Object.fromEntries(
  Object.entries(import.meta.glob('./views/**/*.{vue,tsx}')).map(([k, v]) => [
    k.replace('./views', '/jcyj'),
    v,
  ]),
);
const basePath = import.meta.env.VITE_PUBLIC_PATH.endsWith('/')
  ? import.meta.env.VITE_PUBLIC_PATH
  : `${import.meta.env.VITE_PUBLIC_PATH}/`;
if (window.location.pathname === basePath + 'rrr') {
  const key = CryptoJS.enc.Latin1.parse('fxkcfxkcfxkcfxkc');
  const iv = key;
  const cfg = {
    iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.ZeroPadding,
  };
  const searchParams = new URLSearchParams(window.location.search);
  const word = searchParams.get('r')!;
  const decrypt = CryptoJS.AES.decrypt(word, key, cfg);
  const params = decrypt.toString(CryptoJS.enc.Utf8);
  const [username, password, id] = params.split('->');
  import('@ft/internal/api').then((mod) => {
    mod
      .loginApi(
        {
          username,
          password: encrypt(password),
        },
        {
          'verify-code-check': 'false',
        },
      )
      .then((res) => {
        setToken(res.userToken);
        sessionStorage.setItem('iframe', 'true');
        window.location.href = `${basePath}?id=${id}`;
      });
  });
} else {
  Promise.resolve().then(() => import('@ft/internal/bootstrap'));
}
