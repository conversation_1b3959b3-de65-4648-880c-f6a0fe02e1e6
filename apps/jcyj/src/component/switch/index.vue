<script setup lang="ts">
  interface Option {
    label: string;
    key: string;
  }

  interface Props {
    options: Option[];
    modelValue: string;
    fontSize?: string;
  }

  defineProps<Props>();
  const emit = defineEmits(['update:modelValue']);
  function onClick(key) {
    emit('update:modelValue', key);
  }
</script>

<template>
  <div class="switch-wrapper">
    <div
      v-for="option in options"
      :key="option.key"
      class="item"
      :class="[{ active: option.key === modelValue }]"
      @click="onClick(option.key)"
      :style="{ fontSize: fontSize ? fontSize : '16px' }"
    >
      {{ option.label }}
    </div>
  </div>
</template>

<style scoped lang="less">
  .switch-wrapper {
    display: flex;
    align-items: center;
    width: 100%;
    // height: 22px;
    position: relative;
    z-index: 2;

    .item {
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0 6px;
      // height: 24px;
      font-size: 16px;
      color: var(--color-3);
      background: transparent;
      max-width: 310px;

      &.active {
        background-image: linear-gradient(
          180deg,
          rgb(255 255 255 / 85%) 14%,
          rgb(25 184 254 / 85%) 80%
        );
        background-clip: text;
        -webkit-text-fill-color: transparent;
        text-shadow: 0 0 6px #52c8ff;
      }
    }
  }
</style>
