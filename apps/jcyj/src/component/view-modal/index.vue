<script setup lang="ts">
  import { Modal } from 'ant-design-vue';
  import { computed } from 'vue';
  interface Props {
    visible: boolean;
    title?: string;
  }
  const props = defineProps<Props>();

  const emit = defineEmits(['update:visible', 'close']);

  const modelValue = computed({
    get: () => props.visible,
    set: (value) => {
      emit('update:visible', value);
    },
  });

  function onClose() {
    modelValue.value = false;
    emit('close');
  }
  const getContainer = () => document.getElementById('data-view') as HTMLElement;
</script>

<template>
  <Modal
    v-bind="$attrs"
    wrapClassName="jcyj-view-modal"
    v-model:visible="modelValue"
    centered
    :mask-style="{ 'backdrop-filter': 'blur(6px)', background: 'rgba(0, 0, 0, 0.5)' }"
    :closable="false"
    :mask-closable="false"
    :footer="null"
    :getContainer="getContainer"
  >
    <template #title>
      <div class="text-center relative font-italic text-[24px]">
        <span>{{ title }}</span>
        <div class="close-btn" @click="onClose"></div>
      </div>
    </template>
    <slot></slot>
  </Modal>
</template>
<style lang="less">
  .jcyj-view-modal {
    .ant-modal {
      background: url('/@/assets/images/data-view/modal/title-bg.png') center top no-repeat,
        url('/@/assets/images/data-view/modal/bg.png') center/103% 103% no-repeat;
      border: 2px solid rgba(#00aeff, 0.2);
      border-radius: 16px;
    }

    .close-btn {
      position: absolute;
      right: -30px;
      top: -21px;
      width: 76px;
      height: 76px;
      background: url('/@/assets/svg/data-view/modal/close-btn/deafult.svg') center/100% 100%
        no-repeat;
      cursor: pointer;

      &:hover {
        background-image: url('/@/assets/svg/data-view/modal/close-btn/active.svg');
      }
    }

    .ant-modal-content {
      & > .ant-modal-header {
        padding: 14px 24px 0 !important;

        & > .ant-modal-title {
          color: white !important;
        }
      }

      & > .ant-modal-body {
        padding: 24px !important;
      }
    }

    .ant-modal-content,
    .ant-modal-header {
      background-color: transparent;
      border: none;
      color: white;
    }
  }
</style>
