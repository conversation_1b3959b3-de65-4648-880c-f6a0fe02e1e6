<script setup lang="ts">
  import { inject, ref, unref } from 'vue';
  import CommonTab from '../../component/common-tab/index.vue';
  import CardChart from '../../component/card-chart/index.vue';
  import useChartData from '../../helper/useChartData';
  import { chartHeight, chartWidth } from '../../helper/chart';
  import Switch from '../../component/switch/index.vue';

  const props = withDefaults(
    defineProps<{
      chartKey: string;
      height: string;
      width: string;
      autoplay?: boolean;
      type?: number;
    }>(),
    {
      chartKey: '',
      height: chartHeight,
      width: chartWidth,
      autoplay: true,
    },
  );
  const emit = defineEmits(['custom-click']);
  const chartConfigMap = inject('chartConfigMap', {});
  const chartKey = ref(props.chartKey);
  const chartData = useChartData(chartKey, chartConfigMap);
</script>

<template>
  <CommonTab :title="chartData.title" :type="type">
    <div class="relative">
      <div v-if="chartData.switchOptions" class="absolute" style="top: 10px; left: 10px">
        <Switch
          :model-value="unref(chartData.switchValue)"
          @update:model-value="chartData.updateSwitchValue?.($event)"
          :options="chartData.switchOptions"
          :fontSize="chartData.switchFontSize"
        />
      </div>
      <CardChart
        :key="chartKey + '#' + unref(chartData.switchValue)"
        :title="chartData.title"
        :width="width"
        :height="height"
        :configs="unref(chartData.chartConfig)"
        :option="unref(chartData.chartOption)"
        :autoplay="autoplay"
        @custom-click="emit('custom-click', $event)"
      >
        <template #[item]="data" v-for="item in Object.keys($slots)" :key="item">
          <slot :name="item" v-bind="data || {}"></slot>
        </template>
      </CardChart>
    </div>
  </CommonTab>
</template>
