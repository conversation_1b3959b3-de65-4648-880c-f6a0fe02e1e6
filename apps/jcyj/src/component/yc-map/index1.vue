<script setup lang="ts">
  /* eslint-disable @typescript-eslint/ban-ts-comment */
  /* eslint-disable @typescript-eslint/prefer-ts-expect-error  */
  import { computed, nextTick, onMounted, ref, watch } from 'vue';
  import { storeToRefs } from 'pinia';
  import 'leaflet/dist/leaflet.css';
  import L from 'leaflet';
  import g from './g.json';
  import { useMapStore } from '/@/store/modules/map';
  import { heatMarkers, markers } from './data';

  import heatmap from './heatmap';
  import heatmapPlugin from './heatmap-leaflet';
  import './BoundaryCanvas';
  import './fix-gap';

  const HeatmapOverlay = heatmapPlugin(heatmap(), L);
  let map: any;
  let heatmapOverlay;
  const { points, position, onClickMapFn } = storeToRefs(useMapStore());
  const mapInit = ref(false);
  const markerLayers: any[] = [];
  // const levels = [
  //   {
  //     step: 10,
  //     rgb: '253,174,97',
  //   },
  //   {
  //     step: 50,
  //     rgb: '215,48,39',
  //   },
  //   {
  //     step: 100,
  //     rgb: '165,0,38',
  //   },
  // ] as const;

  const style = computed(() => {
    if (position.value === 'center-6') {
      nextTick(() => {
        setTimeout(() => {
          map.invalidateSize();
          map.setZoom(9.3);
        }, 10);
      });
      return {
        height: '960px',
        width: '960px',
      };
    } else if (position.value === 'center-5') {
      nextTick(() => {
        setTimeout(() => {
          map.invalidateSize();
          map.setZoom(8.3);
        }, 10);
      });
      return {
        height: '450px',
        width: '960px',
      };
    } else if (position.value === 'center-4') {
      nextTick(() => {
        setTimeout(() => {
          map.invalidateSize();
          map.setZoom(8.8);
        }, 10);
      });
      return {
        height: '636px',
        width: '960px',
      };
    } else if (position.value === 'center-3') {
      nextTick(() => {
        setTimeout(() => {
          map.invalidateSize();
          map.setZoom(8.4);
        }, 10);
      });
      return {
        height: '500px',
        width: '960px',
      };
    } else if (position.value === 'center-2') {
      nextTick(() => {
        setTimeout(() => {
          map.invalidateSize();
          map.setZoom(8.6);
        }, 10);
      });
      return {
        height: '540px',
        width: '960px',
      };
    } else {
      nextTick(() => {
        setTimeout(() => {
          map.invalidateSize();
          map.setZoom(8.9);
        }, 10);
      });
      return {
        height: '670px',
        width: '960px',
      };
    }
  });

  watch(
    () => [points.value, mapInit.value],
    () => {
      if (!mapInit.value) return;
      for (const layer of markerLayers) layer.remove();
      markerLayers.length = 0;
      if (points.value.length === 0) return;
      if ('heatAmount' in points.value[0]) {
        const data: any[] = points.value
          .map((m) => [
            m.heatAmount,
            heatMarkers.find((mmm) => mmm.name.substring(0, 2) === m.divisionName.substring(0, 2))
              ?.center,
          ])
          .filter((item) => item[1]);
        console.log(119, {
          max: 3000,
          data: data.map((m) => ({
            lat: m[1][0],
            lng: m[1][1],
            count: m[0],
          })),
        });
        heatmapOverlay.setData({
          max: 3000,
          data: data.map((m) => ({
            lat: m[1][0],
            lng: m[1][1],
            count: m[0],
          })),
        });
        return;
      } else {
        heatmapOverlay.setData({
          data: [],
        });
      }
      for (const m of points.value) {
        let myIcon;
        if ('heatAmount' in m) {
          // const currentLevel = levels.filter((l) => l.step <= (m.heatAmount || 0));
          // let bg = '';
          // if (currentLevel.length === 0) {
          //   bg = `radial-gradient(rgba(${levels[0].rgb}, 1) 0%, rgba(${levels[0].rgb}, 0.4) 70%, rgba(${levels[0].rgb}, 0) 100%)`;
          // } else if (currentLevel.length === 1) {
          //   bg = `radial-gradient(rgba(${levels[1].rgb}, 1) 0%, rgba(${levels[1].rgb}, 0.4) 70%, rgba(${levels[0].rgb}, 0) 100%)`;
          // } else if (currentLevel.length >= 2) {
          //   bg = `radial-gradient(rgba(${levels[2].rgb}, 1) 0%, rgba(${levels[2].rgb}, 0.4) 70%, rgba(${levels[0].rgb}, 0) 100%)`;
          // }
          // const bg = `radial-gradient(${currentLevel
          //   .map((l) => l.color)
          //   .reverse()
          //   .join(',')})`;
          // myIcon = L.divIcon({
          //   className: 'my-heat-marker',
          //   html: `<div class="w-48px h-48px rounded-100%" style="background: ${bg}; mix-blend-mode: multiply"></div>`,
          // });
        } else {
          myIcon = L.divIcon({
            className: 'my-city-marker',
            html: `<div class='px-10px py-7px'>
          <div class='flex items-start gap-2'>
            <span class='text-[16px] w-5em'>${m.divisionName}</span>
            <span 
              style="display: ${m.peopleRate !== undefined ? 'inline' : 'none'}" 
              class='translate-y-1.5 h-12px border-l-1px border-[#B0D4FF]'>
            </span>
            <span style="display: ${m.peopleRate !== undefined ? 'inline' : 'none'}" 
              class='translate-y-0.5 text-[14px] text-[#3CE292]'>
              ${m.peopleRate ? m.peopleRate + '%' : ''}
            </span>
          </div>
          <div style="display: ${m.peopleCount !== undefined ? 'block' : 'none'}">
            <span class='text-#AFD3FF'>患病人数：</span>
            <span class='text-#FF9E37 text-16px'>${m.peopleCount}</span>
          </div>          
          <div style="display: ${m.reportCount !== undefined ? 'block' : 'none'}">
            <span class='text-#AFD3FF'>报告人数：</span>
            <span class='text-#FF9E37 text-16px'>${m.reportCount}</span>
          </div>
          <div style="display: ${m.tubCount !== undefined ? 'block' : 'none'}">
            <span class='text-#AFD3FF'>结核：
            <span class='text-#FF9E37 text-16px'>${m.tubCount}</span>
          </div>
          <div style="display: ${m.hepatitisCount !== undefined ? 'block' : 'none'}">
            <span class='text-#AFD3FF'>乙肝：
            <span class='text-#FF9E37 text-16px'>${m.hepatitisCount}</span>
          </div>
          </div>`,
            // @ts-ignore
            iconSize: [176],
          });
        }
        const mm = markers.find(
          (mmm) => mmm.name.substring(0, 2) === m.divisionName.substring(0, 2),
        );
        if (mm) {
          const layer = L.marker(mm.center as any, { icon: myIcon }).addTo(map);
          onClickMapFn?.value && layer.on('click', () => (onClickMapFn as any).value(m));
          markerLayers.push(layer);
        }
      }
    },
  );

  onMounted(() => {
    nextTick(() => {
      setTimeout(() => {
        // const corner1 = L.latLng(31.56938, 110.255636);
        // const corner2 = L.latLng(29.943074, 112.123057);
        // const bounds = L.latLngBounds(corner1, corner2);

        map = L.map('ycMap', {
          // maxBounds: bounds,
          attributionControl: false,
          zoomControl: false,
          zoomSnap: 0.1,
          zoomDelta: 0.1,
          scrollWheelZoom: false,
          doubleClickZoom: false,
          trackResize: false,
          closePopupOnClick: false,
          dragging: false,
          zoomAnimation: false,
        }).setView([30.76217, 111.176962], 8.9);

        // @ts-ignore
        let leftTop = L.geoJSON(g).getBounds().getNorthWest();
        // @ts-ignore
        let rightBottom = L.geoJSON(g).getBounds().getSouthEast();

        const imgBounds = L.latLngBounds(leftTop, rightBottom);
        const publicPath = import.meta.env.VITE_PUBLIC_PATH || '/';
        const pathPrefix = publicPath.endsWith('/') ? publicPath : publicPath + '/';
        const imageUrl = pathPrefix + 'map-img-yc/yc.png';

        L.imageOverlay(imageUrl, imgBounds).addTo(map);
        heatmapOverlay = new HeatmapOverlay({
          // radius should be small ONLY if scaleRadius is true (or small radius is intended)
          // if scaleRadius is false it will be the constant radius used in pixels
          radius: 0.2,
          maxOpacity: 0.8,
          blur: 0.75,
          // scales the radius based on map zoom
          scaleRadius: true,
          // if set to false the heatmap uses the global maximum for colorization
          // if activated: uses the data maximum within the current map boundaries
          //   (there will always be a red spot with useLocalExtremas true)
          useLocalExtrema: false,
          // which field name in your data represents the latitude - default "lat"
          latField: 'lat',
          // which field name in your data represents the longitude - default "lng"
          lngField: 'lng',
          // which field name in your data represents the data value - default "value"
          valueField: 'count',
        });
        heatmapOverlay.addTo(map);

        map.createPane('pathPane');
        const pathPaneEle = map.getPane('pathPane')!;
        pathPaneEle.style.zIndex = '5';
        pathPaneEle.style.transform = 'translate3d(4px, 12px, 0px)';

        mapInit.value = true;
        // map.on('click', onMapClick)
        // function onMapClick(e) {
        //   console.log(`You clicked the map at ${e.latlng}`)
        // }
      });
    });
  });
</script>

<template>
  <div id="ycMap" style="background: transparent" :style="style"></div>
</template>

<style lang="less">
  .leaflet-container {
    background: #000;
  }

  .my-tile-layer {
    .leaflet-tile {
      // box-shadow: inset 10px 10px red;
    }
  }

  .my-polyline {
    transform: translate(10px, 10px);
    z-index: 2;
  }

  .my-image {
    opacity: 0.7;
  }

  .my-city-marker {
    background-image: url('/@/assets/svg/data-view/mark-bg.png');
    background-size: 100% 100%;
    color: white;
    padding: 15px;
  }

  .my-yj-popup {
    width: 210px;

    .leaflet-popup-content-wrapper {
      width: 100%;
      height: 100%;
      box-shadow: none;
      background-color: transparent;
      background-image: url('/@/assets/svg/data-view/yjts.svg');
      background-size: cover;

      .leaflet-popup-content {
        display: none;
      }
    }

    .leaflet-popup-tip-container {
      display: none;
    }
  }

  .my-yj-marker {
    background-image: url('/@/assets/svg/data-view/yjts.svg');
    background-size: cover;
  }

  .my-pointer-marker {
    background-color: transparent;
    z-index: 400px !important;
  }

  .leaflet-heatmap-layer {
    z-index: 300;
  }
</style>
