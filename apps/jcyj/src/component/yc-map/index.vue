<script setup lang="ts">
  /* eslint-disable @typescript-eslint/ban-ts-comment */
  /* eslint-disable @typescript-eslint/prefer-ts-expect-error  */
  import { computed, nextTick, onMounted, ref, watch } from 'vue';
  import { storeToRefs } from 'pinia';
  import 'leaflet/dist/leaflet.css';
  import L from 'leaflet';
  import g from './g.json';
  import g2 from './g2.json';
  import { markers } from './data';
  import { useMapStore } from '/@/store/modules/map';

  // import yjUrl from '/@/assets/svg/data-view/yjts.svg'

  import './BoundaryCanvas';
  import './fix-gap';

  const { points, cityModalKey, onClickPointFn, onClickCityFn, position } = storeToRefs(
    useMapStore(),
  );
  const mapInit = ref(false);
  const markerLayers: any[] = [];
  let map: any;
  const pointSvg =
    '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="20" height="20" viewBox="0 0 20 20"><defs><clipPath id="master_svg0_107_19870"><rect x="0" y="0" width="20" height="20" rx="0"/></clipPath><filter id="master_svg1_107_19880" filterUnits="objectBoundingBox" color-interpolation-filters="sRGB" x="0" y="0" width="20" height="20"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/><feColorMatrix in="SourceAlpha" type="matrix" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="0" dx="0"/><feGaussianBlur stdDeviation="1.5"/><feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/><feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.364705890417099 0 0 0 0 0.364705890417099 0 0 0 1 0"/><feBlend mode="normal" in2="shape" result="effect1_innerShadow"/></filter><filter id="master_svg2_107_19874" filterUnits="objectBoundingBox" color-interpolation-filters="sRGB" x="0" y="0" width="20" height="20"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/><feColorMatrix in="SourceAlpha" type="matrix" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="0" dx="0"/><feGaussianBlur stdDeviation="1.5"/><feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/><feColorMatrix type="matrix" values="0 0 0 0 0.8705882430076599 0 0 0 0 0.772549033164978 0 0 0 0 0.15294118225574493 0 0 0 1 0"/><feBlend mode="normal" in2="shape" result="effect1_innerShadow"/></filter><filter id="master_svg3_107_19871" filterUnits="objectBoundingBox" color-interpolation-filters="sRGB" x="-2" y="-2" width="14" height="14"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="0" dx="0"/><feGaussianBlur stdDeviation="0.5"/><feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.20000000298023224 0"/><feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/><feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/></filter></defs><g clip-path="url(#master_svg0_107_19870)"><g filter="url(#master_svg1_107_19880)"><ellipse cx="10" cy="10" rx="10" ry="10" fill="#FF5D5D" fill-opacity="0.20000000298023224"/></g><g filter="url(#master_svg2_107_19874)"><ellipse cx="10" cy="10" rx="10" ry="10" fill="#DEC527" fill-opacity="0.20000000298023224"/></g><g filter="url(#master_svg3_107_19871)"><ellipse cx="10" cy="10" rx="5" ry="5" fill="#DEC527" fill-opacity="1"/></g></g></svg>';
  const pointSvg2 =
    '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="20" height="20" viewBox="0 0 20 20"><defs><clipPath id="master_svg0_107_19870"><rect x="0" y="0" width="20" height="20" rx="0"/></clipPath><filter id="master_svg1_107_19880" filterUnits="objectBoundingBox" color-interpolation-filters="sRGB" x="0" y="0" width="20" height="20"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/><feColorMatrix in="SourceAlpha" type="matrix" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="0" dx="0"/><feGaussianBlur stdDeviation="1.5"/><feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/><feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.364705890417099 0 0 0 0 0.364705890417099 0 0 0 1 0"/><feBlend mode="normal" in2="shape" result="effect1_innerShadow"/></filter><filter id="master_svg2_107_19874" filterUnits="objectBoundingBox" color-interpolation-filters="sRGB" x="0" y="0" width="20" height="20"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/><feColorMatrix in="SourceAlpha" type="matrix" result="hardAlpha" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="0" dx="0"/><feGaussianBlur stdDeviation="1.5"/><feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/><feColorMatrix type="matrix" values="0 0 0 0 0.8705882430076599 0 0 0 0 0.772549033164978 0 0 0 0 0.15294118225574493 0 0 0 1 0"/><feBlend mode="normal" in2="shape" result="effect1_innerShadow"/></filter><filter id="master_svg3_107_19871" filterUnits="objectBoundingBox" color-interpolation-filters="sRGB" x="-2" y="-2" width="14" height="14"><feFlood flood-opacity="0" result="BackgroundImageFix"/><feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"/><feOffset dy="0" dx="0"/><feGaussianBlur stdDeviation="0.5"/><feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.20000000298023224 0"/><feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow"/><feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow" result="shape"/></filter></defs><g clip-path="url(#master_svg0_107_19870)"><g filter="url(#master_svg1_107_19880)"><ellipse cx="10" cy="10" rx="10" ry="10" fill="#FF5D5D" fill-opacity="0.20000000298023224"/></g><g filter="url(#master_svg2_107_19874)"><ellipse cx="10" cy="10" rx="10" ry="10" fill="#DEC527" fill-opacity="0.20000000298023224"/></g><g filter="url(#master_svg3_107_19871)"><ellipse cx="10" cy="10" rx="5" ry="5" fill="#FF6B68" fill-opacity="1"/></g></g></svg>';
  const style = computed(() => {
    nextTick(() => {
      map.invalidateSize(false);
    });
    if (position.value === 'right') {
      return {
        top: '40px',
        right: '300px',
        width: '650px',
        height: 'calc(100% - 340px)',
      };
    } else if (position.value === 'right-2') {
      return {
        top: '40px',
        right: '350px',
        width: '840px',
        height: 'calc(100% - 340px)',
      };
    } else {
      return {
        top: '120px',
        left: '350px',
        height: 'calc(100% - 260px)',
        width: '1220px',
      };
    }
  });

  watch(
    () => [points.value, mapInit.value],
    () => {
      if (!mapInit.value) return;
      for (const layer of markerLayers) layer.remove();
      markerLayers.length = 0;
      for (const point of points.value || []) {
        const ele = L.divIcon({
          className: 'my-pointer-marker',
          html: point.red ? pointSvg2 : pointSvg,
          iconSize: [10, 10],
        });
        const layer = L.marker([point.lat, point.lng], { icon: ele });

        if (point.data.yj) {
          const popupLayer = L.popup({
            offset: [110, 20],
            closeButton: false,
            className: 'my-yj-popup',
          }).setLatLng([point.lat, point.lng]);
          // const popup = L.divIcon({ iconSize: [210, 60], className: 'my-yj-marker', offset: []})
          // const popupLayer = L.DivOverlay([point.lat, point.lng], { icon: popup, })
          popupLayer.addTo(map);
          markerLayers.push(popupLayer);
        }

        layer.on('click', () => onClickPointFn.value({ key: point.key, data: point.data }));
        layer.addTo(map);

        markerLayers.push(layer);
      }
    },
  );

  onMounted(() => {
    const corner1 = L.latLng(31.56938, 110.255636);
    const corner2 = L.latLng(29.943074, 112.123057);
    const bounds = L.latLngBounds(corner1, corner2);

    map = L.map('ycMap', {
      attributionControl: false,
      zoomControl: false,
      zoomSnap: 0.1,
      zoomDelta: 0.1,
      minZoom: 8.5,
      maxZoom: 13.4,
      maxBounds: bounds,
      doubleClickZoom: false,
      trackResize: false,
      closePopupOnClick: false,
    }).setView([30.76217, 111.176962], 8.5);

    for (const m of markers) {
      const myIcon = L.divIcon({
        className: 'my-city-marker',
        html: `<div>${m.name}</div>`,
        iconSize: [64, 24],
      });
      L.marker(m.center as any, { icon: myIcon })
        .addTo(map)
        .on('click', () => onClickCityFn.value({ key: cityModalKey.value, data: {} }));
    }

    const geometry = g.features[0].geometry;

    // @ts-ignore
    L.TileLayer.boundaryCanvas('/tiles-yc/{z}/{x}/{y}.png', {
      boundary: geometry,
      // mode: 'show',
      opacity: 1,
      zIndex: 10,
      className: 'my-tile-layer',
    }).addTo(map);

    map.createPane('pathPane');
    const pathPaneEle = map.getPane('pathPane')!;
    pathPaneEle.style.zIndex = '5';
    pathPaneEle.style.transform = 'translate3d(4px, 12px, 0px)';
    // @ts-ignore
    L.geoJSON(g, { color: '#a8dbff', fill: false, className: 'path-1' }).addTo(map);
    // @ts-ignore
    L.geoJSON(g, { color: '#a8dbff', fill: false, className: 'path-2', pane: 'pathPane' }).addTo(
      map,
    );
    // @ts-ignore
    L.geoJSON(g2, {
      color: '#4c9cd4',
      opacity: 0.8,
      fill: false,
      className: 'path-3',
      weight: 2,
    }).addTo(map);

    mapInit.value = true;
    // map.on('click', onMapClick)
    // function onMapClick(e) {
    //   console.log(`You clicked the map at ${e.latlng}`)
    // }
  });
</script>

<template>
  <div class="absolute" :style="style">
    <div id="ycMap" style="background: transparent; height: 100%; width: 100%"></div>
  </div>
</template>

<style lang="less">
  .leaflet-container {
    background: #000;
  }

  .my-tile-layer {
    .leaflet-tile {
      // box-shadow: inset 10px 10px red;
    }
  }

  .my-polyline {
    transform: translate(10px, 10px);
    z-index: 2;
  }

  .my-image {
    opacity: 0.7;
  }

  .my-city-marker {
    background-image: url('/@/assets/svg/data-view/mark-bg.svg');
    background-size: cover;
    color: var(--color-7);
    line-height: 24px;
    text-align: center;
    font-size: 12px;
    white-space: nowrap;

    div {
      transform: scale((0.8));
    }
  }

  .my-yj-popup {
    width: 210px;
    height: 60px;

    .leaflet-popup-content-wrapper {
      width: 100%;
      height: 100%;
      box-shadow: none;
      background-color: transparent;
      background-image: url('/@/assets/svg/data-view/yjts.svg');
      background-size: cover;

      .leaflet-popup-content {
        display: none;
      }
    }

    .leaflet-popup-tip-container {
      display: none;
    }
  }

  .my-yj-marker {
    background-image: url('/@/assets/svg/data-view/yjts.svg');
    background-size: cover;
  }

  .my-pointer-marker {
    background-color: transparent;
    z-index: 400px !important;
  }
</style>
