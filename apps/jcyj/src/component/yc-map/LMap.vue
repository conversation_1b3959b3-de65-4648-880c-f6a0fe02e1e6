<script setup lang="ts">
  import { nextTick, onMounted, shallowRef, watchEffect } from 'vue';
  import 'leaflet/dist/leaflet.css';
  import L from 'leaflet';
  import { merge } from 'lodash-es';
  import g from './g.json';
  import './BoundaryCanvas';
  import './fix-gap';
  import yjRedPoint from '/@/assets/images/data-view/early-warning/yj-point-red.svg?raw';
  import yjOrangePoint from '/@/assets/images/data-view/early-warning/yj-point-orange.svg?raw';
  import yjYellowPoint from '/@/assets/images/data-view/early-warning/yj-point-yellow.svg?raw';
  import yjBluePoint from '/@/assets/images/data-view/early-warning/yj-point-blue.svg?raw';

  const yjPointMap = {
    1: yjRedPoint,
    2: yjOrangePoint,
    3: yjYellowPoint,
    4: yjBluePoint,
  };
  const props = defineProps<{
    options?: L.MapOptions;
    markers?: Array<{ center: [number, number]; orgName: string; msg: string }>;
    popups?: Array<{ center: [number, number]; title: string; content: string; level: number }>;
  }>();

  let map = shallowRef<L.Map>();
  const divRef = shallowRef<HTMLDivElement | null>(null);

  const corner1 = L.latLng(31.56938, 110.255636);
  const corner2 = L.latLng(29.943074, 112.123057);
  const bounds = L.latLngBounds(corner1, corner2);

  const defaultOpt = {
    attributionControl: false,
    zoomControl: false,
    zoomSnap: 0.1,
    zoomDelta: 0.1,
    minZoom: 8,
    maxZoom: 8,
    maxBounds: bounds,
    doubleClickZoom: false,
    trackResize: false,
    closePopupOnClick: false,
    dragging: false,
  };

  onMounted(() => {
    nextTick(() => {
      setTimeout(() => {
        if (!divRef.value) {
          return;
        }
        if (map.value) {
          return;
        }

        const opt = merge(defaultOpt, props.options);

        map.value = L.map(divRef.value, opt).setView([30.76217, 111.176962], 8);

        // @ts-ignore
        let leftTop = L.geoJSON(g).getBounds().getNorthWest();
        // @ts-ignore
        let rightBottom = L.geoJSON(g).getBounds().getSouthEast();

        const imgBounds = L.latLngBounds(leftTop, rightBottom);
        const publicPath = import.meta.env.VITE_PUBLIC_PATH || '/';
        const pathPrefix = publicPath.endsWith('/') ? publicPath : publicPath + '/';
        const imageUrl = pathPrefix + 'map-img-yc/yc.png';

        L.imageOverlay(imageUrl, imgBounds).addTo(map.value);
      });
    });
  });
  const layers: any[] = [];
  watchEffect(() => {
    if (map.value) {
      props.options?.zoom && map.value.setZoom(props.options?.zoom);
      props.options?.minZoom && map.value.setMinZoom(props.options?.minZoom);
      props.options?.maxZoom && map.value.setMaxZoom(props.options?.maxZoom);
      props.options?.maxBounds && map.value.setMaxBounds(props.options?.maxBounds);
      props.options?.center && map.value.setView(props.options?.center);
    }

    if (map.value) {
      // for (const m of props.markers || []) {
      //   const myIcon = L.divIcon({
      //     className: 'my-early-warning-marker',
      //     html: `<div class='px-10px py-7px'>
      //     <div class='flex items-start gap-2'>
      //       <span class='text-[16px] w-5em'>${m.orgName}</span>
      //     </div>
      //     <div>
      //       <span class='text-#AFD3FF'>${m.msg}</span>
      //       </div>
      //     </div>`,
      //     // @ts-ignore
      //     iconSize: [176],
      //   });
      //   L.marker(m.center, { icon: myIcon }).addTo(map.value);
      // }
      for (const layer of layers) layer.remove();

      for (const p of props.popups || []) {
        const myIcon = L.divIcon({
          iconSize: [16, 16],
          html: yjPointMap[p.level] || yjPointMap[4],
          className: 'my-early-warning-marker',
        });
        const marker = L.marker(p.center, { icon: myIcon });
        layers.push(marker);
        marker.addTo(map.value);
        const popupLayer = L.popup({
          closeButton: false,
          className: `my-early-warning-popup early-warning-level-${p.level || 4}`,
          content: /*html*/ `
            <div class='inner'>
              <div class='title'>${p.title}</div>
              <div class='content'>${p.content}</div>
            </div>
          `,
        }).setLatLng(p.center);
        popupLayer.openPopup();
        layers.push(popupLayer);
        popupLayer.addTo(map.value);
      }
    }
  });

  defineExpose({
    map,
  });
</script>

<template>
  <div ref="divRef" style="background: transparent; height: 100%; width: 100%"></div>
</template>

<style lang="less">
  .my-early-warning-marker {
    background: transparent;
  }

  .my-early-warning-popup {
    background-size: 112% 127%;
    background-position: -14px -4px;
    background-repeat: no-repeat;
    min-height: 80px;
    display: flex;
    align-items: center;

    &.early-warning-level-1 {
      background-image: url('/@/assets/images/data-view/early-warning/yj-popup-bg-red.png');
    }

    &.early-warning-level-2 {
      background-image: url('/@/assets/images/data-view/early-warning/yj-popup-bg-orange.png');
    }

    &.early-warning-level-3 {
      background-image: url('/@/assets/images/data-view/early-warning/yj-popup-bg-yellow.png');
    }

    &.early-warning-level-4 {
      background-image: url('/@/assets/images/data-view/early-warning/yj-popup-bg-blue.png');
    }

    .title {
      font-size: 16px;
      font-weight: bold;
      text-shadow: 1px 1px 1px #640202;
    }

    .leaflet-popup-content-wrapper,
    .leaflet-popup-tip {
      background: transparent;
      box-shadow: none;
      height: 100%;
      color: white;
    }
  }
</style>
