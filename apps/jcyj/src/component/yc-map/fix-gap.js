import L from 'leaflet';

(function () {
  if (!L || !<PERSON><PERSON>ay<PERSON> || !L.GridLayer.prototype) return;

  const originalInitTile = L.GridLayer.prototype._initTile;

  L.GridLayer.include({
    _initTile(tile) {
      originalInitTile.call(this, tile);

      const tileSize = this.getTileSize();

      tile.style.width = `${tileSize.x + 0.5}px`;
      tile.style.height = `${tileSize.y + 0.5}px`;
    },
  });
})();
