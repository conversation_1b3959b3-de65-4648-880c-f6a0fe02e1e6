<script setup lang="ts">
  import { nextTick, onMounted, shallowRef, watchEffect } from 'vue';
  import 'leaflet/dist/leaflet.css';
  import L from 'leaflet';
  import '@supermap/iclient-leaflet';
  import yjRedPoint from '/@/assets/images/data-view/early-warning/yj-point-red.svg?raw';
  import yjOrangePoint from '/@/assets/images/data-view/early-warning/yj-point-orange.svg?raw';
  import yjYellowPoint from '/@/assets/images/data-view/early-warning/yj-point-yellow.svg?raw';
  import yjBluePoint from '/@/assets/images/data-view/early-warning/yj-point-blue.svg?raw';

  const yjPointMap = {
    1: yjRedPoint,
    2: yjOrangePoint,
    3: yjYellowPoint,
    4: yjBluePoint,
  };
  const props = defineProps<{
    markers?: Array<{ center: [number, number]; orgName: string; msg: string }>;
    popups?: Array<{
      center: [number, number];
      title: string;
      content: string;
      level: number;
      data: any;
      fn: any;
    }>;
  }>();

  let map = shallowRef<L.Map>();
  const divRef = shallowRef<HTMLDivElement | null>(null);

  onMounted(() => {
    nextTick(() => {
      setTimeout(() => {
        if (!divRef.value) {
          return;
        }
        if (map.value) {
          return;
        }

        map.value = L.map(divRef.value, {
          crs: L.CRS.EPSG4326,
          attributionControl: false,
          zoomControl: false,
          zoomSnap: 0.1,
          zoomDelta: 0.1,
          doubleClickZoom: false,
          trackResize: false,
          closePopupOnClick: false,
        }).setView([30.75473337050005, 111.16632356600007], 8.5);
        // @ts-ignore
        new L.supermap.TiledMapLayer(
          import.meta.env.DEV
            ? 'https://iserver.supermap.io/iserver/services/map-world/rest/maps/World'
            : location.protocol + '//' + location.host + '/tile_url',
          {
            noWrap: true,
          },
        ).addTo(map.value);
      });
    });
  });
  const layers: any[] = [];
  watchEffect(() => {
    if (map.value) {
      for (const layer of layers) layer.remove();
      // let once = true;
      const popups = (props.popups || []).filter(
        (p) => !isNaN(p.center?.[0]) || !isNaN(p.center?.[1]),
      );
      for (const p of popups) {
        const myIcon = L.divIcon({
          iconSize: [16, 16],
          html: yjPointMap[p.level] || yjPointMap[1],
          className: 'my-early-warning-marker',
        });
        const marker = L.marker(p.center, { icon: myIcon });
        layers.push(marker);
        marker.addTo(map.value);
        const open = () => {
          const popupLayer = L.popup({
            closeButton: false,
            className: `my-early-warning-popup early-warning-level-${p.level || 1}`,
            content: /*html*/ `
            <div class='inner'>
              <div class='title'>${p.title}</div>
              <div class='content'>${p.content}</div>
            </div>
          `,
          }).setLatLng(p.center);
          // map.value?.openPopup(popupLayer);
          popupLayer.addTo(map.value!);
          const el = popupLayer.getElement();
          el?.addEventListener('click', () => p.fn(p.data));
          layers.push(popupLayer);
        };
        // marker.on('mouseover', open);
        open();
        // if (once) {
        //   once = false;
        //   open();
        // }
      }
    }
  });

  defineExpose({
    map,
  });
</script>

<template>
  <div ref="divRef" style="background: transparent; height: 100%; width: 100%"></div>
</template>

<style lang="less">
  .iclient-leaflet-logo {
    display: none;
  }

  .my-early-warning-marker {
    background: transparent;
  }

  .my-early-warning-popup {
    background-size: 112% 127%;
    background-position: -14px -4px;
    background-repeat: no-repeat;
    min-height: 80px;
    display: flex;
    align-items: center;

    &.early-warning-level-1 {
      background-image: url('/@/assets/images/data-view/early-warning/yj-popup-bg-red.png');
    }

    &.early-warning-level-2 {
      background-image: url('/@/assets/images/data-view/early-warning/yj-popup-bg-orange.png');
    }

    &.early-warning-level-3 {
      background-image: url('/@/assets/images/data-view/early-warning/yj-popup-bg-yellow.png');
    }

    &.early-warning-level-4 {
      background-image: url('/@/assets/images/data-view/early-warning/yj-popup-bg-blue.png');
    }

    .inner {
      transform: translateY(6px);
    }

    .title {
      font-size: 16px;
      font-weight: bold;
      text-shadow: 1px 1px 1px #640202;
      margin: 4px 0;
    }

    .leaflet-popup-content-wrapper,
    .leaflet-popup-tip {
      background: transparent;
      box-shadow: none;
      height: 100%;
      color: white;
    }
  }
</style>
