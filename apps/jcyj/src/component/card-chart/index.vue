<script setup lang="ts">
  /* eslint-disable @typescript-eslint/ban-ts-comment */
  /* eslint-disable @typescript-eslint/prefer-ts-expect-error  */
  import { nextTick, onBeforeUnmount, ref, watchEffect } from 'vue';
  import { Modal } from 'ant-design-vue';
  import type { EChartsOption } from 'echarts';
  import { cloneDeep, debounce, set } from 'lodash-es';
  import { useECharts } from '@ft/internal/hooks/web/useECharts';
  import { useMouseInElement } from '@vueuse/core';
  import { useDataViewEle } from '../../helper';

  interface Props {
    title: string;
    height: string;
    width: string;
    option: EChartsOption;
    configs: AutoPlayChartConfig[];
    autoplay?: boolean;
    autoplayNumber?: number;
    delay?: number;
    originData?: any[];
  }
  const props = defineProps<Props>();
  const emit = defineEmits(['bar-click', 'custom-click']);
  const { getDataViewEle } = useDataViewEle();

  let timer;
  const chartRef = ref<HTMLDivElement | null>(null);
  const chartRef2 = ref<HTMLDivElement | null>(null);
  const visible = ref(false);
  const { setOptions } = useECharts(chartRef as any);
  const { setOptions: setOptions2, getInstance } = useECharts(chartRef2 as any);
  const { isOutside } = useMouseInElement(chartRef);

  let isBind = false;

  function onClick() {
    if (props.title === '艾滋病用药顺位') {
      emit('custom-click');
      return;
    }
    visible.value = true;
    const optionCopy = cloneDeep(props.option);
    props.configs.forEach((config) => {
      const { path, val } = config;
      if (typeof val === 'function') set(optionCopy, path, val(visible.value));
      else set(optionCopy, path, val);
    });
    set(optionCopy, 'legend.top', 10);
    set(optionCopy, 'legend.orient', 'horizontal');
    if (optionCopy.yAxis) {
      if (Array.isArray(optionCopy.yAxis)) {
        optionCopy.yAxis?.forEach((item) => {
          // @ts-ignore
          item.splitNumber = 5;
        });
      } else {
        // @ts-ignore
        optionCopy.yAxis.splitNumber = 5;
      }
    }

    setOptions2(optionCopy);
    nextTick(() => {
      if (isBind) return;
      getInstance()?.on(
        'click',
        'series',
        debounce(
          (params: any) => {
            const data = props.originData?.find((item) => item.infectionName === params.name);
            emit('bar-click', data);
          },
          1000,
          {
            leading: true,
          },
        ),
      );
      isBind = true;
    });
  }

  let start = 0;
  let end = props.autoplayNumber ?? 5;
  let isInit = true;
  watchEffect(() => {
    clearInterval(timer);
    const len = props.configs?.[0]?.val?.length;

    if (props.autoplay && props.option && len > 5) {
      const optionCopy = cloneDeep(props.option);

      const fn = () => {
        if (!isOutside.value) {
          return;
        }
        props.configs.forEach((config) => {
          const { path, val } = config;
          if (typeof val === 'function') set(optionCopy, path, val(visible.value));
          else set(optionCopy, path, val.slice(start, end));
        });
        if (end >= len) {
          start = 0;
          end = props.autoplayNumber ?? 5;
        } else {
          start++;
          end++;
        }
        setOptions(optionCopy, false);
      };

      if (isInit) {
        fn();
        isInit = false;
      }
      timer = setInterval(fn, props.delay ?? 3000);
    } else {
      const optionCopy = cloneDeep(props.option);
      props.configs.forEach((config) => {
        const { path, val } = config;
        if (typeof val === 'function') set(optionCopy, path, val(visible.value));
        else set(optionCopy, path, val);
      });
      setOptions(optionCopy);
    }
  });

  onBeforeUnmount(() => {
    clearInterval(timer);
  });
</script>

<script lang="ts">
  export interface AutoPlayChartConfig {
    path: string;
    val: ((visible: boolean) => any[]) | any;
  }
</script>

<template>
  <div ref="chartRef" :style="{ width, height }" @click="onClick"></div>
  <Modal
    v-model:visible="visible"
    :get-container="getDataViewEle"
    centered
    width="calc(100% - 358px)"
    wrap-class-name="data-view-modal"
    :mask-style="{ 'backdrop-filter': 'blur(6px)', background: 'rgba(0, 0, 0, 0.5)' }"
    :closable="false"
    :footer="null"
  >
    <div class="relative">
      <slot name="modalBodyBefore"></slot>
      <div class="relative" style="top: 12px; left: 20px; color: #fff; font-size: 20px">
        {{ title }}
      </div>
      <div ref="chartRef2" style="width: 100%; height: 500px; padding: 20px"></div>
      <slot name="modalBodyAfter"></slot>
    </div>
  </Modal>
</template>
<style lang="less">
  .data-view-modal {
    .ant-modal-content {
      background-color: rgb(11 24 46);
    }
  }
</style>
