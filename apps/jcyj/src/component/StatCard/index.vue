<script setup lang="ts">
  import { isString } from 'lodash-es';
  import { computed } from 'vue';

  const props = withDefaults(
    defineProps<{
      title: string;
      count: number | string;
      showRightCount?: boolean;
      rightCount?: number | string;
    }>(),
    {
      showRightCount: false,
    },
  );
  const emit = defineEmits(['click']);

  // 每三位数加一个逗号
  function formatNumber(count: number) {
    return count?.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  const formattedCount = computed(() => {
    return isString(props.count) ? props.count : formatNumber(props.count);
  });
</script>

<template>
  <div class="flex h-80px" @click="emit('click')">
    <div class="icon flex justify-center items-center">
      <slot name="icon"></slot>
    </div>
    <div class="content flex flex-col pt-10px">
      <div class="title-wrap flex items-center">
        <div class="title h-2em text-[14px] leading-16px max-w-106px text-#AFD3FF">{{ title }}</div>
        <div v-if="showRightCount" class="text-#AFD3FF h-2em text-[14px] leading-16px">{{
          `(${rightCount}人)`
        }}</div>
      </div>
      <div class="count flex-1">{{ formattedCount }}</div>
    </div>
  </div>
</template>
<style scoped lang="less">
  .content {
    .count {
      font-size: 36px;
      font-weight: bold;
      line-height: 0.9;
      letter-spacing: 0;
      background: linear-gradient(180deg, #fff 0%, #17abff 100%);
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }
</style>
