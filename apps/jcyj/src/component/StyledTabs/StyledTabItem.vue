<script setup lang="ts">
  import { inject } from 'vue';
  import { Icon } from '@ft/internal/components/Icon';
  import { StyledTabKey } from './types';

  interface Props {
    icon?: string;
    title: string;
    tab: string | number;
  }

  defineProps<Props>();

  const activeKey = inject(StyledTabKey);

  function onClick(tab) {
    activeKey && (activeKey.value = tab);
  }
</script>

<template>
  <li
    v-bind="$attrs"
    class="styled-tab-item"
    :class="[`${activeKey === tab ? 'active' : ''}`]"
    @click="onClick(tab)"
  >
    <slot name="icon">
      <Icon v-if="icon" :icon="icon" />
    </slot>
    <span class="styled-tab-item-text text-white"> <slot></slot> </span>
  </li>
</template>
<style lang="less" scoped>
  .styled-tab-item {
    display: flex;
    min-width: 166px;
    background-image: url('/@/assets/images/data-view/layout/sub-tab-bg.png');
    background-size: 100% 120%;

    &.active {
      background-image: url('/@/assets/images/data-view/layout/sub-tab-bg-active.png');

      .styled-tab-item-text {
        background: linear-gradient(180deg, #fff 0%, #d8bb6c 100%);
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }

    .styled-tab-item-text {
      cursor: pointer;
      display: block;
      flex: 1;
      font-size: 16px;
      font-weight: bold;
      font-style: italic;
      background: linear-gradient(180deg, #fff 0%, #17abff 100%);
      -webkit-text-fill-color: transparent;
      background-clip: text;
      transform: translate(20px, -5px);
      padding: 0 2px;
    }
  }
</style>
