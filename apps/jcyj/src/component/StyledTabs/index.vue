<script setup lang="ts">
  import { computed, provide } from 'vue';
  import { StyledTabKey } from './types';

  interface Props {
    activeKey: string;
  }

  const props = defineProps<Props>();
  const emit = defineEmits(['update:activeKey', 'change']);

  const modelValue = computed({
    get: () => props.activeKey,
    set: (value) => {
      emit('update:activeKey', value);
      emit('change', value);
    },
  });

  provide(StyledTabKey, modelValue);
</script>

<template>
  <ul class="flex pl-0">
    <slot></slot>
  </ul>
</template>
<style scoped></style>
