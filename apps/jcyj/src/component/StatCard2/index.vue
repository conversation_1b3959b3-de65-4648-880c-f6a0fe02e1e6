<script setup lang="ts">
  withDefaults(
    defineProps<{
      title: string;
      value: number | string;

      showTitleRight?: boolean;
      titleRightlabel?: string;
      subValue?: number | string;

      showCompare?: boolean;
      compareLabel?: string;
      compareValue?: number | string;

      decline?: boolean;
    }>(),
    {
      titleRightlabel: '人数',
      showTitleRight: false,
      showCompare: false,
      compareLabel: '同比',
      decline: false,
    },
  );
</script>

<template>
  <div class="flex h-80px gap-10px">
    <div class="icon flex justify-center items-center">
      <slot name="icon"></slot>
    </div>
    <div class="content flex flex-col justify-around gap-8px">
      <div class="title-wrap flex gap-6px items-center">
        <div class="title text-[14px] leading-16px text-#5A86C2 font-bold font-italic">
          {{ title }}
        </div>
        <div v-if="showTitleRight" class="divider h-3/4 border-l-1 border-[#1F507A]"></div>
        <div v-if="showTitleRight" class="title-right">
          <span class="text-#5A86C2 text-12px"> {{ titleRightlabel }}：</span>
          <span class="font-400 text-[#00A6FF]">{{ subValue }}</span>
        </div>
      </div>
      <div class="value-wrap flex gap-4 items-end">
        <div class="value">{{ value }}</div>
        <div class="flex items-center gap-2" v-if="showCompare">
          <span class="text-#5A86C2 text-12px">{{ compareLabel }}</span>
          <span class="text-16px font-500" :class="[decline ? 'text-[#FF9E37]' : 'text-[#3CE292]']">
            {{ decline ? '-' : '+' }}{{ compareValue }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped lang="less">
  .content {
    .value {
      font-size: 28px;
      font-weight: bold;
      line-height: 0.9;
      letter-spacing: 0;
      background: linear-gradient(180deg, #fff 0%, #17abff 100%);
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }
</style>
