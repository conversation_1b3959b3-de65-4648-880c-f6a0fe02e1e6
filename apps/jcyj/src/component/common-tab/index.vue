<script lang="ts" setup>
  defineProps<{
    title: string;
    type?: number;
  }>();

  const emit = defineEmits(['click-title']);

  function handleClickTitle() {
    emit('click-title');
  }
</script>

<template>
  <div>
    <div
      :class="`type-${type || 1}`"
      class="title-name flex items-center cursor-pointer -translate-x-15px"
      @click="handleClickTitle"
    >
      <div class="flex-1" :title="title">
        {{ title }}
      </div>
    </div>
    <div
      style="
        background: linear-gradient(180deg, rgb(13 60 117 / 40%) 0%, rgb(13 60 117 / 12%) 100%);
      "
    >
      <slot></slot>
    </div>
  </div>
</template>

<style lang="less" scoped>
  .type-1 {
    width: 415px;
    background-image: url('/@/assets/svg/data-view/common-tab-title.svg');
  }

  .type-2 {
    width: 466px;
    background-size: 100% 100% !important;
    background-image: url('/@/assets/svg/data-view/common-tab-title2.svg');
  }

  .type-3 {
    width: 800px;
    background-image: url('/@/assets/svg/data-view/common-tab-title3.svg');
  }

  .type-4 {
    width: 960px;
    background-image: url('/@/assets/svg/data-view/common-tab-title4.svg');
  }

  .title-name {
    line-height: 24px;
    background-size: contain;
    padding-left: 60px;
    padding-bottom: 8px;
    font-size: 20px;
    text-shadow: 1px 1px 1px #0c3b5e;
    color: white;
    height: 44px;
  }
</style>
