<script setup lang="ts">
  import Layout from '/@/layout/content.vue';
  import { onMounted, provide } from 'vue';
  import { chartConfigMap } from './config';
  import Left from './Left/index.vue';
  import Right from './Right/index.vue';
  import ContentTop from './Content/Top.vue';
  import ContentBottom from './Content/Bottom.vue';
  import { useMapStore } from '/@/store/modules/map';
  import { getXqbgrs } from '/@/api/overview';
  provide('chartConfigMap', chartConfigMap);
  const { register } = useMapStore();
  onMounted(() => {
    getXqbgrs().then((res) => {
      register({
        position: 'center-5',
        points: (res || []).map((item) => ({
          divisionName: item.divisionName,
          heatAmount: item.reportPatientCount,
        })) as any,
      });
    });
  });
</script>

<template>
  <Layout>
    <template #left>
      <Left />
    </template>
    <template #content-top>
      <ContentTop />
    </template>
    <template #content-bottom>
      <ContentBottom />
    </template>
    <template #right>
      <Right />
    </template>
  </Layout>
</template>
