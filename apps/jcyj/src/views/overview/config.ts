import type { Ref } from 'vue';
import { computed, reactive, ref, watch } from 'vue';
import type { EChartsOption } from 'echarts';
import { genLzChart } from '/@/helper/chart';
import type { ChartData } from '/@/helper/useChartData';
import { getGqyjcl, getRqfb, getZdypkcqk } from '/@/api/overview';
import { forEach, get, map } from 'lodash-es';

const 各区应急车辆 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
    keySeries2Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
        { path: 'series.2.data', val: chartConfigData.keySeries2Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart([{ name: '应急车辆' }], {
      labelNames: ['应急车辆'],
      valueSuffix: ['辆'],
    }),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getGqyjcl().then((res) => {
        if (switchValue.value === 'key') {
          chartConfigData.areaData = map(get(res, 'lineChartVOList', []), 'x');
          chartConfigData.keySeries0Data = map(get(res, 'lineChartVOList', []), 'yvalue');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '各区应急车辆',
  };
};
const 重点药品库存情况 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
    keySeries2Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
        { path: 'series.2.data', val: chartConfigData.keySeries2Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart([{ name: '药品' }], {
      labelNames: ['药品'],
      valueSuffix: [''],
      gridConfig: {
        left: 60,
        bottom: 72,
      },
    }),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getZdypkcqk().then((res) => {
        if (switchValue.value === 'key') {
          chartConfigData.areaData = map(get(res, 'lineChartVOList', []), 'x');
          chartConfigData.keySeries0Data = map(get(res, 'lineChartVOList', []), 'yvalue');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '重点药品库存情况',
  };
};
const 人群分布 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    keyXAxisData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.keyXAxisData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart([{ name: '男性' }, { name: '女性', iconType: 3, colorType: 2 }], {
      labelNames: ['男性', '女性'],
      valueSuffix: ['人', '人'],
    }),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getRqfb().then((res) => {
        if (switchValue.value === 'key') {
          forEach(res, (item) => {
            if (item.typeName === '男性') {
              chartConfigData.keyXAxisData = map(item.lineChartVOList, 'x');
              chartConfigData.keySeries0Data = map(item.lineChartVOList, 'yvalue');
            } else if (item.typeName === '女性') {
              chartConfigData.keySeries1Data = map(item.lineChartVOList, 'yvalue');
            }
          });
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '人群分布',
  };
};

export const chartConfigMap = {
  各区应急车辆,
  重点药品库存情况,
  人群分布,
};
