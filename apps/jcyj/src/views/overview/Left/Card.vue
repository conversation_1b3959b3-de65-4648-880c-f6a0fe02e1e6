<script setup lang="ts">
  import type { Ref } from 'vue';
  import { onMounted, ref } from 'vue';
  import { chartHeight, chartWidth, genGrid } from '/@/helper/chart';
  import CommonTab from '/@/component/common-tab/index.vue';
  import { useECharts } from '@ft/internal/hooks/web/useECharts';
  import { graphic } from 'echarts';
  import { cloneDeep, forEach, get, map } from 'lodash-es';
  import { getYlfhzykcccqk } from '/@/api/overview';
  import Switch from '/@/component/switch/index.vue';
  const chartRef = ref<HTMLDivElement | null>(null);
  const chartRef2 = ref<HTMLDivElement | null>(null);
  const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
  const { setOptions: setOptions2 } = useECharts(chartRef2 as Ref<HTMLDivElement>);

  onMounted(() => {
    const chartConfigData = {} as any;
    const getValue = (name) => {
      const idx = chartConfigData.keyIndicatorData.findIndex((item) => item.name === name);
      if (idx > -1) {
        return chartConfigData.keySeries0Data[idx];
      }
      const idx2 = chartConfigData.key2IndicatorData.findIndex((item) => item.name === name);
      if (idx2 > -1) {
        return chartConfigData.keySeries0Data[idx2];
      }
      return 0;
    };
    getYlfhzykcccqk()
      .then((val) => {
        forEach(val, (res) => {
          if (get(res, 'typeName') === '防护用品') {
            chartConfigData.keySeries0Data = map(get(res, 'lineChartVOList', []), 'yvalue');
            const temp = Math.max(...chartConfigData.keySeries0Data);
            const max = temp + temp * 0.1;
            chartConfigData.keyIndicatorData = map(get(res, 'lineChartVOList', []), (item) => ({
              name: get(item, 'x'),
              max,
            }));
          } else if (get(res, 'typeName') === '消杀产品') {
            chartConfigData.key2Series0Data = map(get(res, 'lineChartVOList', []), 'yvalue');
            const temp = Math.max(...chartConfigData.key2Series0Data);
            const max = temp + temp * 0.3;
            chartConfigData.key2IndicatorData = map(get(res, 'lineChartVOList', []), (item) => ({
              name: get(item, 'x'),
              max,
            }));
          }
        });
      })
      .finally(() => {
        const chartOption = {
          grid: genGrid(),
          radar: {
            radius: '55%',
            center: ['50%', '52%'],
            indicator: [],
            splitArea: {
              areaStyle: {
                color: ['transparent'],
              },
            },
            splitLine: {
              lineStyle: {
                color: '#217EE3',
              },
            },
            axisLine: {
              lineStyle: {
                color: '#217EE3',
              },
            },
            axisName: {
              position: 'outside',
              formatter: (name) => {
                return [`{a|${name}}`, `{b|${getValue(name)}}`].join('\n');
              },
              rich: {
                a: {
                  color: 'white',
                  fontSize: 14,
                  lineHeight: 24,
                },
                b: {
                  color: '#00A6FF',
                  fontSize: 16,
                  fontWeight: 'bold',
                },
              },
            } as any,
            nameGap: 10,
          },
          series: [
            {
              type: 'radar',
              animation: false,
              itemStyle: {
                color: '#00A6FF',
                width: 1,
              } as any,
              areaStyle: {
                color: new graphic.LinearGradient(0, 0, 1, 1, [
                  { offset: 1, color: '#00A6FF' },
                  { offset: 0, color: '#ADE2FF' },
                ]),
              },
              data: [
                {
                  value: [],
                  itemStyle: {
                    opacity: 0,
                  },
                },
              ],
            },
          ],
        } as any;
        const copy = cloneDeep(chartOption);
        copy.radar.indicator = chartConfigData.keyIndicatorData;
        copy.series[0].data[0].value = chartConfigData.keySeries0Data;
        setOptions(copy);
        const copy2 = cloneDeep(chartOption);
        copy2.radar.indicator = chartConfigData.key2IndicatorData;
        copy2.series[0].data[0].value = chartConfigData.key2Series0Data;
        setOptions2(copy2);
      });
  });
</script>

<template>
  <CommonTab title="医疗防护资源库存存储情况" class="relative">
    <div class="p-4 absolute">
      <Switch :options="[{ label: '防护用品类', key: '1' }]" :model-value="'1'" />
    </div>
    <div ref="chartRef" class="pt-6 mb-6" :style="{ width: chartWidth, height: '260px' }"> </div>
    <div class="p-4 absolute">
      <Switch :options="[{ label: '消杀产品', key: '1' }]" :model-value="'1'" />
    </div>
    <div ref="chartRef2" class="mt-16" :style="{ width: chartWidth, height: chartHeight }"> </div>
  </CommonTab>
</template>
