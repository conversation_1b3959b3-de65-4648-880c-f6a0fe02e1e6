<script setup lang="ts">
  import type { Ref } from 'vue';
  import { onMounted, ref } from 'vue';
  import { chartHeight, chartWidth, genSeries } from '/@/helper/chart';
  import CommonTab from '/@/component/common-tab/index.vue';
  import { useECharts } from '@ft/internal/hooks/web/useECharts';
  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
  const colors = ['#4977FE', '#00A6FF', '#23F1F1', '#FF9E37', '#3CE292', '#F1E723'];
  const list = ref([
    { label: '提取和扩增设备', value: '80', percent: 30 },
    { label: '呼吸机', value: '120', percent: 30 },
    { label: 'CRRT', value: '60', percent: 30 },
    { label: '基因测序', value: '40', percent: 30 },
    { label: '心电监护仪', value: '80', percent: 30 },
    { label: 'ECMO体外膜肺', value: '150', percent: 30 },
  ]);
  onMounted(() => {
    setOptions({
      series: [
        genSeries({
          preset: '饼图',
          center: ['20%', '50%'],
          radius: ['40%', '50%'],
          data: list.value.map((item, index) => ({ ...item, itemStyle: { color: colors[index] } })),
          padAngle: 4,
          labelLine: {
            show: false,
          },
        }),
      ],
    });
  });
</script>

<template>
  <CommonTab title="应急设备" class="relative">
    <div
      class="w-200px h-254px absolute flex flex-col items-center justify-center right-30px overflow-y-auto gap-1"
    >
      <div
        v-for="(item, idx) in list"
        :key="idx"
        class="flex justify-between w-full bg-[rgba(255,255,255,0.1)] rounded-4px px-10px py-6px text-12px"
      >
        <div>
          <span
            class="w-12px h-8px inline-block rounded-1px mr-1px"
            :style="{ background: colors[idx] }"
          ></span>
          {{ item.label }}
        </div>
        <div>
          <span class="color-[#00A6FF]">{{ item.value }}</span>
          <span class="color-[#23F1F1] pl-2">{{ item.percent }}%</span>
        </div>
      </div>
    </div>
    <div ref="chartRef" :style="{ width: chartWidth, height: chartHeight }"> </div>
  </CommonTab>
</template>
