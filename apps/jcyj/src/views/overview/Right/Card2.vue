<script setup lang="ts">
  import CommonTab from '/@/component/common-tab/index.vue';
  import { chartHeight, chartWidth } from '/@/helper/chart';
  import { getTop10bgjg } from '/@/api/overview';
  import firstImg from '/@/assets/images/data-view/overview/first.png';
  import secondImg from '/@/assets/images/data-view/overview/second.png';
  import thirdImg from '/@/assets/images/data-view/overview/third.png';
  import dot1Url from '/@/assets/svg/data-view/overview/dot-1.svg';
  import dot2Url from '/@/assets/svg/data-view/overview/dot-2.svg';
  import dot3Url from '/@/assets/svg/data-view/overview/dot-3.svg';
  import dot4Url from '/@/assets/svg/data-view/overview/dot-4.svg';
  import { useRequest } from '@ft/request';
  import { computed } from 'vue';

  const imgList = [firstImg, secondImg, thirdImg];
  const backgroundList = [
    'linear-gradient(270deg, #FFA900 0%, rgba(255, 205, 107, 0) 100%)',
    'linear-gradient(180deg, rgba(13, 60, 117, 0.4) 0%, rgba(13, 60, 117, 0.12) 100%)',
    'linear-gradient(270deg, #916CFF 0%, rgba(148, 112, 255, 0) 100%)',
  ].concat(new Array(7).fill('linear-gradient(270deg, #00A6FF 0%, rgba(129, 211, 255, 0) 100%)'));
  const dotList = [dot1Url, dot2Url, dot3Url].concat(new Array(7).fill(dot4Url));
  const { data } = useRequest(getTop10bgjg);
  const list = computed(() =>
    (data.value || [])
      .sort((a, b) => b.ratio - a.ratio)
      .map((item, idx) => ({
        label: item.reportHospitalName,
        value: parseFloat(item.ratio),
        sort: idx,
      })),
  );
</script>

<template>
  <CommonTab title="报告机构top10">
    <div :style="{ width: chartWidth, height: chartHeight }">
      <div class="flex justify-between color-[#AFD3FF] px-2 pt-2.5 text-14px gap-3">
        <div class="flex flex-col w-50%">
          <div v-for="(item, key) in list.slice(0, 5)" :key="key" class="mb-2">
            <div class="flex justify-between mb-2px">
              <div>
                <img v-if="key < 3" :src="imgList[key]" alt="" class="w-14px h-16px" />
                <span
                  class="w-14px h-16px inline-block color-[#00A6FF] font-bold text-right"
                  v-else
                >
                  {{ item.sort + 1 }}
                </span>
                <span class="pl-1">{{ item.label }}</span>
              </div>
              <div>{{ item.value }}%</div>
            </div>
            <div class="progress-wrapper flex items-center justify-between">
              <div class="progress-bar flex items-center">
                <div
                  class="active-progress-bar"
                  :style="{
                    width: `${item.value}%`,
                    background: backgroundList[item.sort],
                  }"
                ></div>
                <img
                  :src="dotList[item.sort]"
                  alt=""
                  class="w-14px h-14px"
                  style="margin-left: -2px; margin-bottom: 1px"
                />
              </div>
            </div>
          </div>
        </div>
        <div class="flex flex-col w-50%">
          <div v-for="(item, key) in list.slice(5)" :key="key" class="mb-2">
            <div class="flex justify-between mb-2px">
              <div>
                <img v-if="item.sort < 3" :src="imgList[item.sort]" alt="" class="w-14px h-16px" />
                <span
                  class="w-14px h-16px inline-block color-[#00A6FF] font-bold text-right"
                  v-else
                >
                  {{ item.sort + 1 }}
                </span>
                <span class="pl-1">{{ item.label }}</span>
              </div>
              <div>{{ item.value }}%</div>
            </div>
            <div class="progress-wrapper flex items-center justify-between">
              <div class="progress-bar flex items-center">
                <div
                  class="active-progress-bar"
                  :style="{
                    width: `${item.value}%`,
                    background: backgroundList[item.sort],
                  }"
                ></div>
                <img
                  :src="dotList[item.sort]"
                  alt=""
                  class="w-14px h-14px"
                  style="margin-left: -2px; margin-bottom: 1px"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </CommonTab>
</template>
<style lang="less" scoped>
  .progress-wrapper {
    width: calc(100% - 16px);
    margin-left: 16px;
    height: 18px;

    .progress-bar {
      background: #264272;
      width: 100%;
      height: 2px;

      .active-progress-bar {
        height: 2px;
      }
    }
  }
</style>
