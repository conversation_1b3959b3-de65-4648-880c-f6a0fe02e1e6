<script setup lang="ts">
  import { onBeforeUnmount, onMounted, ref } from 'vue';
  import CommonTab from '/@/component/common-tab/index.vue';
  import { chartHeight, chartWidth } from '/@/helper/chart';
  import { getCrbssbgqk } from '/@/api/overview';
  import { useRequest } from '@ft/request';
  import { useMouseInElement } from '@vueuse/core';
  const { data: list } = useRequest(getCrbssbgqk);
  const listRef = ref<HTMLDivElement>();
  const { isOutside } = useMouseInElement(listRef);
  let timer;
  onMounted(() => {
    if (listRef.value) {
      timer = setInterval(() => {
        const listRefDom = listRef.value;
        if (listRefDom && isOutside.value) {
          listRefDom.scrollTop += 48;
          if (listRefDom.scrollTop + listRefDom.clientHeight >= listRefDom.scrollHeight) {
            listRefDom.scrollTop = 0;
          }
        }
      }, 1000);
    }
  });
  onBeforeUnmount(() => {
    clearInterval(timer);
  });
</script>
<template>
  <CommonTab title="传染病实时报告情况">
    <div class="p-2 !pb-10px" :style="{ width: chartWidth, height: chartHeight }">
      <div ref="listRef" class="wrapper">
        <div v-for="(item, idx) in list" :key="idx">
          <div class="truncate w-50px">{{ item.patientName }}</div>
          <div class="truncate w-20px">{{ item.patientSex }}</div>
          <div class="truncate w-30px">{{ item.patientAge }}</div>
          <div class="truncate w-110px mr-2">{{ item.reportDate }}</div>
          <div class="truncate w-120px">{{ item.reportHospitalName }}</div>
          <div class="truncate w-80px">{{ item.infectionName }}</div>
        </div>
      </div>
    </div>
  </CommonTab>
</template>

<style lang="scss" scoped>
  .wrapper {
    height: 100%;
    width: 100%;
    overflow-y: hidden;
    font-size: 16px;
    color: #afd3ff;
    & > div {
      display: flex;
      padding: 0 8px;
      height: 48px;
      line-height: 48px;
    }
    & > div:nth-child(2n) {
      background-color: rgba(255, 255, 255, 0.08);
    }
  }
</style>
