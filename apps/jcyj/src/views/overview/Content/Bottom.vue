<script setup lang="ts">
  import CommonTabChart from '/@/component/common-tab-chart/index.vue';
  import CommonTab from '/@/component/common-tab/index.vue';
  import { Icon } from '@ft/internal/components/Icon';
  import { getFbsw } from '/@/api/overview';
  import { useRequest } from '@ft/request';
  import dayjs from 'dayjs';
  const { data } = useRequest(getFbsw);
  const now = dayjs();
  const cycleText = now.startOf('year').format('YYYY-MM-DD') + '至' + now.format('YYYY-MM-DD');
</script>

<template>
  <div class="flex justify-between mb-58px">
    <CommonTabChart chartKey="重点药品库存情况" width="466px" :type="2" />
    <CommonTab title="发病顺位" width="466px" :type="2">
      <div class="w-466px px-12px">
        <div class="pt-12px pb-12px color-[#AFD3FF] text-14px flex items-center justify-end">
          <Icon :size="16" icon="overview-calendar|svg" />
          <span class="leading-16px pl-1">统计周期: {{ cycleText }}</span>
        </div>
        <div class="a-table-header-container">
          <table>
            <colgroup>
              <col style="width: 80px" />
              <col style="width: 160px" />
              <col style="width: 100px" />
              <col style="width: 100px" />
            </colgroup>
            <thead>
              <tr>
                <th> 序号 </th>
                <th> 病种 </th>
                <th> 发病人数 </th>
                <th> 死亡人数 </th>
              </tr>
            </thead>
          </table>
        </div>
        <div class="a-table-body-container">
          <table>
            <colgroup>
              <col style="width: 80px" />
              <col style="width: 160px" />
              <col style="width: 100px" />
              <col style="width: 100px" />
            </colgroup>
            <tbody>
              <tr v-for="(item, idx) in data" :key="idx">
                <td>{{ idx + 1 }}</td>
                <td>{{ item.diseaseName }}</td>
                <td>{{ item.incidencePatientCount }}</td>
                <td>{{ item.deathPatientCount }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </CommonTab>
  </div>
</template>
<style lang="less" scoped>
  table {
    color: #afd3ff;
    font-size: 14px;
    table-layout: fixed;

    th {
      text-align: left;
      padding: 8px 12px;
      color: #fff;
      background: rgb(255 255 255 / 8%);
    }

    td {
      padding: 8px 12px;
    }

    tr:nth-child(2n) {
      background: rgb(255 255 255 / 8%);
    }
  }

  .a-table-body-container {
    max-height: 160px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }
</style>
