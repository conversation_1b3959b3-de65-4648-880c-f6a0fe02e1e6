<script setup lang="ts">
  import { computed, nextTick, reactive, ref, watch } from 'vue';
  import ViewModal from '/@/component/view-modal/index.vue';
  import { Icon } from '@ft/internal/components/Icon';
  import { Col as AntCol, Form, Input, Pagination, RangePicker, Row } from 'ant-design-vue';
  import { Button } from '@ft/internal/components/Button';
  import ApiSelect from '@ft/internal/components/Form/src/components/ApiSelect.vue';
  import { getReportPatientList } from '/@/api/overview';
  import { getAdministrativeList } from '@ft/internal/api';
  import { useRequest } from '@ft/request';
  import { cloneDeep, omit } from 'lodash-es';
  import StyledTabs from '/@/component/StyledTabs/index.vue';
  import StyledTabItem from '/@/component/StyledTabs/StyledTabItem.vue';
  import dayjs from 'dayjs';

  const defaultFormState = {
    reportTime: [dayjs().startOf('year').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')] as [
      string,
      string,
    ],
    infectiousDiseaseName: '',
    patientName: '',
    pageNum: 1,
    pageSize: 10,
    divisionCode: undefined,
    reportHospitalName: '',
  };

  const FormItem = Form.Item;
  const props = withDefaults(defineProps<{ visible: boolean }>(), {
    visible: false,
  });

  const emit = defineEmits<{
    (e: 'update:visible', value: boolean): void;
  }>();

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => {
      emit('update:visible', value);
    },
  });
  const activeKey = ref('0');
  const tabData = {
    0: '传染病报告人数列表',
    1: '甲类传染病报告人数列表',
    2: '乙类传染病报告人数列表',
    3: '丙类传染病报告人数列表',
  };
  const formState = ref<{
    reportTime?: [string, string];
    startTime?: string;
    endTime?: string;
    infectiousDiseaseName: string;
    patientName?: string;
    pageNum?: number;
    pageSize?: number;
    divisionCode?: string;
    reportHospitalName?: string;
  }>(cloneDeep(defaultFormState));

  const searchState = ref<{
    reportTime?: [string, string];
    infectiousDiseaseName: string;
    patientName?: string;
    divisionCode?: string;
    reportHospitalName?: string;
  }>(cloneDeep(defaultFormState));

  const pageInfo = reactive({
    total: 0,
    pageSize: 10,
    current: 1,
  });
  const tableListData = ref<any[]>([]);
  watch(modalVisible, (val) => {
    if (!val) {
      activeKey.value = '0';
      pageInfo.current = 1;
      formState.value = cloneDeep(defaultFormState);
      searchState.value = cloneDeep(defaultFormState);
    }
  });
  const { runAsync: getTableListRunAsync } = useRequest(
    () =>
      getReportPatientList({
        ...omit(formState.value, 'reportTime'),
        pageNum: pageInfo.current,
        pageSize: pageInfo.pageSize,
        infectionCategoryCode: activeKey.value === '0' ? '' : +activeKey.value,
      }),
    {
      onBefore: () => {
        formState.value.startTime = formState.value.reportTime?.[0];
        formState.value.endTime = formState.value.reportTime?.[1];
        return true;
      },
      onSuccess: (res: any) => {
        tableListData.value = res.list;
        pageInfo.total = res.total;
      },
      ready: modalVisible,
      refreshDeps: [modalVisible],
    },
  );
  const onSearch = () => {
    formState.value = {
      ...cloneDeep(searchState.value),
      pageNum: 1,
      pageSize: pageInfo.pageSize,
    };
    pageInfo.current = 1;
    getTableListRunAsync();
  };
  const handleChangeTab = () => {
    searchState.value = cloneDeep(defaultFormState);
    formState.value = cloneDeep(defaultFormState);
    pageInfo.current = 1;
    nextTick(onSearch);
  };
  function getPopupContainer() {
    return document.querySelector('main.ant-layout-content') as HTMLElement;
  }
</script>

<template>
  <ViewModal width="90%" title="报告人数列表" v-model:visible="modalVisible">
    <div class="py-4 pl-2 pr-4">
      <StyledTabs v-model:active-key="activeKey" @change="handleChangeTab">
        <StyledTabItem title="传染病" tab="0"> 传染病 </StyledTabItem>
        <StyledTabItem title="甲类传染病" tab="1"> 甲类传染病 </StyledTabItem>
        <StyledTabItem title="乙类传染病" tab="2"> 乙类传染病 </StyledTabItem>
        <StyledTabItem title="丙类传染病" tab="3"> 丙类传染病 </StyledTabItem>
      </StyledTabs>
      <div
        class="flex flex-col content h-[780px] rounded-12px border-1 border-[#0C2A4D] bg-[#040B15] p-5"
      >
        <div class="title text-shadow-[1px_1px_1px_#0C3B5E]">
          <Icon class="scale-350" :size="16" icon="modal-title-decoration|svg" />
          <span class="ml-3 text-[20px] leading-24px">{{ tabData[activeKey] }}</span>
        </div>
        <div class="flex-1 h-420px pt-4 flex flex-col">
          <Form :model="searchState">
            <Row :gutter="24">
              <AntCol span="6">
                <FormItem label="上报时间" name="reportTime">
                  <RangePicker
                    style="width: 100%"
                    v-model:value="searchState.reportTime"
                    valueFormat="YYYY-MM-DD"
                    :get-popup-container="getPopupContainer"
                  />
                </FormItem>
              </AntCol>
              <AntCol span="6">
                <FormItem label="传染病诊断" name="infectiousDiseaseName">
                  <Input
                    v-model:value="searchState.infectiousDiseaseName"
                    placeholder="请输入"
                    allowClear
                  />
                </FormItem>
              </AntCol>
              <AntCol span="6">
                <FormItem label="患者姓名" name="patientName">
                  <Input v-model:value="searchState.patientName" placeholder="请输入" allowClear />
                </FormItem>
              </AntCol>
              <AntCol span="6">
                <FormItem label="所属辖区" name="divisionCode">
                  <ApiSelect
                    :api="getAdministrativeList"
                    placeholder="请选择"
                    label-field="name"
                    value-field="code"
                    v-model:value="searchState.divisionCode"
                    :get-popup-container="getPopupContainer"
                    allowClear
                  />
                </FormItem>
              </AntCol>
            </Row>
            <Row :gutter="24">
              <AntCol span="6">
                <FormItem label="上报机构" name="reportHospitalName">
                  <Input
                    v-model:value="searchState.reportHospitalName"
                    placeholder="请输入"
                    allowClear
                  />
                </FormItem>
              </AntCol>
              <AntCol span="6" offset="12" style="text-align: right">
                <FormItem>
                  <Button
                    @click="onSearch"
                    class="w-20 !border-0 !text-sm !font-bold !text-white btn"
                  >
                    搜索
                  </Button>
                </FormItem>
              </AntCol>
            </Row>
          </Form>
          <div class="flex-1 h-300px">
            <div class="a-table-header-container">
              <table>
                <colgroup>
                  <col style="width: 5%" />
                  <col style="width: 10%" />
                  <col style="width: 5%" />
                  <col style="width: 5%" />
                  <col style="width: 15%" />
                  <col style="width: 15%" />
                  <col style="width: 15%" />
                  <col style="width: 15%" />
                  <col style="width: 15%" />
                </colgroup>
                <thead>
                  <tr>
                    <th>序号</th>
                    <th>患者姓名</th>
                    <th>性别</th>
                    <th>年龄</th>
                    <th>身份证号</th>
                    <th>传染病分类</th>
                    <th>传染病诊断</th>
                    <th>上报时间</th>
                    <th>上报机构</th>
                  </tr>
                </thead>
              </table>
            </div>
            <div class="a-table-body-container2">
              <table>
                <colgroup>
                  <col style="width: 5%" />
                  <col style="width: 10%" />
                  <col style="width: 5%" />
                  <col style="width: 5%" />
                  <col style="width: 15%" />
                  <col style="width: 15%" />
                  <col style="width: 15%" />
                  <col style="width: 15%" />
                  <col style="width: 15%" />
                </colgroup>
                <tbody>
                  <tr v-for="(item, index) in tableListData" :key="index">
                    <td>{{ index + 1 }}</td>
                    <td>{{ item.patientName }}</td>
                    <td>{{ item.patientSex }}</td>
                    <td>{{ item.patientAge }}</td>
                    <td>{{ item.idCardNo }}</td>
                    <td>{{ item.infectionCategoryName }}</td>
                    <td>{{ item.infectionName }}</td>
                    <td>{{ item.reportDate }}</td>
                    <td>{{ item.reportHospitalName }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <div class="flex justify-end mt-5" v-if="pageInfo.total > 0">
            <Pagination
              class="mt-5"
              :page-size="pageInfo.pageSize"
              :total="pageInfo.total"
              :show-size-changer="false"
              v-model:current="pageInfo.current"
              @change="getTableListRunAsync"
            />
          </div>
        </div>
      </div>
    </div>
  </ViewModal>
</template>
<style scoped lang="less">
  table {
    color: #afd3ff;
    font-size: 14px;
    table-layout: fixed;
    width: 100%;

    th {
      height: 32px !important;
      text-align: left;
      padding: 8px 12px;
      color: #fff;
      background: rgb(255 255 255 / 8%);
    }

    td {
      height: 32px !important;
      padding: 8px 12px;
    }

    tr:nth-child(2n) {
      background: rgb(255 255 255 / 8%);
    }
  }

  .a-table-body-container {
    max-height: 280px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .a-table-body-container2 {
    max-height: 595px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  :deep {
    .ant-form-item-label > label {
      color: #afd3ff !important;
    }
  }

  .btn {
    background: linear-gradient(180deg, #1590ea 0%, rgb(21 144 234 / 31%) 100%);
  }
</style>
