<script setup lang="ts">
  import { Icon } from '@ft/internal/components/Icon';
  import { computed, ref } from 'vue';
  import { get } from 'lodash-es';
  import StatCard from '/@/component/StatCard/index.vue';
  import { useRequest } from '@ft/request';
  import { getTopCard } from '/@/api/overview';
  import DeathCountModal from './deathCountModal.vue';
  import ReportCountModal from './reportCountModal.vue';

  const visible = ref(false);
  const visible1 = ref(false);
  const { data } = useRequest(getTopCard);
  const reportNum = computed(() =>
    get(data.value, 'totalReportPatientCount', 0).toString().split(''),
  );
  const deathNum = computed(() =>
    get(data.value, 'totalDeathPatientCount', 0).toString().split(''),
  );
  const topList = computed(() => [
    {
      title: '甲类报告人数',
      count: get(data.value, 'reportPatietnCountForA', 0),
      icon: 'overview-num-j|svg',
    },
    {
      title: '乙类报告人数',
      count: get(data.value, 'reportPatietnCountForB', 0),
      icon: 'overview-num-y|svg',
    },
    {
      title: '丙类报告人数',
      count: get(data.value, 'reportPatietnCountForC', 0),
      icon: 'overview-num-b|svg',
    },
  ]);
  const topList2 = computed(() => [
    {
      title: '甲类死亡人数',
      count: get(data.value, 'deathPatientCountForA', 0),
      icon: 'overview-death-j|svg',
      type: 1,
    },
    {
      title: '乙类死亡人数',
      count: get(data.value, 'deathPatientCountForB', 0),
      icon: 'overview-death-y|svg',
      type: 2,
    },
    {
      title: '丙类死亡人数',
      count: get(data.value, 'deathPatientCountForC', 0),
      icon: 'overview-death-b|svg',
      type: 3,
    },
  ]);
  const handleClick = () => {
    visible.value = true;
  };
</script>

<template>
  <div>
    <div class="flex justify-center wrapper h-100px py-2">
      <div class="flex num-wrapper" @click="visible1 = true">
        <Icon :size="80" icon="overview-num|svg" />
        <div>
          <div class="color-#AFD3FF text-16px">报告人数</div>
          <div class="flex gap-1 mt-1">
            <div v-for="item in reportNum" :key="item" class="num-item">{{ item }}</div>
          </div>
        </div>
      </div>
      <template v-for="(item, idx) in topList" :key="item.title">
        <div v-if="idx" class="text-20px color-#5A86C2 pt-24px"> + </div>
        <StatCard class="flex-1 justify-center" :title="item.title" :count="item.count">
          <template #icon> <Icon :size="80" :icon="item.icon" /></template>
        </StatCard>
      </template>
    </div>
    <div class="flex justify-center items-center mt-4 wrapper h-100px py-2">
      <div class="flex num-wrapper" @click="handleClick()">
        <Icon :size="80" icon="overview-death|svg" />
        <div>
          <div class="color-#AFD3FF text-16px">死亡人数</div>
          <div class="flex gap-1 mt-1">
            <div v-for="item in deathNum" :key="item" class="num-item">{{ item }}</div>
          </div>
        </div>
      </div>
      <template v-for="(item, idx) in topList2" :key="item.title">
        <div v-if="idx" class="text-20px color-#5A86C2"> + </div>
        <StatCard class="flex-1 justify-center" :title="item.title" :count="item.count">
          <template #icon> <Icon :size="80" :icon="item.icon" /></template>
        </StatCard>
      </template>
    </div>
    <DeathCountModal v-model:visible="visible" :data="0" />
    <ReportCountModal v-model:visible="visible1" />
  </div>
</template>
<style scoped>
  .wrapper {
    border: 1px solid;
    background: linear-gradient(90deg, rgb(33 128 206 / 4%) 2%, rgb(8 58 94 / 0%) 100%);
    border-image: radial-gradient(66% 66% at 50% 33%, #7bb1ea 0%, rgb(67 73 79 / 11%) 100%) 1;
  }

  .num-wrapper {
    background: rgb(33 128 206 / 12%);
    box-sizing: border-box;
    border: 1px solid;
    border-image: radial-gradient(66% 66% at 50% 33%, #7bb1ea 0%, rgb(67 73 79 / 11%) 100%) 1;
    padding: 4px 24px 4px 12px;
    margin-left: 4px;
  }

  .num-item {
    background-size: 30px 38px;
    background-image: url('/@/assets/svg/data-view/overview/stat-bg.svg');
    width: 30px;
    height: 38px;
    font-size: 28px;
    font-weight: bold;
    line-height: 38px;
    text-align: center;
    letter-spacing: 0;
    color: #fff;
  }
</style>
