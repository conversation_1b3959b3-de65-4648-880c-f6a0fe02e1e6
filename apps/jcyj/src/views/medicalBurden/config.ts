import type { Ref } from 'vue';
import { computed, reactive, ref, watch } from 'vue';
import type { EChartsOption } from 'echarts';
import {
  genGrid,
  genLegend,
  genLzChart,
  genLzYChart,
  genSeries,
  genXAxis,
  genYAxis,
  legendIcon2,
} from '/@/helper/chart';
import {
  getAverageHospitalDays,
  getDoctorPatientRate,
  getDoctorReceptionVolume,
  getInfectionDeathRate,
  getInfectiousDiseaseRecords,
  getNursePatientRate,
  getOutpatientDiseases,
  getSurgicalGradeData,
} from '/@/api/medicalBurden';
import type { ChartData } from '/@/helper/useChartData';
import { forEach, map } from 'lodash-es';

const 发热门诊疾病就诊量 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    keyYAxisData: [] as any[],
    keySeries0Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.keyYAxisData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(genLzChart([{ name: '发热门诊疾病就诊量' }]));

  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getOutpatientDiseases().then((res) => {
        if (switchValue.value === 'key') {
          chartConfigData.keyYAxisData = map(res, 'x');
          chartConfigData.keySeries0Data = map(res, 'yvalue');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '发热门诊疾病就诊量',
  };
};

const 感染科门诊各医生接诊量 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    keyXAxisData: [] as any[],
    keySeries0Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.keyXAxisData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive({
    grid: genGrid({ right: 20 }),
    legend: genLegend({
      preset: 'show',
      data: [{ name: '就诊人数', icon: legendIcon2 }],
    }),
    xAxis: [
      genXAxis({
        features: ['breakLabel'],
      }),
    ],
    yAxis: [genYAxis({})],
    series: [
      genSeries({
        preset: '折线图',
        name: '就诊人数',
        customColor: '#23F1F1',
      }),
    ],
  });

  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getDoctorReceptionVolume().then((res) => {
        if (switchValue.value === 'key') {
          chartConfigData.keyXAxisData = map(res, 'x');
          chartConfigData.keySeries0Data = map(res, 'yvalue');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '感染科门诊各医生接诊量',
  };
};

const 感染科各类传染病住院平均住院日 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    keyYAxisData: [] as any[],
    keySeries0Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'yAxis.0.data', val: chartConfigData.keyYAxisData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(genLzYChart({ name: '平均住院日' }));
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getAverageHospitalDays().then((res) => {
        if (switchValue.value === 'key') {
          chartConfigData.keyYAxisData = map(res, 'x');
          chartConfigData.keySeries0Data = map(res, 'yvalue');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '感染科各类传染病住院平均住院日',
  };
};

const 感染科门诊疾病 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    keyYAxisData: [] as any[],
    keySeries0Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'yAxis.0.data', val: chartConfigData.keyYAxisData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(genLzYChart({ name: '就诊人数' }));
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getInfectiousDiseaseRecords().then((res) => {
        if (switchValue.value === 'key') {
          chartConfigData.keyYAxisData = map(res, 'x');
          chartConfigData.keySeries0Data = map(res, 'yvalue');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '感染科门诊疾病',
  };
};
const 感染科医患比统计 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
    keySeries2Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart([{ name: '医' }, { name: '患', iconType: 3, colorType: 2 }], {
      labelNames: ['医', '患'],
      valueSuffix: ['人', '人'],
    }),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getDoctorPatientRate().then((res) => {
        if (switchValue.value === 'key') {
          console.log(275, res);
          forEach(res, (item) => {
            console.log(item, 'item');
            if (item.type === 1) {
              chartConfigData.areaData = map(item.lineChartVOList, 'x');
              chartConfigData.keySeries0Data = map(item.lineChartVOList, 'yvalue');
            } else if (item.type === 2) {
              chartConfigData.keySeries1Data = map(item.lineChartVOList, 'yvalue');
            }
          });
          console.log(chartConfigData, 'chartConfigData');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '感染科医患比统计',
  };
};

const 感染科护患比统计 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
    keySeries2Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart([{ name: '护' }, { name: '患', iconType: 3, colorType: 2 }], {
      labelNames: ['护', '患'],
      valueSuffix: ['人', '人'],
    }),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getNursePatientRate().then((res) => {
        if (switchValue.value === 'key') {
          forEach(res, (item) => {
            if (item.type === 1) {
              chartConfigData.areaData = map(item.lineChartVOList, 'x');
              chartConfigData.keySeries0Data = map(item.lineChartVOList, 'yvalue');
            } else if (item.type === 2) {
              chartConfigData.keySeries1Data = map(item.lineChartVOList, 'yvalue');
            }
          });
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '感染科护患比统计',
  };
};

const 感染科手术分级管理数据 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart([{ name: '人数' }], {
      labelNames: ['人数'],
      valueSuffix: ['人'],
    }),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getSurgicalGradeData().then((res) => {
        if (switchValue.value === 'key') {
          chartConfigData.areaData = map(res, 'x');
          chartConfigData.keySeries0Data = map(res, 'yvalue');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '感染科手术分级管理数据',
  };
};

const 传染病死亡率 = (_year: Ref<string>): ChartData => {
  const switchOptions = [
    { label: '辖区', key: 'area' },
    { label: '年度', key: 'year' },
  ];

  const switchValue = ref('area');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    areaSeries0Data: [] as any[],
    areaSeries1Data: [] as any[],
    yearData: [] as any[],
    yearSeries0Data: [] as any[],
    yearSeries1Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'area') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.areaSeries0Data },
        { path: 'series.1.data', val: chartConfigData.areaSeries1Data },
      ];
    } else {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.yearData },
        { path: 'series.0.data', val: chartConfigData.yearSeries0Data },
        { path: 'series.1.data', val: chartConfigData.yearSeries1Data },
      ];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart([{ name: '死亡人数' }, { name: '死亡率', iconType: 2, isLine: true }], {
      labelNames: ['死亡人数', '死亡率'],
      valueSuffix: ['人', '%'],
    }),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getInfectionDeathRate({
        type: switchValue.value === 'area' ? '1' : '2',
        year: new Date().getFullYear(),
      }).then((res) => {
        if (switchValue.value === 'area') {
          chartConfigData.areaData = map(res, 'x');
          chartConfigData.areaSeries0Data = map(res, 'yvalue');
          chartConfigData.areaSeries1Data = map(res, 'y');
        } else {
          chartConfigData.yearData = map(res, 'x');
          chartConfigData.yearSeries0Data = map(res, 'yvalue');
          chartConfigData.yearSeries1Data = map(res, 'y');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    switchOptions,
    title: '传染病死亡率',
  };
};

export const chartConfigMap = {
  发热门诊疾病就诊量,
  感染科门诊各医生接诊量,
  感染科各类传染病住院平均住院日,
  感染科门诊疾病,
  感染科医患比统计,
  感染科护患比统计,
  感染科手术分级管理数据,
  传染病死亡率,
};
