<script setup lang="ts">
  import StatCard from '/@/component/StatCard/index.vue';
  import { Icon } from '@ft/internal/components/Icon';
  import { getMedicalBurdenNum } from '/@/api/medicalBurden';
  import { useRequest } from '@ft/request';
  import { computed } from 'vue';
  import { find } from 'lodash-es';

  const { data } = useRequest(getMedicalBurdenNum);

  const topList = computed(() => [
    {
      title: '发热门诊就诊量',
      count: find(data.value, { type: 1 })?.value || 0,
      icon: 'medicalBurden-1|svg',
    },
    {
      title: '感染科门诊接诊总人次',
      count: find(data.value, { type: 2 })?.value || 0,
      icon: 'medicalBurden-2|svg',
    },
    {
      title: '感染科住院总人次',
      count: find(data.value, { type: 3 })?.value || 0,
      icon: 'medicalBurden-3|svg',
    },
    {
      title: '感染科各类传染病住院总人次',
      count: find(data.value, { type: 4 })?.value || 0,
      icon: 'medicalBurden-4|svg',
    },
  ]);
</script>

<template>
  <div class="flex justify-center">
    <StatCard
      class="flex-1 justify-center"
      v-for="item in topList"
      :key="item.title"
      :title="item.title"
      :count="item.count"
    >
      <template #icon> <Icon :size="80" :icon="item.icon" /></template>
    </StatCard>
  </div>
</template>
<style scoped></style>
