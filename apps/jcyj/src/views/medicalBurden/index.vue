<script setup lang="ts">
  import Layout from '/@/layout/content.vue';
  import CenterTop from './CenterTop.vue';
  import { provide } from 'vue';
  import { chartConfigMap } from './config';
  import CommonTabChart from '/@/component/common-tab-chart/index.vue';
  import { useMapStore } from '/@/store/modules/map';
  provide('chartConfigMap', chartConfigMap);

  const { register } = useMapStore();
  register({
    position: 'center-2',
  });
</script>

<template>
  <Layout>
    <template #left>
      <div class="flex flex-col gap-20px">
        <CommonTabChart chartKey="发热门诊疾病就诊量" />
        <CommonTabChart chartKey="感染科门诊各医生接诊量" />
        <CommonTabChart chartKey="感染科各类传染病住院平均住院日" />
      </div>
    </template>
    <template #content-top>
      <CenterTop />
    </template>
    <template #content-bottom>
      <div class="flex justify-between mb-58px">
        <CommonTabChart chartKey="感染科手术分级管理数据" width="466px" :type="2" />
        <CommonTabChart chartKey="传染病死亡率" width="466px" :type="2" />
      </div>
    </template>
    <template #right>
      <div class="flex flex-col gap-20px">
        <CommonTabChart chartKey="感染科门诊疾病" />
        <CommonTabChart chartKey="感染科医患比统计" />
        <CommonTabChart chartKey="感染科护患比统计" />
      </div>
    </template>
  </Layout>
</template>
