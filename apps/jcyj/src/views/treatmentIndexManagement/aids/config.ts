import type { Ref } from 'vue';
import { computed, reactive, ref, watch } from 'vue';
import type { EChartsOption } from 'echarts';
import { genLz<PERSON>hart, genLzY<PERSON><PERSON> } from '/@/helper/chart';
import type { ChartData } from '/@/helper/useChartData';
import {
  queryCD4VLCount,
  queryDbdxzCount,
  queryDetectionCount,
  queryDrugCount,
  queryKbdsbCount,
  queryPatientCount,
} from '/@/api/aids';
import { forEach, map } from 'lodash-es';

const 新增治疗基线 = (_year: Ref<string>): ChartData => {
  const switchOptions = [{ label: '-90 - +30天', key: 'key' }];

  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
    keySeries2Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart([{ name: 'CD4检测人数' }, { name: 'VL检测人数', iconType: 4, colorType: 2 }], {
      labelNames: ['CD4检测人数', 'VL检测人数'],
      valueSuffix: ['人', '人'],
    }),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      queryCD4VLCount().then((res) => {
        if (switchValue.value === 'key') {
          forEach(res, (item) => {
            if (item.type === 4) {
              chartConfigData.areaData = map(item.monitorCountList, 'x');
              chartConfigData.keySeries0Data = map(item.monitorCountList, 'yvalue');
            } else if (item.type === 5) {
              chartConfigData.areaData = map(item.monitorCountList, 'x');
              chartConfigData.keySeries1Data = map(item.monitorCountList, 'yvalue');
            }
          });
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    switchOptions,
    updateSwitchValue,
    switchValue,
    title: '新增治疗基线CD4/VL',
  };
};

const 艾滋病检测率监测 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart([{ name: 'CD4检测率' }, { name: 'VL检测率', iconType: 3, colorType: 2 }], {
      y0: {
        percent: true,
      },
      labelNames: ['CD4检测率', 'VL检测率'],
      valueSuffix: ['%', '%'],
    }),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      queryDetectionCount().then((res) => {
        if (switchValue.value === 'key') {
          forEach(res, (item) => {
            if (item.type === 6) {
              chartConfigData.areaData = map(item.monitorCountList, 'x');
              chartConfigData.keySeries0Data = map(item.monitorCountList, 'y');
            } else if (item.type === 7) {
              chartConfigData.areaData = map(item.monitorCountList, 'x');
              chartConfigData.keySeries1Data = map(item.monitorCountList, 'y');
            }
          });
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '艾滋病随访指标检测率监测',
  };
};

const 艾滋病用药顺位 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    keyYAxisData: [] as any[],
    keySeries0Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'yAxis.0.data', val: chartConfigData.keyYAxisData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(genLzYChart({ name: '使用次数' }));
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      queryDrugCount().then((res) => {
        const temp = res.sort((a, b) => a.sort - b.sort);
        const tt = temp.map((item: any) => ({
          label: item.x,
          value: item.yvalue,
        }));
        if (switchValue.value === 'key') {
          chartConfigData.keyYAxisData = map(tt, 'label');
          chartConfigData.keySeries0Data = map(tt, 'value');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '艾滋病用药顺位',
  };
};

const 低病毒血症人数 = (_year: Ref<string>): ChartData => {
  const switchOptions = [
    { label: '抗病毒治疗≥48周（取开药日期）后病毒载量为50-1000COPS/MLI', key: 'key' },
  ];

  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart([{ name: '人数' }], {
      labelNames: ['低病毒血症人数'],
      valueSuffix: ['人'],
    }),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      queryDbdxzCount().then((res) => {
        if (switchValue.value === 'key') {
          chartConfigData.areaData = map(res, 'x');
          chartConfigData.keySeries0Data = map(res, 'yvalue');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    switchOptions,
    updateSwitchValue,
    switchValue,
    title: '低病毒血症人数',
    switchFontSize: '12px',
  };
};

const 抗病毒失败人数 = (_year: Ref<string>): ChartData => {
  const switchOptions = [{ label: '(连续治疗≥48w，最近一次病载>1000COPS/MLI)', key: 'key' }];

  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart([{ name: '人数' }], {
      labelNames: ['抗病毒失败人数'],
      valueSuffix: ['人'],
    }),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      queryKbdsbCount().then((res) => {
        if (switchValue.value === 'key') {
          chartConfigData.areaData = map(res, 'x');
          chartConfigData.keySeries0Data = map(res, 'yvalue');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    switchOptions,
    updateSwitchValue,
    switchValue,
    title: '抗病毒失败人数',
    switchFontSize: '12px',
  };
};

const 艾滋病病人管理统计 = (_year: Ref<string>): ChartData => {
  const switchOptions = [
    { label: '辖区', key: 'area' },
    { label: '年度', key: 'year' },
  ];

  const switchValue = ref('area');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    areaSeries0Data: [] as any[],
    areaSeries1Data: [] as any[],
    areaSeries2Data: [] as any[],
    yearData: [] as any[],
    yearSeries0Data: [] as any[],
    yearSeries1Data: [] as any[],
    yearSeries2Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'area') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.areaSeries0Data },
        { path: 'series.1.data', val: chartConfigData.areaSeries1Data },
        { path: 'series.2.data', val: chartConfigData.areaSeries2Data },
      ];
    } else {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.yearData },
        { path: 'series.0.data', val: chartConfigData.yearSeries0Data },
        { path: 'series.1.data', val: chartConfigData.yearSeries1Data },
        { path: 'series.2.data', val: chartConfigData.yearSeries2Data },
      ];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart(
      [
        { name: '死亡人数' },
        { name: '在治人数', iconType: 3, colorType: 2 },
        { name: '未治人数', iconType: 4, colorType: 3 },
      ],
      {
        labelNames: ['死亡人数', '在治人数', '未治人数'],
        valueSuffix: ['人', '人', '人'],
      },
    ),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      queryPatientCount(switchValue.value === 'area' ? 1 : 2).then((res) => {
        if (switchValue.value === 'area') {
          forEach(res, (item) => {
            if (item.type === 1) {
              chartConfigData.areaData = map(item.monitorCountList, 'x');
              chartConfigData.areaSeries0Data = map(item.monitorCountList, 'yvalue');
            } else if (item.type === 2) {
              chartConfigData.areaData = map(item.monitorCountList, 'x');
              chartConfigData.areaSeries1Data = map(item.monitorCountList, 'yvalue');
            } else if (item.type === 3) {
              chartConfigData.areaData = map(item.monitorCountList, 'x');
              chartConfigData.areaSeries2Data = map(item.monitorCountList, 'yvalue');
            }
          });
        } else {
          forEach(res, (item) => {
            if (item.type === 1) {
              chartConfigData.yearData = map(item.monitorCountList, 'x');
              chartConfigData.yearSeries0Data = map(item.monitorCountList, 'yvalue');
            } else if (item.type === 2) {
              chartConfigData.yearData = map(item.monitorCountList, 'x');
              chartConfigData.yearSeries1Data = map(item.monitorCountList, 'yvalue');
            } else if (item.type === 3) {
              chartConfigData.yearData = map(item.monitorCountList, 'x');
              chartConfigData.yearSeries2Data = map(item.monitorCountList, 'yvalue');
            }
          });
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    switchOptions,
    updateSwitchValue,
    switchValue,
    title: '艾滋病病人管理统计',
  };
};

export const chartConfigMap = {
  新增治疗基线,
  艾滋病检测率监测,
  艾滋病用药顺位,
  低病毒血症人数,
  抗病毒失败人数,
  艾滋病病人管理统计,
};
