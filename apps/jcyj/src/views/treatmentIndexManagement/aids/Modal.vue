<script setup lang="ts">
  import { computed } from 'vue';
  import ViewModal from '/@/component/view-modal/index.vue';
  import { useRequest } from '@ft/request';
  import { queryDrugList } from '/@/api/aids';

  const props = defineProps<{ visible: boolean }>();

  const emit = defineEmits<{
    (e: 'update:visible', value: boolean): void;
  }>();

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => {
      emit('update:visible', value);
    },
  });

  const { data } = useRequest(queryDrugList, {
    ready: modalVisible,
    refreshDeps: [modalVisible],
  });
</script>

<template>
  <ViewModal width="60%" title="用药顺位列表" v-model:visible="modalVisible">
    <div class="py-4 pl-2 pr-4">
      <div class="a-table-header-container">
        <table>
          <colgroup>
            <col style="width: 10%" />
            <col style="width: 30%" />
            <col style="width: 30%" />
            <col style="width: 30%" />
          </colgroup>
          <thead>
            <tr>
              <th> 序号 </th>
              <th> 艾滋病用药方案名称 </th>
              <th> 艾滋病用药方案缩写 </th>
              <th> 用药人次 </th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="a-table-body-container">
        <table>
          <colgroup>
            <col style="width: 10%" />
            <col style="width: 30%" />
            <col style="width: 30%" />
            <col style="width: 30%" />
          </colgroup>
          <tbody>
            <tr v-for="(item, idx) in data" :key="idx">
              <td>{{ idx + 1 }}</td>
              <td>{{ item?.planName }}</td>
              <td>{{ item?.planAbbreviation }}</td>
              <td>{{ item?.drugNum }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </ViewModal>
</template>
<style lang="less" scoped>
  table {
    color: #afd3ff;
    table-layout: fixed;
    font-size: 14px;
    width: 100%;

    th {
      text-align: left;
      padding: 8px 12px;
      color: #fff;
      background: rgb(255 255 255 / 8%);
    }

    td {
      padding: 8px 12px;
    }

    tr:nth-child(2n) {
      background: rgb(255 255 255 / 8%);
    }
  }

  .a-table-body-container {
    max-height: 500px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }
</style>
