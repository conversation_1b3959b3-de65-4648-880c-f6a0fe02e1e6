<script setup lang="ts">
  import Layout from '/@/layout/content.vue';
  import CenterTop from './center/Top.vue';
  import CenterBottom from './center/Bottom.vue';
  import { provide, ref } from 'vue';
  import { chartConfigMap } from './config';
  import CommonTabChart from '/@/component/common-tab-chart/index.vue';
  import { useMapStore } from '/@/store/modules/map';
  import { useRequest } from '@ft/request';
  import { queryCenter } from '/@/api/aids';
  import { map } from 'lodash-es';
  import TopModal from './center/TopModal.vue';
  import Modal from './Modal.vue';
  provide('chartConfigMap', chartConfigMap);
  const { register } = useMapStore();
  const { data } = useRequest(queryCenter, {
    onSuccess(res) {
      register({
        points: map(res.divisionList, (item) => ({
          divisionName: item.divisionName,
          peopleRate: item.peopleRate,
          peopleCount: item.peopleNum,
          areaCode: item.divisionId,
        })) as any,
        onClickMapFn(data) {
          console.log(101, data);
          visible.value = true;
          activeTitle.value = '确诊存活人数';
          areaCode.value = data.areaCode;
        },
      });
    },
  });
  const visible = ref(false);
  const activeTitle = ref('');
  const areaCode = ref('');
  const visible1 = ref(false);
</script>

<template>
  <Layout>
    <template #left>
      <div class="flex flex-col gap-20px">
        <CommonTabChart chartKey="新增治疗基线" />
        <CommonTabChart chartKey="艾滋病检测率监测" />
        <CommonTabChart chartKey="艾滋病用药顺位" @custom-click="() => (visible1 = true)" />
      </div>
      <Modal v-model:visible="visible1" />
    </template>
    <template #content-top>
      <CenterTop :data="data" />
      <TopModal
        v-model:visible="visible"
        v-model:title="activeTitle"
        :showChart="false"
        :areaCode="areaCode"
      />
    </template>
    <template #content-bottom>
      <CenterBottom :data="data" />
    </template>
    <template #right>
      <div class="flex flex-col gap-20px">
        <CommonTabChart chartKey="低病毒血症人数" />
        <CommonTabChart chartKey="抗病毒失败人数" />
        <CommonTabChart chartKey="艾滋病病人管理统计" />
      </div>
    </template>
  </Layout>
</template>
