<script setup lang="ts">
  import type { Ref } from 'vue';
  import { onMounted, ref } from 'vue';
  import { genSeries } from '/@/helper/chart';
  import { useECharts } from '@ft/internal/hooks/web/useECharts';
  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
  const colors = ['#4977FE', '#00A6FF', '#23F1F1', '#FF9E37', '#3CE292', '#F1E723'];
  const list = ref([
    { label: '医院检验采血检测', value: '0', percent: 30 },
    { label: '老年人体检', value: '0', percent: 30 },
    { label: '义诊', value: '0', percent: 30 },
    { label: '特殊节日活动检测', value: '0', percent: 30 },
    { label: '高危人群检测', value: '0', percent: 30 },
  ]);
  onMounted(() => {
    setOptions({
      title: {
        text: '各筛查途径的筛查人数',
        left: 'center',
        textStyle: {
          color: '#fff',
          fontWeight: 'normal',
        },
      },
      series: [
        genSeries({
          preset: '饼图',
          center: ['50%', '50%'],
          radius: ['40%', '70%'],
          data: list.value.map((item, index) => ({ ...item, itemStyle: { color: colors[index] } })),
          // labelLine: {
          //   show: false,
          // },
          // label: {
          //   show: true,
          //   formatter: '{d}%',
          // },
          padAngle: 5,
        }),
      ],
    });
  });
</script>

<template>
  <div class="h-full flex justify-center items-center relative">
    <div ref="chartRef" :style="{ width: '100%', height: '100%' }"> </div>
    <div
      class="w-200px h-254px absolute right-100px flex flex-col items-center justify-center overflow-y-auto gap-1"
    >
      <div
        v-for="(item, idx) in list"
        :key="idx"
        class="flex justify-between w-full bg-[rgba(255,255,255,0.1)] rounded-4px px-10px py-6px text-12px"
      >
        <div>
          <span
            class="w-12px h-8px inline-block rounded-1px mr-5px"
            :style="{ background: colors[idx] }"
          ></span>
          <span class="text-[#AFD3FF]">{{ item.label }}</span>
        </div>
        <div>
          <span class="color-[#00A6FF]">{{ item.value }}人</span>
          <!-- <span class="color-[#23F1F1] pl-2">{{ item.percent }}%</span> -->
        </div>
      </div>
    </div>
  </div>
</template>
