<script setup lang="ts">
  import { Icon } from '@ft/internal/components/Icon';
  import StatCard2 from '/@/component/StatCard2/index.vue';
  import { computed, ref } from 'vue';
  import { get } from 'lodash-es';
  import TopModal from './TopModal.vue';

  const props = defineProps<{
    data: any;
  }>();

  const bottomList = computed(() => [
    // {
    //   title: '确诊现存活率',
    //   value: get(props, 'data.survivalRate.percent', 0) + '%',
    //   personNum: get(props, 'data.survivalRate.peopleNum', 0),
    //   compareValue: get(props, 'data.survivalRate.basis', 0) + '%',
    //   decline: parseInt(get(props, 'data.survivalRate.basis', 0)) < 0,
    //   icon: 'aids-survival-rate|svg',
    // },
    // {
    //   title: '病毒抑制率',
    //   value: get(props, 'data.inhibitionRate.percent', 0) + '%',
    //   personNum: get(props, 'data.inhibitionRate.peopleNum', 0),
    //   compareValue: get(props, 'data.inhibitionRate.basis', 0) + '%',
    //   decline: parseInt(get(props, 'data.inhibitionRate.basis', 0)) < 0,
    //   icon: 'aids-virus-inhibition-rate|svg',
    // },
    // {
    //   title: '耐药基因检测率',
    //   value: get(props, 'data.geneticTestRate.percent', 0) + '%',
    //   personNum: get(props, 'data.geneticTestRate.peopleNum', 0),
    //   compareValue: get(props, 'data.geneticTestRate.basis', 0) + '%',
    //   decline: parseInt(get(props, 'data.geneticTestRate.basis', 0)) < 0,
    //   icon: 'aids-drug-resistant-rate|svg',
    // },
    // {
    //   title: '扩大筛查人数',
    //   value: get(props, 'data.screeningNum', 0),
    //   icon: 'aids-survival-rate|svg',
    // },
    {
      title: '2周内未开始治疗人数',
      value: get(props, 'data.twoWeekARTNum', 0),
      icon: 'aids-virus-inhibition-rate|svg',
    },
    {
      title: '30天内未开始治疗人数',
      value: get(props, 'data.thirtyDayARTNum', 0),
      icon: 'aids-not-treatment30days|svg',
    },
    {
      title: '病毒抑制率',
      value: get(props, 'data.inhibitionRate', 0) + '%',
      icon: 'aids-drug-resistant-rate|svg',
    },
  ]);
  const activeTitle = ref('');
  const visible = ref(false);
  const handleClick = (title) => {
    if (title !== '扩大筛查人数') return;
    visible.value = true;
    activeTitle.value = title;
  };
</script>

<template>
  <div class="flex justify-around mb-24">
    <StatCard2
      class="justify-center"
      v-for="item in bottomList"
      :key="item.title"
      :title="item.title"
      :value="item.value"
      @click="handleClick(item.title)"
    >
      <template #icon> <Icon :size="80" :icon="item.icon" /></template>
    </StatCard2>
  </div>
  <TopModal v-model:visible="visible" v-model:title="activeTitle" />
</template>
<style scoped></style>
