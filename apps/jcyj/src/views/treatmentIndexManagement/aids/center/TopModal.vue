<script setup lang="ts">
  import { computed, reactive, ref, watch } from 'vue';
  import ViewModal from '/@/component/view-modal/index.vue';
  import { Icon } from '@ft/internal/components/Icon';
  import { genGrid, genLegend, genSeries, genXAxis, genYAxis, legendIcon1 } from '/@/helper/chart';
  import Card<PERSON>hart from '/@/component/card-chart/index.vue';
  import ApiSelect from '@ft/internal/components/Form/src/components/ApiSelect.vue';
  import { Col as AntCol, Form, Pagination, RangePicker, Row } from 'ant-design-vue';
  import { Button } from '@ft/internal/components/Button';
  import {
    queryNewDiagnosisLine,
    queryNewDiagnosisPage,
    queryScreeningLine,
    queryScreeningPage,
    querySurvivalLine,
    querySurvivalPage,
  } from '/@/api/aids';
  import { getAdministrativeList } from '@ft/internal/api';
  import { useRequest } from '@ft/request';
  import { map, omit } from 'lodash-es';
  import PieChart from './PieChart.vue';
  const FormItem = Form.Item;
  const props = withDefaults(
    defineProps<{ visible: boolean; title: string; showChart?: boolean; areaCode?: string }>(),
    {
      visible: false,
      title: '',
      showChart: true,
      areaCode: '',
    },
  );

  const emit = defineEmits<{
    (e: 'update:visible', value: boolean): void;
    (e: 'update:title', value: string): void;
  }>();

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => {
      emit('update:visible', value);
    },
  });

  const activeKey = computed({
    get: () => props.title,
    set: (value) => {
      emit('update:title', value);
    },
  });
  const disabled = computed(() => !!props.areaCode);
  const isShowChart = computed(() => props.showChart);
  const tabData = {
    确诊存活人数: {
      title: '艾滋病确诊存活病人辖区分布',
      listTitle: '艾滋病确诊存活病人列表',
      chartTitle: '宜昌市艾滋病确诊存活人数总数：',
      chartApi: querySurvivalLine,
      listApi: querySurvivalPage,
    },
    扩大筛查人数: {
      title: '艾滋病扩大筛查人数辖区分布',
      listTitle: '艾滋病扩大筛查病人列表',
      chartTitle: '宜昌市全人口筛查比例：',
      chartApi: queryScreeningLine,
      listApi: queryScreeningPage,
    },
    新确诊人数: {
      title: '艾滋病新确诊人数辖区分布',
      listTitle: '艾滋病新确诊病人列表',
      chartTitle: '宜昌市艾滋病新确诊人数总数：',
      chartApi: queryNewDiagnosisLine,
      listApi: queryNewDiagnosisPage,
    },
  };
  const chartOptionData = reactive({
    totalData: 0,
    areaData: [] as any[],
    areaSeries0Data: [] as any[],
  });
  const chartOption = computed(() => {
    return {
      title: {
        text: [
          `{a|${tabData[activeKey.value].chartTitle}}`,
          `{b|${chartOptionData.totalData}}`,
        ].join(''),
        textStyle: {
          rich: {
            a: {
              color: '#5A86C2',
              fontSize: 14,
              fontWeight: 400,
              lineHeight: 22,
            },
            b: {
              color: '#17ABFF',
              fontSize: 16,
              lineHeight: 22,
              fontWeight: 500,
            },
          },
        },
      },
      grid: genGrid({ right: 20 }),
      legend: genLegend({
        preset: 'show',
        left: 'center',
        data: [
          { name: activeKey.value === '扩大筛查人数' ? '筛查比例' : '人数', icon: legendIcon1 },
        ],
      }),
      xAxis: [
        genXAxis({
          features: ['breakLabel'],
          // data: allAreaName,
        }),
      ],
      yAxis: [genYAxis({})],
      series: [
        genSeries({
          preset: '立体柱形',
          name: activeKey.value === '扩大筛查人数' ? '筛查比例' : '人数',
        }),
      ],
    };
  });
  const chartConfigs = computed(() => {
    return [
      { path: 'xAxis.0.data', val: chartOptionData.areaData },
      {
        path: 'series.0.data',
        val: chartOptionData.areaSeries0Data,
      },
    ];
  });
  const searchState = ref({
    areaCode: '',
    diagnosisTime: ['', ''] as [string, string],
  });
  const formState = ref<{
    areaCode?: string;
    diagnosisStartTime?: string;
    diagnosisEndTime?: string;
    pageNum?: number;
    pageSize?: number;
  }>({
    areaCode: undefined,
    pageNum: 1,
    pageSize: 10,
  });
  const pageInfo = reactive({
    total: 0,
    pageSize: 10,
    current: 1,
  });
  const tableListData = ref<any[]>([]);

  watch(modalVisible, (val) => {
    if (!val) {
      pageInfo.current = 1;
      pageInfo.total = 0;
      searchState.value.areaCode = '';
      searchState.value.diagnosisTime = ['', ''];
      formState.value.areaCode = undefined;
      formState.value.diagnosisStartTime = undefined;
      formState.value.diagnosisEndTime = undefined;
    }
  });
  useRequest(() => tabData[activeKey.value].chartApi({}), {
    // manual: true,
    onSuccess: (res: any) => {
      chartOptionData.areaData = map(res.monitorCountList, 'x');
      chartOptionData.areaSeries0Data = map(res.monitorCountList, 'yvalue');
      chartOptionData.totalData = res.allNum;
    },
    ready: modalVisible,
    refreshDeps: [modalVisible],
  });
  const { runAsync: getTableListRunAsync } = useRequest(
    () =>
      tabData[activeKey.value].listApi({
        ...omit(formState.value, 'diagnosisTime'),
        pageNum: pageInfo.current,
        pageSize: pageInfo.pageSize,
      }),
    {
      // manual: true,
      onBefore: () => {
        if (props.areaCode) formState.value.areaCode = props.areaCode;
        return true;
      },
      onSuccess: (res: any) => {
        tableListData.value = res.list;
        pageInfo.total = res.total;
      },
      ready: modalVisible,
      refreshDeps: [modalVisible],
    },
  );

  const onSearch = () => {
    formState.value.areaCode = searchState.value.areaCode;
    formState.value.diagnosisStartTime = searchState.value.diagnosisTime?.[0];
    formState.value.diagnosisEndTime = searchState.value.diagnosisTime?.[1];
    pageInfo.current = 1;
    getTableListRunAsync();
  };

  function getPopupContainer() {
    return document.querySelector('main.ant-layout-content') as HTMLElement;
  }
</script>

<template>
  <ViewModal width="90%" :title="activeKey" v-model:visible="modalVisible">
    <div class="py-4 pl-2 pr-4">
      <!-- <StyledTabs v-model:active-key="activeKey"> -->
      <!-- <StyledTabItem title="确诊存活人数" tab="确诊存活人数"> 确诊存活人数 </StyledTabItem>
        <StyledTabItem title="扩大筛查人数" tab="扩大筛查人数"> 扩大筛查人数 </StyledTabItem>
        <StyledTabItem title="新确诊人数" tab="新确诊人数"> 新确诊人数 </StyledTabItem> -->
      <!-- <StyledTabItem title="2周内未开始ART治疗人数" tab="2周内未开始ART治疗人数">
          2周内未开始ART治疗人数
        </StyledTabItem>
        <StyledTabItem class="ml-7.5" title="30天内未开始ART治疗人数" tab="30天内未开始ART治疗人数">
          30天内未开始ART治疗人数
        </StyledTabItem> -->
      <!-- </StyledTabs> -->
      <div
        class="flex flex-col content h-[818px] rounded-12px border-1 border-[#0C2A4D] bg-[#040B15] p-5"
      >
        <template v-if="!(!isShowChart && activeKey == '确诊存活人数')">
          <div class="title text-shadow-[1px_1px_1px_#0C3B5E]">
            <Icon class="scale-350" :size="16" icon="modal-title-decoration|svg" />
            <span class="ml-3 text-[20px] leading-24px">{{ tabData[activeKey].title }}</span>
          </div>
          <div class="h-276px pt-4">
            <CardChart
              :title="tabData[activeKey].chartTitle"
              width="100%"
              height="100%"
              :configs="chartConfigs"
              :option="chartOption"
            />
          </div>
        </template>
        <template v-if="activeKey !== '扩大筛查人数'">
          <div
            class="title text-shadow-[1px_1px_1px_#0C3B5E]"
            :class="!(!isShowChart && activeKey == '确诊存活人数') ? 'mt-5' : ''"
          >
            <Icon class="scale-350" :size="16" icon="modal-title-decoration|svg" />
            <span class="ml-3 text-[20px] leading-24px">{{ tabData[activeKey].listTitle }}</span>
          </div>
          <div class="flex-1 h-420px pt-4 flex flex-col">
            <Form :model="formState" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
              <Row>
                <AntCol span="4">
                  <FormItem label="所属辖区" name="areaCode">
                    <ApiSelect
                      :api="getAdministrativeList"
                      placeholder="请选择"
                      label-field="name"
                      value-field="code"
                      v-model:value="searchState.areaCode"
                      :disabled="disabled"
                      :get-popup-container="getPopupContainer"
                    />
                  </FormItem>
                </AntCol>
                <AntCol span="6">
                  <FormItem label="确诊时间" name="diagnosisTime">
                    <RangePicker
                      v-model:value="searchState.diagnosisTime"
                      :get-popup-container="getPopupContainer"
                    />
                  </FormItem>
                </AntCol>
                <AntCol span="3">
                  <FormItem>
                    <Button
                      @click="onSearch"
                      class="w-20 !border-0 !text-sm !font-bold !text-white btn"
                    >
                      搜索
                    </Button>
                  </FormItem>
                </AntCol>
              </Row>
            </Form>
            <div class="flex-1 h-300px">
              <div class="a-table-header-container">
                <table>
                  <colgroup>
                    <col style="width: 5%" />
                    <col style="width: 10%" />
                    <col style="width: 10%" />
                    <col style="width: 10%" />
                    <col style="width: 5%" />
                    <col style="width: 10%" />
                    <col style="width: 10%" />
                    <col style="width: 10%" />
                    <col style="width: 10%" />
                    <col style="width: 10%" />
                    <col style="width: 10%" />
                  </colgroup>
                  <thead>
                    <tr>
                      <th>序号</th>
                      <th>所属辖区</th>
                      <th>确诊日期</th>
                      <th>姓名</th>
                      <th>性别</th>
                      <th>身份证号</th>
                      <th>启动时间</th>
                      <th>药物组合</th>
                      <th>传播途径</th>
                      <th>CD4</th>
                      <th>VL</th>
                    </tr>
                  </thead>
                </table>
              </div>
              <div
                :class="
                  !(!isShowChart && activeKey == '确诊存活人数')
                    ? 'a-table-body-container'
                    : 'a-table-body-container2'
                "
              >
                <table>
                  <colgroup>
                    <col style="width: 5%" />
                    <col style="width: 10%" />
                    <col style="width: 10%" />
                    <col style="width: 10%" />
                    <col style="width: 5%" />
                    <col style="width: 10%" />
                    <col style="width: 10%" />
                    <col style="width: 10%" />
                    <col style="width: 10%" />
                    <col style="width: 10%" />
                    <col style="width: 10%" />
                  </colgroup>
                  <tbody>
                    <tr v-for="(item, index) in tableListData" :key="index">
                      <td>{{ index + 1 }}</td>
                      <td>{{ item.areaName }}</td>
                      <td>{{ item.diagnosisTime }}</td>
                      <td>{{ item.patientName }}</td>
                      <td>{{ item.sexName }}</td>
                      <td>{{ item.idCardNo }}</td>
                      <td>{{ item.firstTreatmentTime }}</td>
                      <td>{{ item.drugCombination }}</td>
                      <td>{{ item.infectionRouteDesc }}</td>
                      <td>{{ item.cd4Num }}</td>
                      <td>{{ item.vlnum }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div class="flex justify-end mt-5" v-if="pageInfo.total > 0">
              <Pagination
                class="mt-5"
                :total="pageInfo.total"
                :show-size-changer="false"
                v-model:current="pageInfo.current"
                @change="getTableListRunAsync"
              />
            </div>
          </div>
        </template>
        <div v-else class="h-420px pt-4 mt-5">
          <PieChart />
        </div>
      </div>
    </div>
  </ViewModal>
</template>
<style scoped lang="less">
  .view-modal-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;

    th,
    td {
      text-align: left;
    }

    thead tr,
    tbody tr {
      display: table;
      table-layout: fixed;
      box-sizing: border-box;
      width: 100%;
    }

    tbody {
      max-height: 280px;
      overflow-y: scroll;
      display: block;
      width: calc(100% + 8px);
    }
  }

  table {
    color: #afd3ff;
    font-size: 14px;
    table-layout: fixed;
    width: 100%;

    th {
      height: 32px !important;
      text-align: left;
      padding: 8px 12px;
      color: #fff;
      background: rgb(255 255 255 / 8%);
    }

    td {
      height: 32px !important;
      padding: 8px 12px;
    }

    tr:nth-child(2n) {
      background: rgb(255 255 255 / 8%);
    }
  }

  .a-table-body-container {
    max-height: 280px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .a-table-body-container2 {
    max-height: 595px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  :deep {
    .ant-form-item-label > label {
      color: #afd3ff !important;
    }
  }

  .btn {
    background: linear-gradient(180deg, #1590ea 0%, rgb(21 144 234 / 31%) 100%);
  }
</style>
