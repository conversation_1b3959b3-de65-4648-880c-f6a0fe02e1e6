<script setup lang="ts">
  import { Icon } from '@ft/internal/components/Icon';
  import { computed, ref } from 'vue';
  import { get } from 'lodash-es';
  import TopModal from './TopModal.vue';
  import StatCard from '/@/component/StatCard/index.vue';

  const visible = ref(false);
  const props = defineProps<{
    data: any;
  }>();

  const topList = computed(() => [
    // {
    //   title: '艾滋病人数',
    //   count: get(props, 'data.azbNum', 0),
    //   icon: 'aids-person|svg',
    // },
    {
      title: '确诊存活人数',
      count: get(props, 'data.azbSurvivalNum', 0),
      icon: 'aids-person|svg',
    },
    // {
    //   title: '扩大筛查人数',
    //   count: get(props, 'data.screeningNum', 0),
    //   icon: 'aids-screening|svg',
    // },
    {
      title: '治疗覆盖率',
      count: get(props, 'data.treatmentCoverage.percent', 0) + '%',
      icon: 'aids-screening|svg',
      showRightCount: true,
      rightCount: get(props, 'data.treatmentCoverage.peopleNum', 0),
    },
    {
      title: '新确诊人数',
      count: get(props, 'data.newAzbNum', 0),
      icon: 'aids-new-diagnosis|svg',
    },
    {
      title: '30天内治疗率',
      count: get(props, 'data.newAzbThirtyRate.percent', 0) + '%',
      icon: 'aids-treatment2week|svg',
      showRightCount: true,
      rightCount: get(props, 'data.newAzbThirtyRate.peopleNum', 0),
    },
    // {
    //   title: '2周内未开始ART治疗人数',
    //   count: get(props, 'data.twoWeekARTNum', 0),
    //   icon: 'aids-treatment2week|svg',
    // },
    // {
    //   title: '30天内未开始ART治疗人数',
    //   count: get(props, 'data.thirtyDayARTNum', 0),
    //   icon: 'aids-treatment30day|svg',
    // },
  ]);
  const activeTitle = ref('');
  const handleClick = (title) => {
    if (title === '治疗覆盖率' || title === '30天内治疗率') return;
    activeTitle.value = title;
    visible.value = true;
  };
</script>

<template>
  <div class="flex justify-center mt-10">
    <StatCard
      class="flex-1 justify-center"
      v-for="item in topList"
      :key="item.title"
      :title="item.title"
      :count="item.count"
      :showRightCount="item.showRightCount"
      :rightCount="item.rightCount"
      @click="handleClick(item.title)"
    >
      <template #icon> <Icon :size="80" :icon="item.icon" /></template>
    </StatCard>
  </div>
  <TopModal v-model:visible="visible" v-model:title="activeTitle" />
</template>
<style scoped></style>
