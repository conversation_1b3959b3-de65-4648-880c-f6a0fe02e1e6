import type { Ref } from 'vue';
import { computed, reactive, ref, watch } from 'vue';
import type { EChartsOption } from 'echarts';
import { genLz<PERSON>hart } from '/@/helper/chart';
import type { ChartData } from '/@/helper/useChartData';
import {
  getCheckDetectionRate,
  getDrugFastPatientMonitor,
  getFreeMedicationUsage,
  getPositivePatientMonitor,
  getTreatmentSituation,
  getTuberculosisLineChart,
} from '/@/api/tuberculosis';
import { map } from 'lodash-es';
import { forEach } from '@ft/internal/utils/helper/treeHelper';

const 肺结核病人分布统计 = (_year: Ref<string>): ChartData => {
  const switchOptions = [
    { label: '辖区', key: 'area' },
    { label: '年度', key: 'year' },
  ];

  const switchValue = ref('area');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    areaSeries0Data: [] as any[],
    areaSeries1Data: [] as any[],
    yearData: [] as any[],
    yearSeries0Data: [] as any[],
    yearSeries1Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'area') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.areaSeries0Data },
        { path: 'series.1.data', val: chartConfigData.areaSeries1Data },
      ];
    } else {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.yearData },
        { path: 'series.0.data', val: chartConfigData.yearSeries0Data },
        { path: 'series.1.data', val: chartConfigData.yearSeries1Data },
      ];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart([{ name: '患病人数' }, { name: '占比', iconType: 2, isLine: true, yIndex: 1 }], {
      y0: {
        number: true,
      },
      y1: {
        show: true,
        percent: true,
      },
      labelNames: ['患病人数', '占比'],
      valueSuffix: ['人', '%'],
    }),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getTuberculosisLineChart({ type: switchValue.value === 'area' ? 1 : 2 }).then((res) => {
        if (switchValue.value === 'area') {
          chartConfigData.areaData = map(res, 'x');
          chartConfigData.areaSeries0Data = map(res, 'yvalue');
          chartConfigData.areaSeries1Data = map(res, 'y');
        } else {
          chartConfigData.yearData = map(res, 'x');
          chartConfigData.yearSeries0Data = map(res, 'yvalue');
          chartConfigData.yearSeries1Data = map(res, 'y');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    switchOptions,
    title: '肺结核病人分布统计',
  };
};

const 痰培养或分子生物学检查检测率 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart(
      [
        {
          name: '痰培养或分子生物学检查率',
          markLine: {
            data: [{ name: '阈值', yAxis: 95 }],
          },
        },
        {
          name: '痰培养检查率',
          iconType: 3,
          colorType: 2,
          markLine: {
            data: [{ name: '阈值', yAxis: 95 }],
          },
        },
      ],
      {
        y0: {
          percent: true,
        },
        labelNames: ['痰培养或分子生物学检查率', '痰培养检查率'],
        valueSuffix: ['%', '%'],
      },
    ),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      console.log(165, chartOption);
      getCheckDetectionRate().then((res) => {
        if (switchValue.value === 'key') {
          forEach(res, (item) => {
            if (item.type === 1) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries0Data = map(item.lineChartVOS, 'y');
            } else if (item.type === 2) {
              chartConfigData.keySeries1Data = map(item.lineChartVOS, 'y');
            }
          });
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '痰培养或分子生物学检查检测率',
  };
};

const 病原学阳性患者监测 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
    keySeries2Data: [] as any[],
    keySeries3Data: [] as any[],
    keySeries4Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
        { path: 'series.2.data', val: chartConfigData.keySeries2Data },
        { path: 'series.3.data', val: chartConfigData.keySeries3Data },
        { path: 'series.4.data', val: chartConfigData.keySeries4Data },
      ];
    } else {
      return [];
    }
  });

  const chartOption: EChartsOption = reactive(
    genLzChart(
      [
        { name: '密接筛查人数', yIndex: 1 },
        { name: '密切接触者筛查率', isLine: true, iconType: 2 },
        { name: '密切接触者检查率', isLine: true, iconType: 6, colorType: 4 },
        { name: '耐药筛查率', isLine: true, iconType: 7, colorType: 5 },
        { name: '病原学阳性率', isLine: true, iconType: 8, colorType: 7 },
      ],
      {
        y0: {
          percent: true,
        },
        y1: {
          show: true,
        },
        labelNames: [
          '密接筛查人数',
          '密切接触者筛查率',
          '密切接触者检查率',
          '耐药筛查率',
          '病原学阳性率',
        ],
        valueSuffix: ['人', '%', '%', '%', '%'],
      },
    ),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getPositivePatientMonitor().then((res) => {
        if (switchValue.value === 'key') {
          forEach(res, (item) => {
            if (item.type === 5) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries0Data = map(item.lineChartVOS, 'yvalue');
            } else if (item.type === 1) {
              chartConfigData.keySeries1Data = map(item.lineChartVOS, 'y');
            } else if (item.type === 2) {
              chartConfigData.keySeries2Data = map(item.lineChartVOS, 'y');
            } else if (item.type === 3) {
              chartConfigData.keySeries3Data = map(item.lineChartVOS, 'y');
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
            } else if (item.type === 4) {
              chartConfigData.keySeries4Data = map(item.lineChartVOS, 'y');
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
            }
          });
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '病原学阳性患者监测',
  };
};

const 耐药肺结核患者监测 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart(
      [
        {
          name: '高危人群耐药筛查率',
          markLine: {
            data: [{ name: '阈值', yAxis: 98 }],
          },
        },
        {
          name: '纳入治疗率',
          iconType: 3,
          colorType: 2,
          markLine: {
            data: [{ name: '阈值', yAxis: 95 }],
          },
        },
      ],
      {
        y0: {
          percent: true,
        },
        labelNames: ['高危人群耐药筛查率', '纳入治疗率'],
        valueSuffix: ['%', '%'],
      },
    ),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getDrugFastPatientMonitor().then((res) => {
        if (switchValue.value === 'key') {
          forEach(res, (item) => {
            if (item.type === 1) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries0Data = map(item.lineChartVOS, 'y');
            } else if (item.type === 2) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries1Data = map(item.lineChartVOS, 'y');
            }
          });
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '耐药肺结核患者监测',
  };
};

const 患者治疗情况 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
    keySeries2Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
        { path: 'series.2.data', val: chartConfigData.keySeries2Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart(
      [
        {
          name: '活动性肺结核患者成功治疗率',
          markLine: {
            data: [{ name: '阈值', yAxis: 95 }],
          },
        },
        {
          name: '新病原学阳性患者治愈率',
          iconType: 3,
          colorType: 2,
          markLine: {
            data: [{ name: '阈值', yAxis: 90 }],
          },
        },
        { name: '预防性治疗纳入率', iconType: 4, colorType: 3 },
      ],
      {
        y0: {
          percent: true,
        },
        labelNames: ['活动性肺结核患者成功治疗率', '新病原学阳性患者治愈率', '预防性治疗纳入率'],
        valueSuffix: ['%', '%', '%'],
      },
    ),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getTreatmentSituation().then((res) => {
        forEach(res, (item) => {
          if (item.type === 1) {
            chartConfigData.areaData = map(item.lineChartVOS, 'x');
            chartConfigData.keySeries0Data = map(item.lineChartVOS, 'y');
          } else if (item.type === 2) {
            chartConfigData.areaData = map(item.lineChartVOS, 'x');
            chartConfigData.keySeries1Data = map(item.lineChartVOS, 'y');
          } else if (item.type === 3) {
            chartConfigData.areaData = map(item.lineChartVOS, 'x');
            chartConfigData.keySeries2Data = map(item.lineChartVOS, 'y');
          }
        });
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '患者治疗情况',
  };
};

const 免费药品使用情况 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
    keySeries2Data: [] as any[],
    keySeries3Data: [] as any[],
    keySeries4Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
        { path: 'series.2.data', val: chartConfigData.keySeries2Data },
        { path: 'series.3.data', val: chartConfigData.keySeries3Data },
      ];
    } else {
      return [];
    }
  });

  const chartOption: EChartsOption = reactive(
    genLzChart(
      [
        { name: '免费药品使用患者数' },
        { name: '免费药品使用率', isLine: true, iconType: 2, yIndex: 1 },
        { name: '标准治疗方案使用率', isLine: true, iconType: 6, colorType: 4, yIndex: 1 },
        { name: '免费药品消耗量', iconType: 4, colorType: 3 },
      ],
      {
        y1: {
          show: true,
          percent: true,
        },
        labelNames: [
          '免费药品使用患者数',
          '免费药品使用率',
          '标准治疗方案使用率',
          '免费药品消耗量',
        ],
        valueSuffix: ['人', '%', '%', ''],
      },
    ),
  );

  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getFreeMedicationUsage().then((res) => {
        if (switchValue.value === 'key') {
          forEach(res, (item) => {
            if (item.type === 1) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries0Data = map(item.lineChartVOS, 'yvalue');
            } else if (item.type === 2) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries1Data = map(item.lineChartVOS, 'y');
            } else if (item.type === 3) {
              chartConfigData.keySeries2Data = map(item.lineChartVOS, 'y');
            } else if (item.type === 4) {
              chartConfigData.keySeries3Data = map(item.lineChartVOS, 'yvalue');
            }
          });
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '免费药品使用情况',
  };
};

export const chartConfigMap = {
  肺结核病人分布统计,
  痰培养或分子生物学检查检测率,
  病原学阳性患者监测,
  耐药肺结核患者监测,
  患者治疗情况,
  免费药品使用情况,
};
