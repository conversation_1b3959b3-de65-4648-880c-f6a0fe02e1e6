<script setup lang="ts">
  import StatCard2 from '/@/component/StatCard2/index.vue';
  import { Icon } from '@ft/internal/components/Icon';

  const bottomList = [
    {
      title: '密接者筛查人数',
      value: '12',
      showTitleRight: false,
      showCompare: false,
      decline: false,
      icon: 'tuberculosis-close-contact|svg',
    },
    {
      title: '结核病死亡率',
      value: '12%',
      personNum: 2,
      compareValue: '0.21%',
      showTitleRight: true,
      showCompare: true,
      decline: false,
      icon: 'tuberculosis-death-rate|svg',
    },
    {
      title: '病原学阳性率',
      value: '12%',
      personNum: 2,
      compareValue: '0.21%',
      showTitleRight: true,
      showCompare: true,
      decline: true,
      icon: 'tuberculosis-pathogen-positive-rate|svg',
    },
    {
      title: '免费药品使用率',
      value: '12%',
      personNum: 2,
      compareValue: '0.21%',
      showTitleRight: true,
      showCompare: true,
      decline: true,
      icon: 'tuberculosis-free-drug-rate|svg',
    },
  ];
</script>

<template>
  <div class="flex justify-center mb-24">
    <StatCard2
      class="flex-1 justify-center"
      v-for="item in bottomList"
      :key="item.title"
      :title="item.title"
      :value="item.value"
      :show-title-right="item.showTitleRight"
      :sub-value="item.personNum"
      :show-compare="item.showCompare"
      :compare-value="item.compareValue"
      :decline="item.decline"
    >
      <template #icon> <Icon :size="56" :icon="item.icon" /></template>
    </StatCard2>
  </div>
</template>
<style scoped></style>
