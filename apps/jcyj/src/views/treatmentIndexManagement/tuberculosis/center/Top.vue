<script setup lang="ts">
  import { useRequest } from '@ft/request';
  import StatCard from '/@/component/StatCard/index.vue';
  import { Icon } from '@ft/internal/components/Icon';
  import { getPeopleNumStatistic } from '/@/api/tuberculosis';
  import { computed, reactive } from 'vue';
  import { find } from 'lodash-es';
  import DeathCountModal from './DeathCountModal.vue';
  const { data } = useRequest(getPeopleNumStatistic);

  const modalState = reactive({
    visible: false,
    type: 1,
  });
  const topList = computed(() => [
    // {
    //   title: '肺结核人数',
    //   count: find(data.value, { type: 1 })?.value || 0,
    //   icon: 'tuberculosis-lung|svg',
    // },
    {
      title: '初诊登记人数',
      count: find(data.value, { type: 2 })?.value || 0,
      icon: 'tuberculosis-registered|svg',
    },
    {
      title: '规划管理人数',
      count: find(data.value, { type: 3 })?.value || 0,
      icon: 'tuberculosis-planning|svg',
    },
    {
      title: '耐多药患者数',
      count: find(data.value, { type: 4 })?.value || 0,
      icon: 'tuberculosis-multiple-drugs|svg',
    },
    // {
    //   title: '结核病死亡率',
    //   count: (find(data.value, { type: 5 })?.value || 0) + '%',
    //   icon: 'tuberculosis-receive|svg',
    // },
    {
      title: '敏感结核死亡率',
      count: (find(data.value, { type: 6 })?.value || 0) + '%',
      icon: 'tuberculosis-receive|svg',
      onClick: () => {
        modalState.visible = true;
        modalState.type = 1;
      },
    },
    {
      title: '耐药结核死亡率',
      count: (find(data.value, { type: 7 })?.value || 0) + '%',
      icon: 'tuberculosis-receive|svg',
      onClick: () => {
        modalState.visible = true;
        modalState.type = 2;
      },
    },
  ]);
</script>

<template>
  <div class="flex justify-center mt-10">
    <StatCard
      class="flex-1 justify-center"
      v-for="item in topList"
      :key="item.title"
      :title="item.title"
      :count="item.count"
      @click="item.onClick"
    >
      <template #icon> <Icon :size="80" :icon="item.icon" /></template>
    </StatCard>
    <DeathCountModal v-model:visible="modalState.visible" :data="modalState.type" />
  </div>
</template>
<style scoped></style>
