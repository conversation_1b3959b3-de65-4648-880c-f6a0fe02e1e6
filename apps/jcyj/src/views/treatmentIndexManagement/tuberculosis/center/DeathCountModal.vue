<script setup lang="ts">
  import { computed } from 'vue';
  import ViewModal from '/@/component/view-modal/index.vue';
  import { getDeathPatientInfo } from '/@/api/tuberculosis';
  import { useRequest } from '@ft/request';

  const props = defineProps<{ visible: boolean; data: any }>();

  const emit = defineEmits<{
    (e: 'update:visible', value: boolean): void;
  }>();

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => {
      emit('update:visible', value);
    },
  });

  const { data } = useRequest(() => getDeathPatientInfo(props.data), {
    ready: modalVisible,
    refreshDeps: [modalVisible],
  });
</script>

<template>
  <ViewModal
    width="60%"
    :title="`${props.data === 1 ? '敏感' : '耐药'}结核死亡人数列表`"
    v-model:visible="modalVisible"
  >
    <div class="py-4 pl-2 pr-4">
      <div class="a-table-header-container">
        <table>
          <colgroup>
            <col style="width: 5%" />
            <col style="width: 10%" />
            <col style="width: 5%" />
            <col style="width: 5%" />
            <col style="width: 10%" />
            <col style="width: 20%" />
          </colgroup>
          <thead>
            <tr>
              <th> 序号 </th>
              <th> 患者姓名 </th>
              <th> 性别 </th>
              <th> 年龄 </th>
              <th> 死亡时间 </th>
              <th> 死亡诊断 </th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="a-table-body-container">
        <table>
          <colgroup>
            <col style="width: 5%" />
            <col style="width: 10%" />
            <col style="width: 5%" />
            <col style="width: 5%" />
            <col style="width: 10%" />
            <col style="width: 20%" />
          </colgroup>
          <tbody>
            <tr v-for="(item, idx) in data" :key="idx">
              <td>{{ idx + 1 }}</td>
              <td>{{ item.patientName }}</td>
              <td>{{ item.patientSex }}</td>
              <td>{{ item.patientAge }}</td>
              <td>{{ item.deathTime }}</td>
              <td>{{ item.deathDiagnosis }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </ViewModal>
</template>
<style lang="less" scoped>
  table {
    color: #afd3ff;
    table-layout: fixed;
    font-size: 14px;
    width: 100%;

    th {
      text-align: left;
      padding: 8px 12px;
      color: #fff;
      background: rgb(255 255 255 / 8%);
    }

    td {
      padding: 8px 12px;
    }

    tr:nth-child(2n) {
      background: rgb(255 255 255 / 8%);
    }
  }

  .a-table-body-container {
    max-height: 500px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }
</style>
