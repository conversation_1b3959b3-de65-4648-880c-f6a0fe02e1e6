<script setup lang="ts">
  import Layout from '/@/layout/content.vue';
  import CommonTabChart from '/@/component/common-tab-chart/index.vue';
  import CenterTop from './center/Top.vue';
  import { chartConfigMap } from './config';
  import { onMounted, provide, ref } from 'vue';
  import { useMapStore } from '/@/store/modules/map';
  import { getDistributionData } from '/@/api/tuberculosis';
  import MapModal from './center/MapModal.vue';
  import ModalDetailsChat from './ModalDetailsChat.vue';
  provide('chartConfigMap', chartConfigMap);

  const { register } = useMapStore();
  onMounted(() => {
    getDistributionData().then((res) => {
      register({
        points: res as any,
        onClickMapFn(data) {
          areaCode.value = data.divisionCode;
          visible.value = true;
        },
      });
    });
  });
  const visible = ref(false);
  const areaCode = ref('');
</script>
<template>
  <Layout>
    <template #left>
      <div class="flex flex-col gap-20px">
        <CommonTabChart chart-key="肺结核病人分布统计" />
        <CommonTabChart chart-key="痰培养或分子生物学检查检测率" />
        <CommonTabChart chart-key="病原学阳性患者监测" />
      </div>
    </template>
    <template #content-top>
      <CenterTop />
      <MapModal v-model:visible="visible" :data="areaCode" />
    </template>
    <template #right>
      <div class="flex flex-col gap-20px">
        <CommonTabChart chart-key="耐药肺结核患者监测" />
        <CommonTabChart chart-key="患者治疗情况" />
        <CommonTabChart chart-key="免费药品使用情况">
          <template #modalBodyAfter>
            <ModalDetailsChat />
          </template>
        </CommonTabChart>
      </div>
    </template>
  </Layout>
</template>
