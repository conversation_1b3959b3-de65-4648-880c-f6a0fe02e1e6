<script setup lang="ts">
  import { useECharts } from '@ft/internal/hooks/web/useECharts';
  import { onMounted, ref } from 'vue';
  import {
    getFreeDrugUseRateDrilling,
    getFreeUseDrugPatientCountDrilling,
    getFreeUseDrugTotalDoseDrilling,
  } from '/@/api/tuberculosis';
  import { forEach } from '@ft/internal/utils/helper/treeHelper';
  import { cloneDeep, map, set } from 'lodash-es';
  import { genLzChart } from '/@/helper/chart';

  const chartRef1 = ref<HTMLDivElement | null>(null);
  const chartRef2 = ref<HTMLDivElement | null>(null);
  const chartRef3 = ref<HTMLDivElement | null>(null);
  const { setOptions: setOptions1 } = useECharts(chartRef1 as any);
  const { setOptions: setOptions2 } = useECharts(chartRef2 as any);
  const { setOptions: setOptions3 } = useECharts(chartRef3 as any);

  onMounted(() => {
    getFreeUseDrugPatientCountDrilling().then((res) => {
      const chartOption1 = cloneDeep(
        genLzChart(
          [
            { name: '乙胺吡嗪利福异烟片' },
            { name: '异福片', iconType: 4, colorType: 3 },
            { name: '乙胺丁醇', isLine: true, iconType: 2 },
          ],
          {
            y0: {},

            labelNames: ['乙胺吡嗪利福异烟片', '异福片', '乙胺丁醇'],
            valueSuffix: ['', '', ''],
          },
        ),
      );
      forEach(res, (item) => {
        if (item.type === 1) {
          set(chartOption1, 'xAxis.0.data', map(item.lineChartVOS, 'x'));
        }
        set(chartOption1, `series.${item.type - 1}.data`, map(item.lineChartVOS, 'yvalue'));
      });
      setOptions1(chartOption1, true);
    });
    getFreeUseDrugTotalDoseDrilling().then((res) => {
      const chartOption2 = cloneDeep(
        genLzChart(
          [
            { name: '乙胺吡嗪利福异烟片' },
            { name: '异福片', iconType: 4, colorType: 3 },
            { name: '乙胺丁醇', isLine: true, iconType: 2 },
          ],
          {
            y0: {},
            labelNames: ['乙胺吡嗪利福异烟片', '异福片', '乙胺丁醇'],
            valueSuffix: ['', '', ''],
          },
        ),
      );
      forEach(res, (item) => {
        if (item.type === 1) {
          set(chartOption2, 'xAxis.0.data', map(item.lineChartVOS, 'x'));
        }
        set(chartOption2, `series.${item.type - 1}.data`, map(item.lineChartVOS, 'yvalue'));
      });
      setOptions2(chartOption2);
    });
    getFreeDrugUseRateDrilling().then((res) => {
      const chartOption3 = cloneDeep(
        genLzChart(
          [
            { name: '乙胺吡嗪利福异烟片' },
            { name: '异福片', iconType: 4, colorType: 3 },
            { name: '乙胺丁醇', isLine: true, iconType: 2 },
          ],
          {
            y0: {
              percent: true,
            },
            labelNames: ['乙胺吡嗪利福异烟片', '异福片', '乙胺丁醇'],
            valueSuffix: ['%', '%', '%'],
          },
        ),
      );
      forEach(res, (item) => {
        if (item.type === 1) {
          set(chartOption3, 'xAxis.0.data', map(item.lineChartVOS, 'x'));
        }
        set(chartOption3, `series.${item.type - 1}.data`, map(item.lineChartVOS, 'yvalue'));
      });
      setOptions3(chartOption3);
    });
  });
</script>
<template>
  <div class="px-4 flex flex-col gap-2">
    <div class="flex-1">
      <div class="" style="color: #fff; font-size: 16px"> 免费药品使用患者数 </div>
      <div class="p-2" style="width: 100%; height: 220px" ref="chartRef1"></div>
    </div>
    <div class="flex-1">
      <div class="" style="color: #fff; font-size: 16px"> 免费药品消耗量 </div>
      <div class="p-2" style="width: 100%; height: 220px" ref="chartRef2"></div>
    </div>
    <div class="flex-1">
      <div class="" style="color: #fff; font-size: 16px"> 免费药品使用率 </div>
      <div class="p-2" style="width: 100%; height: 220px" ref="chartRef3"></div>
    </div>
  </div>
</template>
