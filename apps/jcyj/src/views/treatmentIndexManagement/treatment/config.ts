import type { Ref } from 'vue';
import { computed, reactive, ref, watch } from 'vue';
import type { EChartsOption } from 'echarts';
import { genLzChart } from '/@/helper/chart';
import type { ChartData } from '/@/helper/useChartData';
import {
  getHepatitisPatientVisitStatistics,
  getHepatitisTreatmentMonitor,
  getHivPatientVisitStatistics,
  getHivTreatmentMonitor,
  getNNIDReportedIncidence,
  getPregnantIDDRateVO,
  getTbPatientVisitStatistics,
  getTbTreatmentMonitor,
} from '/@/api/treatment';
import { forEach, map } from 'lodash-es';
const 结核病治疗监测 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
    keySeries2Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
        { path: 'series.2.data', val: chartConfigData.keySeries2Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart(
      [
        { name: '可疑症状者结核病检查率' },
        { name: '结核患者病原学阳性率', iconType: 3, colorType: 2 },
        { name: '结核患者/疑似肺结核患者转诊率', iconType: 4, colorType: 3, wrap: true },
      ],
      {
        labelNames: [
          '可疑症状者结核病检查率',
          '结核患者病原学阳性率',
          '结核患者/疑似肺结核患者转诊率',
        ],
        valueSuffix: ['%', '%', '%'],
      },
    ),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getTbTreatmentMonitor().then((res) => {
        if (switchValue.value === 'key') {
          forEach(res, (item) => {
            if (item.type === 1) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries0Data = map(item.lineChartVOS, 'y');
            } else if (item.type === 2) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries1Data = map(item.lineChartVOS, 'y');
            } else if (item.type === 3) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries2Data = map(item.lineChartVOS, 'y');
            }
          });
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '结核病治疗监测',
  };
};
const 结核病患者就诊统计 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
    keySeries2Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
        { path: 'series.2.data', val: chartConfigData.keySeries2Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart(
      [
        { name: '就诊记录统计' },
        { name: '就诊并发症统计', iconType: 3, colorType: 2 },
        { name: '复查率', iconType: 6, colorType: 4, isLine: true },
      ],
      {
        labelNames: ['就诊记录统计', '就诊并发症统计', '复查率'],
        valueSuffix: ['人', '人', '%'],
      },
    ),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getTbPatientVisitStatistics().then((res) => {
        if (switchValue.value === 'key') {
          forEach(res, (item) => {
            if (item.type === 1) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries0Data = map(item.lineChartVOS, 'yvalue');
            } else if (item.type === 2) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries1Data = map(item.lineChartVOS, 'yvalue');
            } else if (item.type === 3) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries2Data = map(item.lineChartVOS, 'y');
            }
          });
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '结核病患者就诊统计',
  };
};
const 法定传染病报告发病率 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
    keySeries2Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
        { path: 'series.2.data', val: chartConfigData.keySeries2Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart(
      [
        { name: '肺结核' },
        { name: '病毒性肝炎', iconType: 3, colorType: 2 },
        { name: '艾滋病', iconType: 4, colorType: 3 },
      ],
      {
        labelNames: ['肺结核', '病毒性肝炎', '艾滋病'],
        valueSuffix: ['%', '%', '%'],
      },
    ),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getNNIDReportedIncidence().then((res) => {
        if (switchValue.value === 'key') {
          forEach(res, (item) => {
            if (item.type === 1) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries0Data = map(item.lineChartVOS, 'y');
            } else if (item.type === 2) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries1Data = map(item.lineChartVOS, 'y');
            } else if (item.type === 3) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries2Data = map(item.lineChartVOS, 'y');
            }
          });
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '法定传染病报告发病率',
  };
};

const HIV治疗监测 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
    keySeries2Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
        { path: 'series.2.data', val: chartConfigData.keySeries2Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart(
      [
        { name: 'HIV感染者和病人诊断发现率' },
        { name: 'HIV符合抗病毒条件纳入治疗率', iconType: 3, colorType: 2 },
        { name: 'HIV符合抗病毒条件治疗成功率', iconType: 4, colorType: 3, wrap: true },
      ],
      {
        labelNames: [
          'HIV感染者和病人诊断发现率',
          'HIV符合抗病毒条件纳入治疗率',
          'HIV符合抗病毒条件治疗成功率',
        ],
        valueSuffix: ['%', '%', '%'],
      },
    ),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getHivTreatmentMonitor().then((res) => {
        if (switchValue.value === 'key') {
          forEach(res, (item) => {
            if (item.type === 1) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries0Data = map(item.lineChartVOS, 'y');
            } else if (item.type === 2) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries1Data = map(item.lineChartVOS, 'y');
            } else if (item.type === 3) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries2Data = map(item.lineChartVOS, 'y');
            }
          });
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: 'HIV治疗监测',
  };
};

const 艾滋病患者就诊统计 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
    keySeries2Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
        { path: 'series.2.data', val: chartConfigData.keySeries2Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart(
      [
        { name: '就诊记录统计' },
        { name: '就诊并发症统计', iconType: 3, colorType: 2 },
        { name: '复查率', iconType: 6, colorType: 4, isLine: true },
      ],
      {
        labelNames: ['就诊记录统计', '就诊并发症统计', '复查率'],
        valueSuffix: ['人', '人', '%'],
      },
    ),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getHivPatientVisitStatistics().then((res) => {
        if (switchValue.value === 'key') {
          forEach(res, (item) => {
            if (item.type === 1) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries0Data = map(item.lineChartVOS, 'yvalue');
            } else if (item.type === 2) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries1Data = map(item.lineChartVOS, 'yvalue');
            } else if (item.type === 3) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries2Data = map(item.lineChartVOS, 'yvalue');
            }
          });
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '艾滋病患者就诊统计',
  };
};

const 孕产妇传染病检出率 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
    keySeries2Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
        { path: 'series.2.data', val: chartConfigData.keySeries2Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart(
      [
        { name: '艾滋病' },
        { name: '乙肝', iconType: 3, colorType: 2 },
        { name: '梅毒', iconType: 4, colorType: 3 },
      ],
      {
        labelNames: ['艾滋病', '乙肝', '梅毒'],
        valueSuffix: ['%', '%', '%'],
      },
    ),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getPregnantIDDRateVO().then((res) => {
        if (switchValue.value === 'key') {
          forEach(res, (item) => {
            if (item.type === 1) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries0Data = map(item.lineChartVOS, 'y');
            } else if (item.type === 2) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries1Data = map(item.lineChartVOS, 'y');
            } else if (item.type === 3) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries2Data = map(item.lineChartVOS, 'y');
            }
          });
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '孕产妇传染病检出率',
  };
};
const 病毒性肝炎治疗监测 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
    keySeries2Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart(
      [
        { name: '符合治疗条件的HCV-RNA阳性患者治疗率' },
        { name: '符合治疗条件的HCV-RNA阳性患者治愈率', iconType: 3, colorType: 2 },
      ],
      {
        labelNames: ['符合治疗条件的HCV-RNA阳性患者治疗率', '符合治疗条件的HCV-RNA阳性患者治愈率'],
        valueSuffix: ['%', '%'],
      },
    ),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getHepatitisTreatmentMonitor().then((res) => {
        if (switchValue.value === 'key') {
          forEach(res, (item) => {
            if (item.type === 1) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries0Data = map(item.lineChartVOS, 'y');
            } else if (item.type === 2) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries1Data = map(item.lineChartVOS, 'y');
            }
          });
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '病毒性肝炎治疗监测',
  };
};

const 病毒性肝炎患者就诊统计 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
    keySeries2Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
        { path: 'series.2.data', val: chartConfigData.keySeries2Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart(
      [
        { name: '就诊记录统计' },
        { name: '就诊并发症统计', iconType: 3, colorType: 2 },
        { name: '复查率', iconType: 6, colorType: 4, isLine: true },
      ],
      {
        labelNames: ['就诊记录统计', '就诊并发症统计', '复查率'],
        valueSuffix: ['人', '人', '%'],
      },
    ),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getHepatitisPatientVisitStatistics().then((res) => {
        if (switchValue.value === 'key') {
          forEach(res, (item) => {
            if (item.type === 1) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries0Data = map(item.lineChartVOS, 'yvalue');
            } else if (item.type === 2) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries1Data = map(item.lineChartVOS, 'yvalue');
            } else if (item.type === 3) {
              chartConfigData.areaData = map(item.lineChartVOS, 'x');
              chartConfigData.keySeries2Data = map(item.lineChartVOS, 'y');
            }
          });
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '病毒性肝炎患者就诊统计',
  };
};

export const chartConfigMap = {
  结核病治疗监测,
  结核病患者就诊统计,
  法定传染病报告发病率,
  HIV治疗监测,
  艾滋病患者就诊统计,
  孕产妇传染病检出率,
  病毒性肝炎治疗监测,
  病毒性肝炎患者就诊统计,
};
