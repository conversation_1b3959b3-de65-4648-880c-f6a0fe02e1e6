<script setup lang="ts">
  import { Icon } from '@ft/internal/components/Icon';
  import { useRequest } from '@ft/request';
  import { getConsultRateCombinationIndex } from '/@/api/treatment';
  const { data } = useRequest(getConsultRateCombinationIndex);
</script>
<template>
  <div class="flex justify-evenly mt-20">
    <div class="flex items-center">
      <Icon :size="80" icon="treatmentIndexManagement-1|svg" />
      <div class="ml-12px">
        <div style="font-weight: bold; text-shadow: 0 0 4px #041832; color: #5a86c2">
          传染病患者县域内就诊率
        </div>
        <div class="flex items-center">
          <div class="percent mr-16px">{{ data?.outpatientWithinCountyRate }}</div>
          <div>
            <span>同比:</span>
            <span class="color-#3CE292 pl-8px">
              {{ data?.outpatientWithinCountyRateOnYear }}
            </span></div
          >
        </div>
      </div>
    </div>
    <div class="flex items-center">
      <Icon :size="80" icon="treatmentIndexManagement-2|svg" />
      <div class="ml-12px">
        <div style="font-weight: bold; text-shadow: 0 0 4px #041832; color: #5a86c2">
          传染病相关病例组合指数
        </div>
        <div class="flex items-center">
          <div class="percent mr-16px">{{ data?.infectiousCaseMixIndex }}</div>
          <div>
            <span>同比:</span>
            <span class="color-#3CE292 pl-8px">{{ data?.infectiousCaseMixIndexOnYear }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
  .percent {
    font-size: 28px;
    font-weight: bold;
    background: linear-gradient(180deg, #fff 0%, #17abff 100%);
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
</style>
