<script setup lang="ts">
  import Layout from '/@/layout/content.vue';
  import CenterTop from './CenterTop.vue';
  import { provide } from 'vue';
  import { chartConfigMap } from './config';
  import CommonTabChart from '/@/component/common-tab-chart/index.vue';
  import { useMapStore } from '/@/store/modules/map';
  provide('chartConfigMap', chartConfigMap);

  const { register } = useMapStore();
  register({
    position: 'center-3',
  });
</script>

<template>
  <Layout>
    <template #left>
      <div class="flex flex-col gap-20px">
        <CommonTabChart chartKey="结核病治疗监测" />
        <CommonTabChart chartKey="结核病患者就诊统计" />
        <CommonTabChart chartKey="法定传染病报告发病率" />
      </div>
    </template>
    <template #content-top>
      <CenterTop />
    </template>
    <template #content-bottom>
      <div class="flex justify-between mb-58px">
        <CommonTabChart chartKey="病毒性肝炎治疗监测" width="466px" :type="2" />
        <CommonTabChart chartKey="病毒性肝炎患者就诊统计" width="466px" :type="2" />
      </div>
    </template>
    <template #right>
      <div class="flex flex-col gap-20px">
        <CommonTabChart chartKey="HIV治疗监测" />
        <CommonTabChart chartKey="艾滋病患者就诊统计" />
        <CommonTabChart chartKey="孕产妇传染病检出率" />
      </div>
    </template>
  </Layout>
</template>
