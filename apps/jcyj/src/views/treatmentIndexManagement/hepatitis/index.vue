<script setup lang="ts">
  import Layout from '/@/layout/content.vue';
  import CenterTop from './center/Top.vue';
  import { onMounted, provide, ref } from 'vue';
  import { chartConfigMap } from './config';
  import CommonTabChart from '/@/component/common-tab-chart/index.vue';
  import { useMapStore } from '/@/store/modules/map';
  import { getMiddleBottomMapData } from '/@/api/hepatitis';
  import MapModal from './center/MapModal.vue';
  provide('chartConfigMap', chartConfigMap);

  const { register } = useMapStore();
  onMounted(() => {
    getMiddleBottomMapData(new Date().getFullYear()).then((res) => {
      register({
        points: res as any,
        onClickMapFn(data) {
          divisionCode.value = data.divisionCode;
          visible.value = true;
        },
      });
    });
  });
  const visible = ref(false);
  const divisionCode = ref('');
</script>
<template>
  <Layout>
    <template #left>
      <div class="flex flex-col gap-20px">
        <CommonTabChart chartKey="新发HCV抗体阳性患者数" />
        <CommonTabChart chartKey="丙肝抗体阳性患者核酸检测率" />
        <CommonTabChart chartKey="符合治疗条件的HCVRNA阳性患者" />
      </div>
    </template>
    <template #content-top>
      <CenterTop />
      <MapModal v-model:visible="visible" :data="divisionCode" />
    </template>
    <template #right>
      <div class="flex flex-col gap-20px">
        <CommonTabChart chartKey="筛查HBSAg阳性率" />
        <CommonTabChart chartKey="乙肝抗病毒治疗人数" />
        <CommonTabChart chartKey="丙肝患者基因分型检测率" />
      </div>
    </template>
  </Layout>
</template>
