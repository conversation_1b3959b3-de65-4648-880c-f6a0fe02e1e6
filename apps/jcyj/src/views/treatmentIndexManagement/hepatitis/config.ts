import type { Ref } from 'vue';
import { computed, reactive, ref, watch } from 'vue';
import type { EChartsOption } from 'echarts';
import { genLz<PERSON><PERSON> } from '/@/helper/chart';
import type { ChartData } from '/@/helper/useChartData';
import {
  getLeftBottomLine<PERSON>hart,
  getLeftMiddleLineChart,
  getLeftTopLineChart,
  getRightBottomLineChart,
  getRightTopLineChart,
  getXfhcvktyxhzs,
} from '/@/api/hepatitis';
import { forEach, map } from 'lodash-es';

const 乙肝抗病毒治疗人数 = (_year: Ref<string>): ChartData => {
  const switchOptions = [
    { label: '辖区', key: 'area' },
    { label: '年度', key: 'year' },
  ];

  const switchValue = ref('area');

  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    areaSeries0Data: [] as any[],
    yearData: [] as any[],
    yearSeries0Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'area') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.areaSeries0Data },
      ];
    } else {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.yearData },
        { path: 'series.0.data', val: chartConfigData.yearSeries0Data },
      ];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart([{ name: '乙型' }], {
      labelNames: ['乙型'],
      valueSuffix: ['人'],
    }),
  );

  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getLeftTopLineChart(switchValue.value === 'area' ? '1' : '2').then((res) => {
        if (switchValue.value === 'area') {
          forEach(res, () => {
            chartConfigData.areaData = map(res, 'x');
            chartConfigData.areaSeries0Data = map(res, 'yvalue');
          });
        } else {
          forEach(res, () => {
            chartConfigData.yearData = map(res, 'x');
            chartConfigData.yearSeries0Data = map(res, 'yvalue');
          });
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    switchOptions,
    updateSwitchValue,
    switchValue,
    title: '乙肝抗病毒治疗人数',
  };
};
const 新发HCV抗体阳性患者数 = (_year: Ref<string>): ChartData => {
  const switchOptions = [
    { label: '辖区', key: 'area' },
    { label: '年度', key: 'year' },
  ];
  const switchValue = ref('area');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    areaSeries0Data: [] as any[],
    yearData: [] as any[],
    yearSeries0Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'area') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.areaSeries0Data },
      ];
    } else {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.yearData },
        { path: 'series.0.data', val: chartConfigData.yearSeries0Data },
      ];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart([{ name: '人数' }], {
      labelNames: ['新发HCV抗体阳性患者数'],
      valueSuffix: ['人'],
    }),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getXfhcvktyxhzs(switchValue.value === 'area' ? '1' : '2').then((res) => {
        if (switchValue.value === 'area') {
          forEach(res, () => {
            chartConfigData.areaData = map(res, 'x');
            chartConfigData.areaSeries0Data = map(res, 'yvalue');
          });
        } else {
          forEach(res, () => {
            chartConfigData.yearData = map(res, 'x');
            chartConfigData.yearSeries0Data = map(res, 'yvalue');
          });
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    switchOptions,
    updateSwitchValue,
    switchValue,
    title: '新发HCV抗体阳性患者数',
  };
};
const 丙肝抗体阳性患者核酸检测率 = (_year: Ref<string>): ChartData => {
  const switchOptions = [
    { label: '辖区', key: 'area' },
    { label: '年度', key: 'year' },
  ];

  const switchValue = ref('area');

  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    areaSeries0Data: [] as any[],
    areaSeries1Data: [] as any[],
    yearData: [] as any[],
    yearSeries0Data: [] as any[],
    yearSeries1Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'area') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.areaSeries0Data },
        { path: 'series.1.data', val: chartConfigData.areaSeries1Data },
      ];
    } else {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.yearData },
        { path: 'series.0.data', val: chartConfigData.yearSeries0Data },
        { path: 'series.1.data', val: chartConfigData.yearSeries1Data },
      ];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart([{ name: '检测人数' }, { name: '检测率', iconType: 2, isLine: true, yIndex: 1 }], {
      y1: {
        show: true,
        percent: true,
      },
      labelNames: ['检测人数', '检测率'],
      valueSuffix: ['人', '%'],
    }),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getLeftMiddleLineChart(switchValue.value === 'area' ? '1' : '2').then((res) => {
        if (switchValue.value === 'area') {
          forEach(res, (item) => {
            if (item.type === 3) {
              chartConfigData.areaData = map(item.lineChartVOList, 'x');
              chartConfigData.areaSeries0Data = map(item.lineChartVOList, 'yvalue');
            } else if (item.type === 4) {
              chartConfigData.areaData = map(item.lineChartVOList, 'x');
              chartConfigData.areaSeries1Data = map(item.lineChartVOList, 'y');
            }
          });
        } else {
          forEach(res, (item) => {
            if (item.type === 3) {
              chartConfigData.yearData = map(item.lineChartVOList, 'x');
              chartConfigData.yearSeries0Data = map(item.lineChartVOList, 'yvalue');
            } else if (item.type === 4) {
              chartConfigData.yearData = map(item.lineChartVOList, 'x');
              chartConfigData.yearSeries1Data = map(item.lineChartVOList, 'y');
            }
          });
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    switchOptions,
    updateSwitchValue,
    switchValue,
    title: '丙肝抗体阳性患者核酸检测率',
  };
};

const 丙肝患者基因分型检测率 = (_year: Ref<string>): ChartData => {
  const switchOptions = [
    { label: '辖区', key: 'area' },
    { label: '年度', key: 'year' },
  ];

  const switchValue = ref('area');

  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    areaSeries0Data: [] as any[],
    areaSeries1Data: [] as any[],
    yearData: [] as any[],
    yearSeries0Data: [] as any[],
    yearSeries1Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'area') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.areaSeries0Data },
        { path: 'series.1.data', val: chartConfigData.areaSeries1Data },
      ];
    } else {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.yearData },
        { path: 'series.0.data', val: chartConfigData.yearSeries0Data },
        { path: 'series.1.data', val: chartConfigData.yearSeries1Data },
      ];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart([{ name: '检测人数' }, { name: '检测率', iconType: 2, isLine: true, yIndex: 1 }], {
      y1: {
        show: true,
        percent: true,
      },
      labelNames: ['检测人数', '检测率'],
      valueSuffix: ['人', '%'],
    }),
  );

  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getLeftBottomLineChart(switchValue.value === 'area' ? '1' : '2').then((res) => {
        if (switchValue.value === 'area') {
          forEach(res, (item) => {
            if (item.type === 3) {
              chartConfigData.areaData = map(item.lineChartVOList, 'x');
              chartConfigData.areaSeries0Data = map(item.lineChartVOList, 'yvalue');
            } else if (item.type === 4) {
              chartConfigData.areaData = map(item.lineChartVOList, 'x');
              chartConfigData.areaSeries1Data = map(item.lineChartVOList, 'y');
            }
          });
        } else {
          forEach(res, (item) => {
            if (item.type === 3) {
              chartConfigData.yearData = map(item.lineChartVOList, 'x');
              chartConfigData.yearSeries0Data = map(item.lineChartVOList, 'yvalue');
            } else if (item.type === 4) {
              chartConfigData.yearData = map(item.lineChartVOList, 'x');
              chartConfigData.yearSeries1Data = map(item.lineChartVOList, 'y');
            }
          });
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    switchOptions,
    updateSwitchValue,
    switchValue,
    title: '丙肝患者基因分型检测率',
  };
};

const 筛查HBSAg阳性率 = (_year: Ref<string>): ChartData => {
  const switchOptions = [
    { label: '辖区', key: 'area' },
    { label: '年度', key: 'year' },
  ];

  const switchValue = ref('area');

  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    areaSeries0Data: [] as any[],
    areaSeries1Data: [] as any[],
    yearData: [] as any[],
    yearSeries0Data: [] as any[],
    yearSeries1Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'area') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.areaSeries0Data },
        { path: 'series.1.data', val: chartConfigData.areaSeries1Data },
      ];
    } else {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.yearData },
        { path: 'series.0.data', val: chartConfigData.yearSeries0Data },
        { path: 'series.1.data', val: chartConfigData.yearSeries1Data },
      ];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart([{ name: '阳性人数' }, { name: '阳性率', iconType: 2, isLine: true, yIndex: 1 }], {
      y1: {
        show: true,
        percent: true,
      },
      labelNames: ['阳性人数', '阳性率'],
      valueSuffix: ['人', '%'],
    }),
  );

  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getRightTopLineChart(switchValue.value === 'area' ? '1' : '2').then((res) => {
        if (switchValue.value === 'area') {
          forEach(res, (item) => {
            if (item.type === 5) {
              chartConfigData.areaData = map(item.lineChartVOList, 'x');
              chartConfigData.areaSeries0Data = map(item.lineChartVOList, 'yvalue');
            } else if (item.type === 6) {
              chartConfigData.areaData = map(item.lineChartVOList, 'x');
              chartConfigData.areaSeries1Data = map(item.lineChartVOList, 'y');
            }
          });
        } else {
          forEach(res, (item) => {
            if (item.type === 5) {
              chartConfigData.yearData = map(item.lineChartVOList, 'x');
              chartConfigData.yearSeries0Data = map(item.lineChartVOList, 'yvalue');
            } else if (item.type === 6) {
              chartConfigData.yearData = map(item.lineChartVOList, 'x');
              chartConfigData.yearSeries1Data = map(item.lineChartVOList, 'y');
            }
          });
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    switchOptions,
    updateSwitchValue,
    switchValue,
    title: '筛查HBSAg阳性率',
  };
};

// const 丙肝RNA阳性治疗率 = (_year: Ref<string>): ChartData => {
//   const switchOptions = [
//     { label: '辖区', key: 'area' },
//     { label: '年度', key: 'year' },
//   ];

//   const switchValue = ref('area');

//   const updateSwitchValue = (val) => {
//     switchValue.value = val;
//   };
//   const chartConfigData = reactive({
//     areaData: [] as any[],
//     areaSeries0Data: [] as any[],
//     yearData: [] as any[],
//     yearSeries0Data: [] as any[],
//   });
//   const chartConfig = computed<any[]>(() => {
//     if (switchValue.value === 'area') {
//       return [
//         { path: 'xAxis.0.data', val: chartConfigData.areaData },
//         { path: 'series.0.data', val: chartConfigData.areaSeries0Data },
//       ];
//     } else {
//       return [
//         { path: 'xAxis.0.data', val: chartConfigData.yearData },
//         { path: 'series.0.data', val: chartConfigData.yearSeries0Data },
//       ];
//     }
//   });
//   const chartOption: EChartsOption = reactive(
//     genLzChart([{ name: '治疗率', iconType: 2, isLine: true }], {
//       y0: {
//         percent: true,
//       },
//       labelNames: ['治疗率'],
//       valueSuffix: ['%'],
//     }),
//   );
//   const cached = new Map();
//   watch(
//     () => switchValue.value,
//     () => {
//       if (cached.get(switchValue.value)) {
//         return;
//       }
//       getRightMiddleLineChart(switchValue.value === 'area' ? '1' : '2').then((res) => {
//         if (switchValue.value === 'area') {
//           forEach(res, (item) => {
//             if (item.type === 7) {
//               chartConfigData.areaData = map(item.lineChartVOList, 'x');
//               chartConfigData.areaSeries0Data = map(item.lineChartVOList, 'y');
//             }
//           });
//         } else {
//           forEach(res, (item) => {
//             if (item.type === 7) {
//               chartConfigData.yearData = map(item.lineChartVOList, 'x');
//               chartConfigData.yearSeries0Data = map(item.lineChartVOList, 'y');
//             }
//           });
//         }
//         cached.set(switchValue.value, true);
//       });
//     },
//     {
//       immediate: true,
//     },
//   );
//   return {
//     chartConfig,
//     chartOption,
//     switchOptions,
//     updateSwitchValue,
//     switchValue,
//     title: '丙肝RNA阳性治疗率',
//   };
// };

const 符合治疗条件的HCVRNA阳性患者 = (_year: Ref<string>): ChartData => {
  const switchOptions = [
    { label: '辖区', key: 'area' },
    { label: '年度', key: 'year' },
  ];

  const switchValue = ref('area');

  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    areaSeries0Data: [] as any[],
    areaSeries1Data: [] as any[],
    yearData: [] as any[],
    yearSeries0Data: [] as any[],
    yearSeries1Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'area') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.areaSeries0Data },
        { path: 'series.1.data', val: chartConfigData.areaSeries1Data },
      ];
    } else {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.yearData },
        { path: 'series.0.data', val: chartConfigData.yearSeries0Data },
        { path: 'series.1.data', val: chartConfigData.yearSeries1Data },
      ];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart([{ name: '治疗率' }, { name: '治愈率', iconType: 3, colorType: 2 }], {
      y0: {
        percent: true,
      },
      labelNames: ['治疗率', '治愈率'],
      valueSuffix: ['%', '%'],
    }),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getRightBottomLineChart(switchValue.value === 'area' ? '1' : '2').then((res) => {
        if (switchValue.value === 'area') {
          forEach(res, (item) => {
            if (item.type === 7) {
              chartConfigData.areaData = map(item.lineChartVOList, 'x');
              chartConfigData.areaSeries0Data = map(item.lineChartVOList, 'y');
            } else if (item.type === 8) {
              chartConfigData.areaData = map(item.lineChartVOList, 'x');
              chartConfigData.areaSeries1Data = map(item.lineChartVOList, 'y');
            }
          });
        } else {
          forEach(res, (item) => {
            if (item.type === 7) {
              chartConfigData.yearData = map(item.lineChartVOList, 'x');
              chartConfigData.yearSeries0Data = map(item.lineChartVOList, 'y');
            } else if (item.type === 8) {
              chartConfigData.yearData = map(item.lineChartVOList, 'x');
              chartConfigData.yearSeries1Data = map(item.lineChartVOList, 'y');
            }
          });
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    switchOptions,
    updateSwitchValue,
    switchValue,
    title: '符合治疗条件的HCV-RNA阳性患者',
  };
};

export const chartConfigMap = {
  乙肝抗病毒治疗人数,
  新发HCV抗体阳性患者数,
  丙肝抗体阳性患者核酸检测率,
  丙肝患者基因分型检测率,
  筛查HBSAg阳性率,
  // 丙肝RNA阳性治疗率,
  符合治疗条件的HCVRNA阳性患者,
};
