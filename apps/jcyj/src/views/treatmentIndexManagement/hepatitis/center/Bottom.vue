<script setup lang="ts">
  import StatCard2 from '/@/component/StatCard2/index.vue';
  import { Icon } from '@ft/internal/components/Icon';

  const bottomList = [
    {
      title: '符合治疗条件的HCV-RNA阳性患者治疗率',
      value: '85',
      personNum: 5231,
      showTitleRight: true,
      showCompare: true,
      compareValue: '0.21%',
      decline: false,
      icon: 'hepatitis-patient-treatment|svg',
    },
    {
      title: '符合治疗条件的HCV-RNA阳性患者治愈率',
      value: '12%',
      personNum: 5124,
      compareValue: '0.21%',
      showTitleRight: true,
      showCompare: true,
      decline: true,
      icon: 'hepatitis-patient-cured|svg',
    },
  ];
</script>

<template>
  <div class="flex justify-center mb-24">
    <StatCard2
      class="flex-1 justify-center"
      v-for="item in bottomList"
      :key="item.title"
      :title="item.title"
      :value="item.value"
      :show-title-right="item.showTitleRight"
      :sub-value="item.personNum"
      :show-compare="item.showCompare"
      :compare-value="item.compareValue"
      :decline="item.decline"
    >
      <template #icon> <Icon :size="56" :icon="item.icon" /></template>
    </StatCard2>
  </div>
</template>
<style scoped></style>
