<script setup lang="ts">
  import { computed } from 'vue';
  import ViewModal from '/@/component/view-modal/index.vue';
  import { useRequest } from '@ft/request';
  import { getPatientList } from '/@/api/hepatitis';
  import dayjs from 'dayjs';

  const props = defineProps<{ visible: boolean; data: any }>();

  const emit = defineEmits<{
    (e: 'update:visible', value: boolean): void;
  }>();

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => {
      emit('update:visible', value);
    },
  });

  const { data: patientList } = useRequest(
    () =>
      getPatientList({ divisionCode: props.data, pageNum: 1, pageSize: 999, year: dayjs().year() }),
    {
      ready: modalVisible,
      refreshDeps: [modalVisible],
    },
  );
</script>

<template>
  <ViewModal width="60%" title="患者列表" v-model:visible="modalVisible">
    <div class="py-4 pl-2 pr-4 flex flex-col">
      <div class="flex my-4 gap-8">
        <div
          >统计日期：{{ dayjs().startOf('year').format('YYYY年MM月DD日') }}-{{
            dayjs().format('YYYY年MM月DD日')
          }}</div
        >
      </div>
      <div class="a-table-header-container">
        <table>
          <colgroup>
            <col style="width: 5%" />
            <col style="width: 10%" />
            <col style="width: 10%" />
            <col style="width: 10%" />
            <col style="width: 15%" />
            <col style="width: 35%" />
            <col style="width: 15%" />
          </colgroup>
          <thead>
            <tr>
              <th> 序号 </th>
              <th> 患者姓名 </th>
              <th> 性别 </th>
              <th> 年龄 </th>
              <th> 疾病 </th>
              <th> 诊断 </th>
              <th> 确诊日期 </th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="a-table-body-container">
        <table>
          <colgroup>
            <col style="width: 5%" />
            <col style="width: 10%" />
            <col style="width: 10%" />
            <col style="width: 10%" />
            <col style="width: 15%" />
            <col style="width: 35%" />
            <col style="width: 15%" />
          </colgroup>
          <tbody>
            <tr v-for="(item, idx) in patientList?.list" :key="idx">
              <td>{{ idx + 1 }}</td>
              <td>{{ item.patientName }}</td>
              <td>{{ item.sex }}</td>
              <td>{{ item.age }}</td>
              <td>{{ item.diseaseName }}</td>
              <td>{{ item.diagnoseName }}</td>
              <td>{{ item.diagnoseDate }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </ViewModal>
</template>
<style lang="less" scoped>
  table {
    color: #afd3ff;
    table-layout: fixed;
    font-size: 14px;
    width: 100%;

    th {
      text-align: left;
      padding: 8px 12px;
      color: #fff;
      background: rgb(255 255 255 / 8%);
    }

    td {
      padding: 8px 12px;
    }

    tr:nth-child(2n) {
      background: rgb(255 255 255 / 8%);
    }
  }

  .a-table-body-container {
    max-height: 500px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }
</style>
