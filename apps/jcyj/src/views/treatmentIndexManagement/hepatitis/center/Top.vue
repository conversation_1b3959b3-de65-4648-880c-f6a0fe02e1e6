<script setup lang="ts">
  import { Modal } from 'ant-design-vue';
  import { useRequest } from '@ft/request';
  import StatCard from '/@/component/StatCard/index.vue';
  import { Icon } from '@ft/internal/components/Icon';
  import { getGxqszrsData, getMiddleTopCardData } from '/@/api/hepatitis';
  import { computed, nextTick, reactive, ref } from 'vue';
  import { cloneDeep, get, map, set } from 'lodash-es';
  import { useDataViewEle } from '/@/helper';
  import { useECharts } from '@ft/internal/hooks/web/useECharts';
  import { genLzChart } from '/@/helper/chart';
  import type { EChartsOption } from 'echarts';

  const { data } = useRequest(getMiddleTopCardData, {
    defaultParams: [new Date().getFullYear()],
  });
  const { runAsync } = useRequest(getGxqszrsData, {
    manual: true,
  });
  const topList = computed(() => [
    {
      title: '全年发病总人数',
      count: get(data.value, 'totalPeopleCount', 0),
      icon: 'hepatitis-total|svg',
      type: 0,
    },
    {
      title: '甲肝发病人数',
      count: get(data.value, 'havPeopleCount', 0),
      icon: 'hepatitis-type-a|svg',
      type: 3,
    },
    {
      title: '乙肝发病人数',
      count: get(data.value, 'hbvPeopleCount', 0),
      icon: 'hepatitis-type-b|svg',
      type: 1,
    },
    {
      title: '丙肝发病人数',
      count: get(data.value, 'hcvPeopleCount', 0),
      icon: 'hepatitis-type-c|svg',
      type: 2,
    },
    {
      title: '戊肝发病人数',
      count: get(data.value, 'hevPeopleCount', 0),
      icon: 'hepatitis-type-d|svg',
      type: 5,
    },
  ]);
  const visible = ref(false);
  const activeTitle = ref('');
  const { getDataViewEle } = useDataViewEle();
  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions } = useECharts(chartRef as any);
  const chartOptionData = reactive({
    areaData: [] as any[],
    areaSeries0Data: [] as any[],
  });
  const chartOption: EChartsOption = reactive(
    genLzChart([{ name: '人数' }], {
      labelNames: ['人数'],
      valueSuffix: ['人'],
    }),
  );
  const chartConfigs = computed(() => {
    return [
      { path: 'xAxis.0.data', val: chartOptionData.areaData },
      {
        path: 'series.0.data',
        val: chartOptionData.areaSeries0Data,
      },
    ];
  });
  const params = reactive<{
    year: number;
    hepatitisType: number;
  }>({
    year: new Date().getFullYear(),
    hepatitisType: 0,
  });
  const handleClick = (title: string, type: number) => {
    activeTitle.value = title;
    params.hepatitisType = type;
    runAsync(params).then((res) => {
      chartOptionData.areaData = map(res, 'x');
      chartOptionData.areaSeries0Data = map(res, 'yvalue');
      nextTick(() => {
        visible.value = true;
        const optionCopy = cloneDeep(chartOption);
        chartConfigs.value.forEach((config) => {
          const { path, val } = config;
          // if (typeof val === 'function') set(optionCopy, path, val(visible.value));
          // else set(optionCopy, path, val);
          set(optionCopy, path, val);
        });
        set(optionCopy, 'legend.top', 10);
        set(optionCopy, 'legend.orient', 'horizontal');
        if (optionCopy.yAxis) {
          if (Array.isArray(optionCopy.yAxis)) {
            optionCopy.yAxis?.forEach((item) => {
              // @ts-ignore
              item.splitNumber = 5;
            });
          } else {
            // @ts-ignore
            optionCopy.yAxis.splitNumber = 5;
          }
        }
        setOptions(optionCopy);
      });
    });
  };
</script>

<template>
  <div class="flex justify-center mt-10">
    <StatCard
      class="flex-1 justify-center"
      v-for="item in topList"
      :key="item.title"
      :title="item.title"
      :count="item.count"
      @click="handleClick(item.title, item.type)"
    >
      <template #icon> <Icon :size="80" :icon="item.icon" /></template>
    </StatCard>
    <Modal
      v-model:visible="visible"
      :get-container="getDataViewEle"
      centered
      width="calc(100% - 358px)"
      wrap-class-name="data-view-modal"
      :mask-style="{ 'backdrop-filter': 'blur(6px)', background: 'rgba(0, 0, 0, 0.5)' }"
      :closable="false"
      :footer="null"
    >
      <div class="relative">
        <div ref="chartRef" style="width: 100%; height: 600px; padding: 30px 20px 20px"></div>
        <div class="absolute" style="top: 12px; left: 20px; color: #fff; font-size: 20px">
          {{ activeTitle }}
        </div>
      </div>
    </Modal>
  </div>
</template>
<style scoped></style>
