import type { Ref } from 'vue';
import { computed, reactive, ref, watch } from 'vue';
import type { EChartsOption } from 'echarts';
import { genLzChart } from '/@/helper/chart';
import type { ChartData } from '/@/helper/useChartData';
import {
  getAnnualVaccinationStatistics,
  getHBVPatientStatistics,
  getHBVPrevalenceStatistics,
  getTBPatientStatistics,
  getTBPrevalenceStatistics,
  getVaccinationStatistics,
} from '/@/api/vaccination';
import { map } from 'lodash-es';

const 各类传染病疫苗接种情况 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
    keySeries2Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart([{ name: '接种人数' }], {
      labelNames: ['接种人数'],
      valueSuffix: ['人'],
    }),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getVaccinationStatistics().then((res) => {
        if (switchValue.value === 'key') {
          chartConfigData.areaData = map(res, 'diseaseName');
          chartConfigData.keySeries0Data = map(res, 'count');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '各类传染病疫苗接种情况',
  };
};
const 结核病患者疫苗接种情况 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
    keySeries2Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
        { path: 'series.2.data', val: chartConfigData.keySeries2Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart(
      [
        { name: '患病总人数' },
        { name: '接种后感染数量', iconType: 3, colorType: 2 },
        { name: '未接种感染数量', iconType: 4, colorType: 3 },
      ],
      {
        labelNames: ['患病总人数', '接种后感染数量', '未接种感染数量'],
        valueSuffix: ['人', '人', '人'],
      },
    ),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getTBPatientStatistics().then((res) => {
        if (switchValue.value === 'key') {
          chartConfigData.areaData = map(res, 'regionName');
          chartConfigData.keySeries0Data = map(res, 'illnessesCount');
          chartConfigData.keySeries1Data = map(res, 'vaccinationCount');
          chartConfigData.keySeries2Data = map(res, 'unVaccinatedCount');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '结核病患者疫苗接种情况',
  };
};
const 结核病疫苗接种患病情况 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
    keySeries2Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart(
      [
        { name: '接种疫苗数量' },
        { name: '接种后感染率', iconType: 6, colorType: 4, isLine: true, yIndex: 1 },
      ],
      {
        y1: {
          show: true,
          percent: true,
        },
        labelNames: ['接种疫苗数量', '接种后感染率'],
        valueSuffix: ['%', '%'],
      },
    ),
  );
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getTBPrevalenceStatistics().then((res) => {
        if (switchValue.value === 'key') {
          chartConfigData.areaData = map(res, 'regionName');
          chartConfigData.keySeries0Data = map(res, 'vaccinationCount');
          chartConfigData.keySeries1Data = map(res, 'infectionRate');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '结核病疫苗接种患病情况',
  };
};
const 传染病各年度接种情况 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
    keySeries2Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart([{ name: '结核病' }, { name: '乙型肝炎', iconType: 3, colorType: 2 }], {
      labelNames: ['结核病', '乙型肝炎'],
      valueSuffix: ['人', '人'],
    }),
  );

  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getAnnualVaccinationStatistics().then((res) => {
        if (switchValue.value === 'key') {
          chartConfigData.areaData = map(res, 'year');
          chartConfigData.keySeries0Data = map(res, 'tbVaccinationCount');
          chartConfigData.keySeries1Data = map(res, 'hbvVaccinationCount');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '传染病各年度接种情况',
  };
};

const 乙型病毒性肝炎患者疫苗接种情况 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
    keySeries2Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
        { path: 'series.2.data', val: chartConfigData.keySeries2Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart(
      [
        { name: '患病总人数' },
        { name: '接种后感染数量', iconType: 3, colorType: 2 },
        { name: '未接种感染数量', iconType: 4, colorType: 3 },
      ],
      {
        labelNames: ['患病总人数', '接种后感染数量', '未接种感染数量'],
        valueSuffix: ['人', '人', '人'],
      },
    ),
  );

  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getHBVPatientStatistics().then((res) => {
        if (switchValue.value === 'key') {
          chartConfigData.areaData = map(res, 'regionName');
          chartConfigData.keySeries0Data = map(res, 'illnessesCount');
          chartConfigData.keySeries1Data = map(res, 'vaccinationCount');
          chartConfigData.keySeries2Data = map(res, 'unVaccinatedCount');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '乙型病毒性肝炎患者疫苗接种情况',
  };
};

const 乙型病毒性肝炎疫苗接种患病情况 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries1Data: [] as any[],
    keySeries2Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.areaData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
        { path: 'series.1.data', val: chartConfigData.keySeries1Data },
        { path: 'series.2.data', val: chartConfigData.keySeries2Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(
    genLzChart(
      [
        { name: '接种疫苗数量' },
        { name: '接种后感染率', iconType: 6, colorType: 4, isLine: true, yIndex: 1 },
      ],
      {
        y1: {
          show: true,
          percent: true,
        },
        labelNames: ['接种疫苗数量', '接种后感染率'],
        valueSuffix: ['%', '人%', '%'],
      },
    ),
  );

  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getHBVPrevalenceStatistics().then((res) => {
        if (switchValue.value === 'key') {
          chartConfigData.areaData = map(res, 'regionName');
          chartConfigData.keySeries0Data = map(res, 'vaccinationCount');
          chartConfigData.keySeries1Data = map(res, 'infectionRate');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '乙型病毒性肝炎疫苗接种患病情况',
  };
};

export const chartConfigMap = {
  各类传染病疫苗接种情况,
  结核病患者疫苗接种情况,
  结核病疫苗接种患病情况,
  传染病各年度接种情况,
  乙型病毒性肝炎患者疫苗接种情况,
  乙型病毒性肝炎疫苗接种患病情况,
};
