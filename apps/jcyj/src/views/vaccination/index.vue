<script setup lang="ts">
  import Layout from '/@/layout/content.vue';
  import { onMounted, provide } from 'vue';
  import { chartConfigMap } from './config';
  import CommonTabChart from '/@/component/common-tab-chart/index.vue';
  import { useMapStore } from '/@/store/modules/map';
  import { getAreaNumberOfPeopleStatistics } from '/@/api/vaccination';
  provide('chartConfigMap', chartConfigMap);

  const { register } = useMapStore();
  onMounted(() => {
    getAreaNumberOfPeopleStatistics().then((res) => {
      register({
        position: 'center-6',
        points: (res || []).map((item) => ({
          divisionName: item.regionName,
          tubCount: item.tbVaccinationCount,
          hepatitisCount: item.hbvVaccinationCount,
        })),
      });
    });
  });
</script>

<template>
  <Layout>
    <template #left>
      <div class="flex flex-col gap-20px">
        <CommonTabChart chartKey="各类传染病疫苗接种情况" />
        <CommonTabChart chartKey="结核病患者疫苗接种情况" />
        <CommonTabChart chartKey="结核病疫苗接种患病情况" />
      </div>
    </template>
    <template #content-top> </template>
    <template #content-bottom> </template>
    <template #right>
      <div class="flex flex-col gap-20px">
        <CommonTabChart chartKey="传染病各年度接种情况" />
        <CommonTabChart chartKey="乙型病毒性肝炎患者疫苗接种情况" />
        <CommonTabChart chartKey="乙型病毒性肝炎疫苗接种患病情况" />
      </div>
    </template>
  </Layout>
</template>
