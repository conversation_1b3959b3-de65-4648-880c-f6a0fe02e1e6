import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { areaSchema, yearSchema } from '../shared';

// 地区 结核病	乙肝 	小儿麻痹症	百日咳、白喉、破伤风
export const columns: BasicColumn[] = [
  {
    title: '地区',
    dataIndex: 'area',
    width: 180,
    fixed: 'left',
  },
  {
    title: '结核病',
    children: [
      {
        title: '患病总人次',
        dataIndex: 'field1',
        width: 120,
      },
      {
        title: '卡介苗',
        children: [
          {
            title: '接种人数',
            dataIndex: 'field2',
            width: 120,
          },
          {
            title: '接种后患病人数',
            dataIndex: 'field3',
            width: 120,
          },
          {
            title: '接种后感染率',
            dataIndex: 'field4',
            width: 120,
          },
          {
            title: '未接种疫苗数量',
            dataIndex: 'field2x',
            width: 120,
          },
        ],
      },
    ],
  },
  {
    title: '乙肝',
    children: [
      {
        title: '患病总人次',
        dataIndex: 'field5',
        width: 120,
      },
      {
        title: '乙肝疫苗（CHO）',
        children: [
          {
            title: '接种人数',
            dataIndex: 'field6',
            width: 120,
          },
          {
            title: '接种后患病人数',
            dataIndex: 'field7',
            width: 120,
          },
          {
            title: '接种后感染率',
            dataIndex: 'field8',
            width: 120,
          },
          {
            title: '未接种疫苗数量',
            dataIndex: 'field6x',
            width: 120,
          },
        ],
      },
      {
        title: '乙肝疫苗（酿酒酵母）',
        children: [
          {
            title: '接种人数',
            dataIndex: 'field9',
            width: 120,
          },
          {
            title: '接种后患病人数',
            dataIndex: 'field10',
            width: 120,
          },
          {
            title: '接种后感染率',
            dataIndex: 'field11',
            width: 120,
          },
          {
            title: '未接种疫苗数量',
            dataIndex: 'field9x',
            width: 120,
          },
        ],
      },
      {
        title: '乙肝疫苗（汉逊酵母）',
        children: [
          {
            title: '接种人数',
            dataIndex: 'field12',
            width: 120,
          },
          {
            title: '接种后患病人数',
            dataIndex: 'field13',
            width: 120,
          },
          {
            title: '接种后感染率',
            dataIndex: 'field14',
            width: 120,
          },
          {
            title: '未接种疫苗数量',
            dataIndex: 'field12x',
            width: 120,
          },
        ],
      },
    ],
  },
  {
    title: '小儿麻痹症',
    children: [
      {
        title: '患病总人次',
        dataIndex: 'field15',
        width: 120,
      },
      {
        title: '脊灰糖丸（二倍体）',
        children: [
          {
            title: '接种人数',
            dataIndex: 'field16',
            width: 120,
          },
          {
            title: '接种后患病人数',
            dataIndex: 'field17',
            width: 120,
          },
          {
            title: '接种后感染率',
            dataIndex: 'field18',
            width: 120,
          },
          {
            title: '未接种疫苗数量',
            dataIndex: 'field16x',
            width: 120,
          },
        ],
      },
      {
        title: '脊灰糖丸（猴肾）',
        children: [
          {
            title: '接种人数',
            dataIndex: 'field19',
            width: 120,
          },
          {
            title: '接种后患病人数',
            dataIndex: 'field20',
            width: 120,
          },
          {
            title: '接种后感染率',
            dataIndex: 'field21',
            width: 120,
          },
          {
            title: '未接种疫苗数量',
            dataIndex: 'field19x',
            width: 120,
          },
        ],
      },
      {
        title: '脊灰灭活疫苗（Salk株）',
        children: [
          {
            title: '接种人数',
            dataIndex: 'field22',
            width: 120,
          },
          {
            title: '接种后患病人数',
            dataIndex: 'field23',
            width: 120,
          },
          {
            title: '接种后感染率',
            dataIndex: 'field24',
            width: 120,
          },
          {
            title: '未接种疫苗数量',
            dataIndex: 'field22x',
            width: 120,
          },
        ],
      },
      {
        title: '脊灰减毒疫苗（液体）',
        children: [
          {
            title: '接种人数',
            dataIndex: 'field25',
            width: 120,
          },
          {
            title: '接种后患病人数',
            dataIndex: 'field26',
            width: 120,
          },
          {
            title: '接种后感染率',
            dataIndex: 'field27',
            width: 120,
          },
          {
            title: '未接种疫苗数量',
            dataIndex: 'field25x',
            width: 120,
          },
        ],
      },
      {
        title: '脊灰灭活疫苗（Sabin株）',
        children: [
          {
            title: '接种人数',
            dataIndex: 'field28',
            width: 120,
          },
          {
            title: '接种后患病人数',
            dataIndex: 'field29',
            width: 120,
          },
          {
            title: '接种后感染率',
            dataIndex: 'field30',
            width: 120,
          },
          {
            title: '未接种疫苗数量',
            dataIndex: 'field28x',
            width: 120,
          },
        ],
      },
      {
        title: '二价脊灰疫苗（液体）',
        children: [
          {
            title: '接种人数',
            dataIndex: 'field31',
            width: 120,
          },
          {
            title: '接种后患病人数',
            dataIndex: 'field32',
            width: 120,
          },
          {
            title: '接种后感染率',
            dataIndex: 'field33',
            width: 120,
          },
          {
            title: '未接种疫苗数量',
            dataIndex: 'field31x',
            width: 120,
          },
        ],
      },
      {
        title: '二价脊灰疫苗（糖丸）',
        children: [
          {
            title: '接种人数',
            dataIndex: 'field34',
            width: 120,
          },
          {
            title: '接种后患病人数',
            dataIndex: 'field35',
            width: 120,
          },
          {
            title: '接种后感染率',
            dataIndex: 'field36',
            width: 120,
          },
          {
            title: '未接种疫苗数量',
            dataIndex: 'field34x',
            width: 120,
          },
        ],
      },
    ],
  },
  {
    title: '百日咳、白喉、破伤风',
    children: [
      {
        title: '患病总人次',
        dataIndex: 'field37',
        width: 120,
      },
      {
        title: '百日破疫苗（全细胞）',
        dataIndex: 'field38',
        width: 160,
      },
      {
        title: '百日破疫苗（无细胞）',
        dataIndex: 'field39',
        width: 160,
      },
      {
        title: '百日破疫苗（青少年用）',
        dataIndex: 'field40',
        width: 180,
      },
      {
        title: '百白',
        dataIndex: 'field41',
        width: 120,
      },
    ],
  },
];

export const SearchSchemas: FormSchema[] = [yearSchema, areaSchema];
