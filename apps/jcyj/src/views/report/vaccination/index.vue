<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { exportUtil } from '@ft/internal/utils';
  import { useRequest } from '@ft/request';
  import { omit } from 'lodash-es';
  import { nextTick, ref } from 'vue';
  import { SearchSchemas, columns } from './data';
  import { exportVaccination, queryList } from '/@/api/report/vaccination';

  const year = ref('2025');
  const [register, tableAction] = useTable({
    api: (params) => queryList(params.areaCode, params.year),
    formConfig: {
      labelWidth: 100,
      schemas: SearchSchemas,
      submitFunc: async () => {
        nextTick(() => {
          year.value = tableAction.getForm().getFieldsValue().year;
          tableAction.reload();
        });
      },
      resetFunc: async () => {
        const values = tableAction.getForm().getFieldsValue();
        if (values.divisionCode !== undefined) {
          values.divisionCode = undefined;
        }
        tableAction.getForm().setFieldsValue({
          ...values,
        });
      },
    },
    useSearchForm: true,
    showIndexColumn: false,
    columns,
    beforeFetch: (params) => {
      params.areaCode = params.divisionCode;
      return omit(params, ['divisionCode']);
    },
    bordered: true,
    immediate: false,
  });
  const { loading: exportTotalLoading, runAsync: exportExpertRunAsync } = useRequest(
    exportVaccination,
    {
      manual: true,
      showSuccessMessage: true,
    },
  );

  function handleExportTotal() {
    exportUtil(
      exportExpertRunAsync(
        tableAction.getForm().getFieldsValue().divisionCode,
        tableAction.getForm().getFieldsValue().year,
      ),
    );
  }
</script>
<template>
  <div>
    <div class="rounded-md p-4">
      <BasicTable class="ft-main-table" @register="register">
        <template #tableTitle>
          <div class="w-full">
            <Button
              pre-icon="ant-design:download-outlined"
              size="small"
              class="mr-2 mt-2 float-right"
              type="link"
              @click="handleExportTotal"
              :loading="exportTotalLoading"
            >
              导出
            </Button>
            <div class="font-bold text-xl text-center mt-10">
              {{ year }}年疫苗接种后患传染病统计
            </div>
          </div>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
