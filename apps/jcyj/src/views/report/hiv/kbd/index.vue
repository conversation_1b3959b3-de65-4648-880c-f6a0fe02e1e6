<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { nextTick, ref } from 'vue';
  import { exportUtil } from '@ft/internal/utils';
  import { useRequest } from '@ft/request';
  import { omit } from 'lodash-es';
  import { SearchSchemas, columns } from './data';
  import { exportAidsMonitor, queryList } from '/@/api/report/hiv';

  const [register, tableAction] = useTable({
    api: queryList,
    formConfig: {
      labelWidth: 100,
      schemas: SearchSchemas,
      fieldMapToTime: [['date', ['startTime', 'endTime'], 'YYYY-MM-DD']],
      submitFunc: async () => {
        nextTick(() => {
          year.value = tableAction.getForm().getFieldsValue().year;
          tableAction.reload();
        });
      },
      resetFunc: async () => {
        const values = tableAction.getForm().getFieldsValue();
        if (values.divisionCode !== undefined) {
          values.divisionCode = undefined;
        }
        tableAction.getForm().setFieldsValue({
          ...values,
        });
      },
    },
    useSearchForm: true,
    showIndexColumn: false,
    columns,
    immediate: false,
    beforeFetch: (params) => {
      params.divisionId = params.divisionCode;
      return omit(params, ['divisionCode']);
    },
    bordered: true,
  });
  const year = ref('2024');
  const { loading: exportTotalLoading, runAsync: exportExpertRunAsync } = useRequest(
    exportAidsMonitor,
    {
      manual: true,
      showSuccessMessage: true,
    },
  );

  function handleExportTotal() {
    exportUtil(
      exportExpertRunAsync({
        divisionId: tableAction.getForm().getFieldsValue().divisionCode,
        ...omit(tableAction.getForm().getFieldsValue(), ['divisionCode']),
      }),
    );
  }
</script>
<template>
  <!-- <div> -->
  <div class="rounded-md">
    <BasicTable class="" @register="register">
      <template #tableTitle>
        <div class="w-full">
          <Button
            pre-icon="ant-design:download-outlined"
            size="small"
            class="mr-2 mt-2 float-right"
            type="link"
            @click="handleExportTotal"
            :loading="exportTotalLoading"
          >
            导出
          </Button>
          <div class="font-bold text-xl text-center mt-10">
            {{ year }}年HIV抗病毒治疗各项指标统计
          </div>
        </div>
      </template>
    </BasicTable>
  </div>
  <!-- </div> -->
</template>
