import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { areaSchema, yearSchema } from '../../shared';

// 月份 HIV抗体检测人数	HIV现存活	当年新确诊	CD4检测人数	病毒学效果	死亡人数	停药人数	失访人数
export const columns: BasicColumn[] = [
  {
    title: '月份',
    dataIndex: 'month',
    width: 120,
    fixed: 'left',
  },
  {
    title: 'HIV抗体检测人数',
    // 门诊就诊人数	门诊检测人数	门诊检测率	住院人数	住院检测人数	住院检测率	院外筛查人次
    children: [
      {
        title: '门诊就诊人数',
        dataIndex: 'outPatientNum',
        width: 120,
      },
      {
        title: '门诊检测人数',
        dataIndex: 'outPatientTestNum',
        width: 120,
      },
      {
        title: '门诊检测率',
        dataIndex: 'outPatientTestRate',
        width: 120,
      },
      {
        title: '住院人数',
        dataIndex: 'beHospitalNum',
        width: 120,
      },
      {
        title: '住院检测人数',
        dataIndex: 'beHospitalTestNum',
        width: 120,
      },
      {
        title: '住院检测率',
        dataIndex: 'beHospitalTestRate',
        width: 120,
      },
      {
        title: '院外筛查人次',
        dataIndex: 'outScreeningNum',
        width: 120,
      },
    ],
  },
  {
    title: 'HIV现存活',
    // 现存活人数 在治人数 治疗覆盖率%
    children: [
      {
        title: '现存活人数',
        dataIndex: 'hbvSurvivalNum',
        width: 120,
      },
      {
        title: '在治人数',
        dataIndex: 'hbvCureNum',
        width: 120,
      },
      {
        title: '治疗覆盖率%',
        dataIndex: 'hbvCureRate',
        width: 120,
      },
    ],
  },
  {
    title: '当年新确诊',
    // 报告人数	30天及时治疗人数	及时治疗率%	ART平均治疗时效
    children: [
      {
        title: '报告人数',
        dataIndex: 'reportNum',
        width: 120,
      },
      {
        title: '30天及时治疗人数',
        dataIndex: 'thirtyDayCureNum',
        width: 150,
      },
      {
        title: '及时治疗率%',
        dataIndex: 'inTimeCureRate',
        width: 120,
      },
      {
        title: 'ART平均治疗时效',
        dataIndex: 'artCureAverage',
        width: 150,
      },
    ],
  },
  {
    title: 'CD4检测人数',
    // CD4应检测人数	CD4实际检测人数	CD4检测率%
    children: [
      {
        title: 'CD4应检测人数',
        dataIndex: 'cd4ShouldTestNum',
        width: 120,
      },
      {
        title: 'CD4实际检测人数',
        dataIndex: 'cd4RealityNum',
        width: 150,
      },
      {
        title: 'CD4检测率%',
        dataIndex: 'cd4TestRate',
        width: 120,
      },
    ],
  },
  {
    title: '病毒学效果',
    // 病载应检测人数	病载实际检测人数	病载检测率%	病毒抑制人数<1000	治疗成功率%	病毒抑制人数<50	病毒抑制率%	低病毒血症%
    children: [
      {
        title: '病载应检测人数',
        dataIndex: 'virusShouldNum',
        width: 120,
      },
      {
        title: '病载实际检测人数',
        dataIndex: 'virusRealityNum',
        width: 150,
      },
      {
        title: '病载检测率%',
        dataIndex: 'virusTestRate',
        width: 120,
      },
      {
        title: '病毒抑制人数<1000',
        dataIndex: 'virusThousandNum',
        width: 150,
      },
      {
        title: '治疗成功率%',
        dataIndex: 'cureSuccessRate',
        width: 120,
      },
      {
        title: '病毒抑制人数<50',
        dataIndex: 'virusFiftyNum',
        width: 150,
      },
      {
        title: '病毒抑制率%',
        dataIndex: 'viralInhibitionRate',
        width: 120,
      },
      {
        title: '低病毒血症%',
        dataIndex: 'virusBloodRate',
        width: 120,
      },
    ],
  },
  {
    title: '死亡人数',
    // 当年死亡人数	累计死亡人数
    children: [
      {
        title: '当年死亡人数',
        dataIndex: 'deathYearNum',
        width: 120,
      },
      {
        title: '累计死亡人数',
        dataIndex: 'totalDeathNum',
        width: 120,
      },
    ],
  },
  {
    title: '停药人数',
    // 当年停药人数	累计死亡人数
    children: [
      {
        title: '当年停药人数',
        dataIndex: 'stopDrugNum',
        width: 120,
      },
      {
        title: '累计死亡人数',
        dataIndex: 'stopDrugDeathNum',
        width: 120,
      },
    ],
  },
  {
    title: '失访人数',
    //当年失访人数	累计失访人数
    children: [
      {
        title: '当年失访人数',
        dataIndex: 'lossFollowNum',
        width: 120,
      },
      {
        title: '累计失访人数',
        dataIndex: 'lossFollowTotalNum',
        width: 120,
      },
    ],
  },
];

export const SearchSchemas: FormSchema[] = [
  areaSchema,
  yearSchema,
  {
    field: 'date',
    label: '统计时间',
    colProps: { span: 6 },
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      style: { width: '100%' },
    },
  },
];
