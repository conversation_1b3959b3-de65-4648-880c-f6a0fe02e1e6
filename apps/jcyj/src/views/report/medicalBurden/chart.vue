<script lang="ts" setup>
  import { ref } from 'vue';
  import { useRequest } from '@ft/request';
  import { getMedicalBurdenStatistic } from '/@/api/report/medicalBurden';
  import { useECharts } from '@ft/internal/hooks/web/useECharts';
  import { graphic } from 'echarts/core';

  const props = defineProps<{ year: number; areaCode: string; areaName: string }>();
  useRequest(
    () =>
      getMedicalBurdenStatistic({
        year: props.year,
        areaCode: props.areaCode,
      }),
    {
      refreshDeps: [() => props.year, () => props.areaCode],
      onSuccess(res) {
        const type1lineChartVOS = res.find((item) => item.type === 1)?.lineChartVOS;
        const type2lineChartVOS = res.find((item) => item.type === 2)?.lineChartVOS;
        const type3lineChartVOS = res.find((item) => item.type === 3)?.lineChartVOS;
        setZzqsOptions({
          title: {
            text: (props.areaName || '全市') + '发热门诊疾病就诊量增长趋势',
            textStyle: {
              fontSize: 18,
            },
          },
          tooltip: {
            trigger: 'axis',
            appendToBody: true,
          },
          grid: {
            top: 78,
            left: '8%',
            right: '8%',
            bottom: '10%',
          },
          legend: {
            top: 42,
          },
          xAxis: {
            data: type1lineChartVOS.map((item) => item.x),
            type: 'category',
            axisTick: {
              show: false,
            },
            axisLabel: {
              color: '#B0B1B4',
            },
            axisLine: {
              lineStyle: {
                color: '#EDEEF0',
              },
            },
          },
          yAxis: {
            type: 'value',
            splitLine: {
              lineStyle: {
                color: '#EDEEF0',
              },
              interval: 30,
            },
            axisLabel: {
              color: '#B0B1B4',
            },
          },
          series: [
            {
              name: '发热门诊就诊量',
              type: 'line',
              smooth: true,
              data: type1lineChartVOS.map((item) => item.yvalue),
              itemStyle: {
                color: '#33BC71',
              },
              areaStyle: {
                color: new graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(86, 255, 168, 0.16)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(148, 155, 253, 0)',
                  },
                ]),
              },
            },
          ],
        });
        setJzqsRefOptions({
          title: {
            text: (props.areaName || '全市') + '发热门诊疾病当前年度就诊趋势',
            textStyle: {
              fontSize: 18,
            },
          },
          tooltip: {
            trigger: 'axis',
            appendToBody: true,
          },
          grid: {
            top: 78,
            left: '10%',
            right: '8%',
            bottom: '10%',
          },
          legend: {
            top: 42,
          },
          xAxis: {
            type: 'value',
            splitLine: {
              lineStyle: {
                color: '#EDEEF0',
              },
              interval: 30,
            },
            axisLabel: {
              color: '#B0B1B4',
            },
          },
          yAxis: {
            data: type2lineChartVOS.map((item) => item.x),
            type: 'category',
            axisTick: {
              show: false,
            },
            axisLabel: {
              color: '#B0B1B4',
              fontSize: 10,
            },
            axisLine: {
              lineStyle: {
                color: '#EDEEF0',
              },
            },
          },
          series: [
            {
              name: '就诊人数',
              data: type2lineChartVOS.map((item) => item.yvalue),
              type: 'bar',
              barWidth: 6,
              itemStyle: {
                borderRadius: [0, 5, 5, 0],
              },
            },
          ],
        });
        setNnfbOptions({
          title: {
            text: (props.areaName || '全市') + '发热门诊患者当前年度就诊年龄分布',
            textStyle: {
              fontSize: 18,
            },
          },
          tooltip: {
            trigger: 'axis',
            appendToBody: true,
            valueFormatter: (value: any) => `${value}%`,
          },
          grid: {
            top: 78,
            left: '8%',
            right: '8%',
            bottom: '10%',
          },
          legend: {
            top: 42,
          },
          xAxis: {
            data: type3lineChartVOS.map((item) => item.x),
            axisTick: {
              show: false,
            },
            axisLabel: {
              color: '#B0B1B4',
              interval: 0,
            },
            axisLine: {
              lineStyle: {
                color: '#EDEEF0',
              },
            },
          },
          yAxis: {
            splitLine: {
              lineStyle: {
                color: '#EDEEF0',
              },
              interval: 30,
            },
            axisLabel: {
              color: '#B0B1B4',
              formatter: `{value}%`,
            },
          },
          series: [
            {
              name: '就诊人数',
              data: type3lineChartVOS.map((item) => item.y),
              type: 'bar',
              barWidth: 6,
              itemStyle: {
                borderRadius: [5, 5, 0, 0],
              },
            },
          ],
        });
      },
    },
  );
  const zzqsRef = ref();
  const jzqsRef = ref();
  const nnfbRef = ref();
  const { setOptions: setZzqsOptions } = useECharts(zzqsRef);
  const { setOptions: setJzqsRefOptions } = useECharts(jzqsRef);
  const { setOptions: setNnfbOptions } = useECharts(nnfbRef);
</script>
<template>
  <div class="h-280px w-full flex">
    <div class="w-33.33% h-280px" ref="zzqsRef"></div>
    <div class="w-33.33% h-280px" ref="jzqsRef"></div>
    <div class="w-33.33% h-280px" ref="nnfbRef"></div>
  </div>
</template>
