import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { areaSchema, yearSchema } from '../shared';

// 地区 发热门诊就诊量	感染科门诊	感染科住院	感染科手术 感染科
export const columns: BasicColumn[] = [
  {
    title: '地区',
    dataIndex: 'area',
    width: 150,
  },
  {
    title: '发热门诊就诊量',
    dataIndex: 'feverOutpatientVisits',
    width: 120,
  },
  {
    title: '感染科门诊',
    // 	就诊量 各医生平均接诊量
    children: [
      {
        title: '就诊量',
        dataIndex: 'outpatientVisits',
        width: 120,
      },
      {
        title: '各医生平均接诊量',
        dataIndex: 'avgDocVisits',
        width: 150,
      },
    ],
  },
  {
    title: '感染科住院',
    // 就诊量 平均住院日
    children: [
      {
        title: '就诊量',
        dataIndex: 'inpatientVisits',
        width: 120,
      },
      {
        title: '平均住院日',
        dataIndex: 'avgHospitalDay',
        width: 120,
      },
    ],
  },
  {
    title: '感染科手术',
    // I级 II级 III级 IV级
    children: [
      {
        title: 'I级',
        dataIndex: 'oneLevelOperation',
        width: 120,
      },
      {
        title: 'II级',
        dataIndex: 'twoLevelOperation',
        width: 120,
      },
      {
        title: 'III级',
        dataIndex: 'threeLevelOperation',
        width: 120,
      },
      {
        title: 'IV级',
        dataIndex: 'fourLevelOperation',
        width: 120,
      },
    ],
  },
  {
    title: '感染科',
    // 传染病死亡率 医患比 护患比
    children: [
      {
        title: '传染病死亡率',
        dataIndex: 'deathRate',
        width: 120,
      },
      {
        title: '医患比',
        dataIndex: 'doctorPatientRatio',
        width: 120,
      },
      {
        title: '护患比',
        dataIndex: 'nursePatientRatio',
        width: 120,
      },
    ],
  },
];

export const SearchSchemas: FormSchema[] = [
  yearSchema,
  areaSchema,
  {
    field: 'areaName',
    label: '地区名称',
    show: false,
    component: 'Input',
  },
];
