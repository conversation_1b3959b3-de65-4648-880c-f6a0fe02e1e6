<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { nextTick, ref } from 'vue';
  import { exportUtil } from '@ft/internal/utils';
  import { useRequest } from '@ft/request';
  import { omit } from 'lodash-es';
  import { SearchSchemas, columns } from './data';
  import { exportMedicalBurden, queryList } from '/@/api/report/medicalBurden';
  import ChartComp from './chart.vue';

  const year = ref(2024);
  const areaCode = ref('');
  const areaName = ref('');

  const [register, tableAction] = useTable({
    api: queryList,
    formConfig: {
      labelWidth: 100,
      schemas: SearchSchemas,
      submitFunc: async () => {
        nextTick(() => {
          const values = tableAction.getForm().getFieldsValue();
          year.value = values.year;
          areaCode.value = values.divisionCode;
          areaName.value = values.areaName;
          tableAction.reload();
        });
      },
      resetFunc: async () => {
        const values = tableAction.getForm().getFieldsValue();
        if (values.divisionCode !== undefined) {
          values.divisionCode = undefined;
        }
        tableAction.getForm().setFieldsValue({
          ...values,
        });
      },
    },
    useSearchForm: true,
    showIndexColumn: false,
    columns,
    immediate: false,
    beforeFetch: (params) => {
      params.areaCode = params.divisionCode;
      return omit(params, ['divisionCode']);
    },
    bordered: true,
  });
  const { loading: exportTotalLoading, runAsync: exportExpertRunAsync } = useRequest(
    exportMedicalBurden,
    {
      manual: true,
      showSuccessMessage: true,
    },
  );

  function handleExport() {
    exportUtil(
      exportExpertRunAsync({
        areaCode: tableAction.getForm().getFieldsValue().divisionCode,
        ...omit(tableAction.getForm().getFieldsValue(), ['divisionCode']),
      }),
    );
  }
</script>
<template>
  <div class="rounded-md p-4">
    <BasicTable class="ft-main-table" @register="register">
      <template #headerTop>
        <ChartComp :year="year" :areaCode="areaCode" :areaName="areaName" />
      </template>
      <template #tableTitle>
        <div class="w-full flex items-center justify-between mt-4">
          <div class="font-bold text-lg text-center"> {{ year }}年传染病医疗负担分析 </div>
          <Button
            pre-icon="ant-design:download-outlined"
            size="small"
            class="block"
            type="link"
            @click="handleExport"
            :loading="exportTotalLoading"
          >
            导出
          </Button>
        </div>
      </template>
    </BasicTable>
  </div>
</template>
