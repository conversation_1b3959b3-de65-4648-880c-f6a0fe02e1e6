<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { exportUtil } from '@ft/internal/utils';
  import { useRequest } from '@ft/request';
  import { nextTick, ref } from 'vue';
  import { SearchSchemas, columns } from './data';
  import {
    exportMedicationStatisticsData,
    queryMedicationStatisticsData,
  } from '/@/api/report/prevention';

  const [register, tableAction] = useTable({
    api: queryMedicationStatisticsData,
    formConfig: {
      labelWidth: 100,
      schemas: SearchSchemas,
      submitFunc: async () => {
        nextTick(() => {
          year.value = tableAction.getForm().getFieldsValue().year;
          tableAction.reload();
        });
      },
      resetFunc: async () => {
        const values = tableAction.getForm().getFieldsValue();
        if (values.drugName !== undefined) {
          values.drugName = undefined;
        }
        if (values.infectionTypeCode !== undefined) {
          values.infectionTypeCode = undefined;
        }
        tableAction.getForm().setFieldsValue({
          ...values,
        });
      },
    },
    immediate: false,
    useSearchForm: true,
    showIndexColumn: false,
    columns,
    bordered: true,
  });
  const year = ref('');
  const { loading: exportTotalLoading, runAsync: exportExpertRunAsync } = useRequest(
    exportMedicationStatisticsData,
    {
      manual: true,
      showSuccessMessage: true,
    },
  );

  function handleExportTotal() {
    exportUtil(
      exportExpertRunAsync({
        ...tableAction.getForm().getFieldsValue(),
      }),
    );
  }
</script>
<template>
  <div>
    <div class="rounded-md p-4">
      <BasicTable class="ft-main-table" @register="register">
        <template #tableTitle>
          <div class="w-full">
            <Button
              pre-icon="ant-design:download-outlined"
              size="small"
              class="mr-2 mt-2 float-right"
              type="link"
              @click="handleExportTotal"
              :loading="exportTotalLoading"
            >
              导出
            </Button>
            <div class="font-bold text-xl text-center mt-10"> {{ year }}年传染病用药统计 </div>
          </div>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
