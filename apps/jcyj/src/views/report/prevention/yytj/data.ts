import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { DictEnum, getDictItemList } from '@ft/internal/api';
import { yearSchema } from '../../shared';
// 传染病药品 用药次数排名 药品使用量

export const columns: BasicColumn[] = [
  {
    title: '传染病药品',
    dataIndex: 'drugName',
    width: 120,
  },
  {
    title: '传染病分类',
    dataIndex: 'infectionTypeName',
    width: 120,
  },
  {
    title: '用药次数排名',
    dataIndex: 'rankingOfMedicationFrequency',
    width: 120,
  },
  {
    title: '药品使用量',
    dataIndex: 'drugUsage',
    width: 120,
  },
];

export const SearchSchemas: FormSchema[] = [
  yearSchema,
  {
    field: 'drugName',
    component: 'Input',
    label: '传染病药品',
    colProps: {
      span: 6,
    },
  },
  {
    field: 'infectionTypeCode',
    component: 'ApiSelect',
    label: '传染病分类',
    componentProps: {
      api: () => getDictItemList(DictEnum.INFECTION_DRUG_TYPE),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      allowClear: true,
      getPopupContainer() {
        return document.body;
      },
    },
    colProps: {
      span: 6,
    },
  },
];
