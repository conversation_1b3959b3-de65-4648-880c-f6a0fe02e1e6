import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { areaSchema, orgSchema, patientSchema, yearSchema } from '../../shared';

// 地区 鼠疫 霍乱	新型冠状病毒感染	猴痘	狂犬病	百日咳	痰疸	登革热 布鲁氏菌病 新生儿破伤风 人感染H7N9禽流感 血吸虫病
export const listColumns: BasicColumn[] = [
  {
    title: '地区',
    dataIndex: 'divisionName',
    width: 150,
    fixed: 'left',
  },
  {
    title: '鼠疫',
    dataIndex: 'plaguePatientCount',
    width: 120,
  },
  {
    title: '霍乱',
    dataIndex: 'choleraPatientCount',
    width: 120,
  },
  {
    title: '新型冠状病毒感染',
    dataIndex: 'covidPatientCount',
    width: 150,
  },
  {
    title: '猴痘',
    dataIndex: 'monkeypoxPatientCount',
    width: 120,
  },
  {
    title: '狂犬病',
    dataIndex: 'rabiesPatientCount',
    width: 120,
  },
  {
    title: '百日咳',
    dataIndex: 'pertussisPatientCount',
    width: 120,
  },
  {
    title: '痰疸',
    dataIndex: 'anthraxPatientCount',
    width: 120,
  },
  {
    title: '登革热',
    dataIndex: 'dengueFeverPatientCount',
    width: 120,
  },
  {
    title: '布鲁氏菌病',
    dataIndex: 'brucellosisPatientCount',
    width: 120,
  },
  {
    title: '新生儿破伤风',
    dataIndex: 'neonatalTetanusPatientCount',
    width: 120,
  },
  {
    title: '人感染H7N9禽流感',
    dataIndex: 'h7n9PatientCount',
    width: 150,
  },
  {
    title: '血吸虫病',
    dataIndex: 'schistosomiasisPatientCount',
    width: 120,
  },
];

// 医疗机构 鼠疫 霍乱 新型冠状病毒感染 猴痘 狂犬病 百日咳 痰疸 登革热 布鲁氏菌病 新生儿破伤风 人感染H7N9禽流感 血吸虫病
export const orgColumns: BasicColumn[] = [
  {
    title: '医疗机构',
    dataIndex: 'hospitalName',
    width: 200,
  },
  {
    title: '鼠疫',
    dataIndex: 'plaguePatientCount',
    width: 120,
  },
  {
    title: '霍乱',
    dataIndex: 'choleraPatientCount',
    width: 120,
  },
  {
    title: '新型冠状病毒感染',
    dataIndex: 'covidPatientCount',
    width: 150,
  },
  {
    title: '猴痘',
    dataIndex: 'monkeypoxPatientCount',
    width: 120,
  },
  {
    title: '狂犬病',
    dataIndex: 'rabiesPatientCount',
    width: 120,
  },
  {
    title: '百日咳',
    dataIndex: 'pertussisPatientCount',
    width: 120,
  },
  {
    title: '痰疸',
    dataIndex: 'anthraxPatientCount',
    width: 120,
  },
  {
    title: '登革热',
    dataIndex: 'dengueFeverPatientCount',
    width: 120,
  },
  {
    title: '布鲁氏菌病',
    dataIndex: 'brucellosisPatientCount',
    width: 120,
  },
  {
    title: '新生儿破伤风',
    dataIndex: 'neonatalTetanusPatientCount',
    width: 120,
  },
  {
    title: '人感染H7N9禽流感',
    dataIndex: 'h7n9PatientCount',
    width: 150,
  },
  {
    title: '血吸虫病',
    dataIndex: 'schistosomiasisPatientCount',
    width: 120,
  },
];

//诊断日期 患者姓名 性别 年龄 身份证号 诊断 就诊机构 所属辖区
export const patientColumns: BasicColumn[] = [
  {
    title: '诊断日期',
    dataIndex: 'diagnosisDate',
    width: 120,
  },
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    width: 120,
  },
  {
    title: '性别',
    dataIndex: 'patientSex',
    width: 120,
  },
  {
    title: '年龄',
    dataIndex: 'patientAge',
    width: 120,
  },
  {
    title: '身份证号',
    dataIndex: 'idCard',
    width: 120,
  },
  {
    title: '诊断',
    dataIndex: 'diagnosisName',
    width: 120,
  },
  {
    title: '就诊机构',
    dataIndex: 'visitHospitalName',
    width: 120,
  },
  {
    title: '所属辖区',
    dataIndex: 'communityName',
    width: 120,
  },
];

export const listFormSchema: FormSchema[] = [yearSchema, areaSchema];
export const orgFormSchema: FormSchema[] = [yearSchema, areaSchema, orgSchema];
export const patientFormSchema: FormSchema[] = [yearSchema, areaSchema, orgSchema, patientSchema];
