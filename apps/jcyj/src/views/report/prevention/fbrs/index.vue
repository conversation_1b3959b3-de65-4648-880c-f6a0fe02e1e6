<script setup lang="ts">
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { nextTick, ref } from 'vue';
  import { exportUtil } from '@ft/internal/utils';
  import { useRequest } from '@ft/request';
  import {
    listColumns,
    listFormSchema,
    orgColumns,
    orgFormSchema,
    patientColumns,
    patientFormSchema,
  } from './data';
  import {
    exportIncidencePatientCountDataByDivision,
    exportIncidencePatientCountDataByHospital,
    exportIncidencePatientList,
    queryIncidencePatientCountByDivision,
    queryIncidencePatientCountByHospital,
    queryIncidencePatientList,
  } from '/@/api/report/prevention';

  const actionColumn = {
    width: 120,
    title: '操作',
    dataIndex: 'action',
  };
  const year = ref(2024);
  const listDivisionCode = ref('');
  const orgDivisionCode = ref('');
  const orgName = ref('');
  const patientOrgName = ref('');
  const patientName = ref('');
  const tableType = ref('list');
  const [register, tableFns] = useTable({
    api: queryIncidencePatientCountByDivision,
    formConfig: {
      labelWidth: 100,
      schemas: listFormSchema,
      submitFunc: async () => {
        nextTick(() => {
          const values = tableFns.getForm().getFieldsValue();
          console.log(43, values);
          year.value = values.year;
          if (tableType.value === 'list') {
            listDivisionCode.value = values.divisionCode;
          } else if (tableType.value === 'org') {
            orgDivisionCode.value = values.divisionCode;
            orgName.value = values.hospitalName;
          } else {
            patientOrgName.value = values.hospitalName;
            patientName.value = values.patientName;
          }
        });
        nextTick(() => {
          tableFns.reload();
        });
      },
      resetFunc: async () => {
        const values = tableFns.getForm().getFieldsValue();
        if (tableType.value === 'list' && values.divisionCode !== undefined) {
          values.divisionCode = undefined;
        }
        if (tableType.value === 'org' && values.hospitalName !== undefined) {
          values.hospitalName = undefined;
        }
        if (tableType.value === 'patient' && values.patientName !== undefined) {
          values.patientName = undefined;
        }
        tableFns.getForm().setFieldsValue({
          ...values,
        });
      },
    },
    useSearchForm: true,
    showIndexColumn: tableType.value === 'patient' ? true : false,
    immediate: false,
    columns: listColumns,
    actionColumn,
    bordered: true,
    beforeFetch: (params) => {
      if (tableType.value === 'patient') {
        params.hospitalCode = hospitalCode.value;
      }
      return params;
    },
  });

  async function switchTable(key: 'list' | 'org' | 'patient') {
    tableType.value = key;
    await tableFns.getForm()?.setFieldsValue({
      year: year.value,
      divisionCode: key === 'list' ? listDivisionCode.value : orgDivisionCode.value,
      hospitalName: key === 'org' ? orgName.value : patientOrgName.value,
      patientName: undefined,
    });
    tableFns.setProps({
      api:
        key === 'list'
          ? queryIncidencePatientCountByDivision
          : key === 'org'
          ? queryIncidencePatientCountByHospital
          : queryIncidencePatientList,
      columns: key === 'list' ? listColumns : key === 'org' ? orgColumns : patientColumns,
      actionColumn: key === 'patient' ? undefined : actionColumn,
    });
    if (key === 'list') {
      tableFns.getForm().resetSchema(listFormSchema);
    } else if (key === 'org') {
      tableFns.getForm().resetSchema(orgFormSchema);
      tableFns.getForm().updateSchema([
        { field: 'year', componentProps: { disabled: true } },
        { field: 'divisionCode', componentProps: { disabled: true } },
      ]);
    } else {
      tableFns.getForm().resetSchema(patientFormSchema);
      tableFns.getForm().updateSchema([
        { field: 'year', componentProps: { disabled: true } },
        { field: 'divisionCode', componentProps: { disabled: true } },
        { field: 'hospitalName', componentProps: { disabled: true } },
      ]);
    }
    nextTick(() => {
      tableFns.reload();
    });
  }
  const hospitalCode = ref('');
  function handleDetail(record) {
    if (tableType.value === 'list') {
      orgDivisionCode.value = record.divisionCode;
      switchTable('org');
    } else {
      patientOrgName.value = record.hospitalName === '汇总' ? undefined : record.hospitalName;
      hospitalCode.value = record.hospitalCode;
      switchTable('patient');
    }
  }
  const { loading: exportByDivisionLoading, runAsync: exportByDivisionRunAsync } = useRequest(
    exportIncidencePatientCountDataByDivision,
    {
      manual: true,
    },
  );
  const { loading: exportByHospitalLoading, runAsync: exportByHospitalRunAsync } = useRequest(
    exportIncidencePatientCountDataByHospital,
    {
      manual: true,
    },
  );
  const {
    loading: exportIncidencePatientListLoading,
    runAsync: exportIncidencePatientListRunAsync,
  } = useRequest(exportIncidencePatientList, {
    manual: true,
  });
  function handleExportTotal() {
    exportUtil(
      exportByDivisionRunAsync({
        ...tableFns.getForm().getFieldsValue(),
      }),
    );
  }
  function handleExportDetail() {
    if (tableType.value === 'org') {
      exportUtil(
        exportByHospitalRunAsync({
          ...tableFns.getForm().getFieldsValue(),
        }),
      );
    } else {
      exportUtil(
        exportIncidencePatientListRunAsync({
          ...tableFns.getForm().getFieldsValue(),
        }),
      );
    }
  }

  function createActions(record, _column) {
    const actions: ActionItem[] = [
      {
        label: tableType.value === 'list' ? '医疗机构' : '发病患者',
        type: 'link',
        onClick: handleDetail.bind(null, record),
      },
    ];

    return actions;
  }
  function handleBack() {
    if (tableType.value === 'org') {
      orgName.value = '';
      switchTable('list');
    } else {
      patientOrgName.value = '';
      switchTable('org');
    }
  }
</script>
<template>
  <div>
    <div class="rounded-md p-4">
      <BasicTable class="ft-main-table" @register="register">
        <template #tableTitle>
          <div class="w-full" v-if="tableType === 'list'">
            <Button
              pre-icon="ant-design:download-outlined"
              size="small"
              class="mr-2 mt-2 float-right"
              type="link"
              @click="handleExportTotal"
              :loading="exportByDivisionLoading"
            >
              导出
            </Button>
            <div class="font-bold text-xl text-center mt-10">
              {{ year }}年各类传染病发病人数统计
            </div>
          </div>
          <div class="w-full" v-else>
            <div class="float-right mr-2 mt-2">
              <Button
                pre-icon="ant-design:rollback-outlined"
                size="small"
                type="link"
                @click="handleBack"
              >
                返回
              </Button>
              <Button
                pre-icon="ant-design:download-outlined"
                size="small"
                type="link"
                @click="handleExportDetail"
                :loading="
                  tableType === 'org' ? exportByHospitalLoading : exportIncidencePatientListLoading
                "
              >
                导出
              </Button>
            </div>
            <div class="font-bold text-xl text-center mt-10">
              {{ year }}年各类传染病发病人数统计
            </div>
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :actions="createActions(record, column)" />
          </template>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
