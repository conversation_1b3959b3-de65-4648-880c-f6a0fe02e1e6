import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { yearSchema } from '../../shared';
// 患者居住地 传染病1 传染病2 传染病3 传染病4 传染病5 传染病6 传染病7 传染病8

export const columns: BasicColumn[] = [
  {
    title: '月份',
    dataIndex: 'month',
    width: 120,
  },
  {
    title: '传染病1',
    dataIndex: 'number',
    width: 120,
  },
  {
    title: '传染病2',
    dataIndex: 'number2',
    width: 120,
  },
  {
    title: '传染病3',
    dataIndex: 'number3',
    width: 120,
  },
  {
    title: '传染病4',
    dataIndex: 'number4',
    width: 120,
  },
  {
    title: '传染病5',
    dataIndex: 'number5',
    width: 120,
  },
  {
    title: '传染病6',
    dataIndex: 'number6',
    width: 120,
  },
  {
    title: '传染病7',
    dataIndex: 'number7',
    width: 120,
  },
  {
    title: '传染病8',
    dataIndex: 'number8',
    width: 120,
  },
];

export const SearchSchemas: FormSchema[] = [yearSchema];
