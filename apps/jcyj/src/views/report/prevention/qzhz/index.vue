<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { exportUtil } from '@ft/internal/utils';
  import { useRequest } from '@ft/request';
  import { computed, ref } from 'vue';
  import { SearchSchemas } from './data';
  import {
    exportAggregatedInfectiousDiseaseStatisticsData,
    getAggregatedInfectiousDiseaseStatisticsTableColumns,
    queryAggregatedInfectiousDiseaseStatisticsData,
  } from '/@/api/report/prevention';

  const { loading, data: tableHeader } = useRequest(
    getAggregatedInfectiousDiseaseStatisticsTableColumns,
  );
  const columns = computed(() => {
    const data =
      tableHeader.value?.map((item) => {
        return {
          title: item.columnName,
          dataIndex: item.columnCode,
        };
      }) || [];
    return data;
  });
  const [register, tableAction] = useTable({
    api: (params) => queryAggregatedInfectiousDiseaseStatisticsData(params.year),
    formConfig: {
      labelWidth: 100,
      schemas: SearchSchemas,
      submitFunc: async () => {
        year.value = tableAction.getForm().getFieldsValue().year;
        tableAction.reload();
      },
      showResetButton: false,
    },
    immediate: false,
    useSearchForm: true,
    showIndexColumn: false,
    columns,
    loading,
    bordered: true,
  });
  const year = ref('');
  const { loading: exportTotalLoading, runAsync: exportExpertRunAsync } = useRequest(
    exportAggregatedInfectiousDiseaseStatisticsData,
    {
      manual: true,
      showSuccessMessage: true,
    },
  );

  function handleExportTotal() {
    exportUtil(exportExpertRunAsync(tableAction.getForm().getFieldsValue().year));
  }
</script>
<template>
  <div>
    <div class="rounded-md p-4">
      <BasicTable class="ft-main-table" @register="register">
        <template #tableTitle>
          <div class="w-full">
            <Button
              pre-icon="ant-design:download-outlined"
              size="small"
              class="mr-2 mt-2 float-right"
              type="link"
              @click="handleExportTotal"
              :loading="exportTotalLoading"
            >
              导出
            </Button>
            <div class="font-bold text-xl text-center mt-10">
              {{ year }}年聚集性传染病确诊患者统计分析
            </div>
          </div>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
