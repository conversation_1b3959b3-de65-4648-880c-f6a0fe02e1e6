import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { yearSchema } from '../../shared';
// 居住社区 不明原因发热患者数量 不明原因发热确诊传染病患者数量 不明原因发热确诊传染病患者占比

export const columns: BasicColumn[] = [
  {
    title: '居住社区',
    dataIndex: 'indicatorName',
    width: 120,
  },
  {
    title: '不明原因发热患者数量',
    dataIndex: 'patientCount',
    width: 240,
  },
  // {
  //   title: '不明原因发热确诊传染病患者数量',
  //   dataIndex: 'confirmedPatientCount',
  //   width: 240,
  // },
  // {
  //   title: '不明原因发热确诊传染病患者占比',
  //   dataIndex: 'ratio',
  //   width: 240,
  // },
];

export const SearchSchemas: FormSchema[] = [yearSchema];
