<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { Tabs } from 'ant-design-vue';
  import { exportUtil } from '@ft/internal/utils';
  import { useRequest } from '@ft/request';
  import { computed, nextTick, ref, watch } from 'vue';
  import { SearchSchemas, columns } from './data';
  import {
    exportFeverOfUnknownStatisticsData,
    queryFeverOfUnknownStatisticsData,
  } from '/@/api/report/prevention';

  const [register, tableAction] = useTable({
    api: queryFeverOfUnknownStatisticsData,
    formConfig: {
      labelWidth: 100,
      schemas: SearchSchemas,
      submitFunc: async () => {
        year.value = tableAction.getForm().getFieldsValue().year;
        tableAction.reload();
      },
      showResetButton: false,
    },
    immediate: false,
    useSearchForm: true,
    showIndexColumn: false,
    columns: computed(() => {
      if (activeTab.value === 1) {
        columns[0].title = '居住社区';
        return columns;
      } else if (activeTab.value === 2) {
        columns[0].title = '确诊工作地';
        return columns;
      } else {
        columns[0].title = '确诊经过地';
        return columns;
      }
    }),
    beforeFetch: (params) => {
      params.statisticType = activeTab.value;
      return params;
    },
    bordered: true,
  });
  const year = ref('');
  const activeTab = ref(1);
  watch(activeTab, () => {
    nextTick(() => {
      tableAction.reload();
    });
  });
  const { loading: exportTotalLoading, runAsync: exportExpertRunAsync } = useRequest(
    exportFeverOfUnknownStatisticsData,
    {
      manual: true,
      showSuccessMessage: true,
    },
  );

  function handleExportTotal() {
    exportUtil(
      exportExpertRunAsync({
        ...tableAction.getForm().getFieldsValue(),
        statisticType: activeTab.value,
      }),
    );
  }
</script>
<template>
  <div>
    <div class="rounded-md p-4">
      <BasicTable class="ft-main-table" @register="register">
        <template #headerTop>
          <Tabs v-model:activeKey="activeTab">
            <Tabs.TabPane :key="1" tab="确诊居住社区分布" />
            <Tabs.TabPane :key="2" tab="确诊工作地分布" />
            <Tabs.TabPane :key="3" tab="确诊经过地分布" />
          </Tabs>
        </template>
        <template #tableTitle>
          <div class="w-full">
            <Button
              pre-icon="ant-design:download-outlined"
              size="small"
              class="mr-2 mt-2 float-right"
              type="link"
              @click="handleExportTotal"
              :loading="exportTotalLoading"
            >
              导出
            </Button>
            <div class="font-bold text-xl text-center mt-10">
              {{ year }}年不明原因发热传染病统计
            </div>
          </div>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
