import type { FormSchema } from '@ft/internal/components/Table';
import { getStatisticYearList } from '/@/api/report/hepatitis';
import { getAdministrativeList } from '@ft/internal/api';

const areaSchema: FormSchema = {
  field: 'divisionCode',
  label: '统计地区',
  component: 'ApiSelect',
  componentProps: ({ formModel }) => ({
    api: getAdministrativeList,
    labelField: 'name',
    valueField: 'code',
    keyField: 'areaId',
    allowClear: true,
    getPopupContainer() {
      return document.body;
    },
    onChange(_, o) {
      formModel.areaName = o?.label;
    },
  }),
  colProps: { span: 6 },
};

const yearSchema: FormSchema = {
  field: 'year',
  label: '统计年度',
  component: 'ApiSelect',
  componentProps: ({ formModel, formActionType }) => {
    return {
      api: getStatisticYearList,
      onOptionsChange: (options) => {
        if (!formModel.year) {
          formModel.year = options?.[0]?.value;
        }
        formActionType.submit();
      },
      allowClear: false,
      getPopupContainer() {
        return document.body;
      },
    };
  },
  colProps: { span: 6 },
};

const monthSchema: FormSchema = {
  field: 'month',
  label: '统计月份',
  component: 'Select',
  componentProps: {
    allowClear: true,
    options: [
      { label: '一月', value: 1 },
      { label: '二月', value: 2 },
      { label: '三月', value: 3 },
      { label: '四月', value: 4 },
      { label: '五月', value: 5 },
      { label: '六月', value: 6 },
      { label: '七月', value: 7 },
      { label: '八月', value: 8 },
      { label: '九月', value: 9 },
      { label: '十月', value: 10 },
      { label: '十一月', value: 11 },
      { label: '十二月', value: 12 },
    ],
    getPopupContainer() {
      return document.body;
    },
  },
  colProps: { span: 6 },
};
const orgSchema: FormSchema = {
  field: 'hospitalName',
  label: '统计机构',
  component: 'Input',
  colProps: { span: 6 },
};

const patientSchema: FormSchema = {
  field: 'patientName',
  label: '患者姓名',
  component: 'Input',
  colProps: { span: 6 },
};

export { areaSchema, yearSchema, monthSchema, orgSchema, patientSchema };
