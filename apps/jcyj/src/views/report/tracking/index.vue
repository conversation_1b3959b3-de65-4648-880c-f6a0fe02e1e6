<script setup lang="ts">
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { Tabs } from 'ant-design-vue';
  import { Button } from '@ft/internal/components/Button';
  import { computed, nextTick, ref, watch } from 'vue';
  import { useRequest } from '@ft/request';
  import { omit } from 'lodash-es';
  import { exportUtil } from '@ft/internal/utils';
  import VisitDetails from '../../../../../sjyy/src/views/doctors-view/VisitDetails.vue';
  import { SearchSchemas, columns, desensitizeTypeMap, patientColumns } from './data';
  import {
    exportExcel,
    queryDiagnosisInfo,
    queryFlowTracePage,
    queryPatientPage,
  } from '/@/api/report/tracking';

  const year = ref('');
  const activeTab = ref(1);
  const tableType = ref('list');
  const [registerForm, ActionForm] = useForm({
    schemas: computed(() => SearchSchemas()),
    labelWidth: 100,
    baseColProps: {
      span: 6,
      style: {
        textAlign: 'left',
      },
    },
    autoSubmitOnEnter: false,
    showAdvancedButton: false,
    actionColOptions: {
      //@ts-ignore
      span: computed(() => (tableType.value === 'list' ? 18 : 12)),
    },
    showResetButton: false,
    showSubmitButton: computed(() => tableType.value === 'list'),
  });
  async function reset() {}
  function handleSubmit(values) {
    year.value = values.year;
    tableAction.reload();
  }

  const [register, tableAction] = useTable({
    api: queryFlowTracePage,
    formConfig: {
      labelWidth: 100,
      schemas: computed(() => SearchSchemas()),
      submitFunc: async () => {
        console.log(65);
        year.value = tableAction.getForm().getFieldsValue().year;
        tableAction.reload();
      },
      showResetButton: false,
    },
    immediate: false,
    useSearchForm: false,
    showIndexColumn: false,
    columns: computed(() => columns(activeTab.value)),
    beforeFetch: (params) => {
      params.type = activeTab.value;
      params.year = year.value;
      return params;
    },
    bordered: true,
  });

  watch(activeTab, () => {
    nextTick(() => {
      tableAction.reload();
    });
  });
  const { loading: exportTotalLoading, runAsync: exportExpertRunAsync } = useRequest(exportExcel, {
    manual: true,
    showSuccessMessage: true,
  });

  function handleExportTotal() {
    exportUtil(
      exportExpertRunAsync({
        year: year.value,
        type: activeTab.value,
      }),
    );
  }
  function handlePatientList(record) {
    console.log(106, record);
    switchTable('patientList');
    ActionForm.setFieldsValue({
      year: year.value,
      ...record,
    });
  }
  const [patientListRegister] = useTable({
    api: queryPatientPage,
    dataSource: [{}],
    useSearchForm: false,
    columns: patientColumns,
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
    beforeFetch: (params) => {
      return {
        type: activeTab.value,
        ...omit(ActionForm.getFieldsValue(), 'communityOrgName'),
        ...params,
      };
    },
  });
  function createActions(record, _column): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '详情',
        type: 'link',
        onClick: handlePatientDetail.bind(null, record),
      },
    ];

    return actions;
  }
  const { data: diagnosisInfo, runAsync: runAsyncQueryDiagnosisInfo } = useRequest(
    queryDiagnosisInfo,
    {
      manual: true,
    },
  );
  function handlePatientDetail(record) {
    console.log(record);
    switchTable('patientDetail');
    runAsyncQueryDiagnosisInfo({ year: year.value, idCardNo: record.idCardNo });
  }

  function switchTable(key: 'list' | 'patientList' | 'patientDetail') {
    tableType.value = key;
    if (key === 'list') {
      ActionForm.resetSchema(SearchSchemas());
    } else if (key === 'patientList' || key === 'patientDetail') {
      ActionForm.resetSchema(SearchSchemas(activeTab.value));
      ActionForm.updateSchema([{ field: 'year', componentProps: { disabled: true } }]);
    }
  }
  function handleBack() {
    if (tableType.value === 'patientList') {
      switchTable('list');
    } else {
      switchTable('patientList');
    }
  }
</script>
<template>
  <div class="rounded-md p-4 w-full h-full flex flex-col">
    <div class="p-4 mb-4 bg-white">
      <BasicForm @register="registerForm" @reset="reset" @submit="handleSubmit" />
    </div>
    <div class="bg-white flex-1 flex">
      <BasicTable v-if="tableType === 'list'" class="ft-main-table" @register="register">
        <template #headerTop>
          <Tabs v-model:activeKey="activeTab">
            <Tabs.TabPane :key="1" tab="相同社区" />
            <Tabs.TabPane :key="2" tab="相同工作单位" />
            <Tabs.TabPane :key="3" tab="相同职业" />
          </Tabs>
        </template>
        <template #tableTitle>
          <div class="w-full">
            <Button
              pre-icon="ant-design:download-outlined"
              size="small"
              class="mr-2 mt-2 float-right"
              type="link"
              @click="handleExportTotal"
              :loading="exportTotalLoading"
            >
              导出
            </Button>
            <div class="font-bold text-xl text-center mt-10"> {{ year }}年患者传染源流调追踪 </div>
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'number'">
            <div class="text-#2879FF cursor-pointer" @click="handlePatientList(record)">{{
              record.number
            }}</div>
          </template>
        </template>
      </BasicTable>
      <BasicTable
        v-if="tableType === 'patientList'"
        class="ft-main-table"
        @register="patientListRegister"
      >
        <template #tableTitle>
          <div class="w-full">
            <div class="float-right mr-2 mt-2">
              <Button
                pre-icon="ant-design:rollback-outlined"
                size="small"
                type="link"
                @click="handleBack"
              >
                返回
              </Button>
            </div>
            <div class="font-bold text-xl text-center mt-10"> {{ year }}年患者传染源流调追踪 </div>
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :divider="false" :actions="createActions(record, column)" />
          </template>
          <template
            v-if="
              column.dataIndex === 'idCardNo' ||
              column.dataIndex === 'patientName' ||
              column.dataIndex === 'phone'
            "
          >
            {{ desensitizeTypeMap[column.dataIndex](record[column.dataIndex]) }}
          </template>
        </template>
      </BasicTable>
      <div v-if="tableType === 'patientDetail'" class="p-4 flex-1 flex flex-col">
        <div class="w-full">
          <div class="float-right mr-2 mt-2">
            <Button
              pre-icon="ant-design:rollback-outlined"
              size="small"
              type="link"
              @click="handleBack"
            >
              返回
            </Button>
          </div>
          <div class="font-bold text-xl text-center mt-10"> {{ year }}年患者传染源流调追踪 </div>
        </div>
        <div class="flex-1 flex flex-col gap-4">
          <div class="flex items-center gap-2">
            <span class="inline-block bg-primary-color w-3px h-1em"></span>
            <span class="text-#333333 fw-bold">患者诊疗详情</span>
          </div>
          <VisitDetails
            :activeItem="{ ...diagnosisInfo }"
            :patientInfo="{ ...diagnosisInfo }"
            :simpleMode="true"
          />
        </div>
      </div>
    </div>
  </div>
</template>
