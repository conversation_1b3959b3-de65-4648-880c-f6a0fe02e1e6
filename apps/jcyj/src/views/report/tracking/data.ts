import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { yearSchema } from '../shared';

export const columns = (activeTab?: any): BasicColumn[] => {
  return [
    {
      title: '社区',
      dataIndex: 'communityOrgName',
      width: 120,
      ifShow: activeTab == 1,
    },
    {
      title: '单位',
      dataIndex: 'orgName',
      width: 120,
      ifShow: activeTab == 2,
    },
    {
      title: '职业',
      dataIndex: 'populationClassification',
      width: 120,
      ifShow: activeTab == 3,
    },
    {
      title: '传染病患者数',
      dataIndex: 'number',
      width: 240,
    },
  ];
};

export const SearchSchemas = (activeTab?: any): FormSchema[] => {
  if (!activeTab) return [yearSchema];
  return [
    yearSchema,
    {
      label: '分配社区机构编码',
      field: 'communityOrgCode',
      component: 'Input',
      ifShow: false,
    },
    {
      label: '统计社区',
      field: 'communityOrgName',
      component: 'Input',
      colProps: { span: 6 },
      ifShow: activeTab == 1,
      componentProps: { disabled: true },
    },
    {
      label: '统计单位',
      field: 'orgName',
      component: 'Input',
      colProps: { span: 6 },
      ifShow: activeTab == 2,
      componentProps: { disabled: true },
    },
    {
      label: '统计职业',
      field: 'populationClassification',
      component: 'Input',
      colProps: { span: 6 },
      ifShow: activeTab == 3,
      componentProps: { disabled: true },
    },
  ];
};

export const patientColumns: BasicColumn[] = [
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    width: 100,
  },
  {
    title: '身份证号',
    dataIndex: 'idCardNo',
    width: 100,
  },
  {
    title: '性别',
    dataIndex: 'sexName',
    width: 100,
  },
  {
    title: '年龄',
    dataIndex: 'age',
    width: 100,
  },
  {
    title: '就诊时间',
    dataIndex: 'diagnosisTime',
    width: 100,
  },
  {
    title: '就诊诊断',
    dataIndex: 'diagnosticResults',
    width: 100,
  },
  {
    title: '就诊机构',
    dataIndex: 'visitOrgName',
    width: 100,
  },
  {
    title: '就诊科室',
    dataIndex: 'deptName',
    width: 100,
  },
  {
    title: '就诊医生',
    dataIndex: 'chiefPhysician',
    width: 100,
  },
];
export const desensitizeTypeMap = {
  phone: (v) => v?.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2'),
  idCardNo: (v) => v?.replace(/(\d{6})(\d{8})(\w{4})/, '$1********$3'),
  patientName: (v) => {
    if (v && v.length > 1) {
      return v?.charAt(0) + '*'.repeat(v?.length - 1);
    }
    return v;
  },
};
