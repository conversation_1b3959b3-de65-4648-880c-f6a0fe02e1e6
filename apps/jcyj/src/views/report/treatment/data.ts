import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { areaSchema, yearSchema } from '../shared';

// 地区 结核病
export const columns1: BasicColumn[] = [
  {
    title: '地区',
    dataIndex: 'areaName',
    width: 150,
  },
  {
    title: '结核病',
    // 可疑症状者检查率	患者病原学阳性率	患者就诊率	患者/疑似患者转诊率	报告发病率
    children: [
      {
        title: '可疑症状者检查率',
        dataIndex: 'tbSuspiciousSymptomsDetectionRate',
        // width: 150,
      },
      {
        title: '患者病原学阳性率',
        dataIndex: 'tbPatientEtiologicalPositiveRate',
        // width: 150,
      },
      {
        title: '患者就诊率',
        dataIndex: 'tbPatientVisitRate',
        // width: 120,
      },
      {
        title: '患者/疑似患者转诊率',
        dataIndex: 'tbPatientReferralRate',
        // width: 150,
      },
      {
        title: '报告发病率',
        dataIndex: 'tbReportedIncidence',
        // width: 120,
      },
    ],
  },
];
// 地区 艾滋病
export const columns2: BasicColumn[] = [
  {
    title: '地区',
    dataIndex: 'areaName',
    width: 150,
  },
  {
    title: '艾滋病',
    // 感染者和病人诊断发现率 符合抗病毒条件纳入治疗率 符合抗病毒条件治疗成功率 报告发病率
    children: [
      {
        title: '感染者和病人诊断发现率',
        dataIndex: 'hivDiagnosticIncidence',
        // width: 180,
      },
      {
        title: '符合抗病毒条件纳入治疗率',
        dataIndex: 'hivInclusionRate',
        // width: 200,
      },
      {
        title: '符合抗病毒条件治疗成功率',
        dataIndex: 'hivTreatmentSuccessRate',
        // width: 200,
      },
      {
        title: '报告发病率',
        dataIndex: 'hivReportedIncidence',
        // width: 120,
      },
      {
        title: '就诊复查率',
        dataIndex: 'hivReviewRate',
      },
      {
        title: '就诊并发症统计',
        dataIndex: 'hivComplicationStatistics',
      },
    ],
  },
];
// 地区 病毒性肝炎
export const columns3: BasicColumn[] = [
  {
    title: '地区',
    dataIndex: 'areaName',
    width: 150,
  },
  {
    title: '病毒性肝炎',
    // 患者就诊率 报告发病率
    children: [
      {
        title: '患者就诊率',
        dataIndex: 'viralHepatitisPatientVisitRate',
      },
      {
        title: '报告发病率',
        dataIndex: 'viralHepatitisReportedIncidence',
      },
    ],
  },
];
// 地区 孕产妇
export const columns4: BasicColumn[] = [
  {
    title: '地区',
    dataIndex: 'areaName',
    width: 150,
  },
  {
    title: '孕产妇',
    // 艾滋病传染病检出率	梅毒传染病检出率	乙肝传染病检出率
    children: [
      {
        title: '艾滋病传染病检出率',
        dataIndex: 'pregnantHivDetectionRate',
      },
      {
        title: '梅毒传染病检出率',
        dataIndex: 'pregnantSyphilisDetectionRate',
      },
      {
        title: '乙肝传染病检出率',
        dataIndex: 'pregnantHbvDetectionRate',
      },
    ],
  },
];

export const SearchSchemas: FormSchema[] = [yearSchema, areaSchema];
