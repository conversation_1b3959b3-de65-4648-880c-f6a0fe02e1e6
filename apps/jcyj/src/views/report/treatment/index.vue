<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { Tabs } from 'ant-design-vue';
  import { nextTick, ref, watch } from 'vue';
  import { exportUtil } from '@ft/internal/utils';
  import { useRequest } from '@ft/request';
  import { omit } from 'lodash-es';
  import { SearchSchemas, columns1, columns2, columns3, columns4 } from './data';
  import { exportTherapeuticIndexManageStatistics, queryList } from '/@/api/report/treatment';

  const [register, tableAction] = useTable({
    api: queryList,
    formConfig: {
      labelWidth: 100,
      schemas: SearchSchemas,
      submitFunc: async () => {
        nextTick(() => {
          year.value = tableAction.getForm().getFieldsValue().year;
          tableAction.reload();
        });
      },
      resetFunc: async () => {
        const values = tableAction.getForm().getFieldsValue();
        if (values.divisionCode !== undefined) {
          values.divisionCode = undefined;
        }
        tableAction.getForm().setFieldsValue({
          ...values,
        });
      },
    },
    useSearchForm: true,
    showIndexColumn: false,
    columns: columns1,
    immediate: false,
    beforeFetch: (params) => {
      params.areaCode = params.divisionCode;
      params.indexType = activeTab.value;
      return omit(params, ['divisionCode']);
    },
    bordered: true,
  });
  const year = ref(2024);
  const { loading: exportTotalLoading, runAsync: exportExpertRunAsync } = useRequest(
    exportTherapeuticIndexManageStatistics,
    {
      manual: true,
      showSuccessMessage: true,
    },
  );

  function handleExport() {
    exportUtil(
      exportExpertRunAsync({
        indexType: activeTab.value,
        areaCode: tableAction.getForm().getFieldsValue().divisionCode,
        ...omit(tableAction.getForm().getFieldsValue(), ['divisionCode']),
      }),
    );
  }
  const activeTab = ref(1);
  watch(activeTab, () => {
    tableAction.setProps({
      columns:
        activeTab.value === 1
          ? columns1
          : activeTab.value === 2
          ? columns2
          : activeTab.value === 3
          ? columns3
          : columns4,
    });
    nextTick(() => {
      tableAction.reload();
    });
  });
</script>
<template>
  <div>
    <div class="rounded-md p-4">
      <BasicTable class="ft-main-table" @register="register">
        <template #headerTop>
          <Tabs v-model:activeKey="activeTab">
            <Tabs.TabPane :key="1" tab="结核病" />
            <Tabs.TabPane :key="2" tab="艾滋病" />
            <Tabs.TabPane :key="3" tab="病毒性肝炎" />
            <Tabs.TabPane :key="4" tab="孕产妇" />
          </Tabs>
        </template>
        <template #tableTitle>
          <div class="w-full">
            <Button
              pre-icon="ant-design:download-outlined"
              size="small"
              class="mr-2 mt-2 float-right"
              type="link"
              @click="handleExport"
              :loading="exportTotalLoading"
            >
              导出
            </Button>
            <div class="font-bold text-xl text-center mt-10"> {{ year }}年治疗指标管理统计 </div>
          </div>
        </template>
      </BasicTable>
    </div>
  </div>
</template>
