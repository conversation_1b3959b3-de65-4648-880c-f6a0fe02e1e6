<script setup lang="ts">
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { ref } from 'vue';
  import { exportUtil } from '@ft/internal/utils';
  import { useRequest } from '@ft/request';
  import { detailColumns, detailFormSchema, listColumns, listFormSchema } from './data';
  import {
    exportViralHepatitisDataReport,
    exportViralHepatitisDataReportDetail,
    queryDetailList,
    queryList,
  } from '/@/api/report/hepatitis';

  const actionColumn = {
    width: 120,
    title: '操作',
    dataIndex: 'action',
  };
  const year = ref('2024');
  const listDivisionCode = ref('');
  const detailDivisionCode = ref('');
  const month = ref('');
  const detailVisible = ref(false);
  const [register, tableFns] = useTable({
    api: queryList,
    formConfig: {
      labelWidth: 100,
      schemas: listFormSchema,
      submitFunc: async () => {
        const values = tableFns.getForm().getFieldsValue();
        year.value = values.year;
        if (detailVisible.value) {
          detailDivisionCode.value = values.divisionCode;
        } else {
          listDivisionCode.value = values.divisionCode;
        }
        month.value = values.month;
        tableFns.reload();
      },
    },
    useSearchForm: true,
    showIndexColumn: false,
    immediate: false,
    columns: listColumns,
    actionColumn,
    bordered: true,
  });

  const { loading: exportTotalLoading, runAsync: exportExpertRunAsync } = useRequest(
    exportViralHepatitisDataReport,
    {
      manual: true,
    },
  );
  const { loading: exportDetailLoading, runAsync: exportDetailRunAsync } = useRequest(
    exportViralHepatitisDataReportDetail,
    {
      manual: true,
    },
  );

  async function switchTable(key: 'list' | 'detail') {
    detailVisible.value = key === 'detail';
    await tableFns.getForm()?.setFieldsValue({
      year: year.value,
      divisionCode: key === 'detail' ? detailDivisionCode.value : listDivisionCode.value,
      month: undefined,
    });
    tableFns.setProps({
      api: key === 'detail' ? queryDetailList : queryList,
      columns: key === 'detail' ? detailColumns : listColumns,
      actionColumn: key === 'detail' ? undefined : actionColumn,
    });
    key === 'detail'
      ? tableFns.getForm().resetSchema(detailFormSchema)
      : tableFns.getForm().resetSchema(listFormSchema);
    if (key === 'detail') {
      tableFns.reload();
    }
  }

  function handleDetail(record) {
    detailDivisionCode.value = record.divisionCode;
    switchTable('detail');
  }

  function handleExportDetail() {
    exportUtil(
      exportDetailRunAsync({
        // ...tableFns.getForm().getFieldsValue(),
        year: year.value,
        divisionCode: detailDivisionCode.value,
        month: month.value,
      }),
    );
  }

  function handleExportTotal() {
    exportUtil(
      exportExpertRunAsync({
        // ...tableFns.getForm().getFieldsValue(),
        year: year.value,
        divisionCode: listDivisionCode.value,
      }),
    );
  }

  function createActions(record, _column) {
    const actions: ActionItem[] = [
      {
        label: '查看详情',
        type: 'link',
        onClick: handleDetail.bind(null, record),
      },
    ];

    return actions;
  }
  function handleBack() {
    switchTable('list');
  }
</script>
<template>
  <div class="rounded-md">
    <BasicTable class="" @register="register">
      <template #tableTitle>
        <div class="w-full" v-if="detailVisible">
          <div class="float-right mr-2 mt-2">
            <Button
              pre-icon="ant-design:rollback-outlined"
              size="small"
              type="link"
              @click="handleBack"
            >
              返回
            </Button>
            <Button
              pre-icon="ant-design:download-outlined"
              size="small"
              type="link"
              @click="handleExportDetail"
              :loading="exportDetailLoading"
            >
              导出
            </Button>
          </div>
          <div class="font-bold text-xl text-center mt-10">
            {{ year }}年病毒性肝炎专病防治中心各项指标月统计
          </div>
        </div>
        <div class="w-full" v-else>
          <Button
            pre-icon="ant-design:download-outlined"
            size="small"
            class="mr-2 mt-2 float-right"
            type="link"
            @click="handleExportTotal"
            :loading="exportTotalLoading"
          >
            导出
          </Button>
          <div class="font-bold text-xl text-center mt-10">
            {{ year }}年病毒性肝炎专病防治中心各项指标统计
          </div>
        </div>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<style scoped lang="less"></style>
