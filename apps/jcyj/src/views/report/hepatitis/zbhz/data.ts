import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { areaSchema, monthSchema, yearSchema } from '../../shared';

// 地区 查HCV人数 抗体阳性人数	检测RNA人数	RNA阳性人数	抗病毒治疗人数	查HBsAg人数	HBsAg阳性人数	HBV-DNA阳性人数
export const listColumns: BasicColumn[] = [
  {
    title: '地区',
    dataIndex: 'divisionName',
    width: 120,
  },
  {
    title: 'HCV人数',
    dataIndex: 'testHcvPatientCount',
    width: 120,
  },
  {
    title: '抗体阳性人数',
    dataIndex: 'hcvPositivePatientCount',
    width: 120,
  },
  {
    title: '检测RNA人数',
    dataIndex: 'testRnaPatientCount',
    width: 120,
  },
  {
    title: 'RNA阳性人数',
    dataIndex: 'rnaPositivePatientCount',
    width: 120,
  },
  {
    title: '抗病毒治疗人数',
    dataIndex: 'treatmentPatientCount',
    width: 120,
  },
  {
    title: '查HBsAg人数',
    dataIndex: 'testHbsAgPatientCount',
    width: 120,
  },
  {
    title: 'HBsAg阳性人数',
    dataIndex: 'hbsAgPositivePatientCount',
    width: 120,
  },
  {
    title: 'HBV-DNA阳性人数',
    dataIndex: 'hbvDnaPositivePatientCount',
    width: 120,
  },
];

// 月份 查HCV人数	抗体阳性人数	检测RNA人数	RNA阳性人数	抗病毒治疗人数	查HBsAg人数	HBsAg阳性人数	HBV-DNA阳性人数
export const detailColumns: BasicColumn[] = [
  {
    title: '月份',
    dataIndex: 'month',
    width: 120,
  },
  {
    title: 'HCV人数',
    dataIndex: 'testHcvPatientCount',
    width: 120,
  },
  {
    title: '抗体阳性人数',
    dataIndex: 'hcvPositivePatientCount',
    width: 120,
  },
  {
    title: '检测RNA人数',
    dataIndex: 'testRnaPatientCount',
    width: 120,
  },
  {
    title: 'RNA阳性人数',
    dataIndex: 'rnaPositivePatientCount',
    width: 120,
  },
  {
    title: '抗病毒治疗人数',
    dataIndex: 'treatmentPatientCount',
    width: 120,
  },
  {
    title: '查HBsAg人数',
    dataIndex: 'testHbsAgPatientCount',
    width: 120,
  },
  {
    title: 'HBsAg阳性人数',
    dataIndex: 'hbsAgPositivePatientCount',
    width: 120,
  },
  {
    title: 'HBV-DNA阳性人数',
    dataIndex: 'hbvDnaPositivePatientCount',
    width: 120,
  },
];

export const listFormSchema: FormSchema[] = [areaSchema, yearSchema];
export const detailFormSchema: FormSchema[] = [areaSchema, monthSchema];
