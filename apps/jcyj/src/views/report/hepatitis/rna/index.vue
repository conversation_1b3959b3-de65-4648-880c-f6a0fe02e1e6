<script setup lang="ts">
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { Upload } from 'ant-design-vue';
  import type { UploadRequestOption } from 'ant-design-vue/lib/vc-upload/interface';
  import { exportUtil } from '@ft/internal/utils';
  import { useRequest } from '@ft/request';
  import { nextTick } from 'vue';
  import { SearchSchemas, columns } from './data';
  import {
    hcvRnaDetectionRecordDelete,
    hcvRnaDetectionRecordDownloadTemplate,
    hcvRnaDetectionRecordImportExcel,
    hcvRnaDetectionRecordPage,
  } from '/@/api/report/hepatitis';

  const [register, tableAction] = useTable({
    api: hcvRnaDetectionRecordPage,
    formConfig: {
      labelWidth: 100,
      schemas: SearchSchemas,
      submitFunc: async () => {
        nextTick(() => {
          tableAction.reload();
        });
      },
      resetFunc: async () => {
        const values = tableAction.getForm().getFieldsValue();
        if (values.hospitalName !== undefined) {
          values.hospitalName = undefined;
        }
        tableAction.getForm().setFieldsValue({
          ...values,
        });
      },
    },
    immediate: false,
    useSearchForm: true,
    showIndexColumn: true,
    columns,
    actionColumn: {
      title: '操作',
      width: 100,
      dataIndex: 'action',
    },
    bordered: true,
  });
  const createActions = (record, _column): ActionItem[] => {
    return [
      {
        label: '删除',
        type: 'link',
        danger: true,
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          okButtonProps: { danger: true },
          confirm: onDel.bind(null, record),
        },
      },
    ];
  };
  const { runAsync: delRunAsync } = useRequest(hcvRnaDetectionRecordDelete, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      tableAction.reload();
    },
  });
  function onDel(record) {
    delRunAsync(record.id);
  }
  const { loading: templateLoading, runAsync: runAsyncDownloadTemplate } = useRequest(
    hcvRnaDetectionRecordDownloadTemplate,
    {
      manual: true,
    },
  );
  async function onTemplateDown() {
    try {
      await exportUtil(runAsyncDownloadTemplate());
    } catch (error) {
      console.error(error);
    } finally {
    }
  }
  const { loading: importLoading, runAsync: importRunAsync } = useRequest(
    hcvRnaDetectionRecordImportExcel,
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess: () => {
        tableAction.reload();
      },
    },
  );

  function onImport(e: UploadRequestOption<any>) {
    const { file } = e;
    importRunAsync({
      // @ts-ignore
      file,
    });
  }
</script>
<template>
  <div class="rounded-md">
    <BasicTable @register="register">
      <template #headerTop>
        <div class="text-16px font-500 mb-4">丙型肝炎病毒RNA检测患者列表</div>
      </template>
      <template #toolbar>
        <Button :loading="templateLoading" @click="onTemplateDown">下载模板</Button>
        <Upload
          accept=".xlsx,.xls"
          :max-count="1"
          :show-upload-list="false"
          :custom-request="onImport"
        >
          <Button :loading="importLoading"> 导入 </Button>
        </Upload>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
