import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { yearSchema } from '../../shared';
// 检测时间 医院名称 患者姓名 门诊（住院）号 患者性别 科室 患者年龄 检验项目 检验结果

export const columns: BasicColumn[] = [
  {
    title: '检测时间',
    dataIndex: 'detectionTime',
    width: 100,
  },
  {
    title: '医院名称',
    dataIndex: 'hospitalName',
    width: 100,
  },
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    width: 100,
  },
  {
    title: '门诊（住院）号',
    dataIndex: 'outInId',
    width: 100,
  },
  {
    title: '患者性别',
    dataIndex: 'patientSex',
    width: 100,
  },
  {
    title: '科室',
    dataIndex: 'deptName',
    width: 100,
  },
  {
    title: '患者年龄',
    dataIndex: 'patientAge',
    width: 100,
  },
  {
    title: '检验项目',
    dataIndex: 'labItemName',
    width: 150,
  },
  {
    title: '检验结果',
    dataIndex: 'labResult',
    width: 100,
  },
];

export const SearchSchemas: FormSchema[] = [
  yearSchema,
  {
    field: 'hospitalName',
    component: 'Input',
    label: '统计医院',
    colProps: {
      span: 6,
    },
  },
];
