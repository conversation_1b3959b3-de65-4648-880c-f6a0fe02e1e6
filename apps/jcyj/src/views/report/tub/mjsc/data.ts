import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { getAdministrativeList } from '@ft/internal/api';
import { yearSchema } from '../../shared';
export const columns: BasicColumn[] = [
  {
    title: '地区',
    dataIndex: 'divisionName',
    width: 120,
    fixed: 'left',
  },
  {
    title: '月份',
    dataIndex: 'month',
    width: 180,
  },
  {
    title: '密接者人数',
    dataIndex: 'peopleCount',
    width: 180,
  },
  {
    title: '密接者筛查率',
    dataIndex: 'screeningRate',
    width: 180,
  },
  {
    title: '密接者检查率',
    dataIndex: 'inspectionRate',
    width: 180,
  },
];
const api = () => getAdministrativeList(undefined, true);
export const SearchSchemas: FormSchema[] = [
  {
    field: 'divisionCode',
    label: '统计地区',
    component: 'ApiSelect',
    componentProps: () => ({
      api: api,
      labelField: 'name',
      valueField: 'code',
      keyField: 'areaId',
      allowClear: true,
      getPopupContainer() {
        return document.body;
      },
    }),
    colProps: { span: 6 },
  },
  yearSchema,
];
