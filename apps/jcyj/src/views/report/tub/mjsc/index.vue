<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { exportUtil } from '@ft/internal/utils';
  import { useRequest } from '@ft/request';
  import { nextTick } from 'vue';
  import { SearchSchemas, columns } from './data';
  import { Upload } from 'ant-design-vue';
  import type { UploadRequestOption } from 'ant-design-vue/lib/vc-upload/interface';
  import {
    tbCloseContactScreeningRecordDownloadTemplate,
    tbCloseContactScreeningRecordImportExcel,
    tbCloseContactScreeningRecordPage,
  } from '/@/api/report/tub';

  const [register, tableAction] = useTable({
    api: tbCloseContactScreeningRecordPage,
    formConfig: {
      labelWidth: 100,
      schemas: SearchSchemas,
      submitFunc: async () => {
        nextTick(() => {
          tableAction.reload();
        });
      },
      resetFunc: async () => {
        const values = tableAction.getForm().getFieldsValue();
        if (values.divisionCode !== undefined) {
          values.divisionCode = undefined;
        }
        tableAction.getForm().setFieldsValue({
          ...values,
        });
      },
    },
    useSearchForm: true,
    showIndexColumn: false,
    columns,
    immediate: false,
    bordered: true,
  });
  const { loading: templateLoading, runAsync: runAsyncDownloadTemplate } = useRequest(
    tbCloseContactScreeningRecordDownloadTemplate,
    {
      manual: true,
    },
  );
  async function onTemplateDown() {
    try {
      await exportUtil(runAsyncDownloadTemplate());
    } catch (error) {
      console.error(error);
    } finally {
    }
  }
  const { loading: importLoading, runAsync: importRunAsync } = useRequest(
    tbCloseContactScreeningRecordImportExcel,
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess: () => {
        tableAction.reload();
      },
    },
  );

  function onImport(e: UploadRequestOption<any>) {
    const { file } = e;
    importRunAsync({
      // @ts-ignore
      file,
    });
  }
</script>
<template>
  <div class="rounded-md">
    <BasicTable class="" @register="register">
      <template #headerTop>
        <div class="w-full">
          <div class="font-bold text-xl text-center mt-10"> 全市结核病密接者筛查情况表 </div>
        </div>
      </template>
      <template #toolbar>
        <Button :loading="templateLoading" @click="onTemplateDown">下载模板</Button>
        <Upload
          accept=".xlsx,.xls"
          :max-count="1"
          :show-upload-list="false"
          :custom-request="onImport"
        >
          <Button :loading="importLoading"> 导入 </Button>
        </Upload>
      </template>
    </BasicTable>
  </div>
</template>
