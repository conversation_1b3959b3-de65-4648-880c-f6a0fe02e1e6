<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { exportUtil } from '@ft/internal/utils';
  import { useRequest } from '@ft/request';
  import { omit } from 'lodash-es';
  import { nextTick } from 'vue';
  import { SearchSchemas, columns } from './data';
  import { exportTuberculosisMonitor, queryList } from '/@/api/report/tub';

  const [register, tableAction] = useTable({
    api: queryList,
    formConfig: {
      labelWidth: 100,
      schemas: SearchSchemas,
      submitFunc: async () => {
        nextTick(() => {
          tableAction.reload();
        });
      },
      resetFunc: async () => {
        const values = tableAction.getForm().getFieldsValue();
        if (values.divisionCode !== undefined) {
          values.divisionCode = undefined;
        }
        if (values.month !== undefined) {
          values.month = undefined;
        }
        tableAction.getForm().setFieldsValue({
          ...values,
        });
      },
    },
    useSearchForm: true,
    showIndexColumn: false,
    columns,
    immediate: false,
    beforeFetch: (params) => {
      params.areaCode = params.divisionCode;
      return omit(params, ['divisionCode']);
    },
    bordered: true,
  });
  const { loading: exportTotalLoading, runAsync: exportExpertRunAsync } = useRequest(
    exportTuberculosisMonitor,
    {
      manual: true,
      showSuccessMessage: true,
    },
  );

  function handleExportTotal() {
    exportUtil(
      exportExpertRunAsync({
        areaCode: tableAction.getForm().getFieldsValue().divisionCode,
        ...omit(tableAction.getForm().getFieldsValue(), ['divisionCode']),
      }),
    );
  }
</script>
<template>
  <div class="rounded-md">
    <BasicTable class="" @register="register">
      <template #tableTitle>
        <div class="w-full">
          <Button
            pre-icon="ant-design:download-outlined"
            size="small"
            class="mr-2 mt-2 float-right"
            type="link"
            @click="handleExportTotal"
            :loading="exportTotalLoading"
          >
            导出
          </Button>
          <div class="font-bold text-xl text-center mt-10"> 全市结核病防治重点指标完成情况表 </div>
        </div>
      </template>
    </BasicTable>
  </div>
</template>
