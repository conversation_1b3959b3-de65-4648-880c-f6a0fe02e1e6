import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { getAdministrativeList } from '@ft/internal/api';
import { monthSchema, yearSchema } from '../../shared';
// 病原学阳区性率(68%) 痰培养或分子生物学检查率(95%) 痰培养检查率(95%)  病原学阳性患者密切接触者筛查率 (99%) 病原学阳性患者密切接触者检查率（75%） 病原学阳性患者有症状的密切接触者检查率（75%）病原学阳性耐药筛查率(95%) 耐多药肺结核高危人群耐药筛查率(98%) 利福平耐药纳入治疗率(95%) 标准治疗方案使用率（90%）报告肺结核和疑似肺结核患者总体到位率（98%）活动性肺结核成功治疗率（95%）新病原学阳性治愈率（90%）活动性肺结核患者规范管理率（95%）规则服药率（95%）预警信号24小时响应率（98%）免费药品使用率（75%）
export const columns: BasicColumn[] = [
  {
    title: '地区',
    dataIndex: 'area',
    width: 120,
    fixed: 'left',
  },
  {
    title: '病原学阳区性率(68%)',
    dataIndex: 'etiologyPositiveRate',
    width: 180,
  },
  {
    title: '痰培养或分子生物学检查率(95%)',
    dataIndex: 'phlegmCultureBiologyExaminationRate',
    width: 240,
  },
  {
    title: '痰培养检查率(95%)',
    dataIndex: 'phlegmCultureExaminationRate',
    width: 180,
  },
  {
    title: '病原学阳性患者密切接触者筛查率 (99%)',
    dataIndex: 'closeContactsScreeningRate',
    width: 280,
  },
  {
    title: '病原学阳性患者密切接触者检查率（75%)',
    dataIndex: 'closeContactsExaminationRate',
    width: 280,
  },
  {
    title: '病原学阳性患者有症状的密切接触者检查率（75%)',
    dataIndex: 'symptomCloseContactsExaminationRate',
    width: 340,
  },
  {
    title: '病原学阳性耐药筛查率(95%)',
    dataIndex: 'positiveDrugResistanceScreeningRate',
    width: 240,
  },
  {
    title: '耐多药肺结核高危人群耐药筛查率(98%)',
    dataIndex: 'drugResistanceHighRiskScreeningRate',
    width: 280,
  },
  {
    title: '利福平耐药纳入治疗率(95%)',
    dataIndex: 'rifampicinDrugResistanceRate',
    width: 240,
  },
  {
    title: '标准治疗方案使用率（90%)',
    dataIndex: 'standardizationSchemeUseRate',
    width: 240,
  },
  // {
  //   title: '报告肺结核和疑似肺结核患者总体到位率（98%)',
  //   dataIndex: 'reportAndSuspectedTuberculosisInPlaceRate',
  //   width: 340,
  // },
  {
    title: '活动性肺结核成功治疗率（95%)',
    dataIndex: 'activityTuberculosisSuccessTreatRate',
    width: 240,
  },
  {
    title: '新病原学阳性治愈率（90%)',
    dataIndex: 'newEtiologyPositiveTreatRate',
    width: 240,
  },
  // {
  //   title: '活动性肺结核患者规范管理率（95%)',
  //   dataIndex: 'activityTuberculosisPatientManagementRate',
  //   width: 260,
  // },
  // {
  //   title: '规则服药率（95%)',
  //   dataIndex: 'regularMedicationRate',
  //   width: 180,
  // },
  // {
  //   title: '预警信号24小时响应率（98%）',
  //   dataIndex: 'earlyWarningSignalResponseRate',
  //   width: 240,
  // },
  {
    title: '免费药品使用率（75%）',
    dataIndex: 'freeMedicationUseRate',
    width: 200,
  },
];
const api = () => getAdministrativeList(undefined, true);
export const SearchSchemas: FormSchema[] = [
  {
    field: 'divisionCode',
    label: '统计地区',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => ({
      api: api,
      labelField: 'name',
      valueField: 'code',
      keyField: 'areaId',
      allowClear: true,
      getPopupContainer() {
        return document.body;
      },
      onChange(_, o) {
        formModel.areaName = o?.label;
      },
    }),
    colProps: { span: 6 },
  },
  yearSchema,
  monthSchema,
];
