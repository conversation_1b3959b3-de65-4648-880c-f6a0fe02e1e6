<script setup lang="ts">
  import Layout from '/@/layout/content.vue';
  import { provide } from 'vue';
  import { chartConfigMap } from './config';
  import CommonTabChart from '/@/component/common-tab-chart/index.vue';
  import { useMapStore } from '/@/store/modules/map';
  provide('chartConfigMap', chartConfigMap);

  const { register } = useMapStore();
  register({
    position: 'center-4',
  });
</script>

<template>
  <Layout>
    <template #left>
      <div class="flex flex-col gap-20px">
        <CommonTabChart chartKey="不明原因发热近七日患者监测" />
        <CommonTabChart chartKey="不明原因发热患者社区分布" />
        <CommonTabChart chartKey="不明原因发热患者单位分布" />
      </div>
    </template>
    <template #content-top>
      <CenterTop />
    </template>
    <template #content-bottom>
      <div class="flex justify-between mb-58px">
        <CommonTabChart chartKey="季节性传染病统计分析" width="466px" :type="2" />
        <CommonTabChart chartKey="聚集性传染病患者学校分布" width="466px" :type="2" />
      </div>
    </template>
    <template #right>
      <div class="flex flex-col gap-20px">
        <CommonTabChart chartKey="聚集性传染病近七日患者监测" />
        <CommonTabChart chartKey="聚集性传染病患者社区分布" />
        <CommonTabChart chartKey="聚集性传染病患者单位分布" />
      </div>
    </template>
  </Layout>
</template>
