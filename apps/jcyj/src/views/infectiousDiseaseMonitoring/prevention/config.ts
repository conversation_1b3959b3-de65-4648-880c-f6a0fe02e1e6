import type { Ref } from 'vue';
import { computed, onMounted, reactive, ref, watch } from 'vue';
import type { EChartsOption } from 'echarts';
import {
  genGrid,
  genLegend,
  genLzChart,
  genLzYChart,
  genSeries,
  genXAxis,
  genYAxis,
  legendIcon2,
} from '/@/helper/chart';
import type { ChartData } from '/@/helper/useChartData';
import {
  getLeftBottomBarChart,
  getLeftMiddleBar<PERSON>hart,
  getLeftTopLineChart,
  getMiddleLeftBottomLineChart,
  getMiddleRightBottomLineChart,
  getRightBottomBar<PERSON>hart,
  getRightMiddleBarChart,
  getRightTopLineChart,
} from '/@/api/prevention';
import { get, map } from 'lodash-es';

const 不明原因发热近七日患者监测 = (_year: Ref<string>): ChartData => {
  const switchOptions = [
    { label: '近七日', key: 'key' },
    { label: '辖区', key: 'key2' },
  ];
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    keyXAxisData: [] as any[],
    keySeries0Data: [] as any[],
    key2XAxisData: [] as any[],
    key2Series0Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.keyXAxisData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
      ];
    } else {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.key2XAxisData },
        { path: 'series.0.data', val: chartConfigData.key2Series0Data },
      ];
    }
  });
  const chartOption: EChartsOption = reactive({
    grid: genGrid({ right: 20 }),
    legend: genLegend({
      preset: 'show',
      data: [{ name: '患者数', icon: legendIcon2 }],
    }),
    xAxis: [
      genXAxis({
        features: ['breakLabel'],
      }),
    ],
    yAxis: [genYAxis({})],
    series: [
      genSeries({
        preset: '折线图',
        name: '患者数',
        customColor: '#23F1F1',
      }),
    ],
  });
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getLeftTopLineChart(switchValue.value === 'key' ? '1' : '2').then((res) => {
        if (switchValue.value === 'key') {
          chartConfigData.keyXAxisData = map(res.lineChartVOList, 'x');
          chartConfigData.keySeries0Data = map(res.lineChartVOList, 'yvalue');
        } else {
          chartConfigData.key2XAxisData = map(res.lineChartVOList, 'x');
          chartConfigData.key2Series0Data = map(res.lineChartVOList, 'yvalue');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    switchOptions,
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '不明原因发热近七日患者监测',
  };
};

const 不明原因发热患者社区分布 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    keyYAxisData: [] as any[],
    keySeries0Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'yAxis.0.data', val: chartConfigData.keyYAxisData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(genLzYChart({ name: '患者数' }));

  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getLeftMiddleBarChart().then((res) => {
        if (switchValue.value === 'key') {
          chartConfigData.keyYAxisData = map(res, 'communityName');
          chartConfigData.keySeries0Data = map(res, 'patientCount');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '不明原因发热患者社区分布',
  };
};

const 不明原因发热患者单位分布 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    keyYAxisData: [] as any[],
    keySeries0Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'yAxis.0.data', val: chartConfigData.keyYAxisData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(genLzYChart({ name: '患者数' }));

  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getLeftBottomBarChart().then((res) => {
        if (switchValue.value === 'key') {
          chartConfigData.keyYAxisData = map(res, 'workUnitName');
          chartConfigData.keySeries0Data = map(res, 'patientCount');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '不明原因发热患者单位分布',
  };
};

const 聚集性传染病近七日患者监测 = (_year: Ref<string>): ChartData => {
  const switchOptions = [
    { label: '近七日', key: 'key' },
    { label: '辖区', key: 'key2' },
  ];
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    keyXAxisData: [] as any[],
    keySeries0Data: [] as any[],
    key2XAxisData: [] as any[],
    key2Series0Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.keyXAxisData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
      ];
    } else {
      return [
        { path: 'xAxis.0.data', val: chartConfigData.key2XAxisData },
        { path: 'series.0.data', val: chartConfigData.key2Series0Data },
      ];
    }
  });
  const chartOption: EChartsOption = reactive({
    grid: genGrid({ right: 20 }),
    legend: genLegend({
      preset: 'show',
      data: [{ name: '患者数', icon: legendIcon2 }],
    }),
    xAxis: [
      genXAxis({
        features: ['breakLabel'],
      }),
    ],
    yAxis: [genYAxis({})],
    series: [
      genSeries({
        preset: '折线图',
        name: '患者数',
        customColor: '#23F1F1',
      }),
    ],
  });
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getRightTopLineChart(switchValue.value === 'key' ? '1' : '2').then((res) => {
        if (switchValue.value === 'key') {
          chartConfigData.keyXAxisData = map(res.lineChartVOList, 'x');
          chartConfigData.keySeries0Data = map(res.lineChartVOList, 'yvalue');
        } else {
          chartConfigData.key2XAxisData = map(res.lineChartVOList, 'x');
          chartConfigData.key2Series0Data = map(res.lineChartVOList, 'yvalue');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    switchOptions,
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '聚集性传染病近七日患者监测',
  };
};

const 聚集性传染病患者社区分布 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    keyYAxisData: [] as any[],
    keySeries0Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'yAxis.0.data', val: chartConfigData.keyYAxisData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(genLzYChart({ name: '患者数' }));

  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getRightMiddleBarChart().then((res) => {
        if (switchValue.value === 'key') {
          // chartConfigData.keyYAxisData = map(res, '');
          // chartConfigData.keySeries0Data = res.keySeries0Data;
          chartConfigData.keyYAxisData = map(res, 'communityName');
          chartConfigData.keySeries0Data = map(res, 'patientCount');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '聚集性传染病患者社区分布',
  };
};

const 聚集性传染病患者单位分布 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    keyYAxisData: [] as any[],
    keySeries0Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'yAxis.0.data', val: chartConfigData.keyYAxisData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(genLzYChart({ name: '患者数' }));
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getRightBottomBarChart().then((res) => {
        if (switchValue.value === 'key') {
          chartConfigData.keyYAxisData = map(res, 'workUnitName');
          chartConfigData.keySeries0Data = map(res, 'patientCount');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '聚集性传染病患者单位分布',
  };
};

const 季节性传染病统计分析 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
    keySeries0Name: '',
    keySeries1Data: [] as any[],
    keySeries1Name: '',
    keySeries2Data: [] as any[],
    keySeries2Name: '',
  });
  const chartConfig = computed<any[]>(() => {
    return [
      { path: 'xAxis.0.data', val: chartConfigData.areaData },
      { path: 'series.0.data', val: chartConfigData.keySeries0Data },
      { path: 'series.1.data', val: chartConfigData.keySeries1Data },
      { path: 'series.2.data', val: chartConfigData.keySeries2Data },
    ];
  });
  const chartOption: EChartsOption = reactive(
    genLzChart(
      [
        { name: '' },
        { name: '', iconType: 3, colorType: 2 },
        { name: '', iconType: 4, colorType: 3 },
      ],
      {
        labelNames: ['', '', ''],
        valueSuffix: ['', '', ''],
      },
    ),
  );
  onMounted(() => {
    getMiddleLeftBottomLineChart().then((res) => {
      const newOption = genLzChart(
        [
          { name: get(res, '0.typeName') },
          { name: get(res, '1.typeName'), iconType: 3, colorType: 2 },
          { name: get(res, '2.typeName'), iconType: 4, colorType: 3 },
        ],
        {
          labelNames: [get(res, '0.typeName'), get(res, '1.typeName'), get(res, '2.typeName')],
          valueSuffix: ['', '', ''],
        },
      );
      Object.assign(chartOption, newOption);
      chartConfigData.areaData = map(get(res, '0.lineChartVOList'), 'x');
      chartConfigData.keySeries0Data = map(get(res, '0.lineChartVOList'), 'yvalue');
      chartConfigData.keySeries1Data = map(get(res, '1.lineChartVOList'), 'yvalue');
      chartConfigData.keySeries2Data = map(get(res, '2.lineChartVOList'), 'yvalue');
    });
  });
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '季节性传染病统计分析',
  };
};

const 聚集性传染病患者学校分布 = (_year: Ref<string>): ChartData => {
  const switchValue = ref('key');
  const updateSwitchValue = (val) => {
    switchValue.value = val;
  };
  const chartConfigData = reactive({
    keyYAxisData: [] as any[],
    keySeries0Data: [] as any[],
  });
  const chartConfig = computed<any[]>(() => {
    if (switchValue.value === 'key') {
      return [
        { path: 'yAxis.0.data', val: chartConfigData.keyYAxisData },
        { path: 'series.0.data', val: chartConfigData.keySeries0Data },
      ];
    } else {
      return [];
    }
  });
  const chartOption: EChartsOption = reactive(genLzYChart({ name: '患者数' }));
  const cached = new Map();
  watch(
    () => switchValue.value,
    () => {
      if (cached.get(switchValue.value)) {
        return;
      }
      getMiddleRightBottomLineChart().then((res) => {
        if (switchValue.value === 'key') {
          chartConfigData.keyYAxisData = map(res, 'schoolName');
          chartConfigData.keySeries0Data = map(res, 'patientCount');
        }
        cached.set(switchValue.value, true);
      });
    },
    {
      immediate: true,
    },
  );
  return {
    chartConfig,
    chartOption,
    updateSwitchValue,
    switchValue,
    title: '聚集性传染病患者学校分布',
  };
};

export const chartConfigMap = {
  不明原因发热近七日患者监测,
  不明原因发热患者单位分布,
  不明原因发热患者社区分布,
  聚集性传染病近七日患者监测,
  聚集性传染病患者社区分布,
  聚集性传染病患者单位分布,
  季节性传染病统计分析,
  聚集性传染病患者学校分布,
};
