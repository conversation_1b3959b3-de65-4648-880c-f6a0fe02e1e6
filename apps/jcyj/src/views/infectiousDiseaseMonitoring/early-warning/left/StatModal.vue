<script setup lang="ts">
  /**
   * @deprecated 已废弃
   */
  import { computed, reactive, ref } from 'vue';
  import ViewModal from '/@/component/view-modal/index.vue';
  import {
    getJurisdictionStatisticData,
    getOrgStatisticData,
    getStatisticTableHeader,
  } from '/@/api/earlyWarnMonitor';
  import { useRequest } from '@ft/request';
  import {
    Col as AntCol,
    Form,
    FormItem,
    Input,
    Pagination,
    RangePicker,
    Row,
    Spin,
  } from 'ant-design-vue';
  import { getAdministrativeList } from '@ft/internal/api';
  import { ApiSelect } from '@ft/internal/components/Form';
  import { Button } from '@ft/internal/components/Button';
  import TabItemDecorate from './svg/tab-item.svg?raw';
  import TabItemDecorateActive from './svg/tab-item-active.svg?raw';

  const props = defineProps<{ visible: boolean }>();

  const emit = defineEmits<{
    (e: 'update:visible', value: boolean): void;
  }>();

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => {
      emit('update:visible', value);
    },
  });

  const activeTab = ref('1');
  const { data: tableHeader } = useRequest(getStatisticTableHeader, {
    ready: modalVisible,
    refreshDeps: [modalVisible],
  });

  const replaceTableHeader = computed(() => {
    return Object.entries(tableHeader.value || {}).reduce((acc, [key, value], index) => {
      acc[key] = activeTab.value === '2' && index === 0 ? '机构' : value;
      return acc;
    }, {});
  });

  const pageInfo = reactive({
    total: 0,
    pageSize: 10,
    current: 1,
  });
  const {
    data: tableData,
    loading,
    run,
  } = useRequest(
    (arg) =>
      activeTab.value === '1' ? getJurisdictionStatisticData(arg) : getOrgStatisticData(arg),
    {
      ready: modalVisible,
      refreshDeps: [modalVisible],
      onSuccess: (res) => {
        pageInfo.total = res.length;
      },
    },
  );

  const pageData = computed(() => {
    return tableData.value?.slice(
      (pageInfo.current - 1) * pageInfo.pageSize,
      pageInfo.current * pageInfo.pageSize,
    );
  });

  const formState = ref<{
    areaCode?: string;
    orgName?: string;
    statTime?: [string, string];
  }>({
    areaCode: undefined,
    statTime: ['', ''],
  });

  function getPopupContainer() {
    return document.querySelector('main.ant-layout-content') as HTMLElement;
  }

  function onSearch() {
    if (activeTab.value === '1') {
      run({
        areaCode: formState.value.areaCode,
        startDate:
          formState.value.statTime && formState.value.statTime?.[0]
            ? formState.value.statTime[0]
            : '',
        endDate:
          formState.value.statTime && formState.value.statTime?.[1]
            ? formState.value.statTime[1]
            : '',
      });
    } else {
      run({
        orgName: formState.value.orgName,
        startDate:
          formState.value.statTime && formState.value.statTime?.[0]
            ? formState.value.statTime[0]
            : '',
        endDate:
          formState.value.statTime && formState.value.statTime?.[1]
            ? formState.value.statTime[1]
            : '',
      });
    }
  }

  function onReset() {
    formState.value.areaCode = undefined;
    formState.value.orgName = undefined;
    formState.value.statTime = ['', ''];
    onSearch();
  }

  function getTableListRunAsync(page: number, pageSize: number) {
    pageInfo.current = page;
    pageInfo.pageSize = pageSize;
  }

  const isActiveTab = (tab: string) => activeTab.value === tab;

  const handleTabChange = (tab: string) => {
    activeTab.value = tab;
    formState.value.areaCode = undefined;
    formState.value.orgName = undefined;
    formState.value.statTime = ['', ''];
    onSearch();
  };
</script>

<template>
  <ViewModal width="60%" title="统计详情" v-model:visible="modalVisible">
    <div class="py-4 pl-2 pr-4 flex flex-col">
      <div class="flex my-4 gap-8">
        <div class="tabs flex gap-2">
          <div class="tab-item relative cursor-pointer" @click="handleTabChange('1')">
            <span class="absolute top-0 left-6">辖区统计</span>
            <div v-html="TabItemDecorate" v-if="!isActiveTab('1')"></div>
            <div v-html="TabItemDecorateActive" v-if="isActiveTab('1')"></div>
          </div>
          <div class="tab-item relative cursor-pointer" @click="handleTabChange('2')">
            <span class="absolute top-0 left-6">医疗机构统计</span>
            <div v-html="TabItemDecorate" v-if="!isActiveTab('2')"></div>
            <div v-html="TabItemDecorateActive" v-if="isActiveTab('2')"></div>
          </div>
        </div>
      </div>
      <Form :model="formState" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <Row>
          <AntCol span="8">
            <FormItem label="统计日期" name="statTime">
              <RangePicker
                style="width: 100%"
                v-model:value="formState.statTime"
                value-format="YYYY-MM-DD"
                :get-popup-container="getPopupContainer"
              />
            </FormItem>
          </AntCol>
          <AntCol span="6">
            <FormItem v-if="activeTab === '1'" label="统计辖区" name="areaCode">
              <ApiSelect
                :api="getAdministrativeList"
                placeholder="请选择"
                label-field="name"
                value-field="code"
                v-model:value="formState.areaCode"
                allowClear
                :get-popup-container="getPopupContainer"
              />
            </FormItem>
            <FormItem v-if="activeTab === '2'" label="医疗机构" name="orgName">
              <Input placeholder="请输入医疗机构名称" v-model:value="formState.orgName" />
            </FormItem>
          </AntCol>

          <AntCol span="3">
            <div class="flex gap-2">
              <Button @click="onSearch" class="w-20 !border-0 !text-sm !font-bold !text-white btn">
                搜索
              </Button>
              <Button
                @click="onReset"
                class="w-20 !border-1 !text-sm !font-bold !text-white !bg-[#204572] !border-[#8EA5C7]"
              >
                重置
              </Button>
            </div>
          </AntCol>
        </Row>
      </Form>
      <div class="w-full relative">
        <div class="a-table-body-container min-h-[256px] max-h-[256px]">
          <table v-if="!loading">
            <colgroup v-if="tableHeader">
              <col v-for="(_, idx) in replaceTableHeader" :key="idx" :style="{ width: 'auto' }" />
            </colgroup>
            <thead v-if="tableHeader">
              <tr>
                <th
                  class="whitespace-nowrap sticky -top-[0.5px]"
                  v-for="(item, idx) in replaceTableHeader"
                  :key="idx"
                >
                  {{ item }}
                </th>
              </tr>
            </thead>
            <tbody class="overflow-y-auto">
              <tr v-for="(item, idx) in pageData || []" :key="idx">
                <td
                  class="min-h-8 max-h-8 overflow-hidden whitespace-nowrap"
                  v-for="(_, idx) in replaceTableHeader || []"
                  :key="idx"
                >
                  {{ item[idx] }}
                </td>
              </tr>
            </tbody>
          </table>
          <div v-else class="flex justify-center items-center py-10">
            <Spin class="!text-white" tip="加载中..." />
          </div>
        </div>
        <div class="flex justify-end mt-5" v-if="pageInfo.total > 0">
          <Pagination
            :show-total="(total) => `共${total}条`"
            class="mt-5 !text-white"
            :total="pageInfo.total"
            :show-size-changer="false"
            v-model:current="pageInfo.current"
            @change="getTableListRunAsync"
          />
        </div>
      </div>
    </div>
  </ViewModal>
</template>
<style lang="less" scoped>
  table {
    color: #afd3ff;
    table-layout: fixed;
    font-size: 14px;

    th {
      text-align: left;
      padding: 8px 12px;
      color: #fff;
      background: #1f262e;
    }

    td {
      padding: 8px 12px;
    }

    tr:nth-child(2n) {
      background: rgb(255 255 255 / 8%);
    }
  }

  .a-table-body-container {
    // max-height: 280px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      // display: none;
      width: 5px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }
  }

  :deep {
    .ant-form-item-label > label {
      color: #afd3ff !important;
    }
  }

  .btn {
    background: linear-gradient(180deg, #1590ea 0%, rgb(21 144 234 / 31%) 100%);
  }
</style>
