<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import {
    Col as AntCol,
    Form,
    FormItem,
    Input,
    RadioButton,
    RadioGroup,
    RangePicker,
    Row,
  } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { useRequest } from '@ft/request';
  import { getJurisdictionStatisticData, getOrgStatisticData } from '/@/api/earlyWarnMonitor';
  import { ApiSelect } from '@ft/internal/components/Form';
  import { getAdministrativeList } from '@ft/internal/api';
  import { Button } from '@ft/internal/components/Button';
  import TabItemDecorateActive from './svg/tab-item-active.svg?raw';
  import Chart from './chart.vue';

  const emit = defineEmits<{
    (e: 'org-warn'): void;
  }>();

  const topRadioValue = ref(10);
  const bottomRadioValue = ref(10);

  function getPopupContainer() {
    return document.querySelector('main.ant-layout-content') as HTMLElement;
  }

  const { data: areaData, run: runArea } = useRequest((arg) => getJurisdictionStatisticData(arg));

  const areaChartData = computed(() => {
    const data =
      Object.entries(areaData.value?.[0] || {})
        .map(([key, value]) => {
          return {
            name: key,
            value: Number(value),
          };
        })
        .filter(Boolean) || [];
    const slice = topRadioValue.value === -1 ? data : data.slice(0, topRadioValue.value);
    return {
      name: '人数',
      xData: slice.map((i) => i?.name),
      seriesData: slice,
    };
  });

  const { data: orgData, run: runOrg } = useRequest((arg) => getOrgStatisticData(arg), {
    manual: true,
  });

  const orgChartData = computed(() => {
    const data =
      Object.entries(orgData.value?.[0] || {})
        .map(([key, value]) => {
          if (key === '0') return undefined;
          return {
            name: key,
            value: Number(value),
          };
        })
        .filter(Boolean) || [];
    const slice = bottomRadioValue.value === -1 ? data : data.slice(0, bottomRadioValue.value);

    return {
      name: '人数',
      xData: slice.map((i) => i?.name),
      seriesData: slice,
    };
  });

  const formStateArea = ref<{
    areaCode?: string;
    statTime?: [dayjs.Dayjs, dayjs.Dayjs];
  }>({
    areaCode: undefined,
    statTime: [dayjs().startOf('year'), dayjs()],
  });
  const formStateOrg = ref<{
    orgName?: string;
    statTime?: [dayjs.Dayjs, dayjs.Dayjs];
  }>({
    orgName: '宜昌市第三人民医院',
    statTime: [dayjs().startOf('year'), dayjs()],
  });
  function onSearchArea() {
    runArea({
      areaCode: formStateArea.value.areaCode,
      startDate:
        formStateArea.value.statTime && formStateArea.value.statTime?.[0]
          ? formStateArea.value.statTime[0]
          : '',
      endDate:
        formStateArea.value.statTime && formStateArea.value.statTime?.[1]
          ? formStateArea.value.statTime[1]
          : '',
    });
  }
  function onResetArea() {
    formStateArea.value.areaCode = undefined;
    formStateArea.value.statTime = [dayjs().startOf('year'), dayjs()];
    onSearchArea();
  }
  function onSearchOrg() {
    runOrg({
      orgName: formStateOrg.value.orgName,
      startDate:
        formStateOrg.value.statTime && formStateOrg.value.statTime?.[0]
          ? formStateOrg.value.statTime[0]
          : '',
      endDate:
        formStateOrg.value.statTime && formStateOrg.value.statTime?.[1]
          ? formStateOrg.value.statTime[1]
          : '',
    });
  }
  function onResetOrg() {
    formStateOrg.value.orgName = '宜昌市第三人民医院';
    formStateOrg.value.statTime = [dayjs().startOf('year'), dayjs()];
    onSearchOrg();
  }
  function handleOrgWarn() {
    emit('org-warn');
  }
  onMounted(() => {
    onSearchOrg();
  });
</script>

<template>
  <div class="h-820px p-4 flex flex-col gap-2">
    <div class="flex flex-col flex-1 of-hidden">
      <div class="tab-item relative cursor-pointer">
        <span class="absolute top-0 left-6">辖区统计</span>
        <div v-html="TabItemDecorateActive"></div>
      </div>
      <Form :model="formStateArea" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <Row>
          <AntCol span="10">
            <FormItem label="统计日期" name="statTime">
              <RangePicker
                style="width: 100%"
                v-model:value="formStateArea.statTime"
                value-format="YYYY-MM-DD"
                :get-popup-container="getPopupContainer"
              />
            </FormItem>
          </AntCol>
          <AntCol span="6">
            <FormItem label="统计辖区" name="areaCode">
              <ApiSelect
                :api="getAdministrativeList"
                placeholder="请选择"
                label-field="name"
                value-field="code"
                v-model:value="formStateArea.areaCode"
                allowClear
                :get-popup-container="getPopupContainer"
              />
            </FormItem>
          </AntCol>

          <AntCol span="3">
            <div class="flex gap-2">
              <Button
                @click="onSearchArea"
                class="w-20 !border-0 !text-sm !font-bold !text-white btn"
              >
                搜索
              </Button>
              <Button
                @click="onResetArea"
                class="w-20 !border-1 !text-sm !font-bold !text-white !bg-[#204572] !border-[#8EA5C7]"
              >
                重置
              </Button>
            </div>
          </AntCol>
        </Row>
      </Form>
      <div class="w-full relative">
        <div class="flex justify-end mt-3">
          <RadioGroup v-model:value="topRadioValue" size="small" button-style="solid">
            <RadioButton :value="10">top10</RadioButton>
            <RadioButton :value="20">top20</RadioButton>
            <RadioButton :value="30">top30</RadioButton>
            <RadioButton :value="-1">全部</RadioButton>
          </RadioGroup>
        </div>
        <Chart
          v-if="areaChartData?.xData?.length && areaChartData?.seriesData?.length"
          :chartData="areaChartData"
        />
        <div v-else class="flex justify-center items-center h-full">
          <span class="text-center text-sm text-white"> 暂无数据 </span>
        </div>
      </div>
    </div>
    <div class="flex flex-col flex-1 of-hidden">
      <div class="flex justify-between">
        <div class="tab-item relative cursor-pointer">
          <span class="absolute top-0 left-6">医疗机构统计</span>
          <div v-html="TabItemDecorateActive"></div>
        </div>
        <span
          @click="handleOrgWarn"
          class="text-base bg-gradient-to-b from-white to-blue-400 bg-clip-text text-transparent shadow-text cursor-pointer"
        >
          机构预警 >
        </span>
      </div>
      <Form :model="formStateOrg" :label-col="{ span: 6 }" :wrapper-col="{ span: 16 }">
        <Row>
          <AntCol span="10">
            <FormItem label="统计日期" name="statTime">
              <RangePicker
                style="width: 100%"
                v-model:value="formStateOrg.statTime"
                value-format="YYYY-MM-DD"
                :get-popup-container="getPopupContainer"
              />
            </FormItem>
          </AntCol>
          <AntCol span="6">
            <FormItem label="医疗机构" name="orgName">
              <Input placeholder="请输入医疗机构名称" v-model:value="formStateOrg.orgName" />
            </FormItem>
          </AntCol>

          <AntCol span="3">
            <div class="flex gap-2">
              <Button
                @click="onSearchOrg"
                class="w-20 !border-0 !text-sm !font-bold !text-white btn"
              >
                搜索
              </Button>
              <Button
                @click="onResetOrg"
                class="w-20 !border-1 !text-sm !font-bold !text-white !bg-[#204572] !border-[#8EA5C7]"
              >
                重置
              </Button>
            </div>
          </AntCol>
        </Row>
      </Form>
      <div class="w-full relative flex-1 of-hidden">
        <div class="flex justify-end mt-3">
          <RadioGroup v-model:value="bottomRadioValue" size="small" button-style="solid">
            <RadioButton :value="10">top10</RadioButton>
            <RadioButton :value="20">top20</RadioButton>
            <RadioButton :value="30">top30</RadioButton>
            <RadioButton :value="-1">全部</RadioButton>
          </RadioGroup>
        </div>
        <Chart
          v-if="orgChartData?.xData?.length && orgChartData?.seriesData?.length"
          :chartData="orgChartData"
        />
        <div v-else class="flex justify-center items-center h-full">
          <span class="text-center text-sm text-white"> 暂无数据 </span>
        </div>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .ant-form-item-label > label {
      color: #afd3ff !important;
    }

    .ant-radio-button-wrapper-checked {
      background: linear-gradient(180deg, #1590ea 0%, rgb(21 144 234 / 31%) 100%) !important;
    }

    .ant-form-item {
      margin-bottom: 0 !important;
    }

    .ant-radio-button-wrapper {
      box-shadow: none !important;
    }
  }

  .btn {
    background: linear-gradient(180deg, #1590ea 0%, rgb(21 144 234 / 31%) 100%);
  }
</style>
