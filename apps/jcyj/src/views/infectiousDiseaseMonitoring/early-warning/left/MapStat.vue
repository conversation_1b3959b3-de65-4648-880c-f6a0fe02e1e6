<script setup lang="ts">
  import { Select } from 'ant-design-vue';
  import LMap from '/@/component/yc-map/LMap.supermap.vue';
  import { useRequest } from '@ft/request';
  import { getInfectiousDiseaseList, getScatterPlot } from '/@/api/earlyWarnMonitor';
  import { computed, ref } from 'vue';
  import { map } from 'lodash-es';
  import Modal from './Modal.vue';

  const emit = defineEmits<{
    (e: 'stat'): void;
  }>();

  const levelMap = {
    红色: 1,
    橙色: 2,
    黄色: 3,
    蓝色: 4,
  };

  const selectedValue = ref();
  const { data: optionsData } = useRequest(getInfectiousDiseaseList, {
    pollingInterval: 60 * 1000,
    onSuccess: (data) => {
      if (selectedValue.value) return;
      // if (data.some((item) => item.infectiousDiseaseCode === '29')) {
      //   selectedValue.value = '29';
      // } else {
      //   selectedValue.value = data[0].infectiousDiseaseCode;
      // }
      selectedValue.value = data.find((item) => item.defaultFlag === 1)?.infectiousDiseaseCode;
    },
  });
  const options = computed(() =>
    optionsData.value?.map((item) => ({
      label: item.infectiousDiseaseName,
      value: item.infectiousDiseaseCode,
    })),
  );
  const { data: scatterData } = useRequest(() => getScatterPlot(selectedValue.value), {
    pollingInterval: 60 * 1000,
    ready: selectedValue,
    refreshDeps: [selectedValue],
  });
  const popupData = computed(() => {
    // const title = optionsData.value?.find(
    //   (item) => item.infectiousDiseaseCode === selectedValue.value,
    // )?.infectiousDiseaseName;
    return map(scatterData.value, (item) => ({
      center: [parseFloat(item.latitude), parseFloat(item.longitude)] as any,
      title: item.orgName || '',
      content: `近“${item.monitorCycle || ''}”日“${item.infectionName}”患者数 “${item.count}”例`,
      level: levelMap[item.color],
      data: {
        ...item,
        infectionCode: selectedValue.value,
      },
      fn: (data) => {
        visible.value = true;
        selectedData.value = data;
      },
    }));
  });
  const visible = ref(false);
  const selectedData = ref({});
  function getPopupContainer() {
    return document.querySelector('main.ant-layout-content') as HTMLElement;
  }
  function handleStat() {
    emit('stat');
  }
</script>
<template>
  <div class="p-4 flex items-center justify-between">
    <Select
      id="select"
      showSearch
      optionFilterProp="label"
      v-model:value="selectedValue"
      :getPopupContainer="getPopupContainer"
      :options="options"
      class="a-select"
      style="width: 200px"
    />
    <span
      class="text-base bg-gradient-to-b from-white to-blue-400 bg-clip-text text-transparent shadow-text cursor-pointer"
      @click="handleStat"
    >
      统计详情 >
    </span>
  </div>

  <div class="h-800px w-960px">
    <LMap :popups="popupData" />
  </div>
  <Modal v-model:visible="visible" :data="selectedData" />
</template>
