<script setup lang="ts">
  import { computed } from 'vue';
  import ViewModal from '/@/component/view-modal/index.vue';
  import { getPatientList } from '/@/api/earlyWarnMonitor';
  import { useRequest } from '@ft/request';
  import dayjs from 'dayjs';

  const props = defineProps<{ visible: boolean; data: any }>();

  const emit = defineEmits<{
    (e: 'update:visible', value: boolean): void;
  }>();

  const modalVisible = computed({
    get: () => props.visible,
    set: (value) => {
      emit('update:visible', value);
    },
  });

  const { data } = useRequest(
    () =>
      getPatientList({
        monitorCycle: props.data?.monitorCycle,
        infectionCode: props.data?.infectionCode,
        visitOrgCode: props.data?.orgCode,
        startDate: dayjs().add(-1, 'day').format('YYYY-MM-DD'),
        endDate: dayjs().format('YYYY-MM-DD'),
      }),
    {
      ready: modalVisible,
      refreshDeps: [modalVisible],
    },
  );
</script>

<template>
  <ViewModal width="60%" title="患者列表" v-model:visible="modalVisible">
    <div class="py-4 pl-2 pr-4 flex flex-col">
      <div class="flex my-4 gap-8">
        <div>监测周期：{{ props.data?.monitorCycle }}天</div>
        <div>统计传染病: {{ props.data?.infectionName }}</div>
        <div>统计医院: {{ props.data?.orgName }}</div>
      </div>
      <div class="a-table-header-container">
        <table>
          <colgroup>
            <col style="width: 80px" />
            <col style="width: 100px" />
            <col style="width: 100px" />
            <col style="width: 100px" />
            <col style="width: 120px" />
            <col style="width: 320px" />
            <col style="width: 200px" />
            <!-- <col style="width: 180px" />
            <col style="width: 120px" /> -->
            <col style="width: 180px" />
          </colgroup>
          <thead>
            <tr>
              <th> 序号 </th>
              <th> 患者姓名 </th>
              <th> 性别 </th>
              <th> 年龄 </th>
              <th> 疾病 </th>
              <th> 诊断 </th>
              <th> 就诊医院 </th>
              <!-- <th> 地址 </th> -->
              <!-- <th> 发病日期 </th> -->
              <th> 诊断日期 </th>
            </tr>
          </thead>
        </table>
      </div>
      <div class="a-table-body-container">
        <table>
          <colgroup>
            <col style="width: 80px" />
            <col style="width: 100px" />
            <col style="width: 100px" />
            <col style="width: 100px" />
            <col style="width: 120px" />
            <col style="width: 320px" />
            <col style="width: 200px" />
            <!-- <col style="width: 180px" /> -->
            <!-- <col style="width: 120px" /> -->
            <col style="width: 180px" />
          </colgroup>
          <tbody>
            <tr v-for="(item, idx) in data" :key="idx">
              <td>{{ idx + 1 }}</td>
              <td>{{ item.patientName }}</td>
              <td>{{ item.sexName }}</td>
              <td>{{ item.age }}</td>
              <td>{{ item.infectionName }}</td>
              <td>{{ item.diseaseName }}</td>
              <td>{{ props.data?.orgName }}</td>
              <!-- <td>{{ item.address }}</td> -->
              <!-- <td>{{ item.morbidityDate }}</td> -->
              <td>{{ item.reportDate }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="text-right mt-4 mr-8"> 合计（人数）：{{ data?.length }} </div>
    </div>
  </ViewModal>
</template>
<style lang="less" scoped>
  table {
    color: #afd3ff;
    table-layout: fixed;
    font-size: 14px;

    th {
      text-align: left;
      padding: 8px 12px;
      color: #fff;
      background: rgb(255 255 255 / 8%);
    }

    td {
      padding: 8px 12px;
    }

    tr:nth-child(2n) {
      background: rgb(255 255 255 / 8%);
    }
  }

  .a-table-body-container {
    max-height: 492px;
    overflow-y: auto;

    &::-webkit-scrollbar {
      display: none;
    }
  }
</style>
