<script setup lang="ts">
  import CommonTab from '/@/component/common-tab/index.vue';
  import MapStat from './MapStat.vue';
  import BarStat from './BarStat.vue';
  import { ref, watchEffect } from 'vue';
  import { useRoute } from 'vue-router';

  const route = useRoute();

  const activeTab = ref(route.path.startsWith('/analysis') ? 'bar' : 'map');

  watchEffect(() => {
    activeTab.value = route.path.startsWith('/analysis') ? 'bar' : 'map';
  });

  const handleOrgWarn = () => {
    activeTab.value = 'map';
  };

  const handleStat = () => {
    activeTab.value = 'bar';
  };
</script>
<template>
  <CommonTab title="聚集性/突发传染病预警" :type="4">
    <BarStat v-if="activeTab === 'bar'" @org-warn="handleOrgWarn" />
    <MapStat v-if="activeTab === 'map'" @stat="handleStat" />
  </CommonTab>
</template>
<style lang="less" scoped>
  :deep {
    .ant-select-selector {
      border-color: #5a86c2 !important;
    }
  }
</style>
