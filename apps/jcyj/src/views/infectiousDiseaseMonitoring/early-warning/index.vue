<script setup lang="ts">
  import Left from './left/index.vue';
  import Right from './right/index.vue';
</script>

<template>
  <div class="h-full w-full text-white flex justify-between gap-5">
    <Left />
    <Right />
  </div>
</template>

<style lang="less" scoped>
  .active {
    background: linear-gradient(180deg, #1590ea 0%, rgb(21 144 234 / 31%) 100%);
    color: white;
    border-image: linear-gradient(180deg, #23befa 0%, rgb(255 255 255 / 0%) 100%) 1;
    box-shadow: -2px 0 4px 0 rgb(0 0 0 / 30%);
  }
</style>
