<script setup lang="ts">
  import { onMounted, ref } from 'vue';
  import { genGrid, genLegend, genSeries, genXAxis, genYAxis, legendIcon1 } from '/@/helper/chart';
  import CardChart from '/@/component/card-chart/index.vue';

  const props = defineProps<{
    chartData: {
      name: string;
      xData: any[];
      seriesData: any;
    };
  }>();

  const chartOption = ref({});
  const chartConfigs = ref<any>([]);
  onMounted(() => {
    chartConfigs.value = [
      { path: 'xAxis.0.data', val: props.chartData.xData },
      {
        path: 'series.0.data',
        val: props.chartData.seriesData,
      },
    ];
    chartOption.value = {
      grid: genGrid({ right: 20 }),
      legend: genLegend({
        preset: 'show',
        data: [{ name: props.chartData.name, icon: legendIcon1 }],
      }),
      xAxis: [
        genXAxis({
          features: ['breakLabel'],
        }),
      ],
      yAxis: [genYAxis({})],
      series: [
        genSeries({
          preset: '立体柱形',
          name: props.chartData.name,
        }),
      ],
    };
  });
</script>

<template>
  <CardChart
    title=""
    width="800px"
    height="278px"
    :configs="chartConfigs"
    :option="chartOption"
    autoplay
  />
</template>
