<script setup lang="ts">
  import { onBeforeUnmount, onMounted, reactive, ref } from 'vue';
  import { forEach } from 'lodash-es';
  import { getEarlyWarnBarChart, getEarlyWarnIndex } from '/@/api/earlyWarnMonitor';
  import LMap from '/@/component/yc-map/LMap.supermap.vue';
  import CommonTab from '/@/component/common-tab/index.vue';
  import Chart from './chart.vue';
  import Right from './right/index.vue';
  import { getRandomCenter } from '/@/helper';
  const isInit = ref(false);

  const activeSelect = reactive({
    1: '',
    3: '',
    4: '',
  });
  const list = ref([
    // {
    //   type: 1,
    //   mapTitle: '学校聚集性/突发传染病预警',
    //   mapDesc: '',
    //   warnList: [],
    //   mapData: {},
    //   chartTitle: '学校传染病发病监测',
    //   chartDesc: '',
    //   chartData: {},
    // },
    {
      type: 3,
      mapTitle: '社区聚集性/突发传染病预警',
      mapDesc: '',
      warnList: [] as any,
      chartData: {} as any,
      chartTitle: '社区传染病发病监测',
      chartDesc: '',
      mapData: {} as any,
    },
    // {
    //   type: 4,
    //   mapTitle: '单位聚集性/突发传染病预警',
    //   mapDesc: '',
    //   warnList: [] as any,
    //   chartData: {} as any,
    //   chartTitle: '单位传染病发病监测',
    //   chartDesc: '',
    //   mapData: {} as any,
    // },
  ]);

  const typeMap = {
    1: '学校',
    3: '社区',
    4: '单位',
  };
  const levelMap = {
    红色: 1,
    橙色: 2,
    黄色: 3,
    蓝色: 4,
  };
  let timer;
  onMounted(() => {
    const fn = () => {
      Promise.all([getEarlyWarnBarChart(), getEarlyWarnIndex()])
        .then((res) => {
          forEach(list.value, (item) => {
            const chartObj = (res[0] || []).find((v) => v.type === item.type);
            const mapObj = (res[1] || []).find((v) => v.type === item.type);
            if (chartObj) {
              item.chartDesc = chartObj.desc;
              item.chartData = {
                name: chartObj.infectionName,
                xData: Object.keys(chartObj.dataMap),
                seriesData: Object.values(chartObj.dataMap),
              };
            }
            if (mapObj) {
              item.warnList = mapObj.indexList.map((v) => v.name);
              item.mapData = Object.fromEntries(
                mapObj.indexList.map((v) => [
                  v.name,
                  v.promptList.map((item) => ({
                    center: getRandomCenter(),
                    title: v.name,
                    content: `${item.infectionName}${item.earlyWarnNum}例！！！`,
                    level: levelMap[item.level],
                  })),
                ]),
              );
              if (item.warnList?.length > 0) {
                activeSelect[item.type] = item.warnList[0];
                item.mapDesc = `请注意，当前有${typeMap[item.type]}有聚集性/突发传染病，请及时关注`;
              } else {
                activeSelect[item.type] = '';
                item.mapDesc = `当前无${typeMap[item.type]}有聚集性/突发传染病`;
              }
            }
          });
        })
        .finally(() => {
          isInit.value = true;
        });
    };
    timer = setInterval(fn, 1000 * 30);
    fn();
  });
  onBeforeUnmount(() => {
    timer = clearInterval(timer);
  });
</script>

<template>
  <div class="h-full w-full text-white pt-4 flex justify-between gap-5">
    <template v-if="isInit">
      <div v-for="item in list" :key="item.type" class="w-800px h-full">
        <div class="h-full flex flex-col">
          <div class="h-600px flex flex-col">
            <CommonTab :title="item.mapTitle">
              <div class="flex items-center h-36px px-2 mb-1">
                <template v-if="item.warnList.length > 0">
                  <img
                    class="h-32px w-auto"
                    src="/@/assets/images/data-view/early-warning/warn-triangle.png"
                    alt=""
                  />
                  <span class="text-[#E55257] leading-36px">
                    {{ item.mapDesc }}
                  </span>
                </template>
                <template v-else>
                  <span class="text-[#00A6FF]">{{ item.mapDesc }}</span>
                </template>
              </div>
              <div v-if="item.warnList?.length > 0" class="flex flex-wrap gap-2 px-4 mb-4">
                <span
                  :class="[activeSelect[item.type] === warn ? 'active' : '']"
                  class="text-[#AFD3FF] bg-[#182C50] px-2 py-1 rounded-sm cursor-pointer border-1-transparent"
                  v-for="warn in item.warnList"
                  :key="warn"
                  @click="activeSelect[item.type] = warn"
                >
                  {{ warn }}
                </span>
              </div>
            </CommonTab>
            <LMap :popups="item.mapData[activeSelect[item.type]]" />
          </div>
          <div class="flex pt-1 gap-5">
            <CommonTab :title="item.chartTitle">
              <div class="relative">
                <span class="text-[#FF9E37] left-4 top-2 absolute">
                  {{ item.chartDesc }}
                </span>
              </div>
              <Chart :chartData="item.chartData" />
            </CommonTab>
          </div>
        </div>
      </div>
    </template>
    <div v-else class="w-800px h-full"></div>
    <Right />
  </div>
</template>

<style lang="less" scoped>
  .active {
    background: linear-gradient(180deg, #1590ea 0%, rgb(21 144 234 / 31%) 100%);
    color: white;
    border-image: linear-gradient(180deg, #23befa 0%, rgb(255 255 255 / 0%) 100%) 1;
    box-shadow: -2px 0 4px 0 rgb(0 0 0 / 30%);
  }
</style>
