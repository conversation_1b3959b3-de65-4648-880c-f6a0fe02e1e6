<script setup lang="ts">
  import { computed, onBeforeUnmount, onMounted, ref } from 'vue';
  import CommonTab from '/@/component/common-tab/index.vue';
  import { useRequest } from '@ft/request';
  import { getUnder7DiseasesMonitor } from '/@/api/earlyWarnMonitor';
  import { useMouseInElement } from '@vueuse/core';
  const { data } = useRequest(getUnder7DiseasesMonitor);
  const list = computed(() =>
    data.value?.map((item) => {
      const split = item.split('出现1例') || [];
      const split2 = split[1].split('患者');
      return [split[0] + '出现1例', split2[0], '患者' + split2[1]];
    }),
  );
  const containerRef = ref<HTMLDivElement>();
  const { isOutside } = useMouseInElement(containerRef);
  let timer;
  onMounted(() => {
    timer = setInterval(() => {
      const divEl = containerRef.value;
      if (divEl && isOutside.value) {
        if (divEl.clientHeight + divEl.scrollTop >= divEl.scrollHeight) {
          divEl.scrollTop = 0;
        } else {
          divEl.scrollTop += divEl.scrollTop === 0 ? 40 : 36;
        }
      }
    }, 1000);
  });
  onBeforeUnmount(() => {
    clearInterval(timer);
  });
</script>
<template>
  <div>
    <CommonTab title="近七日内传染病发病监测" :type="3">
      <div ref="containerRef" class="scroll-container">
        <div v-for="(item, idx) in list" :key="idx" class="truncate">
          {{ item[0] }}
          <span class="color-[#FF6C76]">{{ item[1] }}</span>
          {{ item[2] }}
        </div>
      </div>
    </CommonTab>
  </div>
</template>
<style lang="less" scoped>
  .scroll-container {
    width: 800px;
    height: 292px;
    margin-bottom: 24px;
    padding: 12px;
    padding-top: 10px;
    padding-bottom: 24px;
    font-size: 16px;
    color: #afd3ff;
    overflow-y: hidden;

    & > div {
      height: 36px;
      line-height: 36px;
    }
  }
</style>
