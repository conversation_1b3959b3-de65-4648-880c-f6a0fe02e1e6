<script setup lang="ts">
  import { cloneDeep, debounce, map, set } from 'lodash-es';
  import { Modal } from 'ant-design-vue';
  import { computed, nextTick, onMounted, reactive, ref } from 'vue';
  import { gen<PERSON><PERSON><PERSON><PERSON>, genLz<PERSON><PERSON><PERSON> } from '/@/helper/chart';
  import { useDataViewEle } from '/@/helper';
  import { useECharts } from '@ft/internal/hooks/web/useECharts';
  import CommonTab from '/@/component/common-tab/index.vue';
  import CardChart from '/@/component/card-chart/index.vue';
  import {
    getUnder7DiseasesRankMonitor,
    getUnder7DiseasesRankPatientDistributionByInfectionCode,
    getUnder7DiseasesRankPatientDistributionByInfectionCodeAreaCode,
  } from '/@/api/earlyWarnMonitor';

  let isBind = false;

  const chartOption = reactive(genLzYChart({ name: '人数' }));
  const isInit = ref(false);
  const chartConfigData = reactive({
    keyYAxisData: [] as any[],
    keySeries0Data: [] as any[],
  });
  const chartConfigs = computed<any>(() => [
    { path: 'yAxis.0.data', val: chartConfigData.keyYAxisData },
    { path: 'series.0.data', val: chartConfigData.keySeries0Data },
  ]);
  const originData = ref<any[]>([]);
  onMounted(() => {
    getUnder7DiseasesRankMonitor().then((res) => {
      originData.value = res;
      const temp = res.map((item: any) => ({
        label: item.infectionName,
        value: item.count,
      }));
      const tt = temp
        .sort((a, b) => b.value - a.value)
        .slice(0, 5)
        .reverse();
      chartConfigData.keyYAxisData = tt.map((item) => item.label);
      chartConfigData.keySeries0Data = tt.map((item) => item.value);
      isInit.value = true;
    });
  });
  const chartOption1 = reactive(genLzChart([{ name: '' }]));
  const chartConfigData1 = reactive({
    areaData: [] as any[],
    keySeries0Data: [] as any[],
  });
  const chartConfigs1 = computed<any>(() => [
    { path: 'xAxis.0.data', val: chartConfigData1.areaData },
    { path: 'series.0.data', val: chartConfigData1.keySeries0Data },
  ]);
  const detailVisible = ref(false);
  const { getDataViewEle } = useDataViewEle();
  const chartRef = ref<HTMLDivElement | null>(null);
  const chartRef2 = ref<HTMLDivElement | null>(null);
  const { setOptions, getInstance } = useECharts(chartRef as any);
  const { setOptions: setOptions2, getInstance: getInstance2 } = useECharts(chartRef2 as any);
  const innerVisible = ref(false);
  const currentInfectionCode = ref('');
  const currentAreaName = ref('');

  function innerBarClick(
    infectionName: string,
    infectionCode: string,
    areaCode: string,
    areaName: string,
  ) {
    currentInfectionCode.value = infectionCode;
    currentAreaName.value = areaName;
    innerVisible.value = true;

    nextTick(() => {
      const chartOption = genLzChart([{ name: areaName }], {
        labelNames: [`${infectionName}`],
        valueSuffix: ['人'],
      });

      getUnder7DiseasesRankPatientDistributionByInfectionCodeAreaCode(infectionCode, areaCode).then(
        (res) => {
          if (chartOption.xAxis && Array.isArray(chartOption.xAxis)) {
            (chartOption.xAxis[0] as any).data = map(res, 'x');
          }
          chartOption.series[0].data = map(res, 'yvalue');
          if (chartOption.xAxis && Array.isArray(chartOption.xAxis)) {
            (chartOption.xAxis[0] as any).axisLabel.width = 96;
          }

          setOptions2(chartOption);
        },
      );
    });
  }

  const currentInfection = {
    value: {} as any,
  };

  function barClick(val) {
    getUnder7DiseasesRankPatientDistributionByInfectionCode(val.infectionCode).then((res) => {
      const newOption = genLzChart([{ name: val.infectionName }], {
        labelNames: [`${val.infectionName}`],
        valueSuffix: ['人'],
      });
      Object.assign(chartOption1, newOption);
      chartConfigData1.areaData = map(res, 'x');
      chartConfigData1.keySeries0Data = map(res, 'yvalue');
      nextTick(() => {
        detailVisible.value = true;
        const optionCopy = cloneDeep(chartOption1);
        chartConfigs1.value.forEach((config) => {
          const { path, val } = config;
          set(optionCopy, path, val);
        });
        set(optionCopy, 'legend.top', 10);
        set(optionCopy, 'legend.orient', 'horizontal');
        if (optionCopy.yAxis) {
          if (Array.isArray(optionCopy.yAxis)) {
            optionCopy.yAxis?.forEach((item) => {
              (item as any).splitNumber = 5;
            });
          } else {
            (optionCopy.yAxis as any).splitNumber = 5;
          }
        }
        currentInfection.value = val;
        setOptions(optionCopy);
        nextTick(() => {
          if (isBind) return;
          getInstance()?.on(
            'click',
            'series',
            debounce(
              (params: any) => {
                const areaCode = res.find((item) => item.x === params.name)?.xvalue;
                innerBarClick(
                  currentInfection.value.infectionName,
                  currentInfection.value.infectionCode,
                  areaCode,
                  params.name,
                );
              },
              1000,
              {
                leading: true,
              },
            ),
          );
          isBind = true;
        });
      });
    });
  }
</script>
<template>
  <div>
    <CommonTab title="近七日内传染病发病顺位" :type="3">
      <CardChart
        v-if="isInit"
        title="近七日内传染病发病顺位"
        width="800px"
        height="480px"
        :configs="chartConfigs"
        :option="chartOption"
        :originData="originData"
        @bar-click="barClick"
        autoplay
      />
    </CommonTab>
    <Modal
      v-model:visible="detailVisible"
      :get-container="getDataViewEle"
      centered
      width="calc(100% - 358px)"
      wrap-class-name="data-view-modal"
      :mask-style="{ 'backdrop-filter': 'blur(6px)', background: 'rgba(0, 0, 0, 0.5)' }"
      :closable="false"
      :footer="null"
    >
      <div class="relative">
        <div ref="chartRef" style="width: 100%; height: 600px; padding: 30px 20px 20px"></div>
        <div class="absolute" style="top: 12px; left: 20px; color: #fff; font-size: 20px">
          传染病人数辖区分布情况
        </div>
      </div>
    </Modal>

    <Modal
      v-model:visible="innerVisible"
      :get-container="getDataViewEle"
      centered
      width="calc(100% - 358px)"
      @ok="getInstance2()?.dispose()"
      okText="确定"
      wrap-class-name="data-view-modal"
      :mask-style="{ 'backdrop-filter': 'blur(6px)', background: 'rgba(0, 0, 0, 0.5)' }"
      :footer="null"
    >
      <div class="relative">
        <div ref="chartRef2" style="width: 100%; height: 600px; padding: 30px 20px 20px"></div>
        <div class="absolute" style="top: 12px; left: 20px; color: #fff; font-size: 20px">
          传染病人数机构分布情况
        </div>
      </div>
    </Modal>
  </div>
</template>
<style lang="less">
  .data-view-modal {
    .ant-modal-content {
      background-color: rgb(11 24 46);
    }
  }
</style>
