import type { TooltipOption } from 'echarts/types/dist/shared';
import { merge } from 'lodash-es';

interface CustomTooltipOption {
  labelNames: string[];
  valueSuffix: string[];
}
export function genTooltip(option?: CustomTooltipOption & TooltipOption): any {
  const { labelNames, valueSuffix, ...rest } = (option as any) || {};

  return merge(
    {
      trigger: 'axis',
      confine: true,
      // appendToBody: true,
      axisPointer: {
        type: 'none',
      },
      backgroundColor: 'transparent',
      borderColor: 'transparent',
      borderWidth: 0,
      padding: 4,
      formatter: (params) => {
        let dataStr = `<div style="color: #fff; font-size: 16px;font-weight: bold;">${params[0].name}</div>`;
        for (const idx in params) {
          const item = params[idx];
          dataStr += `<div style="width: 200px; display: flex; margin-top: 8px">
          <div style="color: #AFD3FF; flex: 1;word-break: break-word; white-space:pre-line;">${
            labelNames[item.seriesIndex] || ''
          }</div>
          <div style="float:right;color:#AFD3FF;margin-left:20px; display:inline-block; width: 50px; flex-shrink: 0; text-align:right">${
            item.data
          }${valueSuffix[item.seriesIndex] || ''}</div>
        </div>`;
        }
        return `<div class="jcyj-tooltip" style="transform: scale(0.90);" onclick="event.stopPropagation();">${dataStr}</div>`;
      },
      enterable: true,
      extraCssText: 'max-height: 250px;overflow:auto;z-index: 99999',
    },
    rest,
  );
}

export function genYzTooltip(option?: CustomTooltipOption & TooltipOption): any {
  const { labelNames, valueSuffix, ...rest } = (option as any) || {};
  return merge(
    {
      trigger: 'axis',
      confine: true,
      // appendToBody: true,
      axisPointer: {
        type: 'shadow',
      },
      backgroundColor: '#031733',
      borderColor: '#00E5FF',
      borderWidth: 1,
      padding: 4,
      formatter: (params) => {
        let dataStr = `<div style="color: #fff; font-size: 16px;">${params[0].name}</div>`;
        const p = [params[0], params[3]];
        for (const idx in p) {
          const item = p[idx];
          dataStr += `<div style=" display: flex; margin-top: 8px">
          <div style="color: #fff; flex: 1;word-break: break-word; white-space:pre-line;">${
            labelNames[idx] || ''
          }</div>
          <div style="float:right;color:#00E5FF;margin-left:20px; display:inline-block; width: 50px; flex-shrink: 0; text-align:right">${
            item.data
          }${valueSuffix[idx] || ''}</div>
        </div>`;
        }
        return `<div style="transform: scale(0.9);" onclick="event.stopPropagation();">${dataStr}</div>`;
      },
      enterable: true,
      extraCssText: 'max-height: 250px;overflow:auto;z-index: 99999',
    },
    rest,
  );
}
