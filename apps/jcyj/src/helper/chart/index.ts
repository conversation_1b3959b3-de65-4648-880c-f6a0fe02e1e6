import type {
  CustomSeriesRenderItemAPI,
  CustomSeriesRenderItemParams,
  CustomSeriesRenderItemReturn,
} from 'echarts';
import { graphic } from 'echarts';
import { toString } from 'lodash-es';
import { genGrid } from './grid';
import { genSeries } from './series';
import { genXAxis } from './xAxis';
import { genYAxis } from './yAxis';
import { genLegend } from './legend';
import { genTooltip, genYzTooltip } from './tooltip';
import { barColorSet } from './color';
import icon from '/@/assets/svg/data-view/legend/icon-1.svg';
import icon2 from '/@/assets/svg/data-view/legend/icon-2.svg';
import icon3 from '/@/assets/svg/data-view/legend/icon-3.svg';
import icon4 from '/@/assets/svg/data-view/legend/icon-4.svg';
import icon5 from '/@/assets/svg/data-view/legend/icon-5.svg';
import icon6 from '/@/assets/svg/data-view/legend/icon-6.svg';
import icon7 from '/@/assets/svg/data-view/legend/icon-7.svg';
import icon8 from '/@/assets/svg/data-view/legend/icon-8.svg';

export function register() {
  // import('echarts-gl');
  const leftShape = graphic.extendShape({
    buildPath(ctx, shape) {
      const { topBasicsYAxis, bottomYAxis, basicsXAxis } = shape;
      // 侧面宽度
      const WIDTH = 10;
      // 斜角高度
      const OBLIQUE_ANGLE_HEIGHT = 5;

      const p1 = [basicsXAxis, topBasicsYAxis + OBLIQUE_ANGLE_HEIGHT]; // 上左
      const p2 = [basicsXAxis, bottomYAxis]; // 下左
      const p3 = [basicsXAxis - WIDTH, bottomYAxis]; // 下右
      const p4 = [basicsXAxis - WIDTH, topBasicsYAxis]; // 上右

      ctx.moveTo(p1[0], p1[1]);
      ctx.lineTo(p2[0], p2[1]);
      ctx.lineTo(p3[0], p3[1]);
      ctx.lineTo(p4[0], p4[1]);
      ctx.closePath();
    },
  });

  const rightShape = graphic.extendShape({
    buildPath(ctx, shape) {
      const { topBasicsYAxis, bottomYAxis, basicsXAxis } = shape;
      // 侧面宽度
      const WIDTH = 10;
      // 斜角高度
      const OBLIQUE_ANGLE_HEIGHT = 5;

      const p1 = [basicsXAxis, topBasicsYAxis + OBLIQUE_ANGLE_HEIGHT];
      const p2 = [basicsXAxis, bottomYAxis];
      const p3 = [basicsXAxis + WIDTH, bottomYAxis];
      const p4 = [basicsXAxis + WIDTH, topBasicsYAxis];

      ctx.moveTo(p1[0], p1[1]);
      ctx.lineTo(p2[0], p2[1]);
      ctx.lineTo(p3[0], p3[1]);
      ctx.lineTo(p4[0], p4[1]);
    },
  });

  const topShape = graphic.extendShape({
    buildPath(ctx, shape) {
      const { topBasicsYAxis, basicsXAxis } = shape;
      // 侧面宽度
      const WIDTH = 10;
      // 斜角高度
      const OBLIQUE_ANGLE_HEIGHT = 6;

      // + 向上，- 向下
      const p1 = [basicsXAxis, topBasicsYAxis - OBLIQUE_ANGLE_HEIGHT]; // 上
      const p2 = [basicsXAxis + WIDTH, topBasicsYAxis]; //  左
      const p3 = [basicsXAxis, topBasicsYAxis + OBLIQUE_ANGLE_HEIGHT]; // 下
      const p4 = [basicsXAxis - WIDTH, topBasicsYAxis]; // 右

      ctx.moveTo(p1[0], p1[1]);
      ctx.lineTo(p2[0], p2[1]);
      ctx.lineTo(p3[0], p3[1]);
      ctx.lineTo(p4[0], p4[1]);
    },
  });

  const lineShape = graphic.extendShape({
    buildPath(ctx, shape) {
      const { topBasicsYAxis, basicsXAxis } = shape;
      // 侧面宽度
      // const WIDTH = 10
      // 斜角高度
      const OBLIQUE_ANGLE_HEIGHT = 6;

      // + 向上，- 向下
      const p1 = [basicsXAxis, topBasicsYAxis - OBLIQUE_ANGLE_HEIGHT];

      // ctx.moveTo(p1[0], p1[1])
      ctx.lineTo(p1[0], p1[1]);
    },
  });

  graphic.registerShape('leftShape', leftShape);
  graphic.registerShape('rightShape', rightShape);
  graphic.registerShape('topShape', topShape);
  graphic.registerShape('lineShape', lineShape);

  // 水平菱柱
  const hTopShape = graphic.extendShape({
    buildPath(ctx, shape) {
      const { topBasicsYAxis, bottomYAxis, basicsXAxis } = shape;
      // 菱柱高度
      const HEIGHT = 10;
      // 菱柱宽度
      const OBLIQUE_ANGLE_WIDTH = 9;

      const p1 = [topBasicsYAxis + OBLIQUE_ANGLE_WIDTH, basicsXAxis - HEIGHT]; // 右上
      const p2 = [bottomYAxis, basicsXAxis - HEIGHT]; // 左上
      const p3 = [bottomYAxis, basicsXAxis]; // 左下
      const p4 = [topBasicsYAxis, basicsXAxis]; // 右下

      ctx.moveTo(p1[0], p1[1]);
      ctx.lineTo(p2[0], p2[1]);
      ctx.lineTo(p3[0], p3[1]);
      ctx.lineTo(p4[0], p4[1]);
      ctx.closePath();
    },
  });
  const hBottomShape = graphic.extendShape({
    buildPath(ctx, shape) {
      const { topBasicsYAxis, bottomYAxis, basicsXAxis } = shape;
      // 菱柱高度
      const HEIGHT = 10;
      // 菱柱宽度
      const OBLIQUE_ANGLE_WIDTH = 9;

      // + 向下，- 向上
      const p1 = [topBasicsYAxis + OBLIQUE_ANGLE_WIDTH, basicsXAxis + HEIGHT];
      const p2 = [bottomYAxis, basicsXAxis + HEIGHT];
      const p3 = [bottomYAxis, basicsXAxis];
      const p4 = [topBasicsYAxis, basicsXAxis];

      ctx.moveTo(p1[0], p1[1]);
      ctx.lineTo(p2[0], p2[1]);
      ctx.lineTo(p3[0], p3[1]);
      ctx.lineTo(p4[0], p4[1]);
      ctx.closePath();
    },
  });

  const hRightShape = graphic.extendShape({
    buildPath(ctx, shape) {
      const { topBasicsYAxis, basicsXAxis } = shape;
      // 菱形高度
      const HEIGHT = 10;
      // 菱形宽度
      const OBLIQUE_ANGLE_WIDTH = 9;

      // + 向下，- 向上
      const p1 = [topBasicsYAxis + OBLIQUE_ANGLE_WIDTH, basicsXAxis - HEIGHT]; // 上
      const p2 = [topBasicsYAxis, basicsXAxis]; //  左
      const p3 = [topBasicsYAxis + OBLIQUE_ANGLE_WIDTH, basicsXAxis + HEIGHT]; // 下
      const p4 = [topBasicsYAxis + OBLIQUE_ANGLE_WIDTH * 2, basicsXAxis]; // 右

      ctx.moveTo(p1[0], p1[1]);
      ctx.lineTo(p2[0], p2[1]);
      ctx.lineTo(p3[0], p3[1]);
      ctx.lineTo(p4[0], p4[1]);
      ctx.closePath();
    },
  });

  // 水平菱柱
  graphic.registerShape('hTopShape', hTopShape);
  graphic.registerShape('hBottomShape', hBottomShape);
  graphic.registerShape('hRightShape', hRightShape);

  console.log('registerShape');
}

const canvas = document.createElement('canvas');
const font = '10px "STHeiti", sans-serif';

const context = canvas.getContext('2d');
context!.font = font;

interface RenderDimensionItemOptions {
  params: CustomSeriesRenderItemParams;
  api: CustomSeriesRenderItemAPI;
  /**
   * 垂直菱柱： [左侧部分颜色，右侧部分颜色，顶部菱形颜色，标签颜色]
   * 水平菱柱： [上面部分颜色，下面部分颜色，右侧菱形颜色，标签颜色]
   */
  colors: [
    string | graphic.LinearGradient,
    string | graphic.LinearGradient,
    string | graphic.LinearGradient,
    string | graphic.LinearGradient,
  ];
  offset: number;
  max?: number;
}

export function renderDimensionItem({
  api,
  colors,
  offset,
  max,
}: RenderDimensionItemOptions): CustomSeriesRenderItemReturn {
  // 基础坐标
  const basicsCoord = api.coord([api.value(0), api.value(1)]);
  // 顶部基础 y 轴
  const topBasicsYAxis = basicsCoord[1];
  // 基础 x 轴
  const basicsXAxis = basicsCoord[0] + offset;
  // 底部 y 轴
  const bottomYAxis = api.coord([api.value(0), 0])[1];

  const text = !!max ? `${parseInt((Number(api.value(1)) / max) * 100)}%` : api.value(1);
  const metrics = context?.measureText(toString(text));
  const textWidth = metrics?.width || 20;

  const rectWidth = textWidth + 10;

  return {
    type: 'group',
    children: [
      {
        type: 'rect',
        shape: {
          x: basicsXAxis - rectWidth / 2, // 减去一些值以使得背景比文字稍大
          y: topBasicsYAxis - 30, // 减去一些值以使得背景比文字稍大
          width: rectWidth, // 根据你的需求设置宽度
          height: 20, // 根据你的需求设置高度
        },
        style: {
          fill: new graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(19, 83, 155, 0.5)' }, // 背景顶部颜色
            { offset: 1, color: 'rgba(2, 22, 58, 0.5)' }, // 背景底部颜色
          ]),
        },
        z2: 99,
      },
      {
        type: 'text',
        style: {
          font,
          text,
          x: basicsXAxis,
          y: topBasicsYAxis - 25,
          fill: 'white',
          fontSize: 12,
          textAlign: 'center',
        },
        z2: 100,
      },
      {
        type: 'leftShape',
        shape: {
          topBasicsYAxis,
          basicsXAxis,
          bottomYAxis,
        },
        style: { ...api.style(), fill: colors[0] },
      },
      {
        type: 'rightShape',
        shape: {
          topBasicsYAxis,
          basicsXAxis,
          bottomYAxis,
        },
        style: { ...api.style(), fill: colors[1] },
      },
      {
        type: 'topShape',
        shape: {
          topBasicsYAxis,
          basicsXAxis,
          bottomYAxis,
        },
        style: { ...api.style(), fill: colors[2] },
      },
    ],
  } as unknown as CustomSeriesRenderItemReturn;
}

export function renderHorizontalDimensionItem({
  api,
  colors,
  offset,
  max,
}: RenderDimensionItemOptions): any {
  // 基础坐标
  const basicsCoord = api.coord([api.value(0), api.value(1)]);
  // 顶部基础 y 轴
  const topBasicsYAxis = basicsCoord[0];
  // 基础 x 轴
  const basicsXAxis = basicsCoord[1] + offset;
  // x min
  const bottomYAxis = api.coord([0, api.value(1)])[0];

  const text = !!max ? `${parseInt((Number(api.value(0)) / max) * 100)}%` : api.value(0);
  const metrics = context?.measureText(toString(text));
  const textWidth = metrics?.width || 20;

  const rectWidth = textWidth + 10;
  const labelX = topBasicsYAxis + 20;
  const labelTextX = labelX + rectWidth / 2;
  return {
    type: 'group',
    children: [
      {
        type: 'rect',
        shape: {
          x: labelX, // 减去一些值以使得背景比文字稍大
          y: basicsXAxis - 10, // 减去一些值以使得背景比文字稍大
          width: rectWidth, // 根据你的需求设置宽度
          height: 18, // 根据你的需求设置高度
        },
        style: {
          fill: new graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(19, 83, 155, 0.5)' }, // 背景顶部颜色
            { offset: 1, color: 'rgba(2, 22, 58, 0.5)' }, // 背景底部颜色
          ]),
        },
        z2: 99,
      },
      {
        type: 'text',
        style: {
          font,
          text,
          x: labelTextX,
          y: basicsXAxis - 5,
          fill: 'white',
          fontSize: 12,
          textAlign: 'center',
        },
        z2: 100,
      },
      {
        type: 'hTopShape',
        shape: {
          topBasicsYAxis,
          basicsXAxis,
          bottomYAxis,
        },
        style: { ...api.style(), fill: colors[0] },
      },
      {
        type: 'hBottomShape',
        shape: {
          topBasicsYAxis,
          basicsXAxis,
          bottomYAxis,
        },
        style: { ...api.style(), fill: colors[1] },
      },
      {
        type: 'hRightShape',
        shape: {
          topBasicsYAxis,
          basicsXAxis,
          bottomYAxis,
        },
        style: { ...api.style(), fill: colors[2] },
      },
    ],
  };
}

export { genGrid, genSeries, genXAxis, genYAxis, genLegend, genTooltip, genYzTooltip };

export interface SeriesOption {
  name: string;
  color?: any;
}

interface Options {
  y1Max?: number;
  y0Max?: number;
  legendConfig?: any;
  gap?: boolean;
  gridConfig?: any;
  thousandth?: boolean;
  valueSuffix?: string[];
  labelNames?: string[];
}

export function genLineBarChart(series: SeriesOption[], options: Options) {
  const { y0Max, y1Max, legendConfig, gap, gridConfig, thousandth, labelNames, valueSuffix } =
    options;
  return {
    tooltip: genTooltip({
      labelNames: labelNames || [],
      valueSuffix: valueSuffix || [],
    }),
    grid: genGrid({ top: gap ? 75 : 50, ...gridConfig }),
    legend: genLegend({ preset: 'show', top: gap ? 40 : 12, ...legendConfig }),
    xAxis: [
      genXAxis({
        features: ['italicLabel'],
      }),
    ],
    yAxis: [
      genYAxis({
        preset: 'vertical-percent',
        ...(y0Max && { max: y0Max }),
        ...(thousandth && { axisLabel: { formatter: '{value}' } }),
        splitNumber: gap ? 2 : 5,
      }),
      genYAxis({ preset: 'blank', max: y1Max, splitNumber: gap ? 2 : 5 }),
    ],
    series: series
      .map((item, idx) => [
        genSeries({
          preset: '柱状图',
          stack: 'total',
          customColor: barColorSet[idx],
          yAxisIndex: 1,
        }),
        genSeries({
          preset: '折线图',
          name: item.name,
          customColor: item.color,
          yAxisIndex: 0,
        }),
      ])
      .flat(),
  };
}

export const legendIcon1 = `image://${icon}`;
export const legendIcon2 = `image://${icon2}`;
export const legendIcon3 = `image://${icon3}`;
export const legendIcon4 = `image://${icon4}`;
export const legendIcon5 = `image://${icon5}`;
export const legendIcon6 = `image://${icon6}`;
export const legendIcon7 = `image://${icon7}`;
export const legendIcon8 = `image://${icon8}`;

export const dimensionalCustomColor: CustomColorType = [
  new graphic.LinearGradient(0, 0, 1, 1, [
    { offset: 0, color: '#17B86A' },
    { offset: 1, color: 'rgba(23, 184, 106, 0.12)' },
  ]),
  new graphic.LinearGradient(0, 0, 1, 1, [
    { offset: 0, color: '#4CD995' },
    { offset: 1, color: 'rgba(76, 217, 149, 0.12)' },
  ]),
  '#4FFFAA',
  '#3CE292',
];
export const dimensionalCustomColor2: CustomColorType = [
  new graphic.LinearGradient(0, 0, 1, 1, [
    { offset: 0, color: '#AD7607' },
    { offset: 1, color: 'rgba(173, 118, 7, 0.12)' },
  ]),
  new graphic.LinearGradient(0, 0, 1, 1, [
    { offset: 0, color: '#F4A403' },
    { offset: 1, color: 'rgba(244, 164, 3, 0.12)' },
  ]),
  '#FFCD6B',
  '#FF9E37',
];

interface LzSeriesOption {
  name: string;
  iconType?: number;
  colorType?: number;
  isLine?: boolean;
  wrap?: boolean;
  yMax?: number;
  yIndex?: 0 | 1;
  markLine?: any;
}

interface LzOptions {
  y0?: {
    percent?: boolean;
    max?: number;
    number?: boolean;
  };
  y1?: {
    show?: boolean;
    percent?: boolean;
    max?: number;
  };
  seriesMax?: number;
  valueSuffix?: string[];
  labelNames?: string[];
  gridConfig?: any;
}

const lzSeriesLegendMap = {
  1: legendIcon1,
  2: legendIcon2,
  3: legendIcon3,
  4: legendIcon4,
  5: legendIcon5,
  6: legendIcon6,
  7: legendIcon7,
  8: legendIcon8,
};

const lzColorMap = {
  1: undefined,
  2: dimensionalCustomColor,
  3: dimensionalCustomColor2,
  4: '#F1E723',
  5: '#3CE292',
  6: '#23F1F1',
  7: '#9C6718',
};

const getCustomOffset = (idx: number, len: number) => {
  if (len === 1) return 0;
  if (len === 2) {
    if (idx === 0) return -18;
    if (idx === 1) return 18;
  }
  if (len === 3) {
    if (idx === 0) return -30;
    if (idx === 1) return 4;
    if (idx === 2) return 38;
  }
  return 0;
};

export function genLzChart(series: LzSeriesOption[], options: LzOptions = {}) {
  const { y0, y1, seriesMax, valueSuffix, labelNames, gridConfig } = options;
  const noLineLen = series.filter((item) => !item.isLine).length;
  const legendList: any[] = [];
  let currentLegend = {
    preset: 'show',
    data: [] as any[],
    top: 10,
    right: 10,
  };
  legendList.push(currentLegend);
  for (const item of series) {
    if (item.wrap) {
      currentLegend = {
        preset: 'show',
        data: [
          {
            name: item.name,
            icon: lzSeriesLegendMap[item.iconType || 1],
          },
        ] as any[],
        top: 30,
        right: 10, // TODO
      };
      legendList.push(currentLegend);
    } else {
      currentLegend.data.push({
        name: item.name,
        icon: lzSeriesLegendMap[item.iconType || 1],
      });
    }
  }
  return {
    grid: genGrid(gridConfig || {}),
    tooltip: genTooltip({
      labelNames: labelNames || [],
      valueSuffix: valueSuffix || [],
    }),
    legend: legendList.map((item) => genLegend(item)),
    xAxis: [
      genXAxis({
        features: ['breakLabel'],
      }),
    ],
    yAxis: [
      genYAxis({
        features: y0?.percent ? ['percent'] : y0?.number ? ['number'] : [],
        max: y0?.max,
      }),
    ].concat(
      y1?.show
        ? [
            genYAxis({
              features: y1?.percent ? ['percent'] : [],
              max: y1?.max,
            }),
          ]
        : [],
    ),
    series: series.map((item, idx) =>
      genSeries({
        preset: item.isLine ? '折线图' : '立体柱形',
        name: item.name,
        customColor: lzColorMap[item.colorType || 1],
        max: item.yMax || seriesMax,
        customOffset: getCustomOffset(idx, noLineLen),
        yAxisIndex: item.yIndex,
        markLine: item.markLine,
        labelLayout: {
          moveOverlap: 'shiftX',
        },
      }),
    ),
  };
}

export function genLzYChart({ name }) {
  return {
    grid: genGrid({ right: 20, left: 75, bottom: 40 }),
    legend: genLegend({
      preset: 'show',
      data: [{ name, icon: legendIcon5 }],
      itemWidth: 14,
      itemHeight: 20,
    }),
    xAxis: [
      genXAxis({
        preset: 'vertical',
        max(extent) {
          return extent.max + extent.min;
        },
      }),
    ],
    yAxis: [
      genYAxis({
        preset: 'vertical',
        features: ['breakLabel'],
      }),
    ],
    series: [
      genSeries({
        preset: '水平立体柱形',
        name,
      }),
    ],
  };
}

export const chartWidth = '400px';
export const chartHeight = '254px';
export const gridTop = 70;

export type CustomColorType = [graphic.LinearGradient, graphic.LinearGradient, string, string];
