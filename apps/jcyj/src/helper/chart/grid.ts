import type { GridOption } from 'echarts/types/dist/shared.js';
import { merge } from 'lodash-es';

type Option = GridOption & {
  preset?: 'vertical';
};

export function genGrid(option?: Option): GridOption {
  const { preset, ...rest } = option || {};

  if (preset === 'vertical') {
    return merge(
      {
        right: 20,
        left: 60,
        top: 40,
        bottom: 40,
      },
      rest,
    );
  }
  return merge(
    {
      top: 50,
      right: 40,
      left: 40,
      bottom: 55,
    },
    rest,
  );
}
