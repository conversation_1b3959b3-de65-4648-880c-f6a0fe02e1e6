import type { XAXisOption } from 'echarts/types/dist/shared.js';
import { merge } from 'lodash-es';

type Option = XAXisOption & {
  preset?: 'vertical';
  features?: ('italicLabel' | 'breakLabel')[];
};

export function genXAxis(option: Option): XAXisOption {
  const { preset, features, ...rest } = option;
  const featureOption: XAXisOption = {};
  if (features && features.includes('italicLabel')) {
    featureOption.axisLabel = {
      rotate: 30,
    };
  }
  if (features && features.includes('breakLabel')) {
    featureOption.axisLabel = {
      interval: 0,
      overflow: 'break',
      width: 60,
    };
  }
  if (preset === 'vertical') {
    return merge(
      {
        type: 'value',
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgb(176,212,255,0.25)',
          },
        },
        axisLabel: {
          color: 'white',
          margin: 10,
          fontSize: 16,
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          interval: 0,
          lineStyle: {
            type: 'dashed',
            color: 'rgba(33,126,227,0.25)',
          },
        },
      },
      featureOption,
      rest,
    );
  }
  return merge(
    {
      type: 'category',
      boundaryGap: true,
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgba(176, 212, 255, 0.25)',
          width: 1,
        },
      },
      axisLabel: {
        color: 'white',
        fontSize: 16,
        // formatter: (value) => {
        //   if (value.length > 3) {
        //     return value.slice(0, 3) + '...';
        //   }
        //   return value;
        // },
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: false,
        interval: 0,
        lineStyle: {
          type: 'dashed',
        },
      },
    },
    featureOption,
    rest,
  );
}
