import type { YAXisOption } from 'echarts/types/dist/shared.js';
import { isInteger, merge } from 'lodash-es';

type Option = YAXisOption & {
  preset?: 'blank' | 'vertical' | 'vertical-percent';
  features?: ('percent' | 'verticalLabel' | 'breakLabel' | 'number')[];
};

export function genYAxis(option: Option): YAXisOption {
  const { preset, features, ...rest } = option;
  const featureOption: YAXisOption = {};
  if (features && features.includes('percent')) {
    featureOption.axisLabel = {
      formatter: (value: number) =>
        option.max ? `${(value / Number(option.max)) * 100}%` : `${value}%`,
    };
  }
  if (features && features.includes('number')) {
    featureOption.axisLabel = {
      formatter: `{value}人`,
    };
  }
  if (features && features.includes('verticalLabel')) {
    featureOption.axisLabel = {
      formatter: (value) => value.split('').join('\n'),
    };
  }
  if (features && features.includes('breakLabel')) {
    featureOption.axisLabel = {
      interval: 0,
      overflow: 'break',
      width: 65,
    };
  }
  if (preset === 'blank') {
    return merge(
      {
        type: 'value',
        min: 0,
        splitLine: {
          show: false,
        },
        axisLabel: {
          color: 'white',
          fontSize: 13,
        },
        boundaryGap: [0.2, 0.2],
      },
      featureOption,
      rest,
    );
  }
  if (preset === 'vertical') {
    return merge(
      {
        type: 'category',
        splitLine: {
          show: false,
        },
        axisLabel: {
          color: 'white',
          fontSize: 14,
          formatter: (value) => (value.length > 9 ? `${value.slice(0, 9)}...` : value),
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgb(176,212,255,0.25)',
          },
        },
        boundaryGap: true,
        axisTick: {
          show: false,
        },
      },
      featureOption,
      rest,
    );
  }
  if (preset === 'vertical-percent') {
    return merge(
      {
        type: 'value',
        min: 0,
        // max: 100,
        splitLine: {
          show: false,
        },
        axisLabel: {
          color: 'white',
          fontSize: 13,
          formatter: '{value}%',
        },
        boundaryGap: false,
        axisTick: {
          show: false,
        },
      },
      featureOption,
      rest,
    );
  }
  return merge(
    {
      type: 'value',
      min: 0,
      axisLine: {
        show: true,
        lineStyle: {
          color: 'rgb(176,212,255,0.25)',
        },
      },
      splitLine: {
        lineStyle: {
          type: 'dashed',
          color: 'rgba(33,126,227,0.25)',
        },
        interval: (index, value) => {
          console.log(99, index, value);
          return false;
        },
      },
      axisLabel: {
        color: 'white',
        fontSize: 13,
        formatter: (value) => {
          if (value > 10000) {
            return value / 10000 + 'w';
          }
          return isInteger(value) ? value : '';
        },
      },
      boundaryGap: [0.2, 0.2],
    },
    featureOption,
    rest,
  );
}
