import { graphic } from 'echarts';

export const pieColorSet = [
  '#0783FA',
  '#07D1FA',
  '#FFD15C',
  '#F68E38',
  '#20E6A4',
  '#7C8EFF',
  '#88BCED',
  '#44A43F',
];
// 绿黄橙红紫
export const riskLevelColorSet = ['#52B889', '#FBD147', '#F78C3E', '#FF6B68', '#9646FF'];
export const barColorSet = [
  new graphic.LinearGradient(0, 0, 0, 1, [
    {
      offset: 1,
      color: '#007BFF',
    },
    {
      offset: 0,
      color: 'rgba(11,80,242,0.28)',
    },
  ]),
  new graphic.LinearGradient(0, 0, 0, 1, [
    {
      offset: 1,
      color: '#00E5FF',
    },
    {
      offset: 0,
      color: 'rgba(0,229,255,0.28)',
    },
  ]),
  new graphic.LinearGradient(0, 0, 0, 1, [
    {
      offset: 1,
      color: '#0EF083',
    },
    {
      offset: 0,
      color: 'rgba(14,240,131,0.28)',
    },
  ]),
  new graphic.LinearGradient(0, 0, 0, 1, [
    {
      offset: 1,
      color: '#F1AB1D',
    },
    {
      offset: 0,
      color: 'rgba(241,171,29,0.28)',
    },
  ]),
  new graphic.LinearGradient(0, 0, 0, 1, [
    {
      offset: 1,
      color: '#E2DF19',
    },
    {
      offset: 0,
      color: 'rgba(226,223,25,0.28)',
    },
  ]),
  new graphic.LinearGradient(0, 0, 0, 1, [
    {
      offset: 1,
      color: '#B04EB5',
    },
    {
      offset: 0,
      color: 'rgba(248,121,255,0.28)',
    },
  ]),
  new graphic.LinearGradient(0, 0, 0, 1, [
    {
      offset: 1,
      color: '#9558FF',
    },
    {
      offset: 0,
      color: 'rgba(161,0,255,0.28)',
    },
  ]),
];
export const barColorSet2 = [
  new graphic.LinearGradient(0, 0, 1, 0, [
    {
      offset: 1,
      color: '#007BFF',
    },
    {
      offset: 0,
      color: 'rgba(11,80,242,0.28)',
    },
  ]),
  new graphic.LinearGradient(0, 0, 1, 0, [
    {
      offset: 1,
      color: '#00E5FF',
    },
    {
      offset: 0,
      color: 'rgba(0,229,255,0.28)',
    },
  ]),
  new graphic.LinearGradient(0, 0, 1, 0, [
    {
      offset: 1,
      color: '#0EF083',
    },
    {
      offset: 0,
      color: 'rgba(14,240,131,0.28)',
    },
  ]),
  new graphic.LinearGradient(0, 0, 1, 0, [
    {
      offset: 1,
      color: '#F1AB1D',
    },
    {
      offset: 0,
      color: 'rgba(241,171,29,0.28)',
    },
  ]),
  new graphic.LinearGradient(0, 0, 1, 0, [
    {
      offset: 1,
      color: '#E2DF19',
    },
    {
      offset: 0,
      color: 'rgba(226,223,25,0.28)',
    },
  ]),
  new graphic.LinearGradient(0, 0, 1, 0, [
    {
      offset: 1,
      color: '#B04EB5',
    },
    {
      offset: 0,
      color: 'rgba(248,121,255,0.28)',
    },
  ]),
  new graphic.LinearGradient(0, 0, 1, 0, [
    {
      offset: 1,
      color: '#9558FF',
    },
    {
      offset: 0,
      color: 'rgba(161,0,255,0.28)',
    },
  ]),
];
export const dimensionalCustomColor = [
  new graphic.LinearGradient(0, 0, 1, 1, [
    { offset: 0, color: '#16AABB' },
    { offset: 1, color: 'rgba(53,115,121,0.23)' },
  ]),
  new graphic.LinearGradient(0, 0, 1, 1, [
    { offset: 0, color: '#1BDDF2' },
    { offset: 1, color: 'rgba(27,221,242,0.12)' },
  ]),
  '#52DDED',
];

export const dimensionalCustomColor2 = [
  new graphic.LinearGradient(0, 0, 1, 1, [
    { offset: 0, color: '#A2B017' },
    { offset: 1, color: '#243126' },
  ]),
  new graphic.LinearGradient(0, 0, 1, 1, [
    { offset: 0, color: '#D6E71D' },
    { offset: 1, color: '#2C3D31' },
  ]),
  '#B0BE1E',
];

export const dimensionalCustomColor3 = [
  new graphic.LinearGradient(0, 0, 1, 1, [
    { offset: 0, color: '#393BBC' },
    { offset: 1, color: 'rgba(57, 53, 121, 0.23)' },
  ]),
  new graphic.LinearGradient(0, 0, 1, 1, [
    { offset: 0, color: '#4F37FF' },
    { offset: 1, color: 'rgba(88, 74, 240, 0.12)' },
  ]),
  '#5F52ED',
];
