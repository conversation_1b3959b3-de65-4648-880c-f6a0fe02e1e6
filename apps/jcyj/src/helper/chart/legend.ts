import type { LegendComponentOption } from 'echarts/types/dist/shared.js';
import { merge } from 'lodash-es';

type Option = LegendComponentOption & {
  preset?: 'show';
};

export function genLegend(option?: Option): any {
  const { preset, ...rest } = option || {};
  if (preset === 'show') {
    return merge(
      {
        itemWidth: 18,
        itemHeight: 12,
        icon: 'roundRect',
        top: 10,
        right: 10,
        fontSize: 10,
        selectedMode: false,
        formatter: (name: string) => {
          // if (name.length > 12) return `${name.slice(0, 12)}...`;

          return name;
        },
        textStyle: {
          rich: {
            a: {
              verticalAlign: 'bottom',
            },
          },
          // padding: [1, 0, 0, 0],
          color: 'white',
          fontSize: 14,
        },
      },
      rest,
    );
  }
  return {
    show: false,
    ...(rest as any),
  };
}
