import type { SeriesOption } from 'echarts';
import { graphic } from 'echarts';
import { merge } from 'lodash-es';
import { renderDimensionItem, renderHorizontalDimensionItem } from './index';
import lineLabelBg from '/@/assets/images/data-view/decorate/line-label-bg.png';

type Option = SeriesOption & {
  preset?: '柱状图' | '立体柱形' | '水平立体柱形' | '折线图' | '带填充折线图' | '饼图';
  customColor?: any;
  customOffset?: any;
  max?: number;
};

export function genSeries(option: Option) {
  const { preset, customColor, customOffset, ...rest } = option;
  if (preset === '柱状图') {
    return merge<SeriesOption, SeriesOption>(
      {
        name: '',
        type: 'bar',
        barWidth: 12,
        itemStyle: {
          color:
            customColor ||
            new graphic.LinearGradient(0, 0, 1, 1, [
              {
                offset: 1,
                color: '#007BFF',
              },
              {
                offset: 0,
                color: 'rgba(11,80,242,0.28)',
              },
            ]),
        },
        animation: false,
        markLine: {
          symbol: 'none',
          animation: false,
          silent: true,
          label: { show: false },
          lineStyle: {
            width: 1,
            color: 'rgba(255,255,255,0.2)',
          },
        },
      },
      rest,
    );
  }
  if (preset === '立体柱形') {
    return merge(
      {
        name: '',
        type: 'custom',
        renderItem: (params, api) =>
          renderDimensionItem({
            params,
            api,
            colors: customColor || [
              new graphic.LinearGradient(0, 0, 1, 1, [
                { offset: 0, color: '#0693D9' },
                { offset: 1, color: 'rgba(3, 165, 245, 0.12)' },
              ]),
              new graphic.LinearGradient(0, 0, 1, 1, [
                { offset: 0, color: '#57CAFF' },
                { offset: 1, color: 'rgba(87, 202, 255, 0.12)' },
              ]),
              '#B6EDFF',
              '#00A6FF',
            ],
            offset: customOffset || 0,
            max: option.max,
          }),
        markLine: {
          symbol: 'none',
          animation: false,
          silent: true,
          label: { show: true, color: '#fff', formatter: '{c}%' },
          lineStyle: {
            width: 1,
            color: '#fff',
          },
        },
      },
      rest,
    );
  }

  if (preset === '水平立体柱形') {
    return merge(
      {
        name: '',
        type: 'custom',
        renderItem: (params, api) =>
          renderHorizontalDimensionItem({
            params,
            api,
            colors: customColor || [
              new graphic.LinearGradient(0, 0, 1, 1, [
                { offset: 1, color: '#0693D9' },
                { offset: 0, color: 'rgba(3, 165, 245, 0.12)' },
              ]),
              new graphic.LinearGradient(0, 0, 1, 1, [
                { offset: 1, color: '#57CAFF' },
                { offset: 0, color: 'rgba(87, 202, 255, 0.12)' },
              ]),
              '#B6EDFF',
              '#00A6FF',
            ],
            offset: customOffset || 0,
            max: option.max,
          }),
      },
      rest,
    );
  }

  if (preset === '折线图') {
    return merge(
      {
        animation: false,
        type: 'line',
        smooth: false,
        symbol: 'circle',
        symbolSize: 4,
        emphasis: {
          scale: false,
        },
        label: {
          show: true,
          position: 'right',
          color: customColor || '#23F1F1',
          backgroundColor: {
            image: lineLabelBg,
          },
          padding: [3, 4],
        },
        itemStyle: {
          color: customColor || '#23F1F1',
          shadowOffsetX: 0, // 折线的X偏移
          shadowOffsetY: 0, // 折线的Y偏移
          shadowBlur: 4, // 折线模糊
          shadowColor: '#189C9C', //折线颜色
        },
      },
      rest,
    );
  }
  if (preset === '带填充折线图') {
    return merge(
      {
        animation: false,
        type: 'line',
        smooth: false,
        itemStyle: {
          color: customColor || '#00E5FF',
        },
        areaStyle: {
          color: new graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(50,118,255,0.40)' },
            { offset: 1, color: 'rgba(216,216,216,0.00)' },
          ]),
        },
      },
      rest,
    );
  }

  if (preset === '饼图') {
    return merge(
      {
        type: 'pie',
        animation: false,
        smooth: false,
        hoverAnimation: false,
        label: { show: false },
      },
      rest,
    );
  }

  return {
    ...(rest as any),
  };
}
