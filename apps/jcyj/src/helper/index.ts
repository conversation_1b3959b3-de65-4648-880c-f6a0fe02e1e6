import { onMounted, ref } from 'vue';

export const allAreaName = [
  '西陵区',
  '伍家岗区',
  '点军区',
  '猇亭区',
  '夷陵区',
  '远安县',
  '兴山县',
  '秭归县',
  '长阳土家族自治县',
  '五峰土家族自治县',
  '宜都市',
  '当阳市',
  '枝江市',
];
// export const allAreaName = ['长阳土家族自治县', '五峰土家族自治县', '西陵区', '伍家岗区', '点军区', '猇亭区', '夷陵区', '宜都市', '当阳市', '枝江市', '远安县', '兴山县', '秭归县']

export const allArea = [
  { label: '西陵区', value: 'a' },
  { label: '伍家岗区', value: 'b' },
  { label: '点军区', value: 'c' },
  { label: '猇亭区', value: 'd' },
  { label: '夷陵区', value: 'e' },
  { label: '宜都市', value: 'f' },
  { label: '当阳市', value: 'g' },
  { label: '枝江市', value: 'h' },
  { label: '远安县', value: 'i' },
  { label: '兴山县', value: 'j' },
  { label: '秭归县', value: 'k' },
  { label: '长阳土家族自治县', value: 'l' },
  { label: '五峰土家族自治县', value: 'm' },
];

export const allAreaWithAll = [
  { label: '全部', value: 'all' },
  { label: '西陵区', value: 'a' },
  { label: '伍家岗区', value: 'b' },
  { label: '点军区', value: 'c' },
  { label: '猇亭区', value: 'd' },
  { label: '夷陵区', value: 'e' },
  { label: '宜都市', value: 'f' },
  { label: '当阳市', value: 'g' },
  { label: '枝江市', value: 'h' },
  { label: '远安县', value: 'i' },
  { label: '兴山县', value: 'j' },
  { label: '秭归县', value: 'k' },
  { label: '长阳土家族自治县', value: 'l' },
  { label: '五峰土家族自治县', value: 'm' },
];

export const allYearName = ['2017', '2018', '2019', '2020', '2021'];

export const allYear = [
  { label: 'a', value: '2017' },
  { label: 'b', value: '2018' },
  { label: 'c', value: '2019' },
  { label: 'd', value: '2020' },
  { label: 'e', value: '2021' },
];

export function useDataViewEle() {
  const viewEleRef = ref();

  onMounted(() => {
    viewEleRef.value = document.getElementById('data-view');
  });

  const getDataViewEle = () => viewEleRef.value;
  return { getDataViewEle };
}

export const parseFloatFn = (val: string, positive = true): any => {
  const temp = Number.parseFloat(val);
  return Number.isNaN(temp) ? '' : positive ? Math.abs(temp) : temp;
};

export const getFlagValue = (val: string) => {
  if (!val) {
    return undefined;
  }
  const temp = parseFloatFn(val, false);
  return temp === 0 ? undefined : temp > 0;
};

export const getCardTitle = (val: string) => {
  return val ? (val.includes('%') ? val : `${val}%`) : '';
};

const center = [30.7547, 111.1663];

export const getRandomCenter = () => [
  center[0] + Math.random() * 1 - 0.5,
  center[1] + Math.random() * 1 - 0.5,
];

const getHashCode = (str: string) => {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash = hash & hash;
  }
  return hash;
};

const centerMap = [
  {
    name: '西陵区',
    center: [31.019864, 111.518718],
  },
  {
    name: '伍家岗区',
    center: [30.75149, 111.580938],
  },
  {
    name: '点军区',
    center: [30.870796, 111.077318],
  },
  {
    name: '猇亭区',
    center: [30.550879, 111.448198],
  },
  {
    name: '夷陵区',
    center: [31.25, 111.249522],
  },
  {
    name: '远安县',
    center: [31.370982, 111.778513],
  },
  {
    name: '兴山县',
    center: [31.505578, 110.7567],
  },
  {
    name: '秭归县',
    center: [31.06, 110.552553],
  },
  {
    name: '长阳土家族自治县',
    center: [30.648506, 110.587903],
  },
  {
    name: '五峰土家族自治县',
    center: [30.318626, 110.508007],
  },
  {
    name: '宜都市',
    center: [30.341557, 111.270069],
  },
  {
    name: '当阳市',
    center: [30.920229, 111.97563],
  },
  {
    name: '枝江市',
    center: [30.545182, 111.923108],
  },
];

export const getRealCenter = (address: string) => {
  const hashCode = getHashCode(address);
  const value = hashCode / 4000000000;
  const center = centerMap.find((item) => address.includes(item.name))?.center;
  if (center) {
    return [center[0] + value, center[1] + value];
  }
  return [30.7547 + value, 111.1663 + value];
};

export * from './chart/color';
