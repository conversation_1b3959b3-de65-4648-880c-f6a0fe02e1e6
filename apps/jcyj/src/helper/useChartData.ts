import type { Ref } from 'vue';
import { inject, shallowRef, watch } from 'vue';

export interface ChartData {
  chartConfig: any;
  chartOption: any;
  switchOptions?: any;
  switchValue?: any;
  updateSwitchValue?: (val: string) => void;
  title: any;
  dropdownOptions?: any;
  dropdownValue?: any;
  quotaOptions?: any;
  quotaValue?: any;
  switchFontSize?: any;
}

export default function useChartData(key: Ref<string>, configMap: any) {
  const year = inject<Ref<string>>('year')!;
  const chartData = shallowRef<ChartData>({
    chartConfig: [],
    chartOption: {},
    switchOptions: undefined,
    switchValue: undefined,
    updateSwitchValue: () => {},
    title: '',
    switchFontSize: '',
  });
  watch(
    key,
    () => {
      chartData.value = configMap[key.value]?.(year) || {};
    },
    { immediate: true },
  );
  return chartData;
}
