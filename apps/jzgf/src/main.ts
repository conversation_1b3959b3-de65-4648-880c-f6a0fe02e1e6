import '@ft/internal/bootstrap';
import './design/global.less';
window['DYNAMIC_VIEW_MODULES'] = Object.fromEntries(
  Object.entries(import.meta.glob('./views/**/*.{vue,tsx}')).map(([k, v]) => [
    k.replace('./views', '/jzgf'),
    v,
  ]),
);
Promise.resolve().then(() => {
  import('@ft/internal/bootstrap');
});

// window['getSubMenu'] = json.getSubMenu1;
Promise.resolve().then(() => import('@ft/internal/bootstrap'));
