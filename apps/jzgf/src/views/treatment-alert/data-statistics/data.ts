import type { FormSchema } from '@ft/internal/components/Table';
import dayjs from 'dayjs';

export const formSchema = (mode: string): FormSchema[] => {
  return [
    {
      field: 'year',
      label: '统计年度',
      component: 'DatePicker',
      componentProps: {
        picker: 'year',
        style: { width: '100%' },
        format: 'YYYY',
        disabled: mode === 'detail',
        valueFormat: 'YYYY',
      },
      isHandleDateDefaultValue: false,
      defaultValue: dayjs().format('YYYY'),
      colProps: { span: 6 },
    },
    {
      field: 'areaName',
      component: 'Input',
      label: '统计区域',
      componentProps: {
        disabled: mode === 'detail',
      },
      colProps: { span: 6 },
    },
    {
      field: 'statisticalTime',
      component: 'RangePicker',
      label: '统计时间',
      colProps: {
        span: 6,
      },
      componentProps: {
        style: {
          width: '100%',
        },
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        disabled: mode === 'detail',
      },
    },
  ];
};
