<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { useRequest } from '@ft/request';
  import { computed } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { formSchema } from './data';
  import { queryNStandardTreatMedicalStatistics } from '/@/api/data-statistics';

  const query = useRoute().query;
  const areaName = computed(() => (query.areaName === '合计' ? '' : query.areaName));
  const statisticalTime = computed(() =>
    query.startDate && query.endDate ? [query.startDate, query.endDate] : undefined,
  );

  function delNullChildren(obj: any) {
    for (const key in obj) {
      obj.dataIndex = obj.key;
      obj.width = 200;
      if (obj[key] === null) {
        delete obj[key];
      } else if (typeof obj[key] === 'object') {
        delNullChildren(obj[key]);
      }
    }
  }
  const {
    data: tableData,
    runAsync: runAsync,
    loading,
  } = useRequest(
    () =>
      queryNStandardTreatMedicalStatistics({
        ...ActionTable.getForm().getFieldsValue(),
        ...query,
        areaName: areaName.value,
      }),
    {
      onBefore: () => {
        ActionTable.getForm().setFieldsValue({
          ...query,
          statisticalTime: statisticalTime.value,
        });
      },
      onSuccess: (res) => {
        const _c = res?.columns?.map((item) => ({ ...item, dataIndex: item.key }));
        delNullChildren(_c);
        ActionTable.setColumns(_c);
      },
    },
  );
  const [registerTable, ActionTable] = useTable({
    formConfig: {
      colon: true,
      labelWidth: 120,
      showAdvancedButton: false,
      actionColOptions: {
        span: 6,
        style: {
          textAlign: 'right',
        },
      },
      schemas: formSchema('detail'),
      fieldMapToTime: [['statisticalTime', ['startDate', 'endDate'], 'YYYY-MM-DD']],
      submitFunc: async () => {
        await runAsync();
      },
    },
    loading: loading,
    dataSource: computed(() => tableData.value?.dataList || []),
    useSearchForm: true,
    showIndexColumn: false,
    bordered: false,
    size: 'small',
    resizeHeightOffset: 20,

    beforeFetch: (params) => {
      params.orgType = 1;
      return params;
    },
    afterFetch: (data) => {
      data.forEach((item) => {
        item.loading = false;
      });
      return data;
    },
  });
  const router = useRouter();
  function handlePatientDetail(record, column) {
    router.push({
      name: 'PatientDetail',
      query: {
        notStandardTreatRuleId: column.dataIndex,
        areaName: areaName.value,
        ruleTitle: column.customTitle,
        orgName: record.area,
        // diseaseCode: query.diseaseCode,
        // diseaseName: query.diseaseName,
        // startDate: query.startDate,
        // endDate: query.endDate,
        // year: query.year,
        ...query,
      },
    });
  }
</script>

<template>
  <div class="w-full h-full pr-4">
    <BasicTable class="data-statistics" @register="registerTable">
      <template #headerTop>
        <div class="flex items-center gap-2">
          <span class="inline-block bg-primary-color w-3px h-1em"></span>
          <span class="text-#333333 fw-bold">传染病不规范治疗数据患者列表</span>
        </div>
      </template>
      <template #tableTitle> </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex !== 'area'">
          <!-- <span> {{ column.dataIndex }}-{{ record }} </span> -->
          <span
            @click="handlePatientDetail(record, column)"
            v-if="record[column.dataIndex] !== null"
            class="text-primary-color cursor-pointer"
            >{{ record[column.dataIndex] }}</span
          >
          <span v-else>{{ record[column.dataIndex] }}</span>
        </template>
      </template>
    </BasicTable>
  </div>
</template>

<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }

    .vben-basic-table-form-container .ant-form {
      padding: 16px 16px 10px;
      margin-bottom: 10px;
    }
  }
</style>
