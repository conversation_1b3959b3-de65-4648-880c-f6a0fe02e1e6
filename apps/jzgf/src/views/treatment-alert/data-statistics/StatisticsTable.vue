<script setup lang="ts">
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';

  import { useRouter } from 'vue-router';
  import { useRequest } from '@ft/request';
  import { computed, h } from 'vue';
  import { Button, notification } from 'ant-design-vue';
  import { setActiveRoutePathCache } from '@ft/internal/utils/auth';
  import { formSchema } from './data';
  import { earlyWarningReminder, queryNStandardTreatAreaStatistics } from '/@/api/data-statistics';

  const props = defineProps({
    /**
     * 1	病毒性肝炎
     * 2	艾滋病
     * 3	结核病
     */
    diseaseCode: {
      type: Number,
      default: 1,
    },
    diseaseName: {
      type: String,
      default: '',
    },
  });

  const router = useRouter();
  function delNullChildren(obj: any) {
    if (obj.key === 'area') {
      obj.fixed = 'left';
    }
    for (const key in obj) {
      obj.dataIndex = obj.key;
      obj.width = 160;
      if (obj[key] === null) {
        delete obj[key];
      } else if (typeof obj[key] === 'object') {
        delNullChildren(obj[key]);
      }
    }
  }
  const {
    data: tableData,
    runAsync: runAsync,
    loading,
  } = useRequest(
    () =>
      queryNStandardTreatAreaStatistics({
        ...ActionTable.getForm().getFieldsValue(),
        diseaseCode: props.diseaseCode,
      }),
    {
      onSuccess: (res) => {
        const _c = res?.columns?.map((item) => ({ ...item, dataIndex: item.key }));
        delNullChildren(_c);
        ActionTable.setColumns(_c);
      },
    },
  );
  useRequest(earlyWarningReminder, {
    showSuccessMessage: false,
    onSuccess: (result) => {
      if (result.reminderStatus == 1) {
        const key = `open${Date.now()}`;
        notification.open({
          message: '不规范治疗提醒',
          description: `${result?.msgContent}`,
          placement: 'bottomRight',
          duration: null,
          btn: () =>
            h(
              Button,
              {
                type: 'primary',
                size: 'small',
                onClick: () => notification.close(key),
              },
              { default: () => '关闭' },
            ),
          key,
          onClose: close,
        });
      }
    },
  });

  const [registerTable, ActionTable] = useTable({
    formConfig: {
      colon: true,
      labelWidth: 90,
      showAdvancedButton: false,
      actionColOptions: {
        span: 6,
        style: {
          textAlign: 'right',
        },
      },
      fieldMapToTime: [['statisticalTime', ['startDate', 'endDate'], 'YYYY-MM-DD']],
      submitFunc: async () => {
        await runAsync();
      },
      schemas: formSchema('write'),
    },
    loading: loading,
    dataSource: computed(() => tableData.value?.dataList || []),
    useSearchForm: true,
    showIndexColumn: false,
    bordered: false,
    size: 'small',
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
    },
    // resizeHeightOffset: 20,
  });

  function handleDetail(record) {
    const { startDate, endDate, year } = ActionTable.getForm().getFieldsValue();
    router.push({
      name: 'DataStatisticsDetail',
      query: {
        areaName: record.area,
        year: year,
        startDate: startDate,
        endDate: endDate,
        diseaseCode: props.diseaseCode,
        diseaseName: props.diseaseName,
      },
    });
  }
  function handlePatientDetail(record) {
    setActiveRoutePathCache(router.currentRoute.value.path);
    const { startDate, endDate, year } = ActionTable.getForm().getFieldsValue();
    router.push({
      name: 'PatientDetail',
      query: {
        diseaseCode: props.diseaseCode,
        diseaseName: props.diseaseName,
        areaName: record.area,
        startDate: startDate,
        endDate: endDate,
        year: year,
      },
    });
  }

  function createActions(record): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '医疗机构',
        type: 'link',
        // ifShow: record.area !== '合计',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '查看患者',
        type: 'link',
        // ifShow: record.area !== '合计',
        onClick: handlePatientDetail.bind(null, record),
      },
    ];
    return actions;
  }
</script>

<template>
  <div class="w-full h-full pr-2">
    <BasicTable @register="registerTable">
      <template #headerTop>
        <div class="flex items-center gap-2">
          <span class="inline-block bg-primary-color w-3px h-1em"></span>
          <span class="text-#333333 fw-bold">{{ diseaseName }}不规范治疗数据统计</span>
        </div>
      </template>
      <template #tableTitle> </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>

<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }

    .vben-basic-table-form-container .ant-form {
      padding: 16px 16px 10px;
      margin-bottom: 10px;
    }
  }
</style>
