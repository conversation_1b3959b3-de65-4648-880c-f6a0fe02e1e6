<script setup lang="tsx">
  import { Dropdown, Empty } from 'ant-design-vue';
  import { Icon } from '@ft/internal/components/Icon';
  import { computed, nextTick, ref, unref, watch } from 'vue';
  import { BasicTree } from '@ft/internal/components/Tree';
  import { filterTree } from './helper';

  const props = defineProps({
    activeKey: { type: String, default: '' },
    width: { type: String, default: '210px' },
    treeData: { type: Array, default: () => [] },
    titleField: { type: String, default: 'title' },
    valueField: { type: String, default: 'key' },
    showAction: { type: Boolean, default: true },
    searchValue: { type: String, default: '' },
  });

  const emit = defineEmits(['update:activeKey', 'selected']);

  const selectedKeys = ref<string[]>(
    Array.isArray(props.activeKey) ? props.activeKey : [props.activeKey],
  );
  watch(
    () => props.activeKey,
    (val) => {
      selectedKeys.value = Array.isArray(val) ? val : [val];
    },
  );

  const filterTreeList = computed(() => {
    return filterTree(unref(getFilter(props.treeData)), props.titleField, props.searchValue);
  });

  function getFilter(data) {
    return data?.map((item) => {
      if (item?.children && item?.children.length > 0) {
        item.class = 'tree-children';
        item.children = getFilter(item.children);
      } else {
        item.class = 'tree-node';
      }
      item._name = item[props.titleField];
      return item;
    });
  }

  function handleSelect(_, e) {
    selectedKeys.value = [e.node.key];
    emit('selected', e.node.dataRef);
    nextTick(() => {
      emit('update:activeKey', selectedKeys.value[0] ?? '');
    });
  }
</script>

<template>
  <div class="h-full flex flex-col">
    <slot name="search"></slot>
    <div class="list-wrap of-y-auto of-x-hidden min-h-0 flex-1 basis-0">
      <BasicTree
        v-if="filterTreeList && filterTreeList.length > 0"
        class="menu-tree"
        draggable
        block-node
        iconPosition="right"
        v-model:selectedKeys="selectedKeys"
        :fieldNames="{ key: valueField, title: titleField, children: 'children' }"
        :tree-data="filterTreeList"
        @select="handleSelect"
        v-bind="$attrs"
      >
        <template #switcherIcon>
          <Icon class="w-24px" icon="ant-design:down-outlined" />
        </template>
        <template #title="item">
          <span class="flex justify-between items-center w-full">
            <span
              class="block w-[calc(100%-20px)] overflow-hidden truncate"
              :title="item[titleField]"
            >
              {{ item[titleField] }}
            </span>
            <Dropdown
              v-if="showAction"
              :class="[selectedKeys && selectedKeys[0] === item[valueField] ? '!block' : '!hidden']"
              placement="bottom"
            >
              <Icon
                class="ant-dropdown-link flex justify-between hidden absolute right-16px"
                @click.prevent
                color="#ff6da0"
                :size="18"
                icon="ant-design:more-outlined"
              />
              <template #overlay>
                <div
                  class="bg-#fff color-#333 w-fit min-w-80px rounded-3px text-left cursor-pointer shadow-md"
                >
                  <slot name="dropMenuContent" v-bind="item"></slot>
                </div>
              </template>
            </Dropdown>
          </span>
        </template>
      </BasicTree>
      <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE" v-if="!treeData.length">
        <template #description>
          <slot name="empty"></slot>
        </template>
      </Empty>
    </div>
  </div>
</template>

<style lang="less" scoped>
  .menu-tree {
    :deep {
      .ant-tree {
        .ant-tree-node-content-wrapper {
          display: block !important;
        }

        .ant-tree-node-content-wrapper.ant-tree-node-selected {
          background-color: transparent;
        }

        .ant-tree-node-content-wrapper:hover {
          background-color: transparent;
        }

        .ant-tree-treenode {
          position: relative;
          padding: 7px 12px;
          border-radius: 6px;
          max-height: 36px;
          transition: background-color 0.3s;

          &:hover {
            background: #ffebf2;
            color: #ff6da0;
          }

          &.ant-tree-treenode-selected {
            color: #ff6da0;
            background: #ffebf2;
          }

          &.ant-tree-treenode-selected::before {
            --at-apply: bg-primary;

            left: 0;
            right: unset;
            width: 8px;
            height: 20px;
            top: 50%;
            content: '';
            position: absolute;
            transform: translateY(-50%);
          }

          .ant-tree-switcher {
            position: absolute;
            right: 0;
            z-index: 2;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            align-items: center;
            justify-content: center;

            svg {
              transform: rotate(0deg);
              transition: 0.3s all;
              transform-origin: center;
            }
          }

          &.ant-tree-treenode-switcher-open {
            svg {
              transform: rotate(180deg);
              transition: 0.3s all;
              transform-origin: center;
            }
          }
        }
      }
    }
  }
  @prefix-cls: ~'@{namespace}-4a-dept-list';

  @menu-active-bg: #eef5ff;

  .@{prefix-cls} {
    .list-wrap {
      &::-webkit-scrollbar-track {
        background-color: transparent; /* 轨道背景颜色 */
      }

      &::-webkit-scrollbar-thumb {
        background-color: #ccc; /* 滑块背景颜色 */
        border-radius: 6px;
        height: 20px;
      }

      &::-webkit-scrollbar {
        width: 4px; /* 滚动条宽度 */
      }
    }
  }
</style>
