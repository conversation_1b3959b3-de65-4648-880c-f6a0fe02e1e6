import { get, pick } from 'lodash-es';

interface GetParentKeyOpt {
  key: string;
  tree: any[];
  keyField?: string;
  childrenField?: string;
}
export function getParentKey({
  key,
  tree,
  keyField = 'key',
  childrenField = 'children',
}: GetParentKeyOpt) {
  function findParentKey(nodes: any[], targetKey: string, currentPath: any[] = []) {
    for (const node of nodes) {
      // 当前路径包含了从根节点到当前节点的 key
      const newPath = [...currentPath, get(node, keyField)];

      if (get(node, keyField) === targetKey) {
        // 如果找到目标节点，返回除最后一个节点外的路径，即父节点路径
        return currentPath;
      }

      if (node[childrenField]) {
        // 在子节点中递归寻找
        const result = findParentKey(node[childrenField], targetKey, newPath);
        if (result) return result;
      }
    }
    // 如果没有找到，返回 undefined
    return undefined;
  }

  return findParentKey(tree, key) || [];
}

/**
 *
 * @param tree tree data
 * @param filterKey node key
 * @param filterValue search string
 */
export function filterTree(tree: any[], key: string, searchString: string) {
  // 如果搜索字符串为空，则返回整个树
  if (!searchString) {
    return tree;
  }

  // 递归函数来过滤树
  function filterNode(node) {
    // 如果节点匹配搜索字符串，则返回该节点
    if (new RegExp(searchString, 'gui').test(node[key])) {
      return { ...node };
    }

    // 如果有子节点，则递归过滤子节点
    if (node.children && node.children.length > 0) {
      const filteredChildren = node.children.map(filterNode).filter((child) => child != null);

      // 如果过滤后的子节点不为空，则返回当前节点和过滤后的子节点
      if (filteredChildren.length > 0) {
        return { ...node, children: filteredChildren };
      }
    }

    // 如果当前节点和子节点都不匹配，返回null
    return null;
  }

  // 对树中的每个节点应用过滤函数
  return tree.map(filterNode).filter((node) => node != null);
}

export function getDynamicField<T = any>(props: T) {
  return pick(props, ['titleField', 'valueField', 'iconField', 'childrenField']);
}
