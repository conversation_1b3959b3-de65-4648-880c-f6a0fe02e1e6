<script setup lang="tsx">
  import { Icon } from '@ft/internal/components/Icon';
  export interface TreeDropMenuItem {
    ifShow?: boolean;
    label: string;
    icon: string;
    onClick: any;
    auth?: any;
  }

  withDefaults(defineProps<{ createMenu?: TreeDropMenuItem[] }>(), {
    createMenu: () => [],
  });

  function getIfShow(menu: TreeDropMenuItem) {
    return Reflect.has(menu, 'ifShow') ? menu.ifShow : true;
  }
</script>

<template>
  <template v-for="(menu, idx) in createMenu">
    <span
      :key="idx"
      class="py-2 px-3 block hover:text-primary-color"
      v-if="getIfShow(menu)"
      @click="menu.onClick"
    >
      <Icon :icon="menu.icon" class="mr-1" />
      {{ menu.label }}
    </span>
  </template>
</template>
