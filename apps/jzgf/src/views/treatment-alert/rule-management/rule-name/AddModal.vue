<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { addFormSchema } from './data';
  import {
    addManualHivNStandardTreatEarlyPatient,
    getManualHivNStandardTreatEarlyPatientInfo,
    updateManualHivNStandardTreatEarlyPatient,
  } from '/@/api/rule-management';
  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm({
    schemas: addFormSchema,
    labelWidth: 100,
    colon: false,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });
  const mode = ref<'add' | 'edit'>('add');
  const title = computed(() => {
    return mode.value === 'edit' ? '编辑' : '新增';
  });
  const { runAsync: manualHivNStandardTreatEarlyPatientInfoRunAsync } = useRequest(
    getManualHivNStandardTreatEarlyPatientInfo,
    {
      showSuccessMessage: false,
      manual: true,
      onSuccess: (data) => {
        formAction.setFieldsValue({ ...data });
      },
    },
  );

  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data?.mode;
    if (mode.value === 'edit') {
      manualHivNStandardTreatEarlyPatientInfoRunAsync(data?.data?.id);
    } else {
      formAction.setFieldsValue({ ...data.data });
    }
  });
  const { loading, runAsync: ruleSaveRunAsync } = useRequest(
    (params) =>
      mode.value === 'add'
        ? addManualHivNStandardTreatEarlyPatient(params)
        : updateManualHivNStandardTreatEarlyPatient(params),
    {
      showSuccessMessage: true,
      manual: true,
      onSuccess: () => {
        emit('success');
        closeModal();
      },
    },
  );
  async function handleOk() {
    formAction.validate().then((values) => {
      ruleSaveRunAsync(values);
    });
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="title"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    width="1080px"
    @register="register"
    @ok="handleOk"
    centered
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
