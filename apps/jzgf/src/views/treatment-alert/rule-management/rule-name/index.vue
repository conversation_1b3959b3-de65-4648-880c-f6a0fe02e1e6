<script setup lang="ts">
  import { computed, nextTick, ref, watch } from 'vue';
  import { Button, Input } from 'ant-design-vue';
  import { Icon } from '@ft/internal/components/Icon';
  import { useModal } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import {
    type ActionItem,
    BasicTable,
    TableAction,
    useTable,
  } from '@ft/internal/components/Table';
  import TreeList from '../comp/TreeList/index.vue';
  import { columns } from './data';
  import AddModal from './AddModal.vue';
  import {
    deleteManualHivNStandardTreatEarlyPatient,
    getHivNStandardTreatEarlyRuleList,
    getManualHivNStandardTreatEarlyPatientPage,
  } from '/@/api/rule-management';

  /**
   * 传染病不规范治疗规则
   */
  const searchName = ref('');
  const activeKey = ref<string>('');
  const searchValue = ref('');

  watch(
    () => activeKey.value,
    () => {
      nextTick(() => {
        ActionTable?.reload();
      });
    },
  );

  const { data: treeData, loading } = useRequest(getHivNStandardTreatEarlyRuleList, {
    showSuccessMessage: false,
    onSuccess(data) {
      activeKey.value = data[0]?.id ?? '';
    },
  });

  function transformRuleList(ruleList, parentItem) {
    return (
      ruleList?.map((rule) => ({
        id: rule.id,
        kindName: rule.ruleName,
        ruleKindName: parentItem.kindName,
        ...rule,
      })) || []
    );
  }

  const getTreeData = computed(() => {
    return treeData.value?.map((item) => ({
      ...item,
      children: transformRuleList(item.ruleList, item),
    }));
  });

  const activeItem = computed(() =>
    treeData.value
      ?.flatMap((item) => transformRuleList(item.ruleList, item))
      .find((rule) => rule.id === activeKey.value),
  );
  const [register, ActionTable] = useTable({
    api: getManualHivNStandardTreatEarlyPatientPage,
    showIndexColumn: false,
    useSearchForm: false,
    inset: true,
    immediate: false,
    dataSource: [{ orgName: '111' }],
    beforeFetch(params) {
      params.notStandardTreatRuleId = activeItem.value?.id ?? undefined;
      params.notStandardTreatRuleKindId = activeItem.value
        ? activeItem.value?.kindId
        : activeKey.value;
      return params;
    },
    fetchSetting: {
      sizeField: 'pageSize',
      pageField: 'pageNum',
      listField: 'list',
    },
    actionColumn: {
      width: 120,
      title: '操作',
      align: 'left',
      dataIndex: 'action',
    },
    columns,
  });
  function createActions(record) {
    const actions: ActionItem[] = [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: handleDel.bind(null, record),
        },
      },
    ];
    return actions;
  }
  const { runAsync: runDel } = useRequest(deleteManualHivNStandardTreatEarlyPatient, {
    manual: true,
    showSuccessMessage: true,
    onSuccess() {
      ActionTable?.reload();
    },
  });

  function handleSearch(value: string) {
    searchName.value = value;
  }
  const [registerAddModal, { openModal: openAddModal }] = useModal();

  function handleEdit(item, column) {
    console.log(item, column);
    openAddModal(true, {
      mode: 'edit',
      data: {
        ...item,
      },
    });
  }

  function handleDel(item) {
    runDel(item.id);
  }
  function handleAdd() {
    openAddModal(true, {
      mode: 'add',
      data: {
        notStandardTreatRuleId: activeItem.value?.id,
        notStandardTreatRuleKindId: activeItem.value?.kindId,
        ruleName: activeItem.value?.ruleName,
        ruleKindName: activeItem.value?.ruleKindName,
      },
    });
  }
</script>

<template>
  <div class="flex gap-2 h-full">
    <div class="flex flex-col gap-3 w-228px bg-#fff rounded-[0px_8px_8px_8px] p-3">
      <span class="text-#333333 fw-bold">传染病不规范治疗规则</span>
      <TreeList
        default-expand-all
        :loading="loading"
        value-field="id"
        addIfShow
        title-field="kindName"
        @search="handleSearch"
        v-model:activeKey="activeKey"
        :treeData="getTreeData"
        :searchValue="searchValue"
        :showAction="false"
        class="flex-1"
      >
        <template #search>
          <div class="search mb-12px flex items-center text-center gap-9px">
            <Input allow-clear placeholder="输入关键字" v-model:value="searchValue">
              <template #suffix>
                <Icon icon="ant-design:search-outlined" />
              </template>
            </Input>
          </div>
        </template>
        <template #empty>
          <div>暂无数据</div>
        </template>
      </TreeList>
    </div>
    <div class="flex flex-col gap-2 flex-1 min-w-757px rounded-2 bg-white p-4">
      <div class="flex items-center gap-2">
        <span class="inline-block bg-primary-color w-3px h-1em"></span>
        <span class="text-#333333 fw-bold">传染病不规范治疗</span>
      </div>
      <BasicTable class="role-list" @register="register">
        <template #tableTitle>
          <Button type="primary" :disabled="!activeItem" @click="handleAdd"> 新增 </Button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :divider="false" :actions="createActions(record)" />
          </template>
        </template>
      </BasicTable>
      <AddModal @register="registerAddModal" @success="ActionTable.reload()" />
    </div>
  </div>
</template>

<style lang="less" scoped></style>
