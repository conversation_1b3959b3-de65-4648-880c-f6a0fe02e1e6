import type { BasicColumn } from '@ft/internal/components/Table';
import type { FormSchema } from '@ft/internal/components/Form';
import {
  getAdministrativeList,
  getInfectiousDiagnosisList,
  queryOrganizationList,
} from '@ft/internal/api';
import { useUserStoreWithOut } from '@ft/internal/store/modules/user';

/**
 * 所属区域
 * 医疗机构
 * 患者姓名
 * 年龄
 * 入院时间
 * 临床诊断
 * 不符合规则的名称
 * 不规范治疗内容
 */
export const columns: BasicColumn[] = [
  {
    title: '所属区域',
    dataIndex: 'areaName',
    width: 80,
    align: 'left',
  },
  {
    title: '医疗机构',
    dataIndex: 'medicalName',
    width: 160,
    align: 'left',
  },
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    width: 80,
    align: 'left',
  },
  {
    title: '年龄',
    dataIndex: 'age',
    width: 60,
    align: 'left',
  },
  {
    title: '就诊时间',
    dataIndex: 'admitHospitalTime',
    width: 180,
    align: 'left',
  },
  {
    title: '临床诊断',
    dataIndex: 'diagnosticResults',
    width: 180,
    align: 'left',
  },
  {
    title: '不符合规则的名称',
    dataIndex: 'ruleName',
    width: 140,
    align: 'left',
  },
  {
    title: '不规范治疗内容',
    dataIndex: 'notRegulateContent',
    width: 180,
    align: 'left',
  },
];
/**
 * 规则类别
 * 规则名称
 * 患者姓名
 * 年龄
 * 所属区域
 * 医疗机构
 * 入院时间
 * 临床诊断
 * 不规范内容
 */

const { userInfo } = useUserStoreWithOut();
export const addFormSchema: FormSchema[] = [
  {
    field: 'infectionDisease',
    component: 'Input',
    label: '病种',
    colProps: { span: 8 },
    componentProps: {},
    defaultValue: '2',
    show: false,
  },
  {
    field: 'id',
    component: 'Input',
    label: '表主键',
    colProps: { span: 8 },
    componentProps: {},
    show: false,
  },
  {
    field: 'notStandardTreatRuleId',
    component: 'Input',
    label: '不规范治疗规则ID',
    colProps: { span: 8 },
    componentProps: {},
    show: false,
  },
  {
    field: 'notStandardTreatRuleKindId',
    component: 'Input',
    label: '不规范治疗规则类型ID',
    colProps: { span: 8 },
    componentProps: {},
    show: false,
  },
  {
    field: 'ruleKindName',
    component: 'Input',
    label: '规则类别',
    colProps: { span: 8 },
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'ruleName',
    component: 'Input',
    label: '规则名称',
    colProps: { span: 15 },
    itemProps: { wrapperCol: { span: 9 } },
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'patientName',
    component: 'Input',
    label: '患者姓名',
    colProps: { span: 8 },
  },
  {
    field: 'age',
    component: 'InputNumber',
    label: '年龄',
    colProps: { span: 8 },
    componentProps: {
      min: 0,
    },
  },
  {
    field: 'areaName',
    component: 'Input',
    label: '所属区域',
    show: false,
  },
  {
    field: 'areaCode',
    label: '所属区域',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => ({
      api: getAdministrativeList,
      labelField: 'name',
      valueField: 'code',
      keyField: 'areaId',
      allowClear: true,
      getPopupContainer: () => document.body,
      onChange(_, o) {
        if (o?.label) formModel.areaName = o?.label;
      },
    }),
    colProps: { span: 8 },
  },
  {
    field: 'medicalName',
    component: 'Input',
    label: '医疗机构',
    defaultValue: userInfo?.orgName,
    show: false,
  },
  {
    field: 'medicalCode',
    label: '医疗机构',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => queryOrganizationList({ orgType: 1, enableFlag: 0 }),
        labelField: 'orgName',
        valueField: 'id',
        showSearch: true,
        optionFilterProp: 'label',
        getPopupContainer: () => document.body,
        onChange(_, o) {
          if (o?.label) formModel.medicalName = o?.label;
        },
      };
    },
    defaultValue: userInfo?.orgId,
    colProps: { span: 8 },
  },
  {
    field: 'admitHospitalTime',
    component: 'DatePicker',
    label: '就诊时间',
    colProps: { span: 8 },
    componentProps: {
      showTime: { format: 'HH:mm:ss' },
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      format: 'YYYY-MM-DD HH:mm:ss',
      style: { width: '100%' },
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'diagnosticResults',
    component: 'ApiSelect',
    label: '临床诊断',
    componentProps: ({ formModel }) => {
      return {
        api: () => getInfectiousDiagnosisList({ infectiousDiseaseId: '34' }),
        getPopupContainer: () => document.body,
        labelField: 'diagnosticName',
        valueField: 'diagnosticName',
        showSearch: true,
        optionFilterProp: 'label',
        onChange(_, opt) {
          if (opt?.label) formModel.applyDiagnosticName = opt?.label;
        },
      };
    },
    colProps: { span: 8 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: 'notRegulateKeys',
    label: '不规范治疗内容',
    labelWidth: 120,
    component: 'InputTextArea',
    componentProps: {
      style: { display: 'none' },
    },
    colProps: { span: 24 },
  },
  {
    field: 'notRegulateContent',
    label: ' ',
    component: 'InputTextArea',
    disabledLabelWidth: true,
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 }, colon: false },
  },
];
