<script setup lang="ts">
  import { h, nextTick, onMounted, ref, watch } from 'vue';
  import { Button, Input, notification } from 'ant-design-vue';
  import { Icon } from '@ft/internal/components/Icon';
  import { useModal } from '@ft/internal/components/Modal';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import { useRequest } from '@ft/request';
  import type { TreeDropMenuItem } from '../comp/TreeList/TreeDropMenu.vue';
  import TreeDropMenu from '../comp/TreeList/TreeDropMenu.vue';
  import TreeList from '../comp/TreeList/index.vue';
  import DateList from './DateList.vue';
  import AddRuleTypeModal from './addRuleTypeModal.vue';
  import { ruleKindDel, ruleKindList } from '/@/api/rule-management';
  import { earlyWarningReminder } from '/@/api/data-statistics';

  const typeList = ref<any>([]);
  const searchName = ref('');
  const activeKey = ref<string>('');
  const firstFetch = ref(false);
  const searchValue = ref('');

  const dateListRef = ref<InstanceType<typeof DateList>>();
  onMounted(() => {
    getTypeList();
  });
  watch(
    () => activeKey.value,
    () => {
      nextTick(() => {
        dateListRef.value?.ActionTable?.reload();
      });
    },
  );
  useRequest(earlyWarningReminder, {
    showSuccessMessage: false,
    onSuccess: (result) => {
      if (result.reminderStatus == 1) {
        const key = `open${Date.now()}`;
        notification.open({
          message: '不规范治疗提醒',
          description: `${result?.msgContent}`,
          placement: 'bottomRight',
          duration: null,
          btn: () =>
            h(
              Button,
              {
                type: 'primary',
                size: 'small',
                onClick: () => notification.close(key),
              },
              { default: () => '关闭' },
            ),
          key,
          onClose: close,
        });
      }
    },
  });
  const { runAsync: runRuleKindList } = useRequest(ruleKindList, {
    manual: true,
    showSuccessMessage: false,
  });
  const { runAsync: runRuleKindDel } = useRequest(ruleKindDel, {
    manual: true,
    showSuccessMessage: true,
  });
  function getTypeList() {
    runRuleKindList(searchName.value).then((res) => {
      typeList.value = res;
      (!firstFetch.value || !activeKey.value) && (activeKey.value = res[0]?.id ?? '');
      firstFetch.value = true;
    });
  }

  function createMenu(item): TreeDropMenuItem[] {
    return [
      {
        label: '编辑',
        icon: 'ant-design:edit-outlined',
        onClick: handleEdit.bind(null, item),
      },
      {
        label: '删除',
        icon: 'ant-design:delete-outlined',
        onClick: handleDel.bind(null, item),
      },
    ];
  }

  function handleSearch(value: string) {
    searchName.value = value;
  }
  const [registerAddRuleTypeModal, { openModal: openAddRuleTypeModal }] = useModal();

  function handleAddRuleType() {
    openAddRuleTypeModal(true, {
      mode: 'add',
      data: {
        sortNum: typeList.value.length + 1,
      },
    });
  }
  function handleEdit(item) {
    openAddRuleTypeModal(true, {
      mode: 'edit',
      data: {
        ...item,
        kindName: item._name,
      },
    });
  }
  const { createConfirm } = useMessage();

  function handleDel(item) {
    createConfirm({
      iconType: 'warning',
      title: '删除目录',
      content: `确定删除该目录吗？`,
      onOk: () => {
        runRuleKindDel(item.id).then(() => {
          getTypeList();
        });
      },
    });
  }
</script>

<template>
  <div class="flex gap-2 h-full">
    <div class="flex flex-col gap-3 w-228px bg-#fff rounded-[0px_8px_8px_8px] p-3">
      <span class="text-#333333 fw-bold">传染病不规范治疗规则类别</span>
      <TreeList
        value-field="id"
        addIfShow
        title-field="kindName"
        @search="handleSearch"
        v-model:activeKey="activeKey"
        :treeData="typeList"
        :searchValue="searchValue"
        class="flex-1"
      >
        <template #search>
          <div class="search mb-30px flex items-center text-center gap-9px">
            <Input allow-clear placeholder="输入关键字" v-model:value="searchValue">
              <template #suffix>
                <Icon icon="ant-design:search-outlined" />
              </template>
            </Input>
            <div
              @click="handleAddRuleType"
              class="w-20px h-20px rounded-50% bg-primary-color cursor-pointer p-1 flex items-center"
            >
              <Icon color="#fff" icon="ant-design:plus-outlined" size="12" />
            </div>
          </div>
        </template>
        <template #dropMenuContent="item">
          <TreeDropMenu :createMenu="createMenu(item)" />
        </template>
        <template #empty>
          <div>暂无数据</div>
        </template>
      </TreeList>
    </div>
    <div class="flex flex-col gap-2 flex-1 min-w-757px rounded-2 bg-white p-4">
      <div class="flex items-center gap-2">
        <span class="inline-block bg-primary-color w-3px h-1em"></span>
        <span class="text-#333333 fw-bold">传染病不规范治疗规则</span>
      </div>
      <DateList ref="dateListRef" :activeKey="activeKey" />
      <AddRuleTypeModal @register="registerAddRuleTypeModal" @success="getTypeList" />
    </div>
  </div>
</template>

<style lang="less" scoped></style>
