<script setup lang="ts">
  // import DataSetModal from '../../StandardDataset/comp/dateModal.vue';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { useModal } from '@ft/internal/components/Modal';
  import { Button, Switch, Tabs } from 'ant-design-vue';
  import { ref, watch } from 'vue';
  import { useRequest } from '@ft/request';
  import { DictEnum, getDictItemList } from '@ft/internal/api';
  import { columns } from './data';
  import AddRuleModal from '../rule-type/addRuleModal.vue';
  import type { RuleListItem } from '/@/api/rule-management';
  import { ruleDel, rulePage, ruleStateSwitch } from '/@/api/rule-management';

  const props = defineProps<{
    activeKey: string;
  }>();
  const diseaseCode = ref();
  const TabPaneList = ref();

  const [register, ActionTable] = useTable({
    api: rulePage,
    showIndexColumn: false,
    useSearchForm: false,
    inset: true,
    immediate: false,
    beforeFetch(params) {
      params.kindId = props.activeKey || '';
      params.diseaseCode = diseaseCode.value;
      return params;
    },
    fetchSetting: {
      sizeField: 'pageSize',
      pageField: 'pageNum',
      listField: 'list',
    },
    actionColumn: {
      width: 200,
      title: '操作',
      align: 'left',
      dataIndex: 'action',
    },
    afterFetch: (data) => {
      data.forEach((item) => {
        item.loading = false;
      });
      return data;
    },
    columns,
  });
  //getDictItemList
  const { runAsync: runGetDictItemList } = useRequest(getDictItemList, {
    manual: true,
    showSuccessMessage: false,
  });
  function getTabPane() {
    runGetDictItemList(DictEnum.INFECTIOUS_DISEASE).then((res) => {
      TabPaneList.value = res;
      diseaseCode.value = res[0]?.dictItemCode;
    });
  }
  watch(
    () => props.activeKey,
    () => {
      getTabPane();
    },
  );
  watch(
    () => diseaseCode.value,
    () => {
      ActionTable.reload();
    },
  );

  function createActions(record, column) {
    const actions: ActionItem[] = [
      {
        label: '编辑',
        onClick: handleRange.bind(null, record, column),
      },
      {
        label: '删除',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: handleDel.bind(null, record),
        },
      },
    ];

    return actions;
  }
  const [registerModal, { openModal: openModal }] = useModal();

  function handleSuccess() {
    ActionTable.reload();
  }
  function handleRange(record, _) {
    openModal(true, {
      data: {
        ...record,
        kindId: props.activeKey,
        diseaseCode: diseaseCode.value,
      },
      mode: 'edit',
    });
  }

  function handleAddRule() {
    openModal(true, {
      data: {
        kindId: props.activeKey,
        diseaseCode: diseaseCode.value,
      },
      mode: 'add',
    });
  }
  const { runAsync: runRuleDel } = useRequest(ruleDel, {
    manual: true,
    showSuccessMessage: true,
  });
  function handleDel(record) {
    runRuleDel(record.id).then(() => {
      ActionTable.reload();
    });
  }
  const { runAsync: runRuleStateSwitch } = useRequest(ruleStateSwitch, {
    manual: true,
    showSuccessMessage: true,
  });
  function onSwitchChange(record: RuleListItem) {
    record.loading = true;
    runRuleStateSwitch(record).finally(() => {
      record.loading = false;
    });
  }
  defineExpose({ ActionTable });
</script>

<template>
  <div class="h-full flex flex-col">
    <Tabs v-model:activeKey="diseaseCode">
      <Tabs.TabPane
        :key="item?.dictItemCode"
        :tab="item?.dictItemName"
        v-for="item in TabPaneList"
      />
    </Tabs>
    <BasicTable class="role-list" @register="register">
      <template #tableTitle>
        <Button type="primary" @click="handleAddRule"> 新增规则 </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'enableFlag'">
          <Switch
            :loading="record.loading"
            :checked-value="1"
            :un-checked-value="0"
            v-model:checked="record.enableFlag"
            @change="onSwitchChange(record)"
          />
        </template>
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
    <AddRuleModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<style scoped lang="less">
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table-form-container .ant-form {
      padding: 0;
      margin-bottom: 10px;
    }
  }
</style>
