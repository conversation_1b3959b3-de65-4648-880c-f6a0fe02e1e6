<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { addRuleModal } from './data';
  import { ruleAdd, ruleEdit } from '/@/api/rule-management';

  const emit = defineEmits(['register', 'success']);
  // const props = defineProps<{
  //   kindId: string;
  // }>();
  const [registerForm, formAction] = useForm({
    schemas: addRuleModal,
    labelWidth: 100,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });
  const mode = ref<'add' | 'edit'>('add');
  const title = computed(() => {
    return mode.value === 'edit' ? '编辑规则' : '新增规则';
  });
  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data?.mode;
    formAction.setFieldsValue({ ...data.data });
    if (mode.value === 'edit') {
      formAction.setFieldsValue(data.data);
    }
  });
  const { loading, runAsync: ruleSaveRunAsync } = useRequest(
    (params) => (mode.value === 'add' ? ruleAdd(params) : ruleEdit(params)),
    {
      showSuccessMessage: true,
      manual: true,
    },
  );
  async function handleOk() {
    formAction.validate().then((values) => {
      ruleSaveRunAsync({
        ...values,
      }).then(() => {
        emit('success');
        closeModal();
      });
    });
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="title"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    width="480px"
    @register="register"
    @ok="handleOk"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
