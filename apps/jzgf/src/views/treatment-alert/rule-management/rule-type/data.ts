import type { BasicColumn } from '@ft/internal/components/Table';
import type { FormSchema } from '@ft/internal/components/Form';
import { ruleKindList } from '/@/api/rule-management';
import { DictEnum, getDictItemList } from '@ft/internal/api';

/**
 *
 *
 * 规则名称
 * 规则机制
 * 是否启用
 */
export const columns: BasicColumn[] = [
  {
    title: '规则名称',
    dataIndex: 'ruleName',
    align: 'left',
  },
  {
    title: '规则机制',
    dataIndex: 'ruleMechanism',
    align: 'left',
  },
  {
    title: '是否启用',
    dataIndex: 'enableFlag',
    align: 'left',
  },
];
export const modalSchema: FormSchema[] = [
  {
    field: 'orgId',
    component: 'Input',
    label: 'orgId',
    show: false,
    colProps: { span: 12 },
  },
  {
    field: 'deptId',
    component: 'Input',
    label: 'deptId',
    show: false,
    colProps: { span: 12 },
  },
  {
    field: 'deptCode',
    component: 'Input',
    label: '科室编码',
    required: true,
    colProps: { span: 12 },
  },
  {
    field: 'deptName',
    component: 'Input',
    label: '科室名称',
    required: true,
    colProps: { span: 12 },
  },
  {
    field: 'deptType',
    component: 'Input',
    label: '科室分类',
    componentProps: {
      api: () => getDictItemList(DictEnum.DEPT_TYPE),
      labelField: 'dictItemName',
      valueField: 'dictItemValue',
    },
    required: true,
    colProps: { span: 12 },
  },
  {
    field: 'deptIntroduction',
    component: 'Input',
    label: '科室介绍',
    colProps: { span: 24 },
  },
  {
    field: 'deptDiseaseType',
    component: 'Input',
    label: '科室病种',
    colProps: { span: 24 },
  },
  {
    field: 'deptManager',
    component: 'Input',
    label: '科室负责人',
    colProps: { span: 12 },
  },
  {
    field: 'deptTelephone',
    component: 'Input',
    label: '科室联系电话',
    colProps: { span: 12 },
  },
  {
    field: 'deptAddress',
    component: 'Input',
    label: '科室地址',
    colProps: { span: 24 },
  },
  {
    field: 'enableFlag',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 0 },
        { label: '禁用', value: 1 },
      ],
    },
    label: '科室状态',
    colProps: { span: 12 },
  },
];
export const addRuleTypeModal: FormSchema[] = [
  {
    field: 'id',
    component: 'Input',
    label: 'id',
    colProps: { span: 24 },
    componentProps: {},
    show: false,
  },
  {
    field: 'kindName',
    component: 'Input',
    label: '类别名称',
    colProps: { span: 24 },
    componentProps: {},
    required: true,
  },
  {
    field: 'sortNum',
    component: 'InputNumber',
    label: '序号',
    colProps: { span: 24 },
    componentProps: {
      min: 0,
    },
    required: true,
  },
];
/**
 * 类别名称
 * 规则名称
 * 规则机制
 * 是否启用
 */
export const addRuleModal: FormSchema[] = [
  {
    field: 'diseaseCode',
    component: 'Input',
    label: '疾病编码',
    colProps: { span: 24 },
    componentProps: {},
    show: false,
  },
  {
    field: 'id',
    component: 'Input',
    label: '表主键',
    colProps: { span: 24 },
    componentProps: {},
    show: false,
  },
  {
    field: 'kindId',
    component: 'ApiSelect',
    label: '类别名称',
    colProps: { span: 24 },
    componentProps: {
      //禁用
      disabled: true,
      api: () => ruleKindList(),
      labelField: 'kindName',
      valueField: 'id',
    },
  },
  {
    field: 'ruleName',
    component: 'Input',
    label: '规则名称',
    colProps: { span: 24 },
    componentProps: {},
    required: true,
  },
  {
    field: 'ruleMechanism',
    component: 'InputTextArea',
    label: '规则机制',
    colProps: { span: 24 },
    componentProps: {
      autoSize: { minRows: 3, maxRows: 3 },
    },
    required: true,
  },
  {
    field: 'enableFlag',
    component: 'RadioGroup',
    label: '是否启用',
    colProps: { span: 24 },
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 },
      ],
    },
    defaultValue: 1,
    required: true,
  },
];
