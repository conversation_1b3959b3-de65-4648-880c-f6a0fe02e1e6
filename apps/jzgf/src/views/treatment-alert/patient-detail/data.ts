import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';

/**
所属区域
医疗机构;
患者姓名;
入院时间;
临床诊断;
年龄;
不符合规则的名称
不规范治疗内容
 */
export const patientColumn = (activeKey: string): BasicColumn[] => {
  return [
    {
      title: '所属区域',
      dataIndex: 'areaName',
      width: 100,
      align: 'left',
    },
    {
      title: '医疗机构',
      dataIndex: 'medicalName',
      width: 180,
      align: 'left',
    },
    {
      title: '患者姓名',
      dataIndex: 'patientName',
      width: 100,
      align: 'left',
    },
    {
      title: '就诊时间',
      dataIndex: 'admitHospitalTime',
      width: 160,
      align: 'left',
    },
    {
      title: '临床诊断',
      dataIndex: 'diagnosticResults',
      width: 100,
      align: 'left',
    },
    {
      title: '年龄',
      dataIndex: 'age',
      width: 60,
      align: 'left',
    },
    {
      title: '不符合规则的名称',
      dataIndex: 'ruleName',
      width: 160,
      align: 'left',
    },
    {
      title: '不规范治疗内容',
      dataIndex: 'notRegulateContent',
      width: 360,
      align: 'left',
    },
    //未处理原因
    {
      title: '未处理原因',
      dataIndex: 'unresolvedCause',
      width: 180,
      align: 'left',
      ifShow: activeKey === '1',
    },
  ];
};

export const patientFormSchema: FormSchema[] = [
  {
    field: 'ruleName',
    component: 'Input',
    label: '不规范治疗规则',
    colProps: { span: 6 },
  },
  {
    field: 'medicalName',
    component: 'Input',
    label: '医疗机构',
    colProps: { span: 6 },
  },
  {
    field: 'statisticalTime',
    component: 'RangePicker',
    label: '统计时间',
    colProps: {
      span: 6,
    },
    componentProps: {
      style: {
        width: '100%',
      },
      disabled: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  },
];
