<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { DictEnum, getDictItemList } from '@ft/internal/api';
  import { notStandardRecordHandle } from '/@/api/data-statistics';

  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm({
    schemas: [
      {
        field: 'id',
        label: 'id',
        component: 'Input',
        show: false,
      },
      {
        field: 'handleStatus',
        label: '是否已处理:',
        component: 'RadioGroup',
        componentProps: {
          options: [
            { label: '是', value: 2 },
            { label: '否', value: 1 },
          ],
        },
        required: true,
        colProps: { span: 22 },
        itemProps: { wrapperCol: { span: 18 } },
      },
      {
        field: 'affectTreatmentReasonCode',
        label: '影响患者治疗的最主要原因:',
        component: 'ApiRadioGroup',
        required: true,
        componentProps: {
          api: () => getDictItemList(DictEnum.AFFECTING_TREATMENT_MAIN_REASON),
          labelField: 'dictItemName',
          valueField: 'dictItemCode',
          getPopupContainer: () => document.body,
        },
        colProps: { span: 22 },
        itemProps: { wrapperCol: { span: 24 } },
        ifShow: ({ model }) => model?.handleStatus === 1,
      },

      {
        field: 'affectTreatmentReasonOther',
        label: '其他原因',
        required: true,
        component: 'InputTextArea',
        componentProps: {
          autoSize: { minRows: 3, maxRows: 3 },
        },
        colProps: { span: 22 },
        itemProps: { wrapperCol: { span: 18 } },
        ifShow: ({ model }) => model?.affectTreatmentReasonCode == 99 && model?.handleStatus === 1,
      },
    ],
    labelWidth: 200,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });
  const [register, { closeModal }] = useModalInner((data) => {
    formAction.setFieldsValue({ ...data });
  });
  const { loading, runAsync: ruleSaveRunAsync } = useRequest(notStandardRecordHandle, {
    showSuccessMessage: true,
    manual: true,
    onSuccess: () => {
      closeModal();
      emit('success');
    },
  });

  async function handleOk() {
    formAction.validate().then((values) => {
      ruleSaveRunAsync({
        ...values,
      });
    });
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    title="处理"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    width="880px"
    @register="register"
    @ok="handleOk"
    centered
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
