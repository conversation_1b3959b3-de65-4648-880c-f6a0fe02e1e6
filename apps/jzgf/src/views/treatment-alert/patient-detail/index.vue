<script setup lang="ts">
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button, Tabs } from 'ant-design-vue';
  import {
    queryNStandardTreatPatientInfoList,
    unStandardTreatPatientInfoListExport,
  } from '/@/api/data-statistics';
  import { exportUtil } from '@ft/internal/utils';
  import { useRequest } from '@ft/request';
  import { computed, ref } from 'vue';
  import { DictEnum, getDictItemList } from '@ft/internal/api';
  import { useModal } from '@ft/internal/components/Modal';
  import { useRoute } from 'vue-router';
  import ProcessingModal from './processingModal.vue';
  import { patientColumn, patientFormSchema } from './data';
  const query = useRoute().query;
  const activeKey = ref();
  const statisticalTime = computed(() =>
    query.startDate && query.endDate ? [query.startDate, query.endDate] : undefined,
  );
  const { data: tabList, runAsync } = useRequest(
    () => getDictItemList(DictEnum.NOT_STANDARD_TREAT_STATUS),
    {
      onSuccess: (res) => {
        if (res.length > 0) {
          activeKey.value = res[0].dictItemCode;
          ActionTable.getForm().setFieldsValue({
            statisticalTime: statisticalTime.value,
            ruleName: query.ruleTitle,
            medicalName: query.orgName,
          });
          ActionTable.getForm().updateSchema([
            {
              field: 'ruleName',
              componentProps: {
                disabled: !!query.ruleTitle,
              },
            },
            {
              field: 'medicalName',
              componentProps: {
                disabled: !!query.orgName,
              },
            },
          ]);
          ActionTable.reload();
        }
      },
    },
  );

  const [registerTable, ActionTable] = useTable({
    api: queryNStandardTreatPatientInfoList,
    columns: computed(() => patientColumn(activeKey.value)),
    formConfig: {
      colon: true,
      labelWidth: 120,
      showAdvancedButton: false,
      actionColOptions: {
        span: 6,
        style: {
          textAlign: 'right',
        },
      },
      schemas: patientFormSchema,
      fieldMapToTime: [['statisticalTime', ['startDate', 'endDate'], 'YYYY-MM-DD']],
      submitFunc: async () => {
        await runAsync();
      },
    },
    useSearchForm: true,
    showIndexColumn: false,
    bordered: false,
    immediate: false,
    size: 'small',
    // resizeHeightOffset: 20,
    beforeFetch: (params) => {
      return {
        ...query,
        ...params,
        handleStatus: activeKey.value,
      };
    },
    scroll: { x: 'max-content' },
    actionColumn: computed(() =>
      activeKey.value === '0'
        ? {
            width: 100,
            title: '操作',
            dataIndex: 'action',
          }
        : undefined,
    ),
  });
  const { loading, runAsync: exportTaskSurveyRecordRun } = useRequest(
    unStandardTreatPatientInfoListExport,
    {
      manual: true,
    },
  );
  function onExport() {
    exportUtil(
      exportTaskSurveyRecordRun({
        ...query,
        handleStatus: activeKey.value,
        ...ActionTable.getForm().getFieldsValue(),
      }),
    );
  }
  function splitByUnicodeNumbers(str) {
    if (!str) return [];
    const regex = /[\u2460-\u2469]+/g;
    const separators = str.match(regex) || [];
    const parts = str
      ?.split(regex)
      ?.filter((s) => s)
      ?.map((s) => `${separators.shift() || ''}${s}`);
    return parts;
  }
  const [registerModal, { openModal }] = useModal();

  function createActions(record): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '处理',
        type: 'link',
        onClick: onProcessing.bind(null, record),
      },
    ];
    return actions;
  }

  function onProcessing(record) {
    openModal(true, {
      id: record.id,
    });
  }
</script>

<template>
  <div class="w-full h-full pr-4">
    <BasicTable class="data-statistics" @register="registerTable">
      <template #headerTop>
        <div class="flex items-center gap-2">
          <span class="inline-block bg-primary-color w-3px h-1em"></span>
          <span class="text-#333333 fw-bold">{{ query?.diseaseName }}不规范治疗数据患者列表</span>
        </div>
        <Tabs v-model:activeKey="activeKey" @change="ActionTable.reload()">
          <Tabs.TabPane v-for="item in tabList" :key="item.dictItemCode" :tab="item.dictItemName" />
        </Tabs>
      </template>
      <template #toolbar>
        <Button type="primary" @click="onExport" :loading="loading">导出</Button>
      </template>
      <template #tableTitle> </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'notRegulateContent'">
          <div class="flex flex-col">
            <div v-for="item in splitByUnicodeNumbers(record[column.dataIndex])" :key="item">
              {{ item }}
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
    <!-- 处理弹窗 -->
    <ProcessingModal @register="registerModal" @success="ActionTable.reload()" />
  </div>
</template>

<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }

    .vben-basic-table-form-container .ant-form {
      padding: 16px 16px 10px;
      margin-bottom: 10px;
    }
  }
</style>
