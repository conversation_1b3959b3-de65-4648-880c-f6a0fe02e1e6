<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { AddSchemas } from './data';
  import { addPatient } from '/@/api/unknown-monitoring/patient-management';

  const emit = defineEmits(['register', 'success']);
  const [registerForm, formAction] = useForm({
    labelWidth: 120,
    schemas: AddSchemas,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });
  const [register, { closeModal }] = useModalInner();
  const { loading, runAsync: runAsyncAddPatient } = useRequest(
    () => addPatient(formAction.getFieldsValue()),
    {
      manual: true,
      showSuccessMessage: true,
      async onBefore() {
        try {
          await formAction.validate();
          return true;
        } catch (e) {
          return false;
        }
      },
      onSuccess() {
        emit('success');
        closeModal();
      },
    },
  );
</script>

<template>
  <BasicModal
    @register="register"
    :destroy-on-close="true"
    :can-fullscreen="false"
    v-bind="$attrs"
    centered
    :min-height="50"
    width="450px"
    title="新增患者"
    :ok-button-props="{
      loading,
    }"
    @ok="runAsyncAddPatient"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<style scoped></style>
