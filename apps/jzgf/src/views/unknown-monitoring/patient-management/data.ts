import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { useRequest } from '@ft/request';
import { getPatientInfoByInpatientNo } from '/@/api/unknown-monitoring/patient-management';
/**
 * @description: 表格列
患者姓名 
性别 
年龄
入院时间
初步诊断
就诊机构
就诊科室
就诊医生
 */
export const columns: BasicColumn[] = [
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    width: 100,
    align: 'left',
  },
  {
    title: '性别',
    dataIndex: 'sexName',
    width: 100,
    align: 'left',
  },
  {
    title: '年龄',
    dataIndex: 'age',
    width: 100,
    align: 'left',
  },
  {
    title: '入院时间',
    dataIndex: 'admitHospitalTime',
    width: 150,
    align: 'left',
  },
  {
    title: '初步诊断',
    dataIndex: 'diagnoseName',
    width: 100,
    align: 'left',
  },
  {
    title: '就诊机构',
    dataIndex: 'visitHospitalName',
    width: 150,
    align: 'left',
  },
  {
    title: '就诊科室',
    dataIndex: 'visitDeptName',
    width: 150,
    align: 'left',
  },
  {
    title: '就诊医生',
    dataIndex: 'visitDoctorName',
    width: 100,
    align: 'left',
  },
];
/**
 * @description: 搜索表单
 * 入院日期
 * 就诊科室
 * 患者姓名
 */
export const formSchema: FormSchema[] = [
  {
    field: 'admitHospitalTime',
    component: 'RangePicker',
    label: '入院日期',
    colProps: {
      span: 6,
    },
    componentProps: {
      style: {
        width: '100%',
      },
    },
  },
  {
    field: 'visitDeptName',
    component: 'Input',
    label: '就诊科室',
    colProps: { span: 6 },
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'patientName',
    component: 'Input',
    label: '患者姓名',
    colProps: { span: 6 },
  },
];
/**
 * 患者住院号
 * 患者姓名
 * 身份证号
 */
const { data: patientInfo, runAsync } = useRequest(
  (inpatientNo: string) => getPatientInfoByInpatientNo(inpatientNo),
  {
    manual: true,
  },
);
export const AddSchemas: FormSchema[] = [
  {
    field: 'inpatientNo',
    label: '患者住院号',
    required: true,
    component: 'Input',
    componentProps: ({ formModel }) => {
      return {
        onPressEnter: async (e: any) => {
          await runAsync(e.target.value);
          formModel.patientName = patientInfo.value?.patientName;
          formModel.idCardNo = patientInfo.value?.idCardNo;
        },
      };
    },
  },
  {
    field: 'patientName',
    label: '患者姓名',
    required: true,
    component: 'Input',
    componentProps: {
      disabled: true,
    },
  },
  {
    field: 'idCardNo',
    label: '身份证号',
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    required: true,
  },
];
