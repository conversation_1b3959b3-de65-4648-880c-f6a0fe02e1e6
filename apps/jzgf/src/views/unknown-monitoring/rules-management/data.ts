import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';

/**
 * 规则名称
 * 创建时间
 * 创建人
 */
export const searchFormSchema: FormSchema[] = [
  {
    field: 'ruleName',
    label: '规则名称',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'time',
    component: 'RangePicker',
    label: '创建时间',
    colProps: {
      span: 6,
    },
    componentProps: {
      style: {
        width: '100%',
      },
    },
  },
  {
    field: 'createUser',
    label: '创建人',
    component: 'Input',
    colProps: { span: 6 },
  },
];

/**
 * 规则名称
 * 规则机制
 * 是否启用
 * 创建时间
 * 创建人
 */
export const columns: BasicColumn[] = [
  {
    title: '规则名称',
    dataIndex: 'ruleName',
    width: 120,
    align: 'left',
  },
  {
    title: '规则机制',
    dataIndex: 'ruleDesc',
    width: 220,
    align: 'left',
  },
  {
    title: '是否启用',
    dataIndex: 'enableFlag',
    width: 80,
    align: 'left',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
    align: 'left',
  },
  {
    title: '创建人',
    dataIndex: 'createUser',
    width: 120,
    align: 'left',
  },
];

/**
 * 规则名称
 * 规则机制
 * 是否启用
 */
export const editFormSchema: FormSchema[] = [
  {
    field: 'id',
    component: 'Input',
    label: '表主键',
    colProps: { span: 24 },
    componentProps: {},
    show: false,
  },
  {
    field: 'ruleName',
    component: 'Input',
    label: '规则名称',
    colProps: { span: 24 },
    componentProps: {},
    required: true,
  },
  {
    field: 'ruleDesc',
    component: 'InputTextArea',
    label: '规则机制',
    colProps: { span: 24 },
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    required: true,
    rules: [
      {
        message: '超出字符限制，请控制在1-250个字符之间',
        trigger: 'change',
        max: 250,
      },
    ],
  },
  {
    field: 'enableFlag',
    component: 'RadioGroup',
    label: '是否启用',
    colProps: { span: 24 },
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 },
      ],
    },
    defaultValue: 1,
    required: true,
  },
];
