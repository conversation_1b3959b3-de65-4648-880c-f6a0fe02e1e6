<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import { StyledList } from '@ft/components';
  import { Button } from '@ft/internal/components/Button';
  import { useRequest } from '@ft/request';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { Empty } from 'ant-design-vue';
  import { DictEnum, getDictItemList } from '@ft/internal/api';
  import { schemasForm } from './data';
  import {
    addMedicalKnowledge,
    getMedicalKnowledgeByIcdCode,
    updateMedicalKnowledge,
  } from '/@/api/unknown-monitoring/medical-knowledge';

  const activeDictItemCode = ref<string>();
  const activeItem = ref<any>({});
  const mode = ref('add');
  const isEdit = ref(true);
  const [registerForm, formAction] = useForm({
    labelWidth: 100,
    disabled: computed(() => !isEdit.value),
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
    schemas: schemasForm,
  });

  const { loading, data: items } = useRequest(() => getDictItemList(DictEnum.MEDICAL_KNOWLEDGE), {
    onSuccess: (res) => {
      activeDictItemCode.value = res.length > 0 ? res[0].dictItemCode : '';
      activeItem.value = res?.[0] || {};
    },
  });
  const { runAsync: runAsyncGetMedicalKnowledgeDetail } = useRequest(getMedicalKnowledgeByIcdCode, {
    manual: true,
    onSuccess: (data) => {
      if (data) {
        mode.value = 'edit';
        formAction.setFieldsValue(data);
        isEdit.value = false;
      } else {
        mode.value = 'add';
        formAction.resetFields();
        isEdit.value = true;
      }
    },
  });
  watch(
    () => activeDictItemCode.value,
    (val) => {
      if (val) {
        runAsyncGetMedicalKnowledgeDetail(val);
      }
    },
  );

  const { runAsync: saveRunAsync, loading: saveLoading } = useRequest(
    () =>
      mode.value === 'add'
        ? addMedicalKnowledge({
            ...formAction.getFieldsValue(),
            icdCode: activeDictItemCode.value,
            knowledgeName: activeItem.value.dictItemName,
            knowledgeType: 1,
          })
        : updateMedicalKnowledge({
            ...formAction.getFieldsValue(),
            icdCode: activeDictItemCode.value,
            knowledgeName: activeItem.value.dictItemName,
            knowledgeType: 1,
          }),
    {
      manual: true,
      showSuccessMessage: true,
      async onBefore() {
        try {
          await formAction.validate();
          return true;
        } catch (e) {
          return false;
        }
      },
      onSuccess: () => {
        runAsyncGetMedicalKnowledgeDetail(activeDictItemCode.value);
      },
    },
  );
  function onCancel() {
    if (mode.value === 'add') {
      formAction.resetFields();
    } else if (mode.value === 'edit') {
      isEdit.value = false;
    }
  }
</script>
<template>
  <div class="flex gap-2 h-[calc(100%-10px)] w-[calc(100%-16px)]">
    <div class="flex flex-col gap-2 w-228px bg-#fff rounded rounded-lt-none p-3">
      <span class="text-#333333 fw-bold">不明原因相关诊疗分类</span>
      <div class="pr-4 of-y-auto of-x-hidden min-w-0">
        <StyledList
          :items="items"
          v-model="activeDictItemCode"
          v-model:value="activeItem"
          valueField="dictItemCode"
          label-field="dictItemName"
          class="flex-1"
          :width="216"
          :loading="loading"
        />
      </div>
    </div>
    <div class="flex-1 bg-#fff rounded p-3">
      <div v-if="items && items.length > 0">
        <div class="flex justify-between items-center">
          <span class="text-#333333 fw-bold">知识内容</span>
          <div>
            <Button
              v-if="!isEdit"
              type="link"
              pre-icon="ant-design:edit-outlined"
              @click="isEdit = !isEdit"
              >编辑</Button
            >
            <Button v-if="isEdit" type="default" class="mr-4" @click="onCancel">取消</Button>
            <Button v-if="isEdit" @click="saveRunAsync" :loading="saveLoading" type="primary"
              >保存</Button
            >
          </div>
        </div>
        <div class="flex-1 basis-0 min-h-0 of-y-auto mt-4">
          <BasicForm style="transform: translateX(-36px)" @register="registerForm" />
        </div>
      </div>
      <Empty v-else :image="Empty.PRESENTED_IMAGE_SIMPLE" />
    </div>
  </div>
</template>
<style lang="less" scoped></style>
