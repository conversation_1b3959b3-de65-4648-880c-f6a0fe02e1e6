import type { FormSchema } from '@ft/internal/components/Form';

export const schemasForm: FormSchema[] = [
  {
    field: 'id',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'knowledgeContent',
    label: ' ',
    component: 'InputTextArea',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入',
      autoSize: { minRows: 25, maxRows: 25 },
    },
    itemProps: { wrapperCol: { span: 20 } },
    required: true,
  },
];
