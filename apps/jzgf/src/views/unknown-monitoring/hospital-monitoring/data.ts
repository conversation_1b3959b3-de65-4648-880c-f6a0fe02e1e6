import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import dayjs from 'dayjs';
import { getDivisionList } from '/@/api/unknown-monitoring/hospital-monitoring';
export const columns: BasicColumn[] = [
  {
    title: '所属区划',
    dataIndex: 'divisionName',
    width: 100,
    align: 'left',
  },
  {
    title: '所属机构',
    dataIndex: 'visitHospitalName',
    width: 180,
    align: 'left',
  },
  {
    title: '入院时间',
    dataIndex: 'admitHospitalTime',
    width: 150,
    align: 'left',
  },
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    width: 100,
    align: 'left',
  },
  // {
  //   title: '患者诊疗卡号',
  //   dataIndex: 'medicalCardNo',
  //   width: 100,
  //   align: 'left',
  // },
  {
    title: '性别',
    dataIndex: 'sexName',
    width: 100,
    align: 'left',
  },
  {
    title: '年龄',
    dataIndex: 'age',
    width: 100,
    align: 'left',
  },
  {
    title: '身份证号',
    dataIndex: 'idCardNo',
    width: 150,
    align: 'left',
  },
  {
    title: '就诊诊断',
    dataIndex: 'diagnoseName',
    width: 100,
    align: 'left',
  },
  {
    title: '就诊科室',
    dataIndex: 'visitDeptName',
    width: 150,
    align: 'left',
  },
  // {
  //   title: '就诊医生',
  //   dataIndex: 'visitDoctorName',
  //   width: 100,
  //   align: 'left',
  // },
];

export const formSchema: FormSchema[] = [
  {
    field: 'year',
    label: '统计年度',
    colProps: { span: 6 },
    disabledLabelWidth: false,
    component: 'DatePicker',
    componentProps: {
      picker: 'year',
      showTime: false,
      showNow: false,
      valueFormat: 'YYYY',
      placeholder: '请选择年份',
      style: {
        width: '100%',
      },
      getPopupContainer: () => document.body, // 防止弹出层被modal隐藏掉
    },
    defaultValue: dayjs().format('YYYY'),
    isHandleDateDefaultValue: false,
  },
  {
    field: 'admitHospitalTime',
    component: 'RangePicker',
    label: '入院时间',
    colProps: {
      span: 6,
    },
    componentProps: {
      style: {
        width: '100%',
      },
    },
  },
  {
    field: 'divisionCode',
    component: 'ApiSelect',
    label: '所属区划',
    componentProps: () => ({
      api: getDivisionList,
      labelField: 'text',
      valueField: 'value',
      defaultValue: '',
      getPopupContainer: () => document.body,
    }),
    colProps: { span: 6 },
  },
];

//所属区域 医疗机构; 患者姓名; 入院时间; 临床诊断;年龄;
export const DetailColumns: BasicColumn[] = [
  {
    title: '所属区域',
    dataIndex: 'region',
    width: 100,
    align: 'left',
  },
  {
    title: '医疗机构',
    dataIndex: 'hospital',
    width: 180,
    align: 'left',
  },
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    width: 100,
    align: 'left',
  },
  {
    title: '入院时间',
    dataIndex: 'admissionTime',
    width: 100,
    align: 'left',
  },
  {
    title: '临床诊断',
    dataIndex: 'clinicalDiagnosis',
    width: 100,
    align: 'left',
  },
  {
    title: '年龄',
    dataIndex: 'age',
    width: 100,
    align: 'left',
  },
];

export const DetailFormSchema: FormSchema[] = [
  {
    field: 'rule',
    component: 'ApiSelect',
    label: '不规范治疗规则',
    colProps: { span: 6 },
  },
  {
    field: 'org',
    component: 'ApiSelect',
    label: '医疗机构',
    colProps: { span: 6 },
  },
];
