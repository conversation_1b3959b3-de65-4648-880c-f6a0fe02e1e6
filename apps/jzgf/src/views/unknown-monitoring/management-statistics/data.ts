import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import dayjs from 'dayjs';
import { getReportHospitalList } from '/@/api/unknown-monitoring/auxiliary-diagnosis';
/**
 * 统计年度
 * 上报日期
 * 上报机构
 */
export const searchFormSchema: FormSchema[] = [
  {
    field: 'year',
    label: '统计年度',
    colProps: { span: 6 },
    disabledLabelWidth: false,
    component: 'DatePicker',
    componentProps: {
      picker: 'year',
      showTime: false,
      showNow: false,
      valueFormat: 'YYYY',
      placeholder: '请选择年份',
      style: {
        width: '100%',
      },
      getPopupContainer: () => document.body, // 防止弹出层被modal隐藏掉
    },
    defaultValue: dayjs().format('YYYY'),
    isHandleDateDefaultValue: false,
  },
  {
    field: 'reportTime',
    component: 'RangePicker',
    label: '上报日期',
    colProps: {
      span: 6,
    },
    componentProps: {
      style: {
        width: '100%',
      },
    },
  },
  {
    field: 'reportHospitalCode',
    component: 'ApiSelect',
    label: '上报机构',
    colProps: { span: 6 },
    componentProps: () => ({
      api: getReportHospitalList,
      labelField: 'text',
      valueField: 'value',
      defaultValue: '',

      getPopupContainer: () => document.body,
    }),
  },
];

/**
 * 上报时间
 * 所属区划
 * 所属机构
 * 入院时间
 * 患者姓名
 * 患者诊疗卡号
 * 性别
 * 年龄
 * 身份证号
 * 就诊诊断
 * 就诊科室
 * 就诊医生
 */
export const detailColumns: BasicColumn[] = [
  {
    title: '上报时间',
    dataIndex: 'reportTime',
    width: 100,
    align: 'left',
  },
  {
    title: '所属区划',
    dataIndex: 'divisionName',
    width: 100,
    align: 'left',
  },
  {
    title: '所属机构',
    dataIndex: 'visitHospitalName',
    width: 100,
    align: 'left',
  },
  {
    title: '入院时间',
    dataIndex: 'admitHospitalTime',
    width: 100,
    align: 'left',
  },
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    width: 100,
    align: 'left',
  },
  // {
  //   title: '患者诊疗卡号',
  //   dataIndex: 'medicalCardNo',
  //   width: 100,
  //   align: 'left',
  // },
  {
    title: '性别',
    dataIndex: 'sexName',
    width: 100,
    align: 'left',
  },
  {
    title: '年龄',
    dataIndex: 'age',
    width: 100,
    align: 'left',
  },
  {
    title: '身份证号',
    dataIndex: 'idCardNo',
    width: 100,
    align: 'left',
  },
  {
    title: '就诊诊断',
    dataIndex: 'diagnoseName',
    width: 100,
    align: 'left',
  },
  {
    title: '就诊科室',
    dataIndex: 'visitDeptName',
    width: 100,
    align: 'left',
  },
  {
    title: '就诊医生',
    dataIndex: 'visitDoctorName',
    width: 100,
    align: 'left',
  },
];
