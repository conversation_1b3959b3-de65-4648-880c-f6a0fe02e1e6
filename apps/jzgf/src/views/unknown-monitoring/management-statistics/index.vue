<script setup lang="ts">
  import type { ActionItem, BasicColumn } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button } from 'ant-design-vue';
  import { useRouter } from 'vue-router';
  import { computed } from 'vue';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import { searchFormSchema } from './data';
  import {
    exportManageStatisticsData,
    getManageStatisticsTableColumns,
    queryManageStatisticsData,
  } from '/@/api/unknown-monitoring/management-statistics';

  const { loading, data: tableHeader } = useRequest(getManageStatisticsTableColumns);
  const columns = computed(() => {
    const data =
      tableHeader.value?.map((item) => {
        return {
          title: item.columnName,
          dataIndex: item.columnCode,
          // width: item.fieldName == '申请机构' ? 200 : 100,
        };
      }) || [];
    return data;
  });
  const router = useRouter();
  const [registerTable, tableIns] = useTable({
    api: queryManageStatisticsData,
    columns,
    formConfig: {
      colon: true,
      labelWidth: 90,
      showAdvancedButton: false,
      actionColOptions: {
        span: 6,
        style: {
          textAlign: 'right',
        },
      },
      schemas: searchFormSchema,
      fieldMapToTime: [['reportTime', ['reportStartTime', 'reportEndTime'], 'YYYY-MM-DD']],
    },
    useSearchForm: true,
    showIndexColumn: false,
    bordered: false,
    size: 'small',
    resizeHeightOffset: 20,
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
    loading: loading,
  });

  function handleDetail(record, _column) {
    router.push({
      name: 'ManagementStatisticsDetail',
      query: {
        ...tableIns.getForm().getFieldsValue(),
        reportHospitalCode: record.reportHospitalCode,
      },
    });
  }

  function createActions(record, column: BasicColumn): ActionItem[] {
    const actions: ActionItem[] = [];

    actions.push({
      label: '统计详情',
      type: 'link',
      onClick: handleDetail.bind(null, record, column),
    });
    return actions;
  }
  const { loading: exportLoading, runAsync: exportRunAsync } = useRequest(
    exportManageStatisticsData,
    {
      manual: true,
      showSuccessMessage: true,
    },
  );
  function onExport() {
    exportUtil(exportRunAsync(tableIns.getForm().getFieldsValue()));
  }
</script>

<template>
  <div class="w-full h-full pr-4">
    <BasicTable class="data-statistics" @register="registerTable">
      <template #headerTop>
        <div class="text-16px font-bold">不明原因传染病管理统计数据</div>
      </template>
      <template #toolbar>
        <Button @click="onExport" :loading="exportLoading"> 导出 </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>

<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }
  }
</style>
