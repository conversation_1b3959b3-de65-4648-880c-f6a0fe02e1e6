<script setup lang="ts">
  import type { ActionItem, BasicColumn } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button } from 'ant-design-vue';
  import { nextTick, onMounted } from 'vue';
  import { useRouter } from 'vue-router';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { setActiveRoutePathCache } from '@ft/internal/utils/auth';
  import {
    deletePatient,
    exportPatient,
    getPatientPage,
  } from '/@/api/unknown-monitoring/patient-management';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import { detailColumns, searchFormSchema } from './data';
  const router = useRouter();
  const year = useRouteQuery('year', '', { transform: String });
  const selectOrg = useRouteQuery('reportHospitalCode', '', { transform: String });
  const startTime = useRouteQuery('reportStartTime', '', { transform: String });
  const endTime = useRouteQuery('reportEndTime', '', { transform: String });
  onMounted(() => {
    const values: Recordable = {
      year: year.value,
      reportHospitalCode: selectOrg.value,
    };
    if (startTime.value && endTime.value) {
      values.reportTime = [startTime.value, endTime.value];
    }
    tableInstance.getForm().setFieldsValue(values);
    nextTick(() => {
      tableInstance.getForm().submit();
    });
  });
  const [registerTable, tableInstance] = useTable({
    api: getPatientPage,
    columns: detailColumns,
    formConfig: {
      colon: true,
      labelWidth: 90,
      showAdvancedButton: false,
      showActionButtonGroup: false,
      actionColOptions: {
        span: 6,
        style: {
          textAlign: 'right',
        },
      },
      schemas: searchFormSchema,
      disabled: true,
      fieldMapToTime: [['reportTime', ['reportStartTime', 'reportEndTime'], 'YYYY-MM-DD']],
    },
    immediate: false,
    useSearchForm: true,
    showIndexColumn: true,
    bordered: false,
    size: 'small',
    resizeHeightOffset: 20,
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
    },
  });

  function handleDetail(record, _column) {
    setActiveRoutePathCache(router.currentRoute.value.path);
    router.push({
      name: 'PatientDetail',
      query: {
        patientId: record.id,
      },
    });
  }
  const { runAsync: deletePatientRunAsync } = useRequest(deletePatient, {
    manual: true,
    showSuccessMessage: true,
    onSuccess() {
      tableInstance.reload();
    },
  });
  function handleDel(record) {
    deletePatientRunAsync(record.id);
  }
  function createActions(record, column: BasicColumn): ActionItem[] {
    const actions: ActionItem[] = [];

    actions.push(
      {
        label: '患者详情',
        type: 'link',
        onClick: handleDetail.bind(null, record, column),
      },
      {
        label: '删除',
        type: 'link',
        danger: true,
        popConfirm: {
          title: '删除当前病例',
          content: '确定删除当前病例吗？',
          confirm: handleDel.bind(null, record),
        },
      },
    );
    return actions;
  }
  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(exportPatient, {
    manual: true,
    showSuccessMessage: true,
  });
  function onExportExpert() {
    exportUtil(
      exportExpertRunAsync({
        ...tableInstance.getForm().getFieldsValue(),
      }),
    );
  }
</script>

<template>
  <div class="w-full h-full pr-4">
    <BasicTable class="data-statistics" @register="registerTable">
      <template #headerTop>
        <div class="text-16px font-bold">不明原因传染病管理患者列表</div>
      </template>
      <template #toolbar>
        <Button @click="onExportExpert" :loading="exportLoading"> 导出 </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>

<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }
  }
</style>
