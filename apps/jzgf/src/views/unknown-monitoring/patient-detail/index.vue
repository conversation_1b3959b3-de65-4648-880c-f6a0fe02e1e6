<script setup lang="ts">
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { useRequest } from '@ft/request';
  import { getPatientDetail } from '/@/api/unknown-monitoring/patient-management';
  import { ref } from 'vue';
  import VisitDetails from '../../../../../sjyy/src/views/doctors-view/VisitDetails.vue';
  const patientId = useRouteQuery('patientId', '', { transform: String });
  const propItem = ref({
    id: '',
    visitTypeCode: '',
    inpatientNo: '',
  });
  const { data: patientInfo } = useRequest(() => getPatientDetail(patientId.value), {
    onSuccess: (data) => {
      propItem.value.id = data?.pkVisitInfo || '';
      propItem.value.visitTypeCode = '4';
      propItem.value.inpatientNo = data?.inpatientNo || '';
    },
  });
</script>
<template>
  <div class="w-full h-full pr-4">
    <div ref="VisitDetailsRef" class="flex gap-10px flex-col min-w-0 h-full">
      <div class="rounded-2 bg-#fff p-4">
        <div class="text-16px font-bold mb-4">患者基本信息</div>
        <div class="flex flex-col gap-4 py-4 px-6">
          <div class="flex">
            <!-- <div class="flex-1">
              <span class="text-#999">患者诊疗卡号：</span>
              <span class="text-#333">{{ patientInfo?.medicalCardNo || '-' }}</span>
            </div> -->
            <div class="flex-1">
              <span class="text-#999">患者姓名：</span>
              <span class="text-#333">{{ patientInfo?.patientName || '-' }}</span>
            </div>
            <div class="flex-1">
              <span class="text-#999">性别：</span>
              <span class="text-#333">{{ patientInfo?.sexName || '-' }}</span>
            </div>
            <div class="flex-1">
              <span class="text-#999">年龄：</span>
              <span class="text-#333">{{ patientInfo?.age || '-' }}</span>
            </div>
            <div class="flex-1">
              <span class="text-#999">身份证号：</span>
              <span class="text-#333">{{ patientInfo?.idCardNo || '-' }}</span>
            </div>
            <div class="flex-1">
              <span class="text-#999">所属区划：</span>
              <span class="text-#333">{{ patientInfo?.divisionName || '-' }}</span>
            </div>
          </div>
          <div class="flex">
            <div class="flex-1">
              <span class="text-#999">入院时间：</span>
              <span class="text-#333">{{ patientInfo?.admitHospitalTime || '-' }}</span>
            </div>
            <div class="flex-1">
              <span class="text-#999">就诊诊断：</span>
              <span class="text-#333">{{ patientInfo?.diagnoseName || '-' }}</span>
            </div>
            <div class="flex-1">
              <span class="text-#999">就诊机构：</span>
              <span class="text-#333">{{ patientInfo?.visitHospitalName || '-' }}</span>
            </div>
            <!-- <div class="flex-1">
              <span class="text-#999">就诊科室：</span>
              <span class="text-#333">{{ patientInfo?.visitDeptName || '-' }}</span>
            </div>
            <div class="flex-1">
              <span class="text-#999">医生：</span>
              <span class="text-#333">{{ patientInfo?.visitDoctorName || '-' }}</span>
            </div> -->
            <div class="flex-1"> </div>
            <div class="flex-1"> </div>
          </div>
        </div>
      </div>
      <div class="flex-1 bg-#fff rounded-2 p-4 flex flex-col gap-4">
        <div class="text-16px font-bold">患者诊疗详情</div>
        <VisitDetails
          v-if="patientInfo?.idCardNo"
          :patientInfo="{ ...patientInfo, patientType: 1 }"
          :activeItem="propItem"
        />
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped></style>
