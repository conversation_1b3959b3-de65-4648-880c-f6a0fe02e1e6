<script setup lang="ts">
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { Tabs, notification } from 'ant-design-vue';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { computed, h, nextTick, onBeforeUnmount, ref, watch } from 'vue';
  import { router } from '@ft/internal/router';
  import { exportPatient, getPatientPage } from '/@/api/unknown-monitoring/patient-management';
  import {
    getPatientInfoForRemind,
    updateViewStatus,
  } from '/@/api/unknown-monitoring/auxiliary-diagnosis';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import { setActiveRoutePathCache } from '@ft/internal/utils/auth';
  import { columns, formSchema } from './data';
  onBeforeUnmount(() => {
    notification.destroy();
  });
  useRequest(getPatientInfoForRemind, {
    onSuccess: (data) => {
      if (data) {
        const key = `open${Date.now()}`;
        notification.open({
          message: '不明原因传染病智能提醒',
          description: `请注意，${data.visitHospitalName}监测到有不明原因传染病病例`,
          placement: 'bottomRight',
          duration: null,
          btn: () =>
            h(
              Button,
              {
                type: 'primary',
                size: 'small',
                onClick: () => close(key, data.id),
              },
              { default: () => '关闭' },
            ),
          key,
          onClose: () => close(key, data.id),
        });
      }
    },
  });
  const close = (key, id) => {
    runAsyncUpdateViewStatus(id);
    notification.close(key);
  };
  const { runAsync: runAsyncUpdateViewStatus } = useRequest(updateViewStatus, {
    manual: true,
  });
  //1-手动上报病例，2-自动监测病例
  const activeTab = ref('1');
  watch(
    activeTab,
    () => {
      nextTick(() => {
        tableInstance.reload();
      });
    },
    {
      immediate: true,
    },
  );
  const [registerTable, tableInstance] = useTable({
    fetchSetting: {
      sizeField: 'pageSize',
      pageField: 'pageNum',
      listField: 'list',
    },
    columns: computed(() => {
      if (activeTab.value === '1') {
        return columns;
      } else {
        const newColumns = [...columns];
        newColumns.splice(0, 1, {
          title: '监测时间',
          dataIndex: 'monitorTime',
          width: 100,
          align: 'left',
        });
        return newColumns;
      }
    }),
    api: getPatientPage,
    formConfig: {
      colon: true,
      labelWidth: 100,
      showAdvancedButton: false,
      actionColOptions: {
        span: 18,
        style: {
          textAlign: 'right',
        },
      },
      schemas: formSchema,
      fieldMapToTime: [['reportTime', ['reportStartTime', 'reportEndTime'], 'YYYY-MM-DD']],
    },
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
    beforeFetch: (params) => {
      params.caseType = activeTab.value;
      return params;
    },
    useSearchForm: true,
    immediate: false,
    showIndexColumn: true,
    bordered: false,
    size: 'small',
    resizeHeightOffset: 10,
  });
  function handleDetail(record, _column) {
    setActiveRoutePathCache(router.currentRoute.value.path);
    router.push({
      name: 'PatientDetail',
      query: {
        // tabActiveKey: 'AideDiagnosisDetails',
        patientId: record.id,
      },
    });
  }
  function createActions(record, column): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '患者详情',
        type: 'link',
        size: 'small',
        onClick: handleDetail.bind(null, record, column),
      },
    ];
    return actions;
  }
  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(exportPatient, {
    manual: true,
    showSuccessMessage: true,
  });
  function onExportExpert() {
    exportUtil(
      exportExpertRunAsync({
        ...tableInstance.getForm().getFieldsValue(),
        caseType: activeTab.value,
      }),
    );
  }
</script>
<template>
  <div class="w-full h-full pr-4">
    <BasicTable class="ft-main-table" @register="registerTable">
      <template #headerTop>
        <div class="text-16px font-500">不明原因传染病患者列表</div>
        <Tabs v-model:activeKey="activeTab">
          <Tabs.TabPane key="1" tab="手动上报病例" />
          <Tabs.TabPane key="2" tab="自动监测病例" />
        </Tabs>
      </template>
      <template #toolbar>
        <Button @click="onExportExpert" :loading="exportLoading"> 导出 </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }
  }
</style>
