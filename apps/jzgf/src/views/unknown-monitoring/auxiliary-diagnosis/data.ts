import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import {
  getReportDoctorList,
  getReportHospitalList,
} from '/@/api/unknown-monitoring/auxiliary-diagnosis';
export const formSchema: FormSchema[] = [
  {
    field: 'reportTime',
    label: '上报/监测日期',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      style: { width: '100%' },
    },
    colProps: { span: 6 },
  },
  {
    field: 'reportHospitalCode',
    label: '上报医院',
    component: 'ApiSelect',
    componentProps: () => ({
      api: getReportHospitalList,
      labelField: 'text',
      valueField: 'value',
      defaultValue: '',

      getPopupContainer: () => document.body,
    }),
    colProps: { span: 6 },
  },
  {
    field: 'reportDoctorId',
    label: '上报医生',
    component: 'ApiSelect',
    componentProps: () => ({
      api: getReportDoctorList,
      labelField: 'text',
      valueField: 'value',
      defaultValue: '',
      getPopupContainer: () => document.body,
    }),
    colProps: { span: 6 },
  },
  {
    field: 'auxiliaryStatus',
    label: '辅助状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '待辅助', value: '0' },
        { label: '已辅助', value: '1' },
      ],
      defaultValue: '',
    },
    colProps: { span: 6 },
  },
  {
    field: 'patientName',
    label: '患者姓名',
    component: 'Input',
    colProps: { span: 6 },
  },
];

export const columns: BasicColumn[] = [
  {
    title: '上报时间',
    dataIndex: 'reportTime',
    width: 100,
    align: 'left',
  },
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    width: 100,
    align: 'left',
  },
  {
    title: '性别',
    dataIndex: 'sexName',
    width: 100,
    align: 'left',
  },
  {
    title: '年龄',
    dataIndex: 'age',
    width: 100,
    align: 'left',
  },
  {
    title: '入院时间',
    dataIndex: 'admitHospitalTime',
    width: 100,
    align: 'left',
  },
  {
    title: '诊断',
    dataIndex: 'diagnoseName',
    width: 100,
    align: 'left',
  },
  {
    title: '就诊机构',
    dataIndex: 'visitHospitalName',
    width: 100,
    align: 'left',
  },
  {
    title: '就诊科室',
    dataIndex: 'visitDeptName',
    width: 100,
    align: 'left',
  },
  {
    title: '就诊医生',
    dataIndex: 'visitDoctorName',
    width: 100,
    align: 'left',
  },
  {
    title: '上报医生',
    dataIndex: 'reportDoctorName',
    width: 100,
    align: 'left',
  },
  {
    title: '辅助状态',
    dataIndex: 'auxiliaryStatus',
    width: 100,
    align: 'left',
  },
];
