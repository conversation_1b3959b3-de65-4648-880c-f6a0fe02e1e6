<script setup lang="ts">
  import { BasicTitle } from '@ft/internal/components/Basic';
  import { computed, reactive, ref, watchEffect } from 'vue';
  import type { FormInstance } from 'ant-design-vue';
  import { Col, Form, Input, Row } from 'ant-design-vue';
  import type { SurveyFormModel } from '/@/api/task';
  import type { IQualityControlTemplateDetail } from '/@/api/template';
  import { has, merge, pick } from 'lodash-es';

  const FormItem = Form.Item;

  const props = withDefaults(
    defineProps<{
      templateName?: string;
      orgName?: string;
      createUser?: string;
      phone?: string;
      indexCategoryList?: any[];
      templateId?: string;
      taskId?: string;
      taskRecordId?: string;
      formModal?: SurveyFormModel;
      disabled?: boolean;
      isTemplate?: boolean;
    }>(),
    {
      templateName: '',
      orgName: '',
      createUser: '',
      phone: '',
      indexCategoryList: () => [],
      disabled: false,
      isTemplate: false,
    },
  );

  const formRef = ref<FormInstance>();
  const surveyItemListModel = reactive<SurveyFormModel>({
    phone: '',
    surveyItemList: [],
    taskStatus: 0,
    updateUser: '',
  });

  watchEffect(() => {
    surveyItemListModel.surveyItemList = (
      props.indexCategoryList as IQualityControlTemplateDetail['indexCategoryList']
    )?.reduce((acc, cur) => {
      const surveyItem: SurveyFormModel['surveyItemList'] = [];
      cur.indexItemList
        ?.sort((a, b) => a.sortNum - b.sortNum)
        .forEach((item, idx) => {
          const surveyFormItem =
            props.formModal?.surveyItemList?.find((surveyItem) => surveyItem.indexId === item.id) ||
            {};

          surveyItem.push(
            merge(
              {
                categoryId: cur.id,
                indexCategory: cur.indexCategory,
                indexId: item.id,
                indexName: item.indexName,
                indexValue: '',
                taskId: props.taskId,
                templateId: props.templateId,

                colSlot: idx === 0 ? 'moduleName' : '',
                isRequired: item.isRequired,
                isShow: item.isShow,
                taskRecordId: props.taskRecordId,
              },
              pick(surveyFormItem, 'indexValue'),
            ),
          );
        });
      acc.push(...surveyItem);
      return acc;
    }, [] as SurveyFormModel['surveyItemList']);

    surveyItemListModel.phone = props.formModal?.phone ?? props.phone;
    surveyItemListModel.updateUser = props.formModal?.updateUser ?? props.createUser;
    surveyItemListModel.taskStatus = props.formModal?.taskStatus ?? 0;
  });

  const activeIndex = ref(0);
  function onTabClick(idx: number) {
    activeIndex.value = idx;
    handleNavChange(idx);
  }

  const elRefs = ref<HTMLDivElement[]>([]);
  function setRef(el: HTMLDivElement) {
    if (el && !elRefs.value.includes(el)) {
      const idx = elRefs.value.findIndex((item) => item === el);
      if (idx > 0) {
        elRefs.value.splice(idx, 1, el);
      } else {
        elRefs.value.push(el);
      }
    }
  }

  function handleNavChange(idx: number) {
    if (idx === -1) return;
    elRefs.value[idx].scrollIntoView({
      block: 'start',
      behavior: 'smooth',
    });
  }

  const indexTypeList = computed(() => {
    return surveyItemListModel.surveyItemList?.reduce((acc, cur) => {
      if (!acc.find((item) => item.field === cur.indexCategory)) {
        acc.push({
          field: cur.indexCategory,
          label: cur.indexCategory,
        });
      }
      return acc;
    }, [] as { field: string; label: string }[]);
  });

  function validate() {
    return formRef.value?.validate();
  }

  defineExpose({ validate });
</script>

<template>
  <div class="flex flex-col gap-2.5 w-full h-full">
    <div class="header rounded-lg rounded-lt-none bg-white p-4">
      <BasicTitle class="mb-4" span normal>质控调查任务</BasicTitle>
      <div class="mb-2">
        <span class="text-info-text-color">{{ isTemplate ? '任务调查模板：' : '任务名称：' }}</span>
        <span>{{ templateName }}</span>
      </div>
      <div class="info flex gap-10">
        <div>
          <span class="text-info-text-color">单位名称：</span>
          <span>{{ orgName }}</span>
        </div>
        <div>
          <span class="text-info-text-color">填报人：</span>
          <span>{{ createUser }}</span>
        </div>
        <div>
          <span class="text-info-text-color">联系电话：</span>
          <span>{{ phone }}</span>
        </div>
      </div>
    </div>
    <div class="content flex flex-col flex-1 rounded-lg bg-white p-4">
      <BasicTitle class="mb-4" normal>指标填报</BasicTitle>

      <div class="tabs flex flex-wrap">
        <div
          class="tab-item min-w-263px truncate bg-[#FFFAFC] text-center py-2 cursor-pointer text-secondary-text-color hover:text-primary-color"
          v-for="(type, idx) in indexTypeList"
          :class="[activeIndex === idx ? '!bg-[#FFEBF2] rounded !text-primary-color' : '']"
          :key="type.field + idx"
          @click="onTabClick(idx)"
        >
          {{ type.label }}
        </div>
      </div>
      <div class="preview-form py-4 flex-1 of-y-auto basis-0 pr-4">
        <Form ref="formRef" :model="surveyItemListModel" layout="vertical">
          <FormItem v-show="false" name="id" />
          <FormItem v-show="false" name="phone" />
          <FormItem v-show="false" name="taskStatus" />
          <Row
            v-for="(surveyItem, index) in surveyItemListModel.surveyItemList"
            :key="surveyItem.indexId"
          >
            <Col :span="24">
              <BasicTitle class="mb-4" normal v-if="surveyItem.colSlot === 'moduleName'">
                <div :ref="setRef">{{ surveyItem.indexCategory }}</div>
              </BasicTitle>

              <FormItem v-show="false" :name="['surveyItemList', index, 'id']" />
              <FormItem v-show="false" :name="['surveyItemList', index, 'categoryId']" />
              <FormItem v-show="false" :name="['surveyItemList', index, 'indexCategory']" />
              <FormItem v-show="false" :name="['surveyItemList', index, 'indexId']" />
              <FormItem v-show="false" :name="['surveyItemList', index, 'taskId']" />
              <FormItem v-show="false" :name="['surveyItemList', index, 'templateId']" />
              <FormItem v-show="false" :name="['surveyItemList', index, 'indexName']" />
              <FormItem v-show="false" :name="['surveyItemList', index, 'taskRecordId']" />

              <FormItem
                :required="has(surveyItem, 'isRequired') ? surveyItem.isRequired === 0 : true"
                v-if="has(surveyItem, 'isShow') ? surveyItem.isShow === 0 : true"
                :label="surveyItem.indexName"
                :name="['surveyItemList', index, 'indexValue']"
              >
                <Input
                  v-model:value="surveyItemListModel.surveyItemList[index].indexValue"
                  :disabled="disabled"
                />
              </FormItem>
            </Col>
          </Row>
        </Form>
      </div>
    </div>
  </div>
</template>
