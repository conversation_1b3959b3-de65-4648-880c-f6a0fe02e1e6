<script setup lang="ts">
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { Descriptions, DescriptionsItem } from 'ant-design-vue';
  import { useRequest } from '@ft/request';
  import { reportPatient } from '/@/api/important-monitoring/case-report';
  import type { IPatientListItem } from '/@/api/important-monitoring/case-report';
  import { ref } from 'vue';
  const emit = defineEmits(['register', 'success']);
  const patientInfo = ref<IPatientListItem>();
  const [register, { closeModal }] = useModalInner((data) => {
    patientInfo.value = data.record;
  });

  const { loading, runAsync: runAsyncReportPatient } = useRequest(
    () => reportPatient(patientInfo.value?.id as string),
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess() {
        emit('success', '');
        closeModal();
      },
    },
  );
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    title="重点（重症）病例一键上报"
    :can-fullscreen="false"
    width="900px"
    centered
    @register="register"
    :ok-button-props="{
      loading,
    }"
    @ok="runAsyncReportPatient"
  >
    <div class="bg-#F5F7FA p-4 pb-0 rounded-4px">
      <Descriptions :label-style="{ color: '#B0B1B4' }" :content-style="{ color: '#252931' }">
        <DescriptionsItem label="患者姓名">{{ patientInfo?.patientName }}</DescriptionsItem>
        <DescriptionsItem label="性别">{{ patientInfo?.sexName }}</DescriptionsItem>
        <DescriptionsItem label="年龄">{{ patientInfo?.age }}</DescriptionsItem>
        <DescriptionsItem label="初步诊断">{{ patientInfo?.diagnoseName }}</DescriptionsItem>
      </Descriptions>
    </div>
    <div class="text-base my-4">
      <span>请确认是否将此患者病例上报到诊疗中心进行辅助诊断?</span>
    </div>
  </BasicModal>
</template>
