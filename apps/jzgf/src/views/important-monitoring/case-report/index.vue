<script setup lang="ts">
  import { h, nextTick, onBeforeUnmount, ref, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import type { ActionItem, BasicColumn } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Tabs, notification } from 'ant-design-vue';
  import { Button } from '@ft/internal/components/Button';
  import { useModal } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { setActiveRoutePathCache } from '@ft/internal/utils/auth';
  import { columns, formSchema } from './data';
  import ReportModal from './ReportModal.vue';
  import {
    getPatientInfoForAssistedDiagnosis,
    getPatientPage,
    updateAssistedDiagnosisViewStatus,
  } from '/@/api/important-monitoring/case-report';
  onBeforeUnmount(() => {
    notification.destroy();
  });
  useRequest(getPatientInfoForAssistedDiagnosis, {
    onSuccess: (data) => {
      if (data) {
        const key = `open${Date.now()}`;
        notification.open({
          message: '重点/重症病例诊疗建议提醒',
          description: `请注意，诊疗中心已对${data.patientName}患者进行辅助诊断建议`,
          placement: 'bottomRight',
          duration: null,
          btn: () =>
            h(
              Button,
              {
                type: 'primary',
                size: 'small',
                onClick: () => close(key, data.id),
              },
              { default: () => '关闭' },
            ),
          key,
          onClose: () => close(key, data.id),
        });
      }
    },
  });
  const close = (key, id) => {
    runAsyncUpdateViewStatus(id);
    notification.close(key);
  };
  const { runAsync: runAsyncUpdateViewStatus } = useRequest(updateAssistedDiagnosisViewStatus, {
    manual: true,
  });
  // const { userInfo } = useUserStore();
  // onMounted(() => {
  //   tableInstance.getForm().setFieldsValue({
  //     visitDeptName: userInfo?.deptName,
  //   });
  //   nextTick(() => {
  //     tableInstance.getForm().submit();
  //   });
  // });

  const router = useRouter();
  //上报状态：0-未上报，1-已上报
  const activeKey = ref('0');
  const [registerTable, tableInstance] = useTable({
    fetchSetting: {
      sizeField: 'pageSize',
      pageField: 'pageNum',
      listField: 'list',
    },
    api: getPatientPage,
    columns,
    formConfig: {
      colon: true,
      labelWidth: 100,
      showAdvancedButton: false,
      actionColOptions: {
        span: 24,
        style: {
          textAlign: 'right',
        },
      },
      schemas: formSchema,
      fieldMapToTime: [['visitDate', ['visitStartTime', 'visitEndTime'], 'YYYY-MM-DD']],
    },
    useSearchForm: true,
    showIndexColumn: true,
    immediate: true,
    bordered: false,
    size: 'small',
    resizeHeightOffset: 20,
    beforeFetch: (params) => {
      params.reportStatus = activeKey.value;
      return params;
    },
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
    },
  });

  watch(
    activeKey,
    () => {
      nextTick(() => {
        tableInstance.reload();
      });
    },
    // {
    //   immediate: true,
    // },
  );
  // const [registerAddModal, { openModal: openAddModal }] = useModal();
  // function handleAddPatient() {
  //   openAddModal(true, {
  //     mode: 'add',
  //   });
  // }

  function handleDetail(record, _column) {
    setActiveRoutePathCache(router.currentRoute.value.path);
    router.push({
      name: 'PatientDetail',
      query: {
        patientId: record.id,
      },
    });
  }
  const [registerReportModal, { openModal: openReportModal }] = useModal();
  function handleReport(record, _column) {
    openReportModal(true, { record });
  }
  function createActions(record, column: BasicColumn): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '患者详情',
        type: 'link',
        size: 'small',
        onClick: handleDetail.bind(null, record, column),
      },
    ];
    if (activeKey.value === '0') {
      actions.push({
        label: '一键上报',
        type: 'link',
        size: 'small',
        onClick: handleReport.bind(null, record, column),
      });
    }
    return actions;
  }
  // const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(exportPatient, {
  //   manual: true,
  //   showSuccessMessage: true,
  // });
  // function onExportExpert() {
  //   exportUtil(
  //     exportExpertRunAsync({
  //       ...tableInstance.getForm().getFieldsValue(),
  //       reportStatus: activeKey.value,
  //     }),
  //   );
  // }
</script>

<template>
  <div class="w-full h-full pr-4">
    <BasicTable class="ft-main-table" @register="registerTable">
      <template #headerTop>
        <div class="text-16px font-500">重点（重症）传染病患者列表</div>
        <Tabs v-model:activeKey="activeKey">
          <Tabs.TabPane key="0" tab="未上报数据" />
          <Tabs.TabPane key="1" tab="已上报数据" />
        </Tabs>
      </template>
      <!-- <template #tableTitle>
        <Button type="primary" @click="handleAddPatient"> 新增患者 </Button>
      </template>
      <template #toolbar>
        <Button @click="onExportExpert" :loading="exportLoading"> 导出 </Button>
      </template> -->
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
    <!-- <AddModal @register="registerAddModal" @success="tableInstance.reload" /> -->
    <ReportModal @register="registerReportModal" @success="tableInstance.reload" />
  </div>
</template>

<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }
  }
</style>
