import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';

/**
 * @description: 表格列
就诊时间
患者来源
患者姓名 
就诊卡号
门诊号/住院号
性别 
年龄
疾病诊断
就诊科室
就诊医院
 */
export const columns: BasicColumn[] = [
  {
    title: '就诊时间',
    dataIndex: 'visitDate',
    width: 150,
    align: 'left',
  },
  {
    title: '患者来源',
    dataIndex: 'visitType',
    width: 100,
    align: 'left',
  },
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    width: 100,
    align: 'left',
  },
  // {
  //   title: '就诊卡号',
  //   dataIndex: 'medicalCardNo',
  //   width: 100,
  //   align: 'left',
  // },
  {
    title: '门诊号/住院号',
    dataIndex: 'outInId',
    width: 100,
    align: 'left',
  },
  {
    title: '性别',
    dataIndex: 'sexName',
    width: 100,
    align: 'left',
  },
  {
    title: '年龄',
    dataIndex: 'age',
    width: 100,
    align: 'left',
  },
  {
    title: '疾病诊断',
    dataIndex: 'diagnoseName',
    width: 150,
    align: 'left',
  },
  {
    title: '就诊科室',
    dataIndex: 'visitDeptName',
    width: 100,
    align: 'left',
  },
  {
    title: '就诊医院',
    dataIndex: 'visitHospitalName',
    width: 120,
    align: 'left',
  },
];
/**
 * @description: 搜索表单
 * 就诊时间
 * 患者诊断
 * 患者就诊卡号
 * 门诊号/住院号
 * 患者姓名
 */

export const formSchema: FormSchema[] = [
  {
    field: 'visitDate',
    component: 'RangePicker',
    label: '就诊时间',
    colProps: {
      span: 6,
    },
    componentProps: {
      style: {
        width: '100%',
      },
    },
  },
  {
    field: 'diagnoseName',
    component: 'Input',
    label: '患者诊断',
    colProps: { span: 6 },
  },
  // {
  //   field: 'medicalCardNo',
  //   component: 'Input',
  //   label: '患者就诊卡号',
  //   colProps: { span: 6 },
  // },
  {
    field: 'outInId',
    component: 'Input',
    label: '门诊号/住院号',
    colProps: { span: 6 },
  },
  {
    field: 'patientName',
    component: 'Input',
    label: '患者姓名',
    colProps: { span: 6 },
  },
];
