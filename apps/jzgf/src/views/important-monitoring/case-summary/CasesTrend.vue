<script setup lang="ts">
  import { ref, watchEffect } from 'vue';
  import { useECharts } from '@ft/internal/hooks/web/useECharts';
  import { graphic } from 'echarts/core';
  const props = defineProps({
    data: {
      type: Array,
      default: () => [],
    },
  });
  const chartRef = ref<HTMLDivElement>();
  const { setOptions } = useECharts(chartRef as any);

  watchEffect(() => {
    setOptions({
      tooltip: {
        trigger: 'axis',
        appendToBody: true,
      },
      grid: {
        left: '8%',
        right: '8%',
        bottom: '10%',
      },
      legend: {
        data: ['重点（重症）传染病病例数'],
      },
      xAxis: {
        data: props.data.map((item: any) => item.xname),
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#B0B1B4',
        },
        axisLine: {
          lineStyle: {
            color: '#EDEEF0',
            // color: '#FF6DA0',
          },
        },
      },
      yAxis: {
        splitLine: {
          lineStyle: {
            color: '#EDEEF0',
            // color: '#FF6DA0',
          },
          interval: 30,
        },
        axisLabel: {
          color: '#B0B1B4',
        },
      },
      series: [
        {
          name: '重点（重症）传染病病例数',
          type: 'line',
          data: props.data.map((item: any) => item.yvalue),
          smooth: true,
          itemStyle: {
            // color: '#33BC71',
            color: '#FF6DA0',
          },
          areaStyle: {
            color: new graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(255, 236, 243, 1)',
              },
              {
                offset: 1,
                color: 'rgba(255, 235, 242, 0)',
              },
            ]),
          },
        },
      ],
    });
  });
</script>

<template>
  <div ref="chartRef" class="w-full h-full"></div>
</template>
