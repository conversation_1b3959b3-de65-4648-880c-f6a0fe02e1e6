<script setup lang="ts">
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import { onMounted } from 'vue';
  import { columns, formSchema } from './data';
  import CasesTrend from './CasesTrend.vue';
  import { exportPatient, getPatientPage } from '/@/api/important-monitoring/case-report';
  import { getInpatientCasesCount } from '/@/api/important-monitoring/case-summary';
  const { data: inpatientCasesCount, runAsync: runAsyncGetInpatientCasesCount } = useRequest(
    getInpatientCasesCount,
    {
      manual: true,
    },
  );
  onMounted(async () => {
    await tableInstance.getForm().submit();
  });
  const [registerTable, tableInstance] = useTable({
    api: getPatientPage,
    columns,
    formConfig: {
      colon: true,
      labelWidth: 90,
      showAdvancedButton: false,
      actionColOptions: {
        span: 6,
        style: {
          textAlign: 'right',
        },
      },
      schemas: formSchema,
      fieldMapToTime: [['monitorTime', ['monitorStartTime', 'monitorEndTime'], 'YYYY-MM-DD']],
      submitFunc: async () => {
        await runAsyncGetInpatientCasesCount({ ...tableInstance.getForm().getFieldsValue() });
        await tableInstance.reload();
      },
    },
    useSearchForm: true,
    immediate: false,
    showIndexColumn: true,
    bordered: false,
    minHeight: 240,
    // actionColumn: {
    //   width: 120,
    //   title: '操作',
    //   dataIndex: 'action',
    // },
  });
  // function createActions(record, column: BasicColumn): ActionItem[] {
  //   const actions: ActionItem[] = [];

  //   actions.push({
  //     label: '详情',
  //     type: 'link',
  //     onClick: handleDetail.bind(null, record, column),
  //   });
  //   return actions;
  // }
  // function handleDetail(record, _column) {
  //   setActiveRoutePathCache(router.currentRoute.value.path);
  //   router.push({
  //     name: 'PatientDetail',
  //     query: {
  //       patientId: record.id,
  //     },
  //   });
  // }
  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(exportPatient, {
    manual: true,
    showSuccessMessage: true,
  });
  function onExportExpert() {
    exportUtil(exportExpertRunAsync(tableInstance.getForm().getFieldsValue()));
  }
</script>
<template>
  <div class="!h-[calc(100vh-110px)] of-y-auto pr-4">
    <BasicTable @register="registerTable">
      <template #headerTop>
        <div class="h-300px flex flex-col">
          <div class="flex items-center gap-2">
            <span class="inline-block bg-primary-color w-3px h-1em"></span>
            <span class="text-#333333 fw-bold">传染病重点（重症）病例数趋势图</span>
          </div>
          <div class="flex-1 of-hidden p-4">
            <CasesTrend :data="inpatientCasesCount" />
          </div>
        </div>
      </template>
      <template #tableTitle>
        <div class="flex items-center gap-2">
          <span class="inline-block bg-primary-color w-3px h-1em"></span>
          <span class="text-#333333 fw-bold">传染病重点（重症）在院患者列表</span>
        </div>
      </template>
      <template #toolbar>
        <Button @click="onExportExpert" :loading="exportLoading"> 导出 </Button>
      </template>
      <!-- <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record, column)" />
        </template>
      </template> -->
    </BasicTable>
  </div>
</template>

<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }
  }
</style>
