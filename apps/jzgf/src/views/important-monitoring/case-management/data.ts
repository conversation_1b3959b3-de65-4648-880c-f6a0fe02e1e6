import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import {
  getReportDoctorList,
  getReportHospitalList,
} from '/@/api/important-monitoring/case-management';
export const formSchema: FormSchema[] = [
  {
    field: 'reportTime',
    label: '上报/监测日期',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      style: { width: '100%' },
    },
    colProps: { span: 6 },
  },
  {
    field: 'reportHospitalCode',
    label: '医疗机构',
    component: 'ApiSelect',
    componentProps: () => ({
      api: getReportHospitalList,
      labelField: 'text',
      valueField: 'value',
      defaultValue: '',

      getPopupContainer: () => document.body,
    }),
    colProps: { span: 6 },
  },
  {
    field: 'reportDoctorId',
    label: '上报医生',
    component: 'ApiSelect',
    componentProps: () => ({
      api: getReportDoctorList,
      labelField: 'text',
      valueField: 'value',
      defaultValue: '',
      getPopupContainer: () => document.body,
    }),
    colProps: { span: 6 },
  },
  {
    field: 'auxiliaryStatus',
    label: '辅助状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '全部', value: '' },
        { label: '待辅助', value: '0' },
        { label: '已辅助', value: '1' },
      ],
      defaultValue: '',
    },
    colProps: { span: 6 },
  },
  {
    field: 'visitTime',
    label: '就诊时间',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      style: { width: '100%' },
    },
    colProps: { span: 6 },
  },
  {
    field: 'diagnoseName',
    label: '传染病诊断',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'patientName',
    label: '患者姓名',
    component: 'Input',
    colProps: { span: 6 },
  },
];

export const columns: BasicColumn[] = [
  {
    title: '上报时间',
    dataIndex: 'reportTime',
    width: 150,
    align: 'left',
  },
  {
    title: '患者姓名',
    dataIndex: 'patientName',
    width: 100,
    align: 'left',
  },
  {
    title: '性别',
    dataIndex: 'sexName',
    width: 100,
    align: 'left',
  },
  {
    title: '年龄',
    dataIndex: 'age',
    width: 100,
    align: 'left',
  },
  {
    title: '就诊时间',
    dataIndex: 'visitDate',
    width: 150,
    align: 'left',
  },
  {
    title: '传染病诊断',
    dataIndex: 'diagnoseName',
    width: 150,
    align: 'left',
  },
  {
    title: '就诊机构',
    dataIndex: 'visitHospitalName',
    width: 150,
    align: 'left',
  },
  {
    title: '就诊科室',
    dataIndex: 'visitDeptName',
    width: 100,
    align: 'left',
  },
  {
    title: '就诊医生',
    dataIndex: 'visitDoctorName',
    width: 100,
    align: 'left',
  },
  {
    title: '上报医生',
    dataIndex: 'reportDoctorName',
    width: 100,
    align: 'left',
  },
  {
    title: '辅助状态',
    dataIndex: 'auxiliaryStatus',
    width: 100,
    align: 'left',
  },
];
