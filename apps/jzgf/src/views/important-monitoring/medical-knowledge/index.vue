<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import { StyledList } from '@ft/components';
  import { Button } from '@ft/internal/components/Button';
  import { useRequest } from '@ft/request';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { Dropdown, Empty, Input } from 'ant-design-vue';
  import { Icon } from '@ft/internal/components/Icon';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import { useModal } from '@ft/internal/components/Modal';
  import { schemasForm } from './data';
  import AddModal from './AddModal.vue';
  import {
    deleteMedicalKnowledge,
    getMedicalKnowledgeDetail,
    getMedicalKnowledgeList,
  } from '/@/api/important-monitoring/medical-knowledge';
  import { updateMedicalKnowledge } from '/@/api/unknown-monitoring/medical-knowledge';

  const activeIcdCode = ref<string>();
  const activeItem = ref<any>({});
  const isEdit = ref(false);
  const [registerForm, formAction] = useForm({
    labelWidth: 120,
    layout: 'vertical',
    disabled: computed(() => !isEdit.value),
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
    schemas: schemasForm,
  });
  const searchValue = ref('');
  const {
    loading,
    data: infectiousDiseaseList,
    runAsync: getInfectiousDiseaseListRunAsync,
  } = useRequest(() => getMedicalKnowledgeList(2), {
    onSuccess: (res) => {
      activeIcdCode.value = res.length > 0 ? res[0].icdCode : '';
      activeItem.value = res?.[0] || {};
    },
  });
  const items = computed(() => {
    return infectiousDiseaseList.value?.filter((item) => {
      return item?.knowledgeName?.includes(searchValue.value);
    });
  });
  const { runAsync: runAsyncGetMedicalKnowledgeDetail } = useRequest(getMedicalKnowledgeDetail, {
    manual: true,
    onSuccess: (data) => {
      formAction.setFieldsValue(data);
      isEdit.value = false;
      // if (data) {
      //   formAction.setFieldsValue(data);
      // } else {
      //   formAction.resetFields();
      //   isEdit.value = true;
      // }
    },
  });
  watch(
    () => activeIcdCode.value,
    (val) => {
      if (val) {
        console.log(62, val, activeItem.value);
        runAsyncGetMedicalKnowledgeDetail(activeItem.value.id);
      }
    },
  );

  const { runAsync: saveRunAsync, loading: saveLoading } = useRequest(updateMedicalKnowledge, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      isEdit.value = false;
      getInfectiousDiseaseListRunAsync();
    },
  });
  function handleSave() {
    formAction.validate().then((value) => {
      saveRunAsync({ ...value, knowledgeType: 2, knowledgeName: activeItem.value.knowledgeName });
    });
  }
  const [registerAddModal, { openModal: openAddModal }] = useModal();
  function handleAddInfectious() {
    openAddModal(true, {
      medicalKnowledgeList: infectiousDiseaseList,
    });
  }
  const { runAsync: delRunAsync } = useRequest(deleteMedicalKnowledge, {
    showSuccessMessage: true,
    manual: true,
    onSuccess: () => {
      getInfectiousDiseaseListRunAsync();
    },
  });
  const { createConfirm } = useMessage();
  function handleDel(item) {
    console.log(item);
    createConfirm({
      iconType: 'warning',
      title: '移除',
      content: `确定移除该项吗？`,
      onOk: () => {
        delRunAsync(item.id);
      },
    });
  }
</script>
<template>
  <div class="flex gap-2 h-[calc(100%-10px)] w-[calc(100%-16px)]">
    <div
      class="flex flex-col gap-2 w-228px bg-#fff rounded rounded-lt-none p-3 h-[calc(100vh-113px)]"
    >
      <span class="text-#333333 fw-bold">重点/重症传染病相关诊断</span>
      <div class="search flex items-center text-center gap-9px">
        <Input allow-clear placeholder="输入关键字" v-model:value="searchValue">
          <template #suffix>
            <Icon icon="ant-design:search-outlined" />
          </template>
        </Input>
        <div
          @click="handleAddInfectious"
          class="w-20px h-20px rounded-50% bg-primary-color cursor-pointer p-1 flex items-center"
        >
          <Icon color="#fff" icon="ant-design:plus-outlined" size="12" />
        </div>
      </div>
      <div class="pr-4 of-y-auto of-x-hidden min-w-0">
        <StyledList
          :items="items"
          v-model="activeIcdCode"
          v-model:value="activeItem"
          valueField="icdCode"
          label-field="knowledgeName"
          class="flex-1"
          :width="216"
          :loading="loading"
        >
          <template #default="item">
            <div class="flex justify-between items-center w-full relative">
              <div class="w-[calc(100%-40px)] overflow-hidden truncate"
                >{{ item?.knowledgeName }}
              </div>
              <Dropdown
                placement="bottom"
                :class="[activeIcdCode === item?.icdCode ? '!block' : '!hidden']"
              >
                <div
                  class="hover:bg-#f5f5f5 absolute right-16px rounded-2px flex items-center px-4px py-2px pt-5px"
                >
                  <Icon @click.prevent color="#303133" :size="18" icon="ant-design:more-outlined" />
                </div>
                <template #overlay>
                  <div
                    class="bg-#fff color-#333 w-fit min-w-80px rounded-3px text-left cursor-pointer shadow-md"
                  >
                    <span class="py-2 px-3 block hover:text-primary-color" @click="handleDel(item)">
                      <Icon icon="ant-design:delete-outlined" class="mr-1" />
                      移除
                    </span>
                  </div>
                </template>
              </Dropdown>
            </div>
          </template>
        </StyledList>
      </div>
    </div>
    <div class="flex-1 bg-#fff rounded p-3 h-[calc(100vh-113px)] of-y-auto">
      <div v-if="items && items.length > 0" class="flex flex-col">
        <div class="flex justify-between items-center">
          <span class="text-#333333 fw-bold">知识内容</span>
          <div>
            <Button
              v-if="!isEdit"
              type="link"
              pre-icon="ant-design:edit-outlined"
              @click="isEdit = !isEdit"
              >编辑</Button
            >
            <Button v-if="isEdit" type="default" class="mr-4" @click="isEdit = !isEdit"
              >取消</Button
            >
            <Button v-if="isEdit" @click="handleSave" :loading="saveLoading" type="primary"
              >保存</Button
            >
          </div>
        </div>
        <div class="flex-1 of-y-auto mt-4">
          <BasicForm style="" @register="registerForm" />
        </div>
      </div>
      <Empty v-else :image="Empty.PRESENTED_IMAGE_SIMPLE" />
    </div>
    <AddModal @register="registerAddModal" @success="getInfectiousDiseaseListRunAsync()" />
  </div>
</template>
<style lang="less" scoped></style>
