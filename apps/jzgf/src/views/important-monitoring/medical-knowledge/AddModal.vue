<script setup lang="ts">
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { computed, ref, unref } from 'vue';
  import { useRequest } from '@ft/request';
  import { Transfer } from 'ant-design-vue';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import {
    batchSaveMedicalKnowledge,
    getExistAddedDiagnosis,
    getInfectiousDiseases,
  } from '/@/api/important-monitoring/medical-knowledge';

  /**
   * 添加诊断
   */

  const emit = defineEmits(['register', 'success']);
  const targetKeys = ref<string[]>([]);
  const { data: mockData, runAsync } = useRequest(() => getInfectiousDiseases(), {
    showSuccessMessage: false,
    manual: true,
  });
  const { runAsync: getExistList } = useRequest(() => getExistAddedDiagnosis(), {
    showSuccessMessage: false,
    manual: true,
    onSuccess: (res) => {
      targetKeys.value = res.map((item) => item.icdCode) || [];
      console.log(targetKeys.value, 'targetKeys');
    },
  });
  const filterOption = (inputValue: string, option) => {
    return option.title?.indexOf(inputValue) > -1 || option.description?.indexOf(inputValue) > -1;
  };
  const getDataSource = computed(() => {
    return unref(mockData)?.map((item) => {
      return {
        key: item.diagnosticCode,
        title: item.diagnosticName,
        description: item.diagnosticCode,
        chosen: false,
      };
    });
  });
  const medicalKnowledgeList = ref<any[]>();
  // const mode = ref('add');
  const [register, { closeModal }] = useModalInner((data) => {
    // mode.value = data.mode;
    medicalKnowledgeList.value = data.medicalKnowledgeList;
    runAsync();
    getExistList();
  });
  const { createMessage } = useMessage();
  const { runAsync: save, loading } = useRequest(batchSaveMedicalKnowledge, {
    showSuccessMessage: true,
    manual: true,
    onBefore: () => {
      if (targetKeys.value.length === 0) {
        createMessage.warning('请选择诊断');
        return false;
      } else {
        return true;
      }
    },
    onSuccess: () => {
      emit('success');
      closeModal();
    },
  });
  const selectKeys = computed(() => {
    const filterData = mockData.value?.filter((item) =>
      targetKeys.value.includes(item.diagnosticCode),
    );
    const params = filterData?.map((item) => {
      const exist = medicalKnowledgeList.value?.find(
        (item2) => item.diagnosticCode === item2.icdCode,
      );
      if (exist) {
        return exist;
      } else {
        return {
          icdCode: item.diagnosticCode,
          knowledgeName: item.diagnosticName,
          knowledgeType: 2,
        };
      }
    });
    return params;
  });
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    title="重点/重症传染病相关诊断"
    :can-fullscreen="false"
    :minHeight="150"
    width="60%"
    :ok-button-props="{
      loading,
    }"
    @register="register"
    @ok="save(selectKeys)"
  >
    <Transfer
      v-model:target-keys="targetKeys"
      :data-source="getDataSource"
      :showSelectAll="false"
      show-search
      :list-style="{
        width: '523px',
        height: '400px',
      }"
      :filter-option="filterOption"
      :titles="['传染病诊断库', '已添加诊断']"
      search-placeholder="ICD编码或诊断名称检索"
      pagination
    >
      <template #render="item">
        <div class="flex gap-0">
          <span class="flex-1 overflow-hidden truncate">{{ item.key + ' ' + item.title }} </span>
        </div>
      </template>
      <template #notFoundContent>
        <span>没数据</span>
      </template>
    </Transfer>
  </BasicModal>
</template>
