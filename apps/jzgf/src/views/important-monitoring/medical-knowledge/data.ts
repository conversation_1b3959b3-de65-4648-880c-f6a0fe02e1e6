import type { FormSchema } from '@ft/internal/components/Form';

export const schemasForm: FormSchema[] = [
  {
    field: 'id',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'morbiditySituation',
    label: '1.发病情况',
    component: 'InputTextArea',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入',
      autoSize: { minRows: 5, maxRows: 5 },
    },
    itemProps: { wrapperCol: { span: 20 } },
  },
  {
    field: 'contagionWay',
    label: '2.传染途径',
    component: 'InputTextArea',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入',
      autoSize: { minRows: 5, maxRows: 5 },
    },
    itemProps: { wrapperCol: { span: 20 } },
  },
  {
    field: 'differentialDiagnosisMethod',
    label: '3. 鉴别诊断方法',
    component: 'InputTextArea',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入',
      autoSize: { minRows: 5, maxRows: 5 },
    },
    itemProps: { wrapperCol: { span: 20 } },
  },
  {
    field: 'medicineRecommendation',
    label: '4. 用药推荐',
    component: 'InputTextArea',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入',
      autoSize: { minRows: 5, maxRows: 5 },
    },
    itemProps: { wrapperCol: { span: 20 } },
  },
  {
    field: 'diagRecommendation',
    label: '5. 治疗建议',
    component: 'InputTextArea',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入',
      autoSize: { minRows: 5, maxRows: 5 },
    },
    itemProps: { wrapperCol: { span: 20 } },
  },
];
