<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { computed, ref } from 'vue';
  import { useRequest } from '@ft/request';
  import { editFormSchema } from './data';
  import { rulesMgApi } from '/@/api/unknown-monitoring/rules-management';

  const emit = defineEmits(['register', 'success']);

  const mode = ref('add');
  const getTitle = computed(() => (mode.value === 'add' ? '新增规则' : '编辑规则'));

  const [registerForm, formAction] = useForm({
    labelWidth: 100,
    schemas: editFormSchema,
    colon: true,
    showActionButtonGroup: false,
  });
  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data.mode;
    if (!data.record) return;
    formAction.setFieldsValue(data.record);
  });
  const { loading, runAsync } = useRequest(
    () => {
      return mode.value === 'edit'
        ? rulesMgApi.edit({ ...formAction.getFieldsValue(), ruleType: 2 })
        : rulesMgApi.add({ ...formAction.getFieldsValue(), ruleType: 2 });
    },
    {
      manual: true,
      showSuccessMessage: true,
      onBefore() {
        return formAction.validate();
      },
      onSuccess() {
        emit('success');
        closeModal();
      },
    },
  );
</script>

<template>
  <BasicModal
    @register="register"
    :destroy-on-close="true"
    :can-fullscreen="false"
    v-bind="$attrs"
    centered
    :width="450"
    :title="getTitle"
    :ok-button-props="{
      loading,
    }"
    @ok="runAsync()"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<style scoped></style>
