<script setup lang="ts">
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { Switch } from 'ant-design-vue';
  import { useRequest } from '@ft/request';
  import { useModal } from '@ft/internal/components/Modal';
  import { columns, searchFormSchema } from './data';
  import EditModal from './EditModal.vue';
  import { rulesMgApi } from '/@/api/unknown-monitoring/rules-management';
  const { run: onSwitchChange } = useRequest(rulesMgApi.editEnable, {
    manual: true,
    showSuccessMessage: true,
    onSuccess() {
      tableIns.reload();
    },
  });
  const { run: onDelete } = useRequest(rulesMgApi.delete, {
    manual: true,
    showSuccessMessage: true,
    onSuccess() {
      tableIns.reload();
    },
  });
  const [registerTable, tableIns] = useTable({
    api: rulesMgApi.page,
    columns,
    useSearchForm: true,
    formConfig: {
      colon: true,
      labelWidth: 100,
      schemas: searchFormSchema,
      fieldMapToTime: [
        [
          'time',
          ['createStartTime', 'createEndTime'],
          ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59'],
        ],
      ],
    },
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
    resizeHeightOffset: 10,
  });

  const [register, { openModal }] = useModal();

  function onAdd() {
    openModal(true, {
      mode: 'add',
    });
  }

  function onEdit(record) {
    openModal(true, {
      mode: 'edit',
      record,
    });
  }

  function createActions(record): ActionItem[] {
    return [
      {
        label: '编辑',
        type: 'link',
        onClick: onEdit.bind(null, record),
      },
      {
        label: '删除',
        type: 'link',
        danger: true,
        popConfirm: {
          title: '确定删除吗？',
          placement: 'topRight',
          okButtonProps: { danger: true },
          confirm: () => onDelete(record.id),
        },
      },
    ];
  }
</script>

<template>
  <div class="h-full pr-4 pb-4">
    <div class="h-full">
      <BasicTable @register="registerTable">
        <template #headerTop>
          <span class="text-base font-bold">重点（重症）病例监测规则</span>
        </template>
        <template #tableTitle>
          <Button class="mt-4" type="primary" @click="onAdd">新增</Button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'enableFlag'">
            <Switch
              :loading="record.loading"
              :checked-value="1"
              :un-checked-value="0"
              :checked="record.enableFlag"
              @change="onSwitchChange({ id: record.id, enableFlag: $event })"
            />
          </template>
          <template v-if="column.dataIndex === 'action'">
            <TableAction :divider="false" :actions="createActions(record)" />
          </template>
        </template>
      </BasicTable>
    </div>
    <EditModal @register="register" @success="tableIns.reload" />
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table .ant-table-wrapper {
      padding: 16px;
    }
  }
</style>
