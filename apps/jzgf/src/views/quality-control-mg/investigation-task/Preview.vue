<script setup lang="ts">
  import { FixedAction } from '@ft/components';
  import { computed, ref } from 'vue';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { Button } from '@ft/internal/components/Button';
  import { useRequest } from '@ft/request';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { getQualityControlTemplateDetail } from '/@/api/template';
  import { useUserStore } from '@ft/internal/store/modules/user';
  import { useRoutePageMode } from '@ft/internal/hooks/web/useRoutePageMode';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import { useModal } from '@ft/internal/components/Modal';
  import { isNil } from 'lodash-es';
  import TemplatePreview from '../../components/TemplatePreview.vue';
  import type { SurveyFormModel } from '/@/api/task';
  import {
    getQualityControlTaskDetails,
    getTaskSurveyRecordDetail,
    getTaskSurveyRecordLastRecord,
    submitTaskSurveyRecord,
    taskSurveyRecordCheck,
  } from '/@/api/task';
  import SurveyRecordDetail from '../task/completion/SurveyRecordDetail.vue';

  const divRef = ref();
  const templatePreview = ref<InstanceType<typeof TemplatePreview>>();

  const { isEditMode, isViewMode } = useRoutePageMode();
  const templateId = useRouteQuery('templateId', '', { transform: String });
  const taskId = useRouteQuery('taskId', '', { transform: String });
  const taskSurveyId = useRouteQuery('taskSurveyId', '', { transform: String });

  const { data } = useRequest(() => getQualityControlTemplateDetail(templateId.value), {
    onError: (error) => {
      error && goBack();
    },
  });

  const { data: taskDetail } = useRequest(() => getQualityControlTaskDetails(taskId.value), {});

  const taskName = computed(() => `${taskDetail.value?.taskName}`);

  const userStore = useUserStore();
  const go = useGo();

  function goBack() {
    go({
      name: 'InvestigationTask',
    });
  }

  // submitTaskSurveyRecord
  const { run: submitTaskSurveyRecordRun } = useRequest(submitTaskSurveyRecord, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      goBack();
    },
  });
  function onSave() {
    templatePreview.value?.validate()?.then((values) => {
      submitTaskSurveyRecordRun({
        ...values,
        taskStatus: 2, // 保存
        id: taskSurveyId.value,
      });
    });
  }
  function onSubmit() {
    templatePreview.value?.validate()?.then((values) => {
      submitTaskSurveyRecordRun({
        ...values,
        taskStatus: 3, // 提交
        id: taskSurveyId.value,
      });
    });
  }

  const { data: lastTaskSurveyRecord } = useRequest(
    () => getTaskSurveyRecordLastRecord(taskSurveyId.value),
    {
      ready: taskSurveyId,
    },
  );

  // getTaskSurveyRecordDetail
  const { data: TaskSurveyRecordDetail } = useRequest(
    () => getTaskSurveyRecordDetail(taskSurveyId.value),
    {
      // @ts-ignore
      ready: taskSurveyId,
    },
  );

  const formModal = computed(() => {
    // 判断 TaskSurveyRecordDetail.value?.indexCategoryList 中的 taskSurveyItemList 中的 所有 indexValue 是否为空
    const indexValueAllEmpty = TaskSurveyRecordDetail.value?.indexCategoryList?.every((category) =>
      category.taskSurveyItemList.every((item) => isNil(item.indexValue)),
    );

    // 如果 indexValueAllEmpty 为 true，则使用 lastTaskSurveyRecord
    const indexCategoryList = indexValueAllEmpty
      ? lastTaskSurveyRecord.value?.indexCategoryList
      : TaskSurveyRecordDetail.value?.indexCategoryList;

    return {
      phone: TaskSurveyRecordDetail.value?.phone,
      taskStatus: TaskSurveyRecordDetail.value?.taskStatus,
      updateUser: TaskSurveyRecordDetail.value?.updateUser,
      surveyItemList: indexCategoryList?.reduce((acc, cur) => {
        return acc.concat(cur.taskSurveyItemList);
      }, [] as SurveyFormModel['surveyItemList']),
    } as SurveyFormModel;
  });
  const { notification } = useMessage();
  useRequest(taskSurveyRecordCheck, {
    defaultParams: [taskSurveyId.value],
    onSuccess: (data) => {
      data &&
        notification.info({
          message: '提示',
          description: '该任务模板有改动，请注意',
        });
    },
  });
  const baseInfo = computed(() => {
    return {
      templateName: taskName.value,
      orgName: isViewMode ? TaskSurveyRecordDetail.value?.orgName : userStore.userInfo?.orgName,
      createUser: isViewMode
        ? TaskSurveyRecordDetail.value?.updateUser
        : userStore.userInfo?.employeeName,
      phone: isViewMode ? TaskSurveyRecordDetail.value?.phone : userStore.userInfo?.phone,
    };
  });
  const [registerModal, { openModal }] = useModal();
  function onExport() {
    openModal(true, {
      record: {
        id: taskSurveyId.value,
      },
    });
  }
</script>

<template>
  <div class="h-[calc(100%-16px)] w-[calc(100%-16px)] relative">
    <Button v-if="isViewMode" class="!absolute top-4 right-4" type="primary" @click="onExport">
      导出
    </Button>
    <div ref="divRef" class="w-full h-full pb-10">
      <TemplatePreview
        v-if="data && formModal"
        :formModal="formModal"
        ref="templatePreview"
        :indexCategoryList="data?.indexCategoryList"
        :templateName="taskName"
        :templateId="templateId"
        :taskId="taskId"
        :orgName="baseInfo.orgName"
        :phone="baseInfo.phone"
        :createUser="baseInfo.createUser"
        :taskRecordId="taskSurveyId"
        :disabled="isViewMode"
      />
    </div>
    <teleport to="body">
      <FixedAction class="justify-end pr-4" :referenceEl="divRef">
        <Button @click="goBack">取消</Button>
        <Button v-if="isEditMode" @click="onSave">保存</Button>
        <Button v-if="isEditMode" type="primary" @click="onSubmit">提交</Button>
      </FixedAction>
    </teleport>
    <SurveyRecordDetail title="导出预览" @register="registerModal" />
  </div>
</template>
