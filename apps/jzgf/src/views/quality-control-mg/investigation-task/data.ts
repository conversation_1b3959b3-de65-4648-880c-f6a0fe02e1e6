import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';

export const SearchFormSchemas: FormSchema[] = [
  // 任务名称
  {
    field: 'taskName',
    label: '任务名称',
    component: 'Input',
    colProps: { span: 8 },
  },
  // 发布时间
  {
    field: 'time',
    label: '任务截止时间',
    component: 'RangePicker',
    labelWidth: 100,
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      style: { width: '100%' },
    },
    colProps: { span: 10 },
  },
];

/**
 * 任务名称
 * 任务截止时间
 * 医疗机构
 * 任务状态
 */
export const columns: BasicColumn[] = [
  {
    title: '任务名称',
    dataIndex: 'taskName',
    width: 120,
    align: 'left',
  },
  // 任务内容
  {
    title: '任务内容',
    dataIndex: 'taskContent',
    width: 180,
  },
  {
    title: '任务截止时间',
    dataIndex: 'finishDate',
    width: 180,
  },
  {
    title: '医疗机构',
    dataIndex: 'orgName',
    width: 150,
  },
  // 任务联系人
  {
    title: '任务联系人',
    dataIndex: 'contact',
    width: 150,
  },
  // 任务质控模板
  {
    title: '任务质控模板',
    dataIndex: 'templateName',
    width: 150,
  },
  {
    title: '任务状态',
    dataIndex: 'taskStatusName',
    width: 80,
  },
  {
    title: '发布时间',
    dataIndex: 'createTime',
    width: 180,
  },
  {
    title: '任务完成提交时间',
    dataIndex: 'completionTime',
    width: 180,
  },
];

/**
 * * 任务名称：
 * 任务目的：
 * 任务内容：
 * 任务年度：
 * 任务完成截止时间：
 * 任务联系人：
 * 任务对象：
 * * 任务质控模板：
 */
export const EditTaskSchemas: FormSchema[] = [
  {
    field: 'taskName',
    label: '任务名称',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
  },
  {
    field: 'taskPurpose',
    label: '任务目的',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 3, maxRows: 3 },
    },
    colProps: { span: 24 },
  },
  {
    field: 'taskContent',
    label: '任务内容',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 3, maxRows: 3 },
    },
    colProps: { span: 24 },
  },
  {
    field: 'taskYear',
    label: '任务年度',
    component: 'Input',
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    field: 'taskDeadline',
    label: '任务完成截止时间',
    component: 'DatePicker',
    componentProps: {
      getPopupContainer: () => document.body,
      style: { width: '100%' },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    field: 'taskContact',
    label: '任务联系人',
    component: 'Input',
    colProps: { span: 24 },
  },
  {
    field: 'taskObject',
    label: '任务对象',
    component: 'Input',
    colProps: { span: 24 },
  },
  {
    field: 'taskTemplate',
    label: '任务质控模板',
    component: 'ApiSelect',
    required: true,
    colProps: { span: 24 },
  },
];
