<script setup lang="ts">
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import { useModal } from '@ft/internal/components/Modal';
  import EvaluationSuggestionModal from '../task/completion/EvaluationSuggestionModal.vue';
  import { SearchFormSchemas, columns } from './data';
  import BasicTag from '/@/components/BasicTag/index.vue';
  import SurveyRecordDetail from '../task/completion/SurveyRecordDetail.vue';

  import type { TaskSurveyRecordDetail } from '/@/api/task';
  import { exportTaskSurveyRecord, getMyTaskSurveyRecordPage } from '/@/api/task';

  const [registerTable, tableIns] = useTable({
    api: getMyTaskSurveyRecordPage,
    columns,
    useSearchForm: true,
    formConfig: {
      labelWidth: 80,
      schemas: SearchFormSchemas,
      fieldMapToTime: [
        ['time', ['startTime', 'endTime'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ],
    },
    indexColumnProps: {
      width: 50,
    },
    resizeHeightOffset: 16,
    inset: true,
    rowKey: 'id',
    actionColumn: {
      title: '操作',
      dataIndex: 'action',
      width: 200,
    },
  });

  const go = useGo();

  function onReport(record: TaskSurveyRecordDetail) {
    go({
      name: 'InvestigationTaskFeedback',
      query: {
        templateId: record.templateId,
        templateName: record.templateName,
        taskId: record.taskId,
        taskSurveyId: record.id,
        mode: record.taskStatus === 3 ? 'view' : 'edit',
      },
    });
  }

  const { loading, runAsync: exportTaskSurveyRecordRun } = useRequest(exportTaskSurveyRecord, {
    manual: true,
  });
  function onExport() {
    exportUtil(exportTaskSurveyRecordRun(tableIns.getForm().getFieldsValue()));
  }

  const [registerEvaluationModal, { openModal: openEvaluationSuggestionModal }] = useModal();
  function onViewSuggestion(record: TaskSurveyRecordDetail) {
    openEvaluationSuggestionModal(true, {
      taskRecordId: record.id,
    });
  }
  function createActions(record: TaskSurveyRecordDetail): ActionItem[] {
    return [
      {
        label: '质控指标反馈',
        onClick: onReport.bind(null, record),
        ifShow: () => record.taskStatus !== 3,
      },
      {
        label: '查看详情',
        onClick: onDetailRecord.bind(null, record),
        ifShow: () => record.taskStatus === 3,
      },
      {
        label: '查看评价建议',
        onClick: onViewSuggestion.bind(null, record),
      },
    ];
  }
  const [registerModal, { openModal }] = useModal();
  function onDetailRecord(record) {
    openModal(true, {
      mode: 'view',
      record,
    });
  }
</script>

<template>
  <div class="flex gap-2.5 h-[calc(100%-16px)] w-[calc(100%-16px)]">
    <div class="flex-1 of-hidden bg-white rounded rounded-lt-none p-4 flex flex-col">
      <div class="text-base font-bold">质控任务列表</div>
      <div class="flex-1 of-hidden">
        <BasicTable @register="registerTable">
          <template #toolbar>
            <Button type="primary" @click="onExport" :loading="loading">导出</Button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
              <TableAction :divider="false" :actions="createActions(record)" />
            </template>
            <template v-if="column.dataIndex === 'taskStatusName'">
              <BasicTag v-if="record.taskStatus === 3" color="green">
                {{ record.taskStatusName }}
              </BasicTag>
              <BasicTag v-if="record.taskStatus === 2" color="blue">
                {{ record.taskStatusName }}
              </BasicTag>
              <BasicTag v-if="record.taskStatus === 1" color="blue">
                {{ record.taskStatusName }}
              </BasicTag>
            </template>
          </template>
        </BasicTable>
      </div>
    </div>
    <EvaluationSuggestionModal disabled @register="registerEvaluationModal" />
    <!-- 查看详情 -->
    <SurveyRecordDetail @register="registerModal" />
  </div>
</template>

<style lang="less" scoped>
  .org-info-list {
    :deep(li) {
      padding-left: 0;
    }
  }
</style>
