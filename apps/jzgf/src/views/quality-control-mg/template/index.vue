<script setup lang="ts">
  import { ref, watch } from 'vue';
  import { Icon } from '@ft/internal/components/Icon';
  import { useModal } from '@ft/internal/components/Modal';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import { BasicTitle } from '@ft/internal/components/Basic';
  import { Button } from '@ft/internal/components/Button';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import { useRequest } from '@ft/request';
  import { StyledList } from '@ft/components';
  import { Dropdown, InputNumber, Switch } from 'ant-design-vue';
  import type { TreeDropMenuItem } from '../../treatment-alert/rule-management/comp/TreeList/TreeDropMenu.vue';
  import TreeDropMenu from '../../treatment-alert/rule-management/comp/TreeList/TreeDropMenu.vue';
  import EditTemplateModal from './EditTemplateModal.vue';
  import type { EditIndexModalDT, EditIndexTypeModalDT, EditTemplateModalDT } from './data';
  import { IndexColumns } from './data';
  import EditIndexTypeModal from './EditIndexTypeModal.vue';
  import EditIndexModal from './EditIndexModal.vue';
  import type {
    IQualityControlIndex,
    IQualityControlIndexItem,
    IQualityControlTemplate,
  } from '/@/api/template';
  import {
    getQualityControlIndexItemPage,
    getQualityControlIndexList,
    getQualityControlTemplateList,
    removeQualityControlIndex,
    removeQualityControlIndexItem,
    removeQualityControlTemplate,
    updateQualityControlIndexItem,
  } from '/@/api/template';
  import type { RecordWithExtra } from '/@/types';

  const activeKey = ref<string>('');
  const activeItem = ref();

  const activeIndexKey = ref<string>('');
  const activeIndexItem = ref();

  const { data: qualityControlTemplateList, refresh: refreshTemplist } = useRequest(
    getQualityControlTemplateList,
    {
      onSuccess(data) {
        activeKey.value = data[0]?.id;
        activeItem.value = data[0];
      },
    },
  );

  const { data: qualityControlIndexList, refresh: refreshIndexType } = useRequest(
    () => getQualityControlIndexList(activeKey.value),
    {
      ready: activeKey,
      refreshDeps: [activeKey],
      onSuccess(data) {
        activeIndexKey.value = data[0]?.id;
        activeIndexItem.value = data[0];
      },
    },
  );

  function createMenu(item: IQualityControlTemplate): TreeDropMenuItem[] {
    return [
      {
        label: '编辑',
        icon: 'ant-design:edit-outlined',
        onClick: handleEditTemplate.bind(null, item),
      },
      {
        label: '删除',
        icon: 'ant-design:delete-outlined',
        onClick: handleDelTemplate.bind(null, item),
      },
    ];
  }

  function createIndexTypeMenu(item: IQualityControlIndex): TreeDropMenuItem[] {
    return [
      {
        label: '编辑',
        icon: 'ant-design:edit-outlined',
        onClick: onEditIndexType.bind(null, item),
      },
      {
        label: '删除',
        icon: 'ant-design:delete-outlined',
        onClick: handleDelIndexType.bind(null, item),
      },
    ];
  }

  const [registerEditTemplateModal, { openModal: openEditTemplateModal }] = useModal();
  const [registerEditIndexTypeModal, { openModal: openEditIndexTypeModal }] = useModal();
  const [registerEditIndexModal, { openModal: openEditIndexModal }] = useModal();

  function handleAddTemplate() {
    openEditTemplateModal<EditTemplateModalDT>(true, {
      mode: 'add',
    });
  }
  function handleEditTemplate(record: IQualityControlTemplate) {
    openEditTemplateModal<EditTemplateModalDT>(true, {
      mode: 'edit',
      record,
    });
  }
  const { createConfirm } = useMessage();

  // removeQualityControlTemplate
  const { run: removeTemplateRun } = useRequest(removeQualityControlTemplate, {
    manual: true,
    onSuccess() {
      refreshTemplist();
    },
  });
  function handleDelTemplate(item: IQualityControlTemplate) {
    createConfirm({
      iconType: 'warning',
      title: '删除模板',
      content: `确定删除该模板吗？`,
      onOk: () => {
        console.log(`item`, item);
        removeTemplateRun(item.id);
      },
    });
  }

  // removeQualityControlIndex
  const { run: removeIndexRun } = useRequest(removeQualityControlIndex, {
    manual: true,
    onSuccess() {
      refreshIndexType();
    },
  });

  function handleDelIndexType(item: IQualityControlIndex) {
    createConfirm({
      iconType: 'warning',
      title: '删除指标分类',
      content: `确定删除该指标分类吗？`,
      onOk: () => {
        removeIndexRun(item.id);
      },
    });
  }

  function onAddIndexType() {
    openEditIndexTypeModal<EditIndexTypeModalDT>(true, {
      mode: 'add',
      record: {
        templateId: activeKey.value,
      },
    });
  }

  function onEditIndexType(record: IQualityControlIndex) {
    openEditIndexTypeModal<EditIndexTypeModalDT>(true, {
      mode: 'edit',
      record: {
        ...record,
      },
    });
  }

  function onAddIndex() {
    openEditIndexModal<EditIndexModalDT>(true, {
      mode: 'add',
      record: {
        templateId: activeKey.value,
        categoryId: activeIndexKey.value,
        indexCategory: activeIndexItem.value?.indexCategory,
      },
    });
  }

  const [registerTable, tableIns] = useTable({
    api: getQualityControlIndexItemPage,
    columns: IndexColumns,
    immediate: false,
    beforeFetch: (params) => {
      return {
        ...params,
        categoryId: activeIndexKey.value,
        templateId: activeKey.value,
      };
    },
    afterFetch: (data) => {
      return data.map((item) => {
        return {
          ...item,
          loading: false,
        };
      });
    },
    actionColumn: {
      title: '操作',
      width: 100,
      dataIndex: 'action',
    },
  });

  watch([activeKey, activeIndexKey], ([newActiveKey, newActiveIndexKey]) => {
    if (newActiveKey && newActiveIndexKey) tableIns?.reload();
  });

  function onEditIndex(record: IQualityControlIndexItem) {
    openEditIndexModal<EditIndexModalDT>(true, {
      mode: 'edit',
      record,
    });
  }

  const { run: removeQualityControlIndexItemRun } = useRequest(removeQualityControlIndexItem, {
    manual: true,
    onSuccess() {
      tableIns.reload();
    },
  });

  function onDelIndex(record: IQualityControlIndexItem) {
    removeQualityControlIndexItemRun(record.id);
  }

  const go = useGo();

  function onPreview() {
    go({
      name: 'TemplatePreview',
      query: {
        templateId: activeKey.value,
        templateName: activeItem.value?.templateName,
      },
    });
  }

  function createActions(record, _column): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: onEditIndex.bind(null, record),
      },
      {
        label: '删除',
        danger: true,
        popConfirm: {
          title: '删除指标',
          content: '确定删除该指标吗？',
          confirm: onDelIndex.bind(null, record),
        },
      },
    ];
  }
  // updateQualityControlIndexItem
  const { runAsync: updateQualityControlIndexItemRun } = useRequest(updateQualityControlIndexItem, {
    manual: true,
    showSuccessMessage: true,
    onFinally() {
      tableIns.reload();
    },
  });
  function onSwitch(checked, field: string, record: RecordWithExtra<IQualityControlIndexItem>) {
    record.loading = true;
    updateQualityControlIndexItemRun({
      ...record,
      [field]: checked,
    }).finally(() => {
      record.loading = false;
    });
  }

  function onSortNumChange(value, record: IQualityControlIndexItem) {
    updateQualityControlIndexItemRun({
      ...record,
      sortNum: value,
    });
  }
</script>

<template>
  <div class="flex gap-2 h-[calc(100%-16px)] w-[calc(100%-16px)]">
    <div class="flex flex-col gap-3 w-228px bg-#fff rounded-[0px_8px_8px_8px] p-3">
      <div class="flex gap-3">
        <span class="text-#333333 fw-bold flex-1">质控指标模板</span>
        <div
          @click="handleAddTemplate"
          class="w-20px h-20px rounded-50% bg-primary-color cursor-pointer p-1 flex items-center"
        >
          <Icon color="#fff" icon="ant-design:plus-outlined" size="12" />
        </div>
      </div>
      <div class="h-[calc(100%-22px)]">
        <StyledList
          :width="208"
          v-model="activeKey"
          v-model:value="activeItem"
          :items="qualityControlTemplateList"
          value-field="id"
          label-field="templateName"
        >
          <template #default="item">
            <div class="flex relative w-full">
              <span
                class="block w-[calc(100%-10px)] overflow-hidden truncate"
                :title="item.templateName"
              >
                {{ item.templateName }}
              </span>
              <Dropdown placement="bottom">
                <Icon
                  class="ant-dropdown-link flex justify-between hidden absolute right-0 top-1/2 -translate-y-1/2"
                  @click.prevent
                  color="#ff6da0"
                  :size="18"
                  icon="ant-design:more-outlined"
                />

                <template #overlay>
                  <div
                    class="bg-#fff color-#333 w-fit min-w-80px rounded-3px text-left cursor-pointer shadow-md"
                  >
                    <TreeDropMenu :createMenu="createMenu(item)" />
                  </div>
                </template>
              </Dropdown>
            </div>
          </template>
        </StyledList>
      </div>
    </div>
    <div class="flex flex-col gap-2 flex-1 min-w-757px">
      <div class="flex items-center justify-between gap-2 bg-white p-4 rounded-lg">
        <BasicTitle span normal>{{ activeItem?.templateName }}</BasicTitle>
        <Button color="primary" ghost @click="onPreview">预览</Button>
      </div>
      <div class="flex-1 flex flex-col of-hidden rounded-lg bg-white">
        <div
          class="flex items-center justify-between gap-2 bg-white p-4 border-b-1 border-[#EDEEF0]"
        >
          <BasicTitle span normal>指标明细</BasicTitle>
        </div>
        <div class="flex of-hidden">
          <div class="flex flex-col gap-3 min-w-236px bg-#fff p-4 border-r-1 border-[#EDEEF0]">
            <div class="flex gap-3">
              <span class="text-#333333 fw-bold flex-1">指标分类</span>
              <div
                v-if="activeKey"
                @click="onAddIndexType"
                class="rounded-50% text-primary-color cursor-pointer p-1 flex items-center"
              >
                新增
              </div>
            </div>
            <StyledList
              :width="208"
              v-model="activeIndexKey"
              v-model:value="activeIndexItem"
              :items="qualityControlIndexList"
              value-field="id"
            >
              <template #default="item">
                <div class="flex relative w-full">
                  <span
                    class="block w-[calc(100%-10px)] overflow-hidden truncate"
                    :title="item.indexCategory"
                  >
                    {{ item.indexCategory }}
                  </span>
                  <Dropdown placement="bottom">
                    <Icon
                      class="ant-dropdown-link flex justify-between hidden absolute right-0 top-1/2 -translate-y-1/2"
                      @click.prevent
                      color="#ff6da0"
                      :size="18"
                      icon="ant-design:more-outlined"
                    />

                    <template #overlay>
                      <div
                        class="bg-#fff color-#333 w-fit min-w-80px rounded-3px text-left cursor-pointer shadow-md"
                      >
                        <TreeDropMenu :createMenu="createIndexTypeMenu(item)" />
                      </div>
                    </template>
                  </Dropdown>
                </div>
              </template>
            </StyledList>
          </div>
          <div class="flex-1 of-hidden p-4">
            <BasicTable @register="registerTable">
              <template #headerTop>
                <div class="font-bold mb-4">指标明细</div>
              </template>
              <template #tableTitle>
                <Button :disabled="!activeIndexKey" color="primary" @click="onAddIndex">
                  新增
                </Button>
              </template>
              <template #bodyCell="{ record, column }">
                <template v-if="['isShow', 'isRequired'].includes(column.dataIndex)">
                  <Switch
                    :loading="record.loading"
                    :checked-value="0"
                    :un-checked-value="1"
                    v-model:checked="record[column.dataIndex]"
                    @click="onSwitch($event, column.dataIndex, record)"
                  />
                </template>
                <template v-if="column.dataIndex === 'sortNum'">
                  <InputNumber
                    v-model:value="record.sortNum"
                    @change="onSortNumChange($event, record)"
                  />
                </template>
                <template v-if="column.dataIndex === 'action'">
                  <TableAction :divider="false" :actions="createActions(record, column)" />
                </template>
              </template>
            </BasicTable>
          </div>
        </div>
      </div>
    </div>
    <EditTemplateModal @register="registerEditTemplateModal" @success="refreshTemplist" />
    <EditIndexTypeModal @register="registerEditIndexTypeModal" @success="refreshIndexType" />
    <EditIndexModal @register="registerEditIndexModal" @success="tableIns.reload" />
  </div>
</template>

<style lang="less" scoped></style>
