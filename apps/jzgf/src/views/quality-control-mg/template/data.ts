import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import type {
  IQualityControlIndex,
  IQualityControlIndexItem,
  IQualityControlTemplate,
} from '/@/api/template';
import type { ModalEditDT } from '/@/types';

export type EditTemplateModalDT = ModalEditDT<IQualityControlTemplate>;
export const EditTemplateSchemas: FormSchema[] = [
  //id
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'templateName',
    label: '模板名称',
    component: 'Input',
    required: true,
  },
];

export type EditIndexTypeModalDT = ModalEditDT<
  IQualityControlIndex & Pick<IQualityControlTemplate, 'id'>
>;

export const EditIndexTypeSchemas: FormSchema[] = [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false,
  },
  // templateId
  {
    field: 'templateId',
    label: 'templateId',
    component: 'Input',
    show: false,
  },
  {
    field: 'indexCategory',
    label: '指标分类名称',
    component: 'Input',
    required: true,
  },
];

export type EditIndexModalDT = ModalEditDT<IQualityControlIndexItem>;

export const EditIndexSchemas: FormSchema[] = [
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'categoryId',
    label: 'categoryId',
    component: 'Input',
    show: false,
  },
  {
    field: 'templateId',
    label: 'templateId',
    component: 'Input',
    show: false,
  },
  //indexCategory
  {
    field: 'indexCategory',
    label: '指标分类名称',
    component: 'Input',
    show: false,
  },
  {
    field: 'indexName',
    label: '指标名称',
    component: 'Input',
    required: true,
  },
  {
    field: 'isShow',
    label: '是否展示',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '是', value: 0 },
        { label: '否', value: 1 },
      ],
    },
    defaultValue: 0,
    required: true,
  },
  {
    field: 'sortNum',
    label: '展示序号',
    component: 'InputNumber',
    componentProps: {
      min: 0,
      precision: 0,
      formatter: (value) => String(value).replace(/[^0-9]/g, ''),
      parser: (value) => String(value).replace(/[^0-9]/g, ''),
    },
    required: true,
  },
  {
    field: 'isRequired',
    label: '是否必填',
    component: 'RadioGroup',
    componentProps: {
      options: [
        { label: '是', value: 0 },
        { label: '否', value: 1 },
      ],
    },
    defaultValue: 0,
    required: true,
  },
];

/**
 * 指标名称
 * 是否展示
 * 展示序号
 * 是否必填
 */
export const IndexColumns: BasicColumn[] = [
  {
    title: '指标名称',
    dataIndex: 'indexName',
    width: 200,
  },
  {
    title: '是否展示',
    dataIndex: 'isShow',
    width: 200,
  },
  {
    title: '展示序号',
    dataIndex: 'sortNum',
    width: 100,
  },
  {
    title: '是否必填',
    dataIndex: 'isRequired',
    width: 200,
  },
];
