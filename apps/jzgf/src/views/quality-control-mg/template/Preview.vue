<script setup lang="ts">
  import { useRequest } from '@ft/request';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import TemplatePreview from '../../components/TemplatePreview.vue';
  import { getQualityControlTemplateDetail } from '/@/api/template';

  const templateId = useRouteQuery('templateId', '', { transform: String });
  const templateName = useRouteQuery('templateName', '', { transform: String });
  // const templateNameByYear = computed(() => `${new Date().getFullYear()}年${templateName.value}`);

  const { data } = useRequest(() => getQualityControlTemplateDetail(templateId.value));
</script>

<template>
  <div class="h-[calc(100%-16px)] w-[calc(100%-16px)]">
    <TemplatePreview
      :indexCategoryList="data?.indexCategoryList"
      :templateName="templateName"
      isTemplate
    />
  </div>
</template>
