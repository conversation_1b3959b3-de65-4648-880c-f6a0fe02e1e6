<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import type { EditIndexModalDT } from './data';
  import { EditIndexSchemas } from './data';
  import type { IQualityControlIndexItem } from '/@/api/template';
  import { saveQualityControlIndexItem, updateQualityControlIndexItem } from '/@/api/template';

  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm<IQualityControlIndexItem>({
    schemas: EditIndexSchemas,
    labelWidth: 120,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });
  const mode = ref<'add' | 'edit' | 'view'>('add');
  const title = computed(() => {
    return mode.value === 'edit' ? '编辑指标明细' : '新增指标明细';
  });
  const [register, { closeModal }] = useModalInner<EditIndexModalDT>((data) => {
    mode.value = data?.mode;
    if (!data.record) return;
    formAction.setFieldsValue(data.record);
  });
  const { loading, run } = useRequest(
    () =>
      mode.value === 'add'
        ? saveQualityControlIndexItem(formAction.getFieldsValue())
        : updateQualityControlIndexItem(formAction.getFieldsValue()),
    {
      showSuccessMessage: () => '操作成功',
      manual: true,
      onBefore: () => {
        return formAction.validate();
      },
      onSuccess: () => {
        emit('success');
        closeModal();
      },
    },
  );

  async function handleOk() {
    run();
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="title"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    centered
    :min-height="50"
    width="480px"
    @register="register"
    @ok="handleOk"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
