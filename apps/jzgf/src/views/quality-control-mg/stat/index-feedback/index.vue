<script lang="ts" setup>
  import type { BasicColumn } from '@ft/internal/components/Table';
  import { BasicTable, useTable } from '@ft/internal/components/Table';
  import { computed, ref } from 'vue';
  import { pick } from 'lodash-es';
  import { searchFormSchemas } from './data';
  import type { IQualityStat } from '/@/api/quality-stat';
  import { getQualityStatList } from '/@/api/quality-stat';

  const formModel = ref();
  const [registerTable, tableAction] = useTable({
    api: getQualityStatList,
    useSearchForm: true,
    immediate: false,
    formConfig: {
      labelWidth: 100,
      schemas: searchFormSchemas,
      showResetButton: false,
    },
    pagination: false,
    showIndexColumn: false,
    beforeFetch: (params) => {
      formModel.value = params;
      return pick(params, 'taskId');
    },

    afterFetch: (data: IQualityStat[]) => {
      const columns: BasicColumn[] = [
        {
          title: '指标',
          dataIndex: 'indexName',
          align: 'left',
          ellipsis: false,
        },
      ];
      if (data.length > 0) {
        const firstItem = data[0];
        const indexValueList = firstItem?.indexValueList || [];
        for (const [index, item] of indexValueList.entries()) {
          columns.push({
            title: item.orgName,
            dataIndex: ['indexValueList', index, 'indexValue'],
            align: 'center',
          });
        }
      }
      tableAction.setColumns(columns);
      return data;
    },
  });

  const getTaskName = computed(() => {
    let taskName = '';
    if (formModel.value?.taskYear) {
      taskName += `${formModel.value?.taskYear}年`;
    }
    if (formModel.value?.taskMonth) {
      taskName += `${formModel.value?.taskMonth}月`;
    }
    if (formModel.value?.taskName) {
      taskName += formModel.value?.taskName;
    }

    return taskName;
  });
</script>
<template>
  <div
    class="flex gap-2.5 h-[calc(100%-16px)] w-[calc(100%-16px)] bg-white rounded-lg rounded-lt-0"
  >
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <div class="flex items-center justify-center text-lg font-bold w-full">
          {{ getTaskName }}
        </div>
      </template>
    </BasicTable>
  </div>
</template>
