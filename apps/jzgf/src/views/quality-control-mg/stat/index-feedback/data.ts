import type { FormSchema } from '@ft/internal/components/Form';
import { getQualityControlTaskList } from '/@/api/task';

export const searchFormSchemas: FormSchema[] = [
  {
    field: 'taskName',
    label: '任务信息',
    component: 'Input',
    colProps: { span: 24 },
    show: false,
  },
  {
    field: 'taskYear',
    label: '质控年份',
    component: 'InputNumber',
    show: false,
  },
  {
    field: 'taskMonth',
    label: '质控月份',
    component: 'InputNumber',
    show: false,
  },
  {
    field: 'taskId',
    label: '任务名称',
    component: 'ApiSelect',
    componentProps: ({ formModel, formActionType }) => {
      return {
        api: getQualityControlTaskList,
        labelField: 'taskName',
        valueField: 'id',
        showSearch: true,
        optionFilterProp: 'label',
        allowClear: false,
        onChange: (_value, option) => {
          if (option) {
            formModel.taskYear = option.taskYear;
            formModel.taskMonth = option.taskMonth;
            formModel.taskName = option.label;
          }
        },
        onOptionsChange: (list) => {
          if (list[0]) {
            formModel.taskId = list[0].value;

            formModel.taskYear = list[0].taskYear;
            formModel.taskMonth = list[0].taskMonth;
            formModel.taskName = list[0].label;
            formActionType.submit();
          }
        },
      };
    },
    colProps: { span: 6 },
  },
  // {
  //   field: 'taskYear',
  //   label: '质控年份',
  //   component: 'DatePicker',
  //   componentProps: {
  //     picker: 'year',
  //     format: 'YYYY',
  //     valueFormat: 'YYYY',
  //   },
  //   defaultValue: dayjs().format('YYYY'),
  //   colProps: { span: 6 },
  // },
  // {
  //   field: 'taskMonth',
  //   label: '质控月份',
  //   component: 'Select',
  //   componentProps: {
  //     options: Array.from({ length: 12 }, (_, i) => ({
  //       label: `${i + 1}月`,
  //       value: `${i + 1}`,
  //     })),
  //   },
  //   colProps: { span: 6 },
  //   defaultValue: dayjs().format('M'),
  // },
];
