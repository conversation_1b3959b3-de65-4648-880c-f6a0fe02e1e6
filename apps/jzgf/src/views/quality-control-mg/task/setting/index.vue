<script setup lang="ts">
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { useModal } from '@ft/internal/components/Modal';
  import { ref } from 'vue';
  import { useRequest } from '@ft/request';
  import { SearchFormSchemas, columns } from './data';
  import EditTaskModal from './EditTaskModal.vue';
  import type { IQualityControlTask } from '/@/api/task';
  import { getQualityControlTaskPage, removeQualityControlTask } from '/@/api/task';

  const activeRow = ref<IQualityControlTask>();

  const [registerTable, tableIns] = useTable({
    api: getQualityControlTaskPage,
    columns,
    useSearchForm: true,
    formConfig: {
      labelWidth: 80,
      schemas: SearchFormSchemas,
      fieldMapToTime: [['createTime', ['publishStartTime', 'publishEndTime'], 'YYYY-MM-DD']],
    },
    actionColumn: {
      title: '操作',
      width: 100,
      dataIndex: 'action',
    },
    resizeHeightOffset: 16,
    inset: true,
    rowKey: 'id',
  });

  function onRowClick(record: IQualityControlTask) {
    activeRow.value = record;
  }

  const [registerEditTaskModal, { openModal }] = useModal();

  function onAddTask() {
    openModal(true, {
      mode: 'add',
    });
  }

  function onEditTask(record) {
    openModal(true, {
      mode: 'edit',
      record,
    });
  }

  const { run } = useRequest(removeQualityControlTask, {
    manual: true,
    onSuccess: () => {
      tableIns.reload();
    },
  });

  function onDelTask(record: IQualityControlTask) {
    run(record.id);
  }

  function createActions(record: IQualityControlTask, _column): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: onEditTask.bind(null, record),
      },
      {
        label: '删除',
        danger: true,
        popConfirm: {
          placement: 'topRight',
          title: '删除指标',
          content: '确定删除该任务吗？',
          confirm: onDelTask.bind(null, record),
        },
      },
    ];
  }
</script>

<template>
  <div class="flex gap-2.5 h-[calc(100%-16px)] w-[calc(100%-16px)]">
    <div class="flex-1 of-hidden bg-white rounded rounded-lt-none p-4 flex flex-col">
      <div class="flex-1 of-hidden">
        <BasicTable @row-click="onRowClick" @register="registerTable">
          <template #headerTop>
            <div class="text-base font-bold mb-2">质控任务列表</div>
          </template>
          <template #tableTitle>
            <Button type="primary" @click="onAddTask">新增</Button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'action'">
              <TableAction :divider="false" :actions="createActions(record, column)" />
            </template>
          </template>
        </BasicTable>
      </div>
    </div>

    <EditTaskModal @register="registerEditTaskModal" @success="tableIns.reload" />
  </div>
</template>

<style lang="less" scoped>
  .org-info-list {
    :deep(li) {
      padding-left: 0;
    }
  }
</style>
