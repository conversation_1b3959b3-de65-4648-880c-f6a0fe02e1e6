<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { queryOrganizationList } from '@ft/internal/api';
  import { ObjectTree } from '@ft/components';
  import { filter } from 'lodash-es';
  import type { EditTaskModalDT } from './data';
  import { EditTaskSchemas } from './data';
  import {
    type IQualityControlTask,
    editQualityControlTask,
    getQualityControlTaskDetails,
    saveQualityControlTask,
  } from '/@/api/task';

  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm<IQualityControlTask>({
    schemas: EditTaskSchemas,
    labelWidth: 140,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });
  const mode = ref<'add' | 'edit' | 'view'>('add');
  const title = computed(() => {
    return mode.value === 'edit' ? '编辑任务' : '新增任务';
  });

  const { run: getTaskDetails } = useRequest(getQualityControlTaskDetails, {
    manual: true,
    onSuccess: (data) => {
      formAction.setFieldsValue(data);
    },
  });
  const [register, { closeModal }] = useModalInner<EditTaskModalDT>((data) => {
    mode.value = data?.mode;
    if (data?.mode === 'edit' && data.record?.id) {
      getTaskDetails(data.record.id);
    }
  });
  const { loading, run } = useRequest(
    () => {
      const values = formAction.getFieldsValue();
      const params = {
        ...values,
        orgInfoList: filter(values.orgInfoList, (item) => item.orgId !== '0'),
      };
      return mode.value === 'add' ? saveQualityControlTask(params) : editQualityControlTask(params);
    },
    {
      showSuccessMessage: () => '操作成功',
      manual: true,
      onBefore: () => {
        return formAction.validate();
      },
      onSuccess: () => {
        emit('success');
        closeModal();
      },
    },
  );

  async function handleOk() {
    run();
  }

  async function getMedicalOrg() {
    const data = await queryOrganizationList({
      orgType: 1,
    });
    return [
      {
        id: '0',
        orgName: '医疗卫生机构',
        children: data,
      },
    ];
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="title"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    centered
    :min-height="500"
    width="650px"
    @register="register"
    @ok="handleOk"
  >
    <BasicForm @register="registerForm">
      <template #taskObject="{ model, field }">
        <ObjectTree
          :height="130"
          :api="getMedicalOrg"
          defaultExpandAll
          v-model:value="model[field]"
          valueField="orgId"
          checkable
          :field-names="{
            children: 'children',
            title: 'orgName',
            key: 'id',
          }"
          :value-field-map="{
            divisionId: 'divisionId',
            divisionName: 'divisionName',
            orgId: 'id',
            orgName: 'orgName',
            phone: 'phone',
          }"
        />
      </template>
    </BasicForm>
  </BasicModal>
</template>
