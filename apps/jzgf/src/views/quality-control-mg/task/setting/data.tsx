import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import type { ModalEditDT } from '/@/types';
import type { IQualityControlTask } from '/@/api/task';
import { getQualityControlTemplateList } from '/@/api/template';
import { DatePicker, FormItemRest, Popover, Select } from 'ant-design-vue';
import dayjs from 'dayjs';
import { isNull } from 'lodash-es';

export type EditTaskModalDT = ModalEditDT<IQualityControlTask>;

export enum ControlTaskTypeEnum {
  /** 年度 */
  YEAR = 1,
  /** 月度 */
  MONTH = 2,
}

export const TaskTypeOptions = [
  { label: '年度', value: ControlTaskTypeEnum.YEAR },
  { label: '月度', value: ControlTaskTypeEnum.MONTH },
];

export const SearchFormSchemas: FormSchema[] = [
  // 任务名称
  {
    field: 'taskName',
    label: '任务名称',
    component: 'Input',
    colProps: { span: 8 },
  },
  // 发布时间
  {
    field: 'createTime',
    label: '发布时间',
    component: 'RangePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
    colProps: { span: 10 },
  },
];

/**
 *
 * 任务名称
 * 质控时间
 * 是否展示报表
 * 任务内容
 * 任务完成截止时间
 * 任务联系人
 * 任务对象
 * 任务质控模板
 * 发布时间
 * 发布人
 */
export const columns: BasicColumn<IQualityControlTask>[] = [
  {
    title: '任务名称',
    dataIndex: 'taskName',
    width: 200,
    align: 'left',
  },
  {
    title: '质控时间',
    dataIndex: 'taskYear',
    width: 120,
    align: 'left',
    customRender({ record }) {
      if (record.taskType === ControlTaskTypeEnum.YEAR) {
        return `${record.taskYear}年`;
      }
      return `${record.taskYear ? `${record.taskYear}年` : ''}${
        record.taskMonth ? `${record.taskMonth}月` : ''
      }`;
    },
  },
  {
    title: '任务对象',
    dataIndex: 'taskObject',
    width: 200,
    align: 'left',
    customRender({ record }) {
      const orgNameList = record.orgInfoList?.map((item) => item.orgName) ?? [];
      const ellipsisOrgName =
        orgNameList?.length > 2
          ? orgNameList?.join('、').slice(0, 11) + '...'
          : orgNameList?.join('、');
      return (
        <Popover placement="right">
          {{
            default: () => {
              return <span>{ellipsisOrgName}</span>;
            },
            content: () => {
              return (
                <div class="w-[260px] max-h-[300px] overflow-y-auto">
                  {orgNameList?.map((item) => (
                    <div>{item}</div>
                  ))}
                </div>
              );
            },
          }}
        </Popover>
      );
    },
    ellipsis: true,
  },
  {
    title: '任务质控模板',
    dataIndex: 'templateName',
    width: 200,
    align: 'left',
  },
  {
    title: '是否展示报表',
    dataIndex: 'statisticsFlag',
    width: 120,
    align: 'left',
    customRender({ record }) {
      if (isNull(record.statisticsFlag)) {
        return '';
      }
      return record.statisticsFlag === 0 ? '是' : '否';
    },
  },
  {
    title: '发布时间',
    dataIndex: 'createTime',
    width: 200,
    align: 'left',
  },
  {
    title: '发布人',
    dataIndex: 'createUser',
    width: 100,
    align: 'left',
  },
  {
    title: '任务内容',
    dataIndex: 'taskContent',
    width: 200,
    align: 'left',
  },
  {
    title: '任务完成截止时间',
    dataIndex: 'finishDate',
    width: 200,
    align: 'left',
  },
  {
    title: '任务联系人',
    dataIndex: 'contact',
    width: 100,
    align: 'left',
  },
];

export const EditTaskSchemas: FormSchema[] = [
  // id
  {
    field: 'id',
    label: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'templateId',
    label: '任务质控模板',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: getQualityControlTemplateList,
        getPopupContainer: () => document.body,
        labelField: 'templateName',
        valueField: 'id',
        onChange: (_, opt) => {
          opt && (formModel.templateName = opt?.label);
          opt && (formModel.taskName = opt?.label);
        },
      };
    },
    required: true,
    colProps: { span: 24 },
  },
  {
    field: 'taskName',
    label: '任务名称',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
  },
  {
    field: 'taskYear',
    label: '任务年度',
    component: 'Input',
    show: false,
    defaultValue: dayjs().format('YYYY'),
  },
  {
    field: 'taskMonth',
    label: '任务月份',
    component: 'Input',
    show: false,
  },
  // *质控时间
  {
    field: 'taskType',
    label: '质控时间',
    component: 'Select',
    colProps: { span: 24 },
    required: true,
    render: ({ model, field }) => {
      return (
        <FormItemRest>
          <div class="flex items-center gap-4">
            <Select
              class="time-type flex-1"
              value={model[field]}
              options={TaskTypeOptions}
              onChange={(value) => {
                model[field] = value;
              }}
            />
            <div class="flex-1 flex items-center gap-2">
              <DatePicker
                value={model['taskYear']}
                picker="year"
                valueFormat="YYYY"
                onChange={(value) => {
                  model['taskYear'] = value;
                }}
              />
              {model[field] === ControlTaskTypeEnum.MONTH && (
                <DatePicker
                  value={model['taskMonth']}
                  picker="month"
                  valueFormat="M"
                  format="M"
                  onChange={(value) => {
                    model['taskMonth'] = value;
                  }}
                />
              )}
            </div>
          </div>
        </FormItemRest>
      );
    },
    defaultValue: ControlTaskTypeEnum.YEAR,
  },
  // 是否展示统计报表 statisticsFlag
  {
    field: 'statisticsFlag',
    label: '是否展示统计报表',
    component: 'Select',
    componentProps: {
      options: [
        { label: '是', value: 0 },
        { label: '否', value: 1 },
      ],
    },
    defaultValue: 0,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
  },

  {
    field: 'taskContent',
    label: '任务内容',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 3, maxRows: 3 },
    },
    colProps: { span: 24 },
  },
  // {
  //   field: 'taskYear',
  //   label: '任务年度',
  //   component: 'DatePicker',
  //   componentProps: {
  //     picker: 'year',
  //     valueFormat: 'YYYY',
  //     getPopupContainer: () => document.body,
  //     style: { width: '100%' },
  //   },
  //   defaultValue: dayjs().format('YYYY'),
  //   isHandleDateDefaultValue: false,
  //   colProps: { span: 24 },
  //   itemProps: { wrapperCol: { span: 12 } },
  // },
  {
    field: 'finishDate',
    label: '任务完成截止时间',
    component: 'DatePicker',
    componentProps: {
      getPopupContainer: () => document.body,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      showTime: true,
      style: { width: '100%' },
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    field: 'contact',
    label: '任务联系人',
    component: 'Input',
    colProps: { span: 24 },
  },
  {
    field: 'orgInfoList',
    label: '任务对象',
    component: 'Input',
    colProps: { span: 24 },
    slot: 'taskObject',
    required: true,
  },
  {
    field: 'templateName',
    label: '任务质控模板',
    component: 'Input',
    show: false,
  },
];
