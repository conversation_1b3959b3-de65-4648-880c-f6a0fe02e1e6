<script setup lang="ts">
  import type { ActionItem, TableRowSelection } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { useModal } from '@ft/internal/components/Modal';
  import { computed, ref } from 'vue';
  import { TabPane, Tabs } from 'ant-design-vue';
  import { SearchFormSchemas, columns } from '../setting/data';
  import { InvestigationColumns, InvestigationSearchSchemas } from './data';
  import SurveyRecordDetail from './SurveyRecordDetail.vue';
  import { getQualityControlTaskPage, getTaskSurveyRecordPage } from '/@/api/task';
  import EvaluationSuggestionModal from './EvaluationSuggestionModal.vue';

  const selectedRowKeys = ref<any[]>([]);
  const rowSelection = computed<TableRowSelection>(() => {
    return {
      type: 'radio',
      selectedRowKeys: selectedRowKeys.value,
      onChange: (keys: any, _) => {
        if (keys.length === 0) {
          return;
        }
        selectedRowKeys.value = keys;
      },
    };
  });

  const [registerTable] = useTable({
    api: getQualityControlTaskPage,
    columns,
    useSearchForm: true,
    formConfig: {
      labelWidth: 80,
      schemas: SearchFormSchemas,
      fieldMapToTime: [['createTime', ['publishStartTime', 'publishEndTime'], 'YYYY-MM-DD']],
    },
    resizeHeightOffset: 16,
    inset: true,
    afterFetch: (data) => {
      selectedRowKeys.value = [data[0]?.id];
      return data;
    },
    rowKey: 'id',
  });

  const finishFlag = ref(0);

  const [registerRecordTable, rightTableIns] = useTable({
    api: getTaskSurveyRecordPage,
    columns: InvestigationColumns,
    useSearchForm: true,
    formConfig: {
      labelWidth: 80,
      schemas: InvestigationSearchSchemas,
    },
    actionColumn: {
      title: '操作',
      width: 160,
      dataIndex: 'action',
    },
    beforeFetch: (params) => {
      return {
        ...params,
        taskId: selectedRowKeys.value[0] || '-1',
        finishFlag: finishFlag.value,
      };
    },
    resizeHeightOffset: 16,
    inset: true,
    immediate: false,
  });

  function onSelectionChange() {
    rightTableIns.reload();
  }

  const [registerModal, { openModal }] = useModal();

  function onDetailRecord(record) {
    openModal(true, {
      mode: 'view',
      record,
    });
  }

  const [registerEvaluationModal, { openModal: openEvaluationModal }] = useModal();

  function onEvaluationSuggestion(record) {
    openEvaluationModal(true, {
      taskRecordId: record.id,
    });
  }

  function createActionsRecord(record, _column): ActionItem[] {
    return [
      {
        label: '查看详情',
        onClick: onDetailRecord.bind(null, record),
      },
      //  评价建议
      {
        label: '评价建议',
        onClick: onEvaluationSuggestion.bind(null, record),
      },
    ];
  }
</script>

<template>
  <div class="flex gap-2.5 h-[calc(100%-16px)] w-[calc(100%-16px)]">
    <div class="flex-1 of-hidden bg-white rounded rounded-lt-none p-4 flex flex-col">
      <div class="text-base font-bold">质控任务列表</div>
      <div class="flex-1 of-hidden">
        <BasicTable
          @selection-change="onSelectionChange"
          :rowSelection="rowSelection"
          @register="registerTable"
        />
      </div>
    </div>
    <div class="flex-1 of-hidden bg-white rounded p-4 of-auto basis-0">
      <div class="text-base font-bold mb-4">调查记录列表</div>
      <BasicTable @register="registerRecordTable">
        <template #tableTitle>
          <Tabs v-model:activeKey="finishFlag" @change="() => rightTableIns.reload()">
            <TabPane :key="0" tab="已提交" />
            <TabPane :key="1" tab="未提交" />
          </Tabs>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :divider="false" :actions="createActionsRecord(record, column)" />
          </template>
        </template>
      </BasicTable>
    </div>
    <SurveyRecordDetail @register="registerModal" />
    <EvaluationSuggestionModal @register="registerEvaluationModal" />
  </div>
</template>
