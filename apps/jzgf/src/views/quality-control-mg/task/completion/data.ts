import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { isNull } from 'lodash-es';

/**
 * 调查机构
 * 所属辖区
 */
export const InvestigationSearchSchemas: FormSchema[] = [
  {
    field: 'orgName',
    label: '调查机构',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'divisionName',
    label: '所属辖区',
    component: 'Input',
    colProps: { span: 8 },
  },
];

/**
 * 调查完成时间
 * 调查机构
 * 所属辖区
 * 调查指标录入者
 */
export const InvestigationColumns: BasicColumn[] = [
  // 是否完成
  {
    title: '是否完成',
    dataIndex: 'finishFlag',
    width: 100,
    customRender({ text }) {
      if (isNull(text)) {
        return '';
      }
      return text === 0 ? '是' : '否';
    },
  },
  {
    title: '调查完成时间',
    dataIndex: 'completionTime',
    width: 180,
  },
  {
    title: '调查机构',
    dataIndex: 'orgName',
    width: 180,
  },
  {
    title: '所属辖区',
    dataIndex: 'divisionName',
    width: 180,
  },
  {
    title: '调查指标录入者',
    dataIndex: 'updateUser',
    width: 100,
  },
];
