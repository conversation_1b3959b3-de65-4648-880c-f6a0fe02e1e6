<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { getTaskEvaluationDetail, saveTaskEvaluation, updateTaskEvaluation } from '/@/api/task';
  import { ref } from 'vue';

  const props = withDefaults(
    defineProps<{
      disabled?: boolean;
    }>(),
    {
      disabled: false,
    },
  );

  const emit = defineEmits(['register', 'success']);
  const mode = ref('add');

  const [registerForm, formAction] = useForm({
    layout: 'vertical',
    disabled: props.disabled,
    schemas: [
      // id
      {
        field: 'id',
        label: 'ID',
        component: 'Input',
        show: false,
      },
      {
        field: 'taskRecordId',
        label: '调查记录ID',
        component: 'Input',
        show: false,
      },
      // 关闭随访原因
      {
        field: 'taskEvaluation',
        label: '质控评价',
        component: 'InputTextArea',
        componentProps: {
          autoSize: { minRows: 4, maxRows: 4 },
        },
        colProps: { span: 24 },
      },
      {
        field: 'taskSuggest',
        label: '改进建议',
        component: 'InputTextArea',
        componentProps: {
          autoSize: { minRows: 4, maxRows: 4 },
        },
        colProps: { span: 24 },
      },
    ],
    showActionButtonGroup: false,
  });

  const [register, { closeModal }] = useModalInner((data) => {
    formAction.setFieldsValue({
      taskRecordId: data.taskRecordId,
    });
    getTaskEvaluationDetail(data.taskRecordId).then((data) => {
      if (data) {
        mode.value = 'edit';
        formAction.setFieldsValue(data);
      }
    });
  });

  const { loading, run: saveRun } = useRequest(
    (values) => (mode.value === 'add' ? saveTaskEvaluation(values) : updateTaskEvaluation(values)),
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess: () => {
        emit('success');
        closeModal();
      },
    },
  );

  function onOk() {
    formAction.validate().then((values) => {
      saveRun(values);
    });
  }
</script>

<template>
  <BasicModal
    v-bind="$attrs"
    @register="register"
    :width="600"
    :min-height="100"
    title="评价建议"
    centered
    @ok="onOk"
    :show-ok-btn="!props.disabled"
    :show-cancel-btn="!props.disabled"
    :ok-button-props="{
      loading,
    }"
    :destroy-on-close="true"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<style scoped></style>
