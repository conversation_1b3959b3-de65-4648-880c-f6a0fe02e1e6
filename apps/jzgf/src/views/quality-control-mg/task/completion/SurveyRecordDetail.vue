<script setup lang="ts">
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import type { TaskSurveyRecordDetail } from '/@/api/task';
  import { getTaskSurveyRecordDetail } from '/@/api/task';
  import { Button } from '@ft/internal/components/Button';
  import htmlToPdf from '@ft/internal/utils/htmlToPdf';

  withDefaults(
    defineProps<{
      title?: string;
    }>(),
    {
      title: '查看详情',
    },
  );
  defineEmits(['register']);

  const taskSurveyRecordDetail = ref<TaskSurveyRecordDetail>();

  const { runAsync: getTaskSurveyRecord } = useRequest(getTaskSurveyRecordDetail, {
    manual: true,
    onSuccess: (data) => {
      taskSurveyRecordDetail.value = data;
    },
  });

  const [register] = useModalInner((data) => {
    if (!data?.record?.id) return;
    getTaskSurveyRecord(data?.record?.id);
  });

  const loading = ref(false);
  function onDownload() {
    if (taskSurveyRecordDetail.value?.taskName) {
      loading.value = true;
      htmlToPdf(taskSurveyRecordDetail.value?.taskName, '#task-survey-preview').finally(() => {
        loading.value = false;
      });
    }
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :can-fullscreen="false"
    centered
    :min-height="600"
    width="1200px"
    @register="register"
    :footer="null"
  >
    <template #title>
      <div class="flex justify-between items-center pr-20">
        <span>{{ title }}</span>
        <Button type="primary" :loading="loading" @click="onDownload">下载</Button>
      </div>
    </template>
    <div class="max-h-550px">
      <div id="task-survey-preview" class="px-10 bg-white">
        <h2 class="text-center">{{ taskSurveyRecordDetail?.taskName }}</h2>

        <div class="basic-info mt-4 text-base w-3/4 ma pb-4">
          <div class="mb-4">
            <span>单位名称：</span>
            <span class="inline-flex w-20em border-b-1">{{ taskSurveyRecordDetail?.orgName }}</span>
          </div>
          <div class="flex gap-2">
            <div>
              <span>填报人：</span>
              <span class="inline-flex w-10em border-b-1">
                {{ taskSurveyRecordDetail?.updateUser }}
              </span>
            </div>
            <div>
              <span>电话：</span>
              <span class="inline-flex w-10em border-b-1">{{ taskSurveyRecordDetail?.phone }}</span>
            </div>
          </div>
        </div>

        <div
          class="w-3/4 ma"
          v-for="category in taskSurveyRecordDetail?.indexCategoryList ?? []"
          :key="category.id"
        >
          <div class="font-bold py-4">{{ category.indexCategory }}</div>
          <div
            class="flex border-1 border-t-0"
            v-for="(surveyItem, index) in category.taskSurveyItemList"
            :key="surveyItem.id"
            :class="{ '!border-t-1': index === 0 }"
          >
            <span class="w-50px px-2 text-center">
              {{ index + 1 }}
            </span>
            <span class="border-x-1 flex-1 px-2">{{ surveyItem.indexName }}</span>
            <span class="w-1/6 px-2">
              {{ surveyItem.indexValue }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </BasicModal>
</template>
