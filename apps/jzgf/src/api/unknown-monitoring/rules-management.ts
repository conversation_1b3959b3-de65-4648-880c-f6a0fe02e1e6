import { defHttp } from '@ft/request';

export const rulesMgApi = {
  add: (data) => defHttp.post({ url: '/monitorRuleInfo/save', data }),
  edit: (data) => defHttp.post({ url: '/monitorRuleInfo/update', data }),
  editEnable: (data) => defHttp.post({ url: '/monitorRuleInfo/switchEnableFlag', data }),
  page: (data) => defHttp.post({ url: '/monitorRuleInfo/page', data }),
  detail: (id) => defHttp.get({ url: '/monitorRuleInfo/detail/' + id }),
  delete: (id) => defHttp.delete({ url: '/monitorRuleInfo/delete/' + id }),
};
