import { defHttp } from '@ft/request';

/**
 * 管理统计-获取不明原因传染病管理统计数据表列集合
 * /unknownCausePatientInfo/getManageStatisticsTableColumns
 * */
export const getManageStatisticsTableColumns = () => {
  return defHttp.get({
    url: '/unknownCausePatientInfo/getManageStatisticsTableColumns',
  });
};
/**
 * 管理统计-获取不明原因传染病管理统计数据
 * /unknownCausePatientInfo/queryManageStatisticsData
 * */
export const queryManageStatisticsData = (data: any) => {
  return defHttp.post({
    url: '/unknownCausePatientInfo/queryManageStatisticsData',
    data,
  });
};

/**
 * 管理统计-导出不明原因传染病管理统计数据Excel
 * /unknownCausePatientInfo/exportManageStatisticsData
 * */
export const exportManageStatisticsData = (data: any) => {
  return defHttp.post(
    { url: '/unknownCausePatientInfo/exportManageStatisticsData', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );
};
