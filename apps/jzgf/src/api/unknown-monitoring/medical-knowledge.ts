import { defHttp } from '@ft/request';

/**
 * @description 新增
 * /medicalKnowledge/add
 * */
export const addMedicalKnowledge = (data?: any) => {
  return defHttp.post({
    url: '/medicalKnowledge/add',
    data,
  });
};

/**
 * @description 更新
 * /medicalKnowledge/update
 * */
export const updateMedicalKnowledge = (data?: any) => {
  return defHttp.post({
    url: '/medicalKnowledge/update',
    data,
  });
};

/**
 * @description 根据诊断编码查看详情
 * /medicalKnowledge/getByIcdCode/{icdCode}
 * */
export const getMedicalKnowledgeByIcdCode = (icdCode?: string) => {
  return defHttp.get({
    url: `/medicalKnowledge/getByIcdCode/${icdCode}`,
  });
};
