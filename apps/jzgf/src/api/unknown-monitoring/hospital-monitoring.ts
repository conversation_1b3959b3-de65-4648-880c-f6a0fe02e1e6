import { defHttp } from '@ft/request';

/**
 * @description  不明原因传染病在院病例数
 */
export const getInpatientCasesCount = (data: any) => {
  return defHttp.post({
    url: '/unknownCausePatientInfo/queryInpatientCasesCount',
    data,
  });
};

/**
 * @description  获取所属区划下拉列表选项
 */
export const getDivisionList = () => {
  return defHttp.get({
    url: '/unknownCausePatientInfo/dropDown/getDivisionList',
  });
};
