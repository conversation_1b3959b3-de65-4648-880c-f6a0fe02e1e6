import { defHttp } from '@ft/request';

export interface IIndexValueList {
  /** 主键ID */
  id: string;
  /** 指标ID */
  indexId: string;
  /** 指标名称 */
  indexName: string;
  /** 指标值 */
  indexValue: string;
  /** 机构ID */
  orgId: string;
  /** 机构单位名称 */
  orgName: string;
  /** 调查任务记录ID */
  taskRecordId: string;
}
export interface IQualityStat {
  /** 指标分类ID */
  categoryId: string;
  /** 指标分类名称 */
  indexCategory: string;
  /** 指标ID */
  indexId: string;
  /** 指标名称 */
  indexName: string;
  /** 指标值集合 */
  indexValueList?: IIndexValueList[];
  /** 调查任务ID */
  taskId: string;
  /** 模板ID */
  templateId: string;
}
/**
 * 调查任务记录服务-查询质控统计指标列表
 */
export function getQualityStatList(params: Recordable) {
  return defHttp.get<IQualityStat[]>({
    url: `/taskSurveyRecord/queryItemList`,
    params,
  });
}
