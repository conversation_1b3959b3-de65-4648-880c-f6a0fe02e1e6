import { defHttp } from '@ft/request';

/**
 * @description 传染病不规范治疗统计服务-不规范治疗-预警提醒
 */
// const { apiUrl, urlPrefix } = useGlobSetting();
// export const DearlyWarningReminderSSEConnect = `${apiUrl}${urlPrefix}/NStandardTreatStatistics/earlyWarningReminder`;
export const earlyWarningReminder = (data?: any) => {
  return defHttp.post({
    url: '/NStandardTreatStatistics/earlyWarningReminder',
    data,
  });
};

interface INStandardTreatStatistic {
  columns: Children[];
  dataList: any[];
  year: string;
  startDate: string;
  endDate: string;
}
export interface Children {
  children: Children[];
  key: string;
  title: string;
}

/**
 * @description 传染病不规范治疗统计服务-不规范治疗数据统计查询-区域
 */
export const queryNStandardTreatAreaStatistics = (data?: any) => {
  return defHttp.post<INStandardTreatStatistic>({
    url: '/NStandardTreatStatistics/queryNStandardTreatAreaStatistics',
    data,
  });
};

/**
 * @description 传染病不规范治疗统计服务-不规范治疗数据统计查询-机构
 */
export const queryNStandardTreatMedicalStatistics = (data?: any) => {
  return defHttp.post<INStandardTreatStatistic>({
    url: '/NStandardTreatStatistics/queryNStandardTreatMedicalStatistics',
    data,
  });
};
export interface INStandardTreatPatientInfoList {
  admitHospitalTime: string;
  age: number;
  areaName: string;
  diagnosticResults: string;
  medicalName: string;
  notRegulateContent: string;
  patientName: string;
  ruleName: string;
  id: string;
}

/**
 * @description 不规范治疗数据患者信息-列表查询
 */
export const queryNStandardTreatPatientInfoList = (data?: any) => {
  return defHttp.post<INStandardTreatPatientInfoList[]>({
    url: '/NStandardTreatStatistics/queryNStandardTreatPatientInfoList',
    data,
  });
};

/**
 * @description 不规范治疗数据患者信息-列表导出
 */
export function unStandardTreatPatientInfoListExport(data?: any) {
  return defHttp.post(
    {
      url: `/NStandardTreatStatistics/unStandardTreatPatientInfoListExport`,
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
}
/**
 * 不规范治疗-不规范记录人工处理
 * /NStandardTreatStatistics/notStandardRecordHandle
 */
export const notStandardRecordHandle = (data?: any) => {
  return defHttp.post({
    url: '/NStandardTreatStatistics/notStandardRecordHandle',
    data,
  });
};
