import { defHttp } from '@ft/request';

/**
 * @description  病例汇总-重点（重症）传染病在院病例数
 */
export const getInpatientCasesCount = (data: any) => {
  return defHttp.post({
    url: '/severeCausePatientInfo/queryInpatientCasesCount',
    data,
  });
};

/**
 * @description  获取所属区划下拉列表选项
 */
export const getDivisionList = () => {
  return defHttp.get({
    url: '/severeCausePatientInfo/dropDown/getDivisionList',
  });
};
