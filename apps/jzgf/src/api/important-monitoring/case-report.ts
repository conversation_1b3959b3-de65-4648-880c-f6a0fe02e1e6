import { defHttp } from '@ft/request';

/**
 * @description  患者列表 分页
 */
export interface IPatientListItem {
  id: string;
  medicalCardNo?: any;
  patientName: string;
  idCardNo: string;
  sexName: string;
  age: number;
  admitHospitalTime: string;
  diagnoseName: string;
  visitHospitalName: string;
  visitDeptName: string;
  visitDoctorName: string;
  divisionName: string;
  reportDoctorName?: any;
  reportHospitalName?: any;
  reportTime?: any;
  monitorTime?: any;
  auxiliaryStatus: string;
}
export const getPatientPage = (data?: any) => {
  return defHttp.post<{ list: IPatientListItem[] }>({
    url: '/severeCausePatientInfo/page',
    data,
  });
};

/**
 * @description  新增患者
 */
// export const addPatient = (data?: any) => {
//   return defHttp.post<unknown>({
//     url: '/unknownCausePatientInfo/save',
//     data,
//   });
// };
/**
 * @description  删除患者
 */
export const deletePatient = (id: string) => {
  return defHttp.delete({
    url: '/severeCausePatientInfo/delete?id=' + id,
  });
};
/**
 * @description  根据患者住院号获取患者信息
 */
// export interface IPatientInfo {
//   id?: any;
//   inpatientNo: string;
//   medicalCardNo?: any;
//   patientId: string;
//   patientName: string;
//   idCardNo: string;
//   sexName: string;
//   age: number;
//   admitHospitalTime: string;
//   diagnoseName: string;
//   visitHospitalName: string;
//   visitDeptName: string;
//   visitDoctorName: string;
//   divisionName: string;
// }
// export const getPatientInfoByInpatientNo = (inpatientNo: string) => {
//   return defHttp.get<IPatientInfo>({
//     url: '/unknownCausePatientInfo/getPatientInfoByInpatientNo?inpatientNo=' + inpatientNo,
//   });
// };

/**
 * @description  一键上报
 */
export const reportPatient = (id: string) => {
  return defHttp.get<unknown>({
    url: '/severeCausePatientInfo/doReport?id=' + id,
  });
};

/**
 * @description  导出
 */
export const exportPatient = (data: any) => {
  return defHttp.post(
    { url: '/severeCausePatientInfo/export', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );
};

/**
 * @description  患者详情
 */
export interface IPatientDetail {
  id: string;
  pkVisitInfo?: any;
  outInId: string;
  medicalCardNo?: any;
  patientName: string;
  idCardNo: string;
  sexName: string;
  age: number;
  visitDate: string;
  diagnoseName: string;
  visitHospitalName: string;
  visitDeptName: string;
  visitDoctorName: string;
  divisionName: string;
}
export const getPatientDetail = (id: string) => {
  return defHttp.get<IPatientDetail>({
    url: '/severeCausePatientInfo/detail?id=' + id,
  });
};

/**
 * @description  医院端_病例上报》提醒_获取重点（重症）病例辅助诊断建议提醒最早一条的患者信息
 */
export const getPatientInfoForAssistedDiagnosis = () => {
  return defHttp.get({ url: '/severeCausePatientInfo/getPatientInfoForAssistedDiagnosis' });
};

/**
 * @description  医院端_病例上报》提醒_更新重点（重症）病例辅助诊断建议提醒查看状态为已查看
 */
export const updateAssistedDiagnosisViewStatus = (id: string) => {
  return defHttp.get({
    url: '/severeCausePatientInfo/updateAssistedDiagnosisViewStatus?id=' + id,
  });
};
