import { defHttp } from '@ft/request';

/**
 * @description  获取上报医疗机构下拉列表选项
 */
export const getReportHospitalList = () => {
  return defHttp.get({
    url: '/severeCausePatientInfo/dropDown/getReportHospitalList',
  });
};

/**
 *  @description  获取上报医生下拉列表选项
 */
export const getReportDoctorList = () => {
  return defHttp.get({
    url: '/severeCausePatientInfo/dropDown/getReportDoctorList',
  });
};

/**
 * @description  查看详情
 */
export const getAssistedDiagnosisInfo = (id: string) => {
  return defHttp.get({
    url: `/assistedDiagnosisInfo/detail/${id}`,
  });
};

/**
 * @description  新增
 */
export const addAssistedDiagnosis = (data: any) => {
  return defHttp.post({ url: '/assistedDiagnosisInfo/save', data });
};

/**
 * @description  更新
 */
export const updateAssistedDiagnosis = (data: any) => {
  return defHttp.post({ url: '/assistedDiagnosisInfo/update', data });
};

/**
 * @description  中心端_病例管理》智能提醒_获取重点（重症）病例监测管理提醒最早一条的患者信息
 */
export const getPatientInfoForRemind = () => {
  return defHttp.get({ url: '/severeCausePatientInfo/getPatientInfoForRemind' });
};

/**
 * @description  中心端_病例管理》智能提醒_更新重点（重症）病例监测管理提醒查看状态为已查看
 */
export const updateViewStatus = (id: string) => {
  return defHttp.get({ url: '/severeCausePatientInfo/updateViewStatus?id=' + id });
};
