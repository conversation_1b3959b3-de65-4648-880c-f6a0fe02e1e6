import { defHttp } from '@ft/request';

/**
 * @description 获取列表
 * /medicalKnowledge/list
 * */
export const getMedicalKnowledgeList = (knowledgeType: number) => {
  return defHttp.get({
    url: '/medicalKnowledge/list?knowledgeType=' + knowledgeType,
  });
};
/**
 * @description 列表批量新增
 * /medicalKnowledge/batchSave
 * */
export const batchSaveMedicalKnowledge = (data?: any) => {
  return defHttp.post({
    url: '/medicalKnowledge/batchSave',
    data,
  });
};
/**
 * @description 查看详情
 * /medicalKnowledge/detail/{id}
 * */
export const getMedicalKnowledgeDetail = (id?: string) => {
  return defHttp.get({
    url: `/medicalKnowledge/detail/${id}`,
  });
};
/**
 * @description 删除
 * /medicalKnowledge/delete/{id}
 * */
export const deleteMedicalKnowledge = (id?: string) => {
  return defHttp.delete({
    url: `/medicalKnowledge/delete/${id}`,
  });
};

/**
 * @description 获取传染病诊断信息
 * /medicalKnowledge/getInfectiousDiseases
 * */
export const getInfectiousDiseases = () => {
  return defHttp.get({
    url: '/medicalKnowledge/getInfectiousDiseases',
  });
};

/**
 * @description 获取已添加诊断
 * /medicalKnowledge/getAddedDiagnosis
 * */
export const getExistAddedDiagnosis = () => {
  return defHttp.get({
    url: '/medicalKnowledge/getAddedDiagnosis',
  });
};
