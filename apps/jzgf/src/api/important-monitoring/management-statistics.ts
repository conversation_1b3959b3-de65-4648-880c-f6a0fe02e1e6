import { defHttp } from '@ft/request';

/**
 * 管理统计-获取重点（重症）传染病管理统计数据表列集合
 * /severeCausePatientInfo/getManageStatisticsTableColumns
 * */
export const getManageStatisticsTableColumns = () => {
  return defHttp.get({
    url: '/severeCausePatientInfo/getManageStatisticsTableColumns',
  });
};
/**
 * 管理统计-获取重点（重症）传染病管理统计数据
 * /severeCausePatientInfo/queryManageStatisticsData
 * */
export const queryManageStatisticsData = (data: any) => {
  return defHttp.post({
    url: '/severeCausePatientInfo/queryManageStatisticsData',
    data,
  });
};

/**
 * 管理统计-导出重点（重症）传染病管理统计数据Excel
 * /severeCausePatientInfo/exportManageStatisticsData
 * */
export const exportManageStatisticsData = (data: any) => {
  return defHttp.post(
    {
      url: '/severeCausePatientInfo/exportManageStatisticsData',
      responseType: 'blob',
      data,
    },
    {
      isReturnNativeResponse: true,
    },
  );
};
