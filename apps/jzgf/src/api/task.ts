import { defHttp } from '@ft/request';

export interface IQualityControlTask {
  contact: string;
  createTime: string;
  createUser: string;
  deleteFlag: number;
  finishDate: string;
  id: string;
  orgInfoList?: OrgInfoList[];
  taskContent: string;
  taskGoal: string;
  taskName: string;
  taskYear: string;
  templateId: string;
  templateName: string;
  taskObject: string;
  updateTime: string;
  updateUser: string;
  taskMonth: string;
  taskType: number;
  statisticsFlag: number;
}

export interface OrgInfoList {
  divisionId: string;
  divisionName: string;
  orgId: string;
  orgName: string;
  phone: string;
}

/**
 * 质控任务管理服务-查询任务列表
 */
export function getQualityControlTaskPage(data = {}) {
  return defHttp.post({ url: `/qualityControlTask/queryList`, data });
}

/**
 * 质控任务管理服务-查询质控任务下拉列表
 */
export function getQualityControlTaskList(taskName = '') {
  return defHttp.get({ url: `/qualityControlTask/queryTaskList`, params: { taskName } });
}

/**
 * 质控任务管理服务-查询任务详情
 */
export function getQualityControlTaskDetails(id: string) {
  return defHttp.get<IQualityControlTask>({ url: `/qualityControlTask/queryTaskById/${id}` });
}

/**
 * 质控任务管理服务-新增质控任务
 */
export function saveQualityControlTask(data: Omit<IQualityControlTask, 'id'>) {
  return defHttp.post({ url: `/qualityControlTask/add`, data });
}

/**
 * 质控任务管理服务-编辑质控任务
 */
export function editQualityControlTask(data: IQualityControlTask) {
  return defHttp.post({ url: `/qualityControlTask/edit`, data });
}

/**
 * 质控任务管理服务-删除质控任务
 */
export function removeQualityControlTask(taskId: string) {
  return defHttp.delete({ url: `/qualityControlTask/delete/${taskId}` });
}

/**
 * 调查任务记录服务-提交或保存质控调查任务反馈
 */
export function submitTaskSurveyRecord(data?: any) {
  return defHttp.post({ url: `/taskSurveyRecord/submit`, data });
}

/**
 * 调查任务记录服务-分页查询任务调查记录信息
 */
export function getTaskSurveyRecordPage(data?: any) {
  return defHttp.post({ url: `/taskSurveyRecord/queryPage`, data });
}

/**
 * 调查任务记录服务-分页查询任务调查记录信息
 */
export function getMyTaskSurveyRecordPage(data?: any) {
  return defHttp.post({ url: `/taskSurveyRecord/queryMyPage`, data });
}

export interface TaskSurveyRecordDetail {
  completionTime: string;
  divisionId: string;
  divisionName: string;
  id: string;
  indexCategoryList: IndexCategoryList[];
  orgId: string;
  orgName: string;
  phone: string;
  taskId: string;
  taskName: string;
  /** 任务状态：1未开始，2 已保存，3 已提交 */
  taskStatus: number;
  templateId: string;
  templateName: string;
  updateTime: string;
  updateUser: string;
}

export interface IndexCategoryList {
  id: string;
  indexCategory: string;
  taskSurveyItemList: TaskSurveyItemList[];
  templateId: string;
}

export interface TaskSurveyItemList {
  categoryId: string;
  createTime: string;
  createUser: string;
  deleteFlag: number;
  id: string;
  indexCategory: string;
  indexId: string;
  indexName: string;
  indexValue: string;
  taskId: string;
  taskRecordId: string;
  templateId: string;
  updateTime: string;
  updateUser: string;
}

/**
 * 调查任务记录服务-查询调查任务指标填报反馈明细
 */
export function getTaskSurveyRecordDetail(id: string) {
  return defHttp.get<TaskSurveyRecordDetail>({
    url: `/taskSurveyRecord/queryRecordDetail?recordId=${id}`,
  });
}

/**
 * 查询调查任务上一次指标填报反馈详细信息
 * GET /taskSurveyRecord/queryLastRecord
 */
export function getTaskSurveyRecordLastRecord(recordId: string) {
  return defHttp.get<TaskSurveyRecordDetail>({
    url: `/taskSurveyRecord/queryLastRecord`,
    params: { recordId },
  });
}
/**
 * 调查任务记录服务-导出任务调查记录
 */
export function exportTaskSurveyRecord(data?: any) {
  return defHttp.post(
    { url: `/taskSurveyRecord/export`, data, responseType: 'blob' },
    {
      isReturnNativeResponse: true,
    },
  );
}

/**
 * 校验质控任务模板是否有更新
 * /taskSurveyRecord/checkUpdate
 */

export function taskSurveyRecordCheck(recordId: string) {
  return defHttp.get({ url: `/taskSurveyRecord/checkUpdate`, params: { recordId } });
}

export interface SurveyFormModel {
  id?: string;
  phone: string;
  surveyItemList: SurveyItemList[];
  taskStatus: number;
  updateUser: string;
}

export interface SurveyItemList {
  categoryId: string;
  id?: string;
  indexCategory: string;
  indexId: string;
  indexName: string;
  indexValue: string;
  taskId?: string;
  taskRecordId?: string;
  templateId?: string;
  /** 为前端使用字段 */
  colSlot?: string;
  /** 为前端使用字段 */
  isRequired?: number;
  /** 为前端使用字段 */
  isShow?: number;
}

export interface ITaskEvaluationDetail {
  /** 创建时间 */
  createTime: any;
  /** 创建人 */
  createUser: string;
  /** 删除标识 0 正常 1 删除 */
  deleteFlag: number;
  /** 主键ID */
  id: string;
  /** 质控评价 */
  taskEvaluation: string;
  /** 质控反馈记录ID */
  taskRecordId: string;
  /** 改进建议 */
  taskSuggest: string;
  /** 更新时间 */
  updateTime: any;
  /** 修改人 */
  updateUser: string;
}
/**
 * task-evaluation-facade-查询质控任务评价建议详情
 */
export function getTaskEvaluationDetail(taskRecordId: string) {
  return defHttp.get<ITaskEvaluationDetail>({
    url: `/taskEvaluation/queryRecordDetail`,
    params: { taskRecordId },
  });
}

export interface ITaskEvaluation {
  id: string;
  taskEvaluation: string;
  taskRecordId: string;
  taskSuggest: string;
}
/**
 * task-evaluation-facade-添加评价建议
 */
export function saveTaskEvaluation(data: Omit<ITaskEvaluation, 'id'>) {
  return defHttp.post({ url: `/taskEvaluation/save`, data });
}

/**
 * task-evaluation-facade-编辑评价建议
 */
export function updateTaskEvaluation(data: ITaskEvaluation) {
  return defHttp.post({ url: `/taskEvaluation/update`, data });
}
