import { defHttp } from '@ft/request';

interface ListItem {
  id: string;
  kindName: string;
  sortNum: number;
  updateTime: string;
  updateUser: string;
}
/**
 * @description 传染病不规范治疗规则类别列表查询 不分页
 */
export const ruleKindList = (data?: any) => {
  return defHttp.post<ListItem[]>({
    url: '/ruleKind/list',
    data,
  });
};
/**
 * @description 传染病不规范治疗规则类别列表查询 分页
 */
export const ruleKindPage = (data?: any) => {
  return defHttp.post<ListItem[]>({
    url: '/ruleKind/page',
    data,
  });
};
/**
 * @description 新增传染病不规范治疗规则类别
 */
export const ruleKindAdd = (data?: any) => {
  return defHttp.post({
    url: '/ruleKind/add',
    data,
  });
};
/**
 * @description 更新传染病不规范治疗规则类别
 */
export const ruleKindEdit = (data?: any) => {
  return defHttp.post({
    url: '/ruleKind/edit',
    data,
  });
};
/**
 * @description 删除传染病不规范治疗规则类别
 */
export const ruleKindDel = (id?: string) => {
  return defHttp.delete({
    url: `/ruleKind/del/${id}`,
  });
};

export interface RuleListItem {
  diseaseCode: string;
  enableFlag: number;
  enableFlagDesc: string;
  id: string;
  kindId: string;
  ruleMechanism: string;
  ruleName: string;
  updateTime: string;
  updateUser: string;
  /**前端使用 */
  loading?: boolean;
}
/**
 * @description  传染病不规范治疗规则列表 不分页
 */
export const ruleList = (data?: any) => {
  return defHttp.post<RuleListItem[]>({
    url: '/rule/list',
    data,
  });
};
/**
 * @description  传染病不规范治疗规则列表 分页
 */
export const rulePage = (data?: any) => {
  return defHttp.post<RuleListItem[]>({
    url: '/rule/page',
    data,
  });
};
/**
 * @description  传染病不规范治疗规则 启用&禁用开关
 */
export const ruleStateSwitch = (data?: any) => {
  return defHttp.post<RuleListItem[]>({
    url: '/rule/stateSwitch',
    data,
  });
};
/**
 * @description  更新传染病不规范治疗规则
 */
export const ruleEdit = (data?: any) => {
  return defHttp.post<RuleListItem[]>({
    url: '/rule/edit',
    data,
  });
};
/**
 * @description  新增传染病不规范治疗规则
 */
export const ruleAdd = (data?: any) => {
  return defHttp.post<RuleListItem[]>({
    url: '/rule/add',
    data,
  });
};
/**
 * @description 删除传染病不规范治疗规则/rule/del/{id}
 */
export const ruleDel = (id?: string) => {
  return defHttp.delete({
    url: `/rule/del/${id}`,
  });
};

/**
 * @description 不规范治疗-艾滋病不规范治疗规则列表
 * /NStandardTreatStatistics/getHivNStandardTreatEarlyRuleList
 */
export interface IHivNStandardTreatEarlyRuleList {
  /** 主键 */
  id: string;
  /** 类别名称 */
  kindName: string;
  /** 规则列表 */
  ruleList: TreatmentRule[];
  /** 疾病编码(字典) */
  diseaseCode: string;
  /** 启用标志 1 启用 0 禁用 */
  enableFlag: number;
  /** 启用标志描述(前端展示用) 1 启用 0 禁用 */
  enableFlagDesc: string;
}
export interface TreatmentRule {
  /** 主键 */
  id: string;
  /** 类别表主键 */
  kindId: string;
  /** 规则机制 */
  ruleMechanism: string;
  /** 规则名称 */
  ruleName: string;
  /** 更新时间 */
  updateTime: string;
  /** 修改人 */
  updateUser: string;
  /** 排序号 */
  sortNum: number;
}
export const getHivNStandardTreatEarlyRuleList = (data?: any) => {
  return defHttp.post<IHivNStandardTreatEarlyRuleList[]>({
    url: '/NStandardTreatStatistics/getHivNStandardTreatEarlyRuleList',
    data,
  });
};

/**
 * @description 不规范治疗-分页查询手动新增艾滋病不规范治疗患者信息
 * /NStandardTreatStatistics/getManualHivNStandardTreatEarlyPatientPage
 */
export interface IManualHivNStandardTreatEarlyPatientPage {
  /** 入院时间 */
  admitHospitalTime: string;
  /** 年龄 */
  age: number;
  /** 所属区域编码 */
  areaCode: string;
  /** 所属区域 */
  areaName: string;
  /** 临床诊断 */
  diagnosticResults: string;
  /** Id */
  id: string;
  /** 病种类型 */
  infectionDisease: string;
  /** 医疗机构编码 */
  medicalCode: string;
  /** 医疗机构 */
  medicalName: string;
  /** 不符合治疗内容 */
  notRegulateContent: string;
  /** 不规范治疗规则ID */
  notStandardTreatRuleId: string;
  /** 不规范治疗规则类型ID */
  notStandardTreatRuleKindId: string;
  /** 患者姓名 */
  patientName: string;
}
export const getManualHivNStandardTreatEarlyPatientPage = (data?: any) => {
  return defHttp.post<IManualHivNStandardTreatEarlyPatientPage[]>({
    url: '/NStandardTreatStatistics/getManualHivNStandardTreatEarlyPatientPage',
    data,
  });
};

/**
 * @description 不规范治疗-手动新增艾滋病不规范治疗患者信息
 * /NStandardTreatStatistics/addManualHivNStandardTreatEarlyPatient
 */
export const addManualHivNStandardTreatEarlyPatient = (data?: any) => {
  return defHttp.post({
    url: '/NStandardTreatStatistics/addManualHivNStandardTreatEarlyPatient',
    data,
  });
};

/**
 * @description 不规范治疗-修改手动新增艾滋病不规范治疗患者信息
 * /NStandardTreatStatistics/updateManualHivNStandardTreatEarlyPatient
 */
export const updateManualHivNStandardTreatEarlyPatient = (data?: any) => {
  return defHttp.post({
    url: '/NStandardTreatStatistics/updateManualHivNStandardTreatEarlyPatient',
    data,
  });
};

/**
 * 不规范治疗-删除手动新增艾滋病不规范治疗患者信息
 * /NStandardTreatStatistics/deleteManualHivNStandardTreatEarlyPatient/{id}
 */
export const deleteManualHivNStandardTreatEarlyPatient = (id: string) => {
  return defHttp.post({
    url: `/NStandardTreatStatistics/deleteManualHivNStandardTreatEarlyPatient/${id}`,
  });
};

/**
 * 不规范治疗-根据ID查询手动新增艾滋病不规范治疗患者信息
 * /NStandardTreatStatistics/getManualHivNStandardTreatEarlyPatientInfo/{id}
 */
export const getManualHivNStandardTreatEarlyPatientInfo = (id: string) => {
  return defHttp.post({
    url: `/NStandardTreatStatistics/getManualHivNStandardTreatEarlyPatientInfo/${id}`,
  });
};
