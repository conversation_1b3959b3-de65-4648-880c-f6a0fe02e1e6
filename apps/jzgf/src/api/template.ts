import { defHttp } from '@ft/request';

export interface IQualityControlTemplate {
  id: string;
  templateName: string;
}

export interface IQualityControlTemplateDetail extends IQualityControlTemplate {
  indexCategoryList: (IQualityControlIndex & { indexItemList?: IQualityControlIndexItem[] })[];
}
/**
 * 质控指标模板服务-查询质控指标模板列表
 */
export function getQualityControlTemplateList() {
  return defHttp.get<IQualityControlTemplate[]>({ url: `/qualityControlTemplate/queryList` });
}
/**
 * 质控指标模板服务-新增质控指标模板
 */
export function saveQualityControlTemplate(data: Omit<IQualityControlTemplate, 'id'>) {
  return defHttp.post({ url: `/qualityControlTemplate/save`, data });
}

/**
 * 质控指标模板服务-修改质控指标模板
 */
export function updateQualityControlTemplate(data: IQualityControlTemplate) {
  return defHttp.post({ url: `/qualityControlTemplate/update`, data });
}

/**
 * 质控指标模板服务-删除质控指标模板
 */
export function removeQualityControlTemplate(id: string) {
  return defHttp.post({ url: `/qualityControlTemplate/remove?id=${id}` });
}

/**
 * 质控指标模板服务-预览质控指标模板明细
 */
export function getQualityControlTemplateDetail(id: string) {
  return defHttp.get<IQualityControlTemplateDetail>({
    url: `/qualityControlTemplate/queryDetail?id=${id}`,
  });
}

export interface IQualityControlIndex {
  id: string;
  indexCategory: string;
  templateId: string;
}

/**
 * 质控指标分类服务-查询质控指标分类列表
 */
export function getQualityControlIndexList(templateId: string) {
  return defHttp.get<IQualityControlIndex[]>({
    url: `/qualityControlIndex/queryList?templateId=${templateId}`,
  });
}

/**
 * 质控指标分类服务-新增质控指标分类
 */
export function saveQualityControlIndex(data: Omit<IQualityControlIndex, 'id'>) {
  return defHttp.post({ url: `/qualityControlIndex/save`, data });
}

/**
 * 质控指标分类服务-修改质控指标分类
 */
export function updateQualityControlIndex(data: IQualityControlIndex) {
  return defHttp.post({ url: `/qualityControlIndex/update`, data });
}

/**
 * 质控指标分类服务-删除质控指标分类
 */
export function removeQualityControlIndex(id: string) {
  return defHttp.post({ url: `/qualityControlIndex/remove?id=${id}` });
}

export interface IQualityControlIndexItem {
  categoryId: string;
  id: string;
  indexCategory: string;
  indexName: string;
  isRequired: number;
  isShow: number;
  sortNum: number;
  templateId: string;
}

/**
 * 质控指标明细服务-分页查询质控指标明细列表
 */
export function getQualityControlIndexItemPage(data = {}) {
  return defHttp.post<IQualityControlIndexItem[]>({
    url: `/qualityControlIndexItem/queryPage`,
    data,
  });
}

/**
 * 质控指标明细服务-保存质控指标明细
 */
export function saveQualityControlIndexItem(data: Omit<IQualityControlIndexItem, 'id'>) {
  return defHttp.post({ url: `/qualityControlIndexItem/save`, data });
}

/**
 * 质控指标明细服务-修改质控指标明细
 */
export function updateQualityControlIndexItem(data: Partial<IQualityControlIndexItem>) {
  return defHttp.post({ url: `/qualityControlIndexItem/update`, data });
}

/**
 * 质控指标明细服务-删除质控指标明细
 */
export function removeQualityControlIndexItem(id: string) {
  return defHttp.post({ url: `/qualityControlIndexItem/delete?id=${id}` });
}
