<script setup lang="ts">
  withDefaults(
    defineProps<{
      color?: 'orange' | 'blue' | 'green';
    }>(),
    {
      color: 'orange',
    },
  );
</script>

<template>
  <div :class="[color]" class="inline-flex rounded-full border-1px px-4px">
    <slot></slot>
  </div>
</template>
<style lang="less">
  .orange {
    color: #ff7d34;
    background: #fff2eb;
    border-color: #ff7d34;
  }

  .blue {
    color: #4c81ff;
    background: #eef3ff;
    border-color: #4c81ff;
  }

  .green {
    color: #32bf2f;
    background: #ecffec;
    border-color: #32bf2f;
  }
</style>
