# Whether to open mock
VITE_USE_MOCK=false

# public path
VITE_PUBLIC_PATH=/crb-xtgl

# Delete console
VITE_DROP_CONSOLE=false

# Whether to enable gzip or brotli compression
# Optional: gzip | brotli | none
# If you need multiple forms, you can use `,` to separate
VITE_BUILD_COMPRESS='none'

# Whether to delete origin files when using compress, default false
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE=false

# Basic interface address SPA
VITE_GLOB_API_URL=/api

# File upload address， optional
# It can be forwarded by nginx or write the actual address directly
VITE_GLOB_UPLOAD_URL=/upload

# Interface prefix
VITE_GLOB_API_URL_PREFIX=/infection-sysmgt

# enable sms check
VITE_GLOB_SMS_CHECK=false