import './design/global.less';
import { basicRoutes } from '@ft/internal/router/routes';
import type { AppRouteRecordRaw } from '@ft/internal/router/types';
window['DYNAMIC_VIEW_MODULES'] = Object.fromEntries(
  Object.entries(import.meta.glob('./views/**/*.{vue,tsx}')).map(([k, v]) => [
    k.replace('./views', '/xtgl'),
    v,
  ]),
);

const LOGIN: AppRouteRecordRaw = {
  path: '/login',
  name: 'login',
  component: () => import('./views/login/index.vue'),
  meta: {
    title: '登录',
    hideMenu: true,
    hideTab: true,
  },
};
const HOME: AppRouteRecordRaw = {
  path: '/home',
  name: 'home',
  component: () => import('./views/home/<USER>'),
  meta: {
    title: '首页',
    hideMenu: true,
    hideTab: true,
  },
};
basicRoutes.push(LOGIN);
basicRoutes.push(HOME);
Promise.resolve().then(() => import('@ft/internal/bootstrap'));
