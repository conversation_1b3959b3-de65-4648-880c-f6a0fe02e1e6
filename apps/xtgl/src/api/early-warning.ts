import { defHttp } from '@ft/request';

export const earlyWarningApi = {
  add: (data) => defHttp.post({ url: '/earlyWarnIndex/save', data }),
  edit: (data) => defHttp.post({ url: '/earlyWarnIndex/edit', data }),
  editEnable: (id) => defHttp.post({ url: '/earlyWarnIndex/editEnableFlag/' + id }),
  editDefaultFlag: (id) => defHttp.post({ url: '/earlyWarnIndex/editDefaultFlag/' + id }),
  page: (data) => defHttp.post({ url: '/earlyWarnIndex/page', data }),
  detail: (id) => defHttp.post({ url: '/earlyWarnIndex/getById/' + id }),
  delete: (id) => defHttp.post({ url: '/earlyWarnIndex/delete/' + id }),
};
