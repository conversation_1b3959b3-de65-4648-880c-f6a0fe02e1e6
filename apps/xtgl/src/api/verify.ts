import { defHttp } from '@ft/request';

export interface IRoleMenu {
  menuName: string;
  roleName: string;
  id: string;
  /** 仅前端使用 */
  _loading: boolean;
}
/**
 * 角色菜单
 * /sysRoleMenu/page
 */
export const getRoleMenu = (data) => {
  return defHttp.post({ url: '/sysRoleMenu/page', data });
};

/**
 * /sysRoleMenu/verifySignData
 */
export const verifyRoleMenuSignData = (id: string) => {
  return defHttp.get({ url: `/sysRoleMenu/verifySignData?id=${id}` });
};

export interface IUserRole {
  id: string;
  roleName: string;
  username: string;
  /** 仅前端使用 */
  _loading: boolean;
}

/**
 * /sysUserRole/page
 */

export const getUserRole = (data) => {
  return defHttp.post({ url: '/sysUserRole/page', data });
};

/**
 * /sysUserRole/verifySignData
 */
export const verifyUserRoleSignData = (id: string) => {
  return defHttp.get({ url: `/sysUserRole/verifySignData?id=${id}` });
};
