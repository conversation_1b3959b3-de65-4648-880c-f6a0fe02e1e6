import { defHttp } from '@ft/request';

export interface IMenu {
  id: string;
  menuComponent: string;
  menuIcon: string;
  menuName: string;
  menuNote: string;
  menuRoutePath: string;
  menuType: number;
  parentId: string;
  sortNo: number;
}

export const deleteMenu = (id: string) => defHttp.delete({ url: '/sysMenu/' + id });

export const getMenuTree = (data?) => defHttp.get({ url: '/sysMenu/tree', params: data });

export const postMenu = (data: Omit<IMenu, 'id'>) =>
  defHttp.post({ url: '/sysMenu/save', params: data });

export const putMenu = (data: IMenu) => defHttp.put({ url: '/sysMenu/edit', data });
