import { defHttp } from '@ft/request';

interface IDetailByCode {
  comment: string;
  id: string;
  msgTypeCode: number;
  msgTypeName: string;
  object: string;
  objectCode: number;
  status: number;
  statusDesc: string;
  typeCode: number;
  typeDesc: string;
}
/**
 * @description 根据系统消息类型编码查询详情
 * /sysMsgReminderConfig/queryDetailByCode/{msgTypeCode}
 */
export const queryDetailByCode = (msgTypeCode: string) => {
  return defHttp.get<IDetailByCode>({
    url: `/sysMsgReminderConfig/queryDetailByCode/${msgTypeCode}`,
  });
};

/**
 * @description 系统消息提醒配置-编辑
 * /sysMsgReminderConfig/edit
 */
export const editSysMsgReminderConfig = (data: any) => {
  return defHttp.post({
    url: '/sysMsgReminderConfig/edit',
    data,
  });
};
