import { defHttp } from '@ft/request';

export interface IInfectiousDrugs {
  createTime: string;
  createUser: string;
  drugIds: string[];
  id: string;
  infectionTypeCode: string;
  infectionTypeName: string;
  planAbbreviation: string;
  planName: string;
}
/**
 *传染病药品库-获取分页数据
 * @returns
 */
export const getInfectionDrugPage = (data?: any) =>
  defHttp.post<IInfectiousDrugs[]>({
    url: '/infectionDrug/page',
    data,
  });
/**
 *传染病药品库-获取不分页数据
 * @returns
 */
export const getInfectionDrugList = (data?: any) =>
  defHttp.post<IInfectiousDrugs[]>({
    url: '/infectionDrug/list',
    data,
  });

/**
 * 传染病药品库-新增-编辑
 * @returns
 */
export const saveOrUpdateInfectionDrug = (data: any) =>
  defHttp.post({
    url: '/infectionDrug/saveOrUpdate',
    data,
  });

/**
 * 传染病药品库-删除
 * @returns
 */
export const deleteInfectionDrug = (id: string) =>
  defHttp.post({
    url: `/infectionDrug/delete/${id}`,
  });
