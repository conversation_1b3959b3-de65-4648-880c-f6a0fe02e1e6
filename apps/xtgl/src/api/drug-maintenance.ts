import { defHttp } from '@ft/request';

export interface IInfectiousDrugPlan {
  createTime: string;
  createUser: string;
  drugIds: Array<string>;
  id: string;
  infectionTypeCode: string;
  infectionTypeName: string;
  planAbbreviation: string;
  planName: string;
}
/**
 *用药方案-获取分页数据
 * @returns
 */
export const getInfectionDrugPlanPage = (data?: any) =>
  defHttp.post<IInfectiousDrugPlan[]>({
    url: '/infectionDrugPlan/page',
    data,
  });
/**
 * 用药方案-新增-编辑
 * @returns
 */
export const saveOrUpdateInfectionDrugPlan = (data: any) =>
  defHttp.post({
    url: '/infectionDrugPlan/saveOrUpdate',
    data,
  });

/**
 * 用药方案-删除
 * @returns
 */
export const deleteInfectionDrugPlan = (id: string) =>
  defHttp.post({
    url: `/infectionDrugPlan/delete/${id}`,
  });
