import { useGlobSetting } from '@ft/internal/hooks/setting';
import { defHttp } from '@ft/request';

export interface DeptInfoModel {
  createTime: string;
  createUser: string;
  deleteFlag: number;
  deptAddress: string;
  deptCode: string;
  deptDiseaseType: string;
  deptId: string;
  deptIntroduction: string;
  deptManager: string;
  deptName: string;
  deptTelephone: string;
  deptType: string;
  enableFlag: number;
  orgId: string;
  updateTime: string;
  updateUser: string;
}
export interface SysDeptPageParams {
  deptCode: string;
  deptName: string;
  deptType: string;
  enableFlag: number;
  orderBy: string;
  orgId: string;
  pageNum: number;
  pageSize: number;
}
export interface SysDeptPage {
  createTime: string;
  deptAddress: string;
  deptCode: string;
  deptDiseaseType: string;
  deptId: string;
  deptIntroduction: string;
  deptManager: string;
  deptName: string;
  deptTelephone: string;
  deptType: string;
  enableFlag: number;
  orgId: string;
}
export interface SysDeptAddParams {
  deptAddress: string;
  deptCode: string;
  deptDiseaseType: string;
  deptId: string;
  deptIntroduction: string;
  deptManager: string;
  deptName: string;
  deptTelephone: string;
  deptType: string;
  enableFlag: number;
  orgId: string;
}
/* 新增机构科室信息*/
export const sysDeptAdd = (data: SysDeptAddParams) => defHttp.post({ url: '/sysDept/add', data });
/* 删除机构科室信息*/
export const sysDeptDelete = (id: any[]) => defHttp.post({ url: '/sysDept/delete', params: id });

/* 修改机构科室信息*/
export const sysDeptUpdate = (data: SysDeptAddParams) =>
  defHttp.post({ url: '/sysDept/update', data });

/* 查询机构科室信息详情*/
export const sysDeptPage = (data: SysDeptPageParams) =>
  defHttp.post<SysDeptPage>({ url: '/sysDept/page', data });

/* 查询机构科室信息详情*/
export const getDictPage = (id: string) =>
  defHttp.get<DeptInfoModel>({ url: '/sysDept/info', params: id });

/**
 * @description 查询机构科室信息列表
 * /sysDept/list
 */

export const getDeptList = (data?: any) => {
  return defHttp.post({ url: '/sysDept/list', data });
};

// 模板下载
export const downloadTemplate = () =>
  defHttp.post(
    { url: '/sysDept/downloadTemplate', responseType: 'blob' },
    { isReturnNativeResponse: true },
  );
// 批量导入
const { apiUrl, urlPrefix } = useGlobSetting();

export const UPLOAD_SYS_DEPT_IMPORT = `${apiUrl}${urlPrefix}/sysDept/import`;

// 导出列表
export const sysDeptExport = (data) =>
  defHttp.post(
    { url: '/sysDept/export', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );

export const DEPT_SIGNATURE_UPLOAD_URL = `${apiUrl}${urlPrefix}/sysDept/uploadSignImg`;
