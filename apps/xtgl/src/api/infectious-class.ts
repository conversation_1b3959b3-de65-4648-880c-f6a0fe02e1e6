import { defHttp } from '@ft/request';

/**
 *传染病分类-新增
 * @returns
 */
export const addInfectiousDiseaseClassification = (data: any) =>
  defHttp.post({
    url: '/infectiousDiseaseClassification/add',
    data,
  });

/**
 * 传染病分类-修改
 * @returns
 */
export const updateInfectiousDiseaseClassification = (data: any) =>
  defHttp.post({
    url: '/infectiousDiseaseClassification/edit',
    data,
  });

/**
 * 传染病分类-删除
 * @returns
 */
export const delInfectiousDiseaseClassification = (id: string) =>
  defHttp.delete({
    url: `/infectiousDiseaseClassification/delete/${id}`,
  });

/**
 * 传染病分类-列表
 * @returns
 */

export interface IInfectiousDiseaseClassificationPage {
  classificationCode: string;
  classificationName: string;
  id: string;
  remark: string;
}

export const getInfectiousDiseaseClassificationPage = (data?: any) =>
  defHttp.post<IInfectiousDiseaseClassificationPage[]>({
    url: '/infectiousDiseaseClassification/queryPage',
    data,
  });
/**
 * 传染病分类关联-新增
 * @returns
 */
export const addInfectiousDiseaseClassificationRelation = (data: any) =>
  defHttp.post({
    url: '/infectiousDiseaseClassificationRelevance/add',
    data,
  });

/**
 * 传染病分类关联-删除
 * @returns
 */
export const delInfectiousDiseaseClassificationRelation = (id: string) =>
  defHttp.delete({
    url: `/infectiousDiseaseClassificationRelevance/delete/${id}`,
  });
/**
 * 传染病分类关联-编辑
 * @returns
 */
export const updateInfectiousDiseaseClassificationRelation = (data: any) =>
  defHttp.post({
    url: '/infectiousDiseaseClassificationRelevance/edit',
    data,
  });

/**
 * 传染病分类关联-列表
 * @returns
 */

export interface IInfectiousDiseaseClassificationRelationPage {
  classificationId: string;
  id: string;
  infectiousDiseaseCode: string;
  infectiousDiseaseId: string;
  infectiousDiseaseName: string;
  sort: number;
}

export const getInfectiousDiseaseClassificationRelationPage = (data?: any) =>
  defHttp.post<IInfectiousDiseaseClassificationRelationPage[]>({
    url: '/infectiousDiseaseClassificationRelevance/queryPage',
    data,
  });
