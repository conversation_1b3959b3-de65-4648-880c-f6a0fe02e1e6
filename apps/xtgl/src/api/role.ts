import { defHttp } from '@ft/request';

interface IRoleItem {
  createTime: string;
  createUser: string;
  /**
   * 数据权限
   */
  orgIds: string[];
  enableFlag: number;
  id: string;
  roleDesc: string;
  roleName: string;
  menuIds: string[];
}
/**
 * @description 角色列表 不分页
 */
export const getRoleList = (data?: any) => {
  return defHttp.post<IRoleItem[]>({
    url: '/sysRole/list',
    data,
  });
};

/**
 * @description 角色列表 分页
 * /sysRole/page
 */
export const getRolePage = (data?: any) => {
  return defHttp.post<IRoleItem[]>({
    url: '/sysRole/page',
    data,
  });
};

/**
 * /sysRole/enable/{id}
 * @description 启用角色
 */
export const toggleRoleActivation = (id: string) => {
  return defHttp.put({
    url: `/sysRole/enable/${id}`,
  });
};

export interface IRole {
  dataPermissionIds: string[];
  enableFlag: number;
  id: string;
  roleDesc: string;
  roleName: string;
  systemPermissionIds: string[];
}
/**
 * /sysRole/edit
 * @description 编辑角色
 */
export const editRole = (data: IRole) => {
  return defHttp.put({
    url: '/sysRole/edit',
    data,
  });
};

/**
 * /sysRole/save
 * @description 新增角色
 */
export const saveRole = (data: Omit<IRole, 'id'>) => {
  return defHttp.post({
    url: '/sysRole/save',
    data,
  });
};

/**
 * /sysRole/{id}
 * @description 获取详情
 */
export const getRoleDetail = (id: string) => {
  return defHttp.get<IRoleItem>({
    url: `/sysRole/${id}`,
  });
};

/**
 * /sysRole/{id}
 * @description 删除角色
 */
export const deleteRole = (id: string) => {
  return defHttp.delete({
    url: `/sysRole/${id}`,
  });
};

interface BatchDeleteRoleReq {
  ids: string[];
}

/**
 * /sysRole/batchDelete
 * @description 批量删除角色
 */
export const batchDeleteRole = (data: BatchDeleteRoleReq) => {
  return defHttp.delete({
    url: '/sysRole/batchDelete',
    data,
  });
};

/**
 * 验证签名合法性
 * /sysRole/verifySignData
 */
export const verifySignData = (params) => {
  return defHttp.get({
    url: '/sysRole/verifySignData',
    params,
  });
};

/**
 * 导出
 * /sysRole/export
 */
export function exportRole(data: any) {
  return defHttp.post(
    {
      url: '/sysRole/export',
      responseType: 'blob',
      data,
    },
    { isReturnNativeResponse: true },
  );
}
