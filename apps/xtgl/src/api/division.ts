import { defHttp } from '@ft/request';

export interface IAdministrativeTree {
  children: IAdministrativeTree[];
  code: string;
  createTime: string;
  createUser: string;
  deleteFlag: number;
  id: string;
  name: string;
  pcode: string;
  pname: string;
  updateTime: string;
  updateUser: string;
}
export interface IAdministrative {
  code: string;
  id: string;
  name: string;
  pcode: string;
  pname: string;
}

/**
 * 行政区划 - 删除
 */
export const delAdministrative = (id: string) =>
  defHttp.post({ url: `/administrative/delete/${id}` });

/**
 * 行政区划 - 查询列表
 */

export const getAdministrativeTree = (data?) =>
  defHttp.post<IAdministrativeTree[]>({ url: '/administrative/queryTree', params: data });

/**
 * 行政区划 - 查询行政区划列表（PCode不传,默认查宜昌市辖区）
 */

export const getAdministrativeTreeList = (data?: { pCode: string }) =>
  defHttp.get<IAdministrative[]>({ url: '/administrative/queryList', params: data });

/**
 * 行政区划 - 根据行政区划代码查询区划信息
 */
export const getAdministrativeTreeByCode = (data?: { code: string }) =>
  defHttp.get<IAdministrative>({ url: '/administrative/queryByCode', params: data });

/**
 * 行政区划 - 新增或修改
 */
export const saveOrUpdateAdministrative = (data) =>
  defHttp.post({ url: '/administrative/saveOrUpdate', data });
