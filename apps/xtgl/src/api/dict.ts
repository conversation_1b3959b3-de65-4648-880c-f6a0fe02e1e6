import { useGlobSetting } from '@ft/internal/hooks/setting';
import { defHttp } from '@ft/request';

export interface IDict {
  dictCode: string;
  dictId: string;
  dictName: string;
  remark: string;
  sortNo: number;
}

export interface IDictItem {
  dictId: string;
  dictItemCode: string;
  dictItemId: string;
  dictItemName: string;
  dictItemRemark: string;
  sortNo: number;
}

/*
 * @description: 获取字典列表
 */
export const getDictPage = (data) => defHttp.post<IDict>({ url: '/sysDict/page', data });

/*
 * @description: 新增字典
 */
export const postDict = (data) => defHttp.post({ url: '/sysDict/add', data });

/*
 * @description: 修改字典
 */
export const putDict = (data) => defHttp.post({ url: '/sysDict/update', data });

/**
 * @description: 删除字典
 */
export const delDict = (id: string) => defHttp.post({ url: `/sysDict/delete?id=${id}` });

/* 分页查询 */
export const getDictItemPage = (data) =>
  defHttp.post<IDictItem[]>({ url: '/sysDictItem/page', data });

/** 新增字典项 */
export const postDictItem = (data: IDictItem) =>
  defHttp.post({ url: '/sysDictItem/add', params: data });

/* 修改字典项 */
export const putDictItem = (data) => defHttp.post({ url: '/sysDictItem/update', params: data });

/* 删除字典项 */
export const delDictItem = (id) => defHttp.post({ url: `/sysDictItem/delete?id=${id}` });

/**
 * /sysDictItem/downloadTemplate
 * @description: 下载字典项导入模板
 */
export const downloadDictItemTemplate = () =>
  defHttp.post(
    { url: '/sysDictItem/downloadTemplate', responseType: 'blob' },
    { isReturnNativeResponse: true },
  );

const { apiUrl, urlPrefix } = useGlobSetting();

export const DICT_ITEM_IMPORT = `${apiUrl}${urlPrefix}/sysDictItem/import`;

/**
 * @description: 获取行政区划列表
 * /administrative/queryList
 */
export const getAdministrativeList = (pCode?: string) =>
  defHttp.get({ url: '/administrative/queryList', params: { pCode } });
