import { defHttp } from '@ft/request';

/**
 * @description: 获取日志列表
 * /sysLog/page
 */
export interface ILog {
  operationDesc: string;
  operationModule: string;
  operationTime: string;
  operationType: string;
  operatorDeptName: string;
  operatorName: string;
  operatorOrgName: string;
}
export const getLogPage = (data) => {
  return defHttp.post<ILog[]>({
    url: '/sysLog/page',
    data,
  });
};

/**
 * 验证签名合法性
 * /sysLog/verifySignData
 */
export const verifySignData = (params) => {
  return defHttp.get({
    url: '/sysLog/verifySignData',
    params,
  });
};
