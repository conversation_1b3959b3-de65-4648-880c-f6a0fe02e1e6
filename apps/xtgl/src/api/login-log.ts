import { defHttp } from '@ft/request';

/**
 * @description: 获取登陆日志列表
 * /sysLoginLog/page
 */
export interface ILoginLogPage {
  /** 主键 */
  id: string;
  /** 登录结果 */
  loginResult: string;
  /** 登录时间 */
  loginTime: string;
  /** 客户端ip */
  remoteIp: string;
  /** 用户名 */
  username: string;
}
export const getLoginLogPage = (data) => {
  return defHttp.post<ILoginLogPage[]>({
    url: '/sysLoginLog/page',
    data,
  });
};

/**
 * 验证签名合法性
 * /sysLoginLog/verifySignData
 */
export const verifySignData = (params) => {
  return defHttp.get({
    url: '/sysLoginLog/verifySignData',
    params,
  });
};
