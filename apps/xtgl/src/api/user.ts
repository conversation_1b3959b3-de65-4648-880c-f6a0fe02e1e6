import { defHttp } from '@ft/request';
import { ContentTypeEnum } from '@ft/internal/enums/httpEnum';
import type { RcFile } from 'ant-design-vue/lib/vc-upload/interface';
import { useGlobSetting } from '@ft/internal/hooks/setting';

export const modifyPassword = (data) =>
  defHttp.post({ url: '/login/change_password', params: data });

export interface IUser {
  deptId: string;
  deptName: string;
  employeeName: string;
  enableFlag: number;
  enableFlagName: string;
  id: string;
  idCard: string;
  jobTitleCode: string;
  jobTitleName: string;
  lastLoginIp: string;
  lastLoginTime: string;
  orgId: string;
  orgName: string;
  password: string;
  phone: string;
  roleIdList: string[];
  userImg: string;
  username: string;
}

/**
 * @description: 获取用户列表分页
 * /sysUser/queryPage
 */
export const getUsersPage = (data: any) => {
  return defHttp.post<IUser[]>({
    url: '/sysUser/queryPage',
    data,
  });
};

/**
 * @description: 保存用户
 * /sysUser/save
 */
export const saveUser = (data: Omit<IUser, 'id'>) => {
  return defHttp.post({
    url: '/sysUser/save',
    data,
  });
};

/**
 * @description: 修改用户
 * /sysUser/update
 */
export const updateUser = (data: IUser) => {
  return defHttp.post({
    url: '/sysUser/update',
    data,
  });
};

/**
 * @description: 获取用户详情
 * /sysUser/detail
 */
export const getUserDetail = (id: string) => {
  return defHttp.get<IUser>({
    url: '/sysUser/detail',
    params: {
      id,
    },
  });
};

/**
 * @description: 删除用户
 * /sysUser/remove
 */
export const deleteUser = (id: string) => {
  return defHttp.post({
    url: '/sysUser/remove',
    params: {
      id,
    },
  });
};

/**
 * @description: 重置密码
 * /sysUser/resetPwd
 */
export const resetPwd = (id: string) => {
  return defHttp.post({
    url: '/sysUser/resetPwd?id=' + id,
  });
};

export const updatePersonalInfo = (data) => {
  return defHttp.post({
    url: '/sysUser/updatePersonalInfo',
    data,
  });
};

/**
 * @description: 修改用户状态
 * /sysUser/updateStatus
 */
export const updateUserStatus = (data: { id: string; enableFlag: number }) => {
  return defHttp.post({
    url: '/sysUser/updateStatus',
    data,
  });
};

/**
 * @description: 批量删除用户
 * /sysUser/batchRemove
 */
export const removeMultipleUser = (data: { idList: string[]; orgId: string }) => {
  return defHttp.post({
    url: '/sysUser/batchRemove',
    data,
  });
};

/**
 * @description: 下载导入用户模板
 * /sysUser/downloadTemplate
 */

export const downloadUserTemplate = (orgId: string) => {
  return defHttp.post(
    {
      url: '/sysUser/downloadTemplate?orgId=' + orgId,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * @description: 导入用户
 * /sysUser/batchImport
 */

export const importUser = (data: { file: RcFile; orgId: string }) => {
  const formData = new FormData();
  formData.append('file', data.file);
  formData.append('orgId', data.orgId);
  return defHttp.post({
    url: '/sysUser/batchImport',
    data: formData,
    headers: {
      'Content-Type': ContentTypeEnum.FORM_DATA,
    },
  });
};
/**
 * @description: 导出用户
 * /sysUser/export
 */

export const exportUser = (data: Recordable) => {
  return defHttp.post(
    {
      url: '/sysUser/export',
      data,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

const { apiUrl, urlPrefix } = useGlobSetting();
export const USER_SIGN_IMG_UPLOAD_URL = `${apiUrl}${urlPrefix}/sysUser/uploadSignImg`;

export function getTelephone(username: string) {
  return defHttp.get<string>({
    url: `/sysUser/getTelephoneByUsername/${username}`,
  });
}

export function getSmsCode(telephone: string) {
  return defHttp.get({
    url: `/login/sendVerifyCode/${telephone}`,
  });
}
