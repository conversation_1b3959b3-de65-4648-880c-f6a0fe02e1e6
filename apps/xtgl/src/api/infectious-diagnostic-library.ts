import { ContentTypeEnum } from '@ft/internal/enums/httpEnum';
import { defHttp } from '@ft/request';
import type { RcFile } from 'ant-design-vue/lib/vc-upload/interface';

/**
 *传染病-新增
 * @returns
 */
export const addInfectiousDisease = (data: any) =>
  defHttp.post({
    url: '/infectiousDisease/add',
    data,
  });

/**
 *传染病-删除
 * @returns
 */
export const delInfectiousDisease = (id: string) =>
  defHttp.delete({
    url: `/infectiousDisease/delete/${id}`,
  });

/**
 *传染病-修改
 * @returns
 */
export const updateInfectiousDisease = (data: any) =>
  defHttp.post({
    url: '/infectiousDisease/edit',
    data,
  });

/**
 *传染病-分页查询
 * @returns
 */

export interface IInfectiousDiseasePage {
  id: string;
  infectiousDiseaseCode: string;
  infectiousDiseaseName: string;
}

export const getInfectiousDiseasePage = (data?: any) =>
  defHttp.post<IInfectiousDiseasePage[]>({
    url: '/infectiousDisease/queryPage',
    data,
  });
/**
 *传染病-列表查询（不分页）
 * @returns
 */

export const getInfectiousDiseaseList = (data?: any) =>
  defHttp.post<IInfectiousDiseasePage[]>(
    {
      url: '/infection-sysmgt/infectiousDisease/queryList',
      data,
    },
    { joinPrefix: false },
  );

/**
 *传染病诊断-新增
 * @returns
 */
export const addInfectiousDiagnosis = (data: any) =>
  defHttp.post({
    url: '/infectiousDiagnosis/add',
    data,
  });

/**
 *传染病诊断-删除
 * @returns
 */
export const delInfectiousDiagnosis = (id: string) =>
  defHttp.delete({
    url: `/infectiousDiagnosis/delete/${id}`,
  });

/**
 *传染病诊断-修改
 * @returns
 */
export const updateInfectiousDiagnosis = (data: any) =>
  defHttp.post({
    url: '/infectiousDiagnosis/edit',
    data,
  });

/**
 *传染病诊断-列表
 * @returns
 */
export interface IInfectiousDiagnosisPage {
  diagnosticCode: string;
  diagnosticName: string;
  id: string;
  infectiousDiseaseId: string;
  infectiousDiseaseName: string;
  remarks: string;
  status: number;
  statusDesc: string;
}

export const getInfectiousDiagnosisPage = (data?: any) =>
  defHttp.post<IInfectiousDiagnosisPage[]>({
    url: '/infectiousDiagnosis/queryPage',
    data,
  });
/**
 * @description: 传染病诊断-导入
 * /infectiousDiagnosis/batchImport
 */

export const importInfectiousDiagnosis = (data: { file: RcFile; infectiousDiseaseId: string }) => {
  const formData = new FormData();
  formData.append('file', data.file);
  formData.append('infectiousDiseaseId', data.infectiousDiseaseId);
  return defHttp.post({
    url: '/infectiousDiagnosis/batchImport',
    data: formData,
    headers: {
      'Content-Type': ContentTypeEnum.FORM_DATA,
    },
  });
};
/**
 * 传染病诊断-导出
 */
export const infectiousDiagnosisExport = (data) =>
  defHttp.post(
    { url: '/infectiousDiagnosis/export', responseType: 'blob', data },
    { isReturnNativeResponse: true },
  );

/**
 * @description: 传染病诊断-下载模板
 * /infectiousDiagnosis/downloadTemplate
 */

export const infectiousDiagnosisTemplateDownload = () => {
  return defHttp.post(
    {
      url: '/infectiousDiagnosis/downloadTemplate',
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};
