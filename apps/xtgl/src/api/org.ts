import { ContentTypeEnum } from '@ft/internal/enums/httpEnum';
import { useGlobSetting } from '@ft/internal/hooks/setting';
import { defHttp } from '@ft/request';
import type { RcFile } from 'ant-design-vue/lib/vc-upload/interface';

export interface IOrganization {
  createTime: string;
  createUser: string;
  deleteFlag: number;
  divisionId: string;
  divisionName: string;
  enableFlag: number;
  id: string;
  orgAddress: string;
  orgCategory: string;
  orgCategoryName: string;
  orgCode: string;
  orgIntroduce: string;
  orgLevel: string;
  orgLevelName: string;
  orgManager: string;
  orgName: string;
  orgTelephone: string;
  /**
   * 机构类型(1-医疗卫生机构，2-公共卫生机构)
   */
  orgType: number;
  updateTime: string;
  updateUser: string;
  userCount: number;
}

/**
 * @description 查询机构分页
 * /organization/queryPage
 */
export const queryOrganizationPage = (data: any) => {
  return defHttp.post<IOrganization[]>({
    url: '/organization/queryPage',
    data,
  });
};

/**
 * @description 查询机构列表
 * /organization/queryByCondition
 *
 */
export const queryOrganizationList = (data: any) => {
  return defHttp.post<Partial<IOrganization>[]>({
    url: '/organization/queryByCondition',
    data,
  });
};

/**
 * @description 保存机构信息
 * /organization/save
 */
export const saveOrganization = (data: Omit<IOrganization, 'id'>) => {
  return defHttp.post({
    url: '/organization/save',
    data,
  });
};

/**
 * @description 更新机构信息
 * /organization/update
 */
export const updateOrganization = (data: Partial<IOrganization>) => {
  return defHttp.post({
    url: '/organization/update',
    data,
  });
};

/**
 * @description 启用机构
 * /organization/enable
 */
export const enableOrganization = (data: { id: string; enableFlag: number }) => {
  return defHttp.post({
    url: '/organization/enable',
    data,
  });
};

/**
 * @description 查询机构详情
 * /organization/detail
 */
export const getOrganizationDetail = (id: string) => {
  return defHttp.get<IOrganization>({
    url: '/organization/detail',
    params: {
      id,
    },
  });
};

/**
 * @description 删除机构
 * /organization/remove
 */
export const deleteOrganization = (id: string) => {
  return defHttp.post({
    url: '/organization/remove',
    params: {
      id,
    },
  });
};

/**
 * @description 批量删除机构
 * /organization/batchRemove
 */

export const batchDeleteOrganization = (data: { orgIdList: string[] }) => {
  return defHttp.post({
    url: '/organization/batchRemove',
    data,
  });
};

/**
 * @description 下载机构模板
 * /organization/download
 */

export const downloadOrganizationTemplate = (orgType: string) => {
  return defHttp.post(
    {
      url: `/organization/download?orgType=${orgType}`,
      responseType: 'blob',
    },
    {
      isReturnNativeResponse: true,
    },
  );
};

/**
 * @description 导入机构
 * /organization/batchImport
 */

export const importOrganization = (data: { file: RcFile; orgType: string }) => {
  const formData = new FormData();
  formData.append('file', data.file);
  formData.append('orgType', data.orgType);

  return defHttp.post({
    url: '/organization/batchImport',
    data: formData,
    headers: {
      'Content-Type': ContentTypeEnum.FORM_DATA,
    },
  });
};

/**
 * @description 导出机构
 * /organization/export
 */
export const exportOrganization = (data: any) => {
  return defHttp.post(
    {
      url: '/organization/export',
      responseType: 'blob',
      data,
    },
    { isReturnNativeResponse: true },
  );
};

/**
 * @description 查询机构树
 * /organization/tree
 */

export const queryOrganizationTree = () => {
  return defHttp.get<any>({
    url: '/organization/tree',
  });
};

// 上传机构院徽
const { apiUrl, urlPrefix } = useGlobSetting();

export const PRG_UPLOAD_SIGNATURE = `${apiUrl}${urlPrefix}/organization/uploadSignImg`;
