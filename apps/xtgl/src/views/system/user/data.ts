import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { type IUser, USER_SIGN_IMG_UPLOAD_URL } from '/@/api/user';
import type { EditModalDataType } from '/@/types';
import { getRoleList } from '/@/api/role';
import { getDeptList } from '/@/api/dept';
import { DictEnum, getDictItemList } from '@ft/internal/api';
export type UserEditModalDataType = EditModalDataType<IUser>;

export const columns: BasicColumn[] = [
  // 登录账号 用户姓名 用户职称 用户科室 用户角色 用户机构 用户状态 创建时间
  {
    title: '登录账号',
    dataIndex: 'username',
    width: 100,
  },
  {
    title: '工号',
    dataIndex: 'employeeNo',
    width: 100,
  },
  {
    title: '用户姓名',
    dataIndex: 'employeeName',
    width: 100,
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
    width: 100,
  },
  {
    title: '用户职称',
    dataIndex: 'jobTitleName',
    width: 100,
  },
  {
    title: '用户科室',
    dataIndex: 'deptName',
    width: 100,
  },
  {
    title: '用户角色',
    dataIndex: 'roleNameList',
    width: 100,
  },
  // userType
  {
    title: '用户类型',
    dataIndex: 'userType',
    width: 100,
    customRender({ text }) {
      return text === 1 ? '医护' : '患者';
    },
  },
  {
    title: '用户机构',
    dataIndex: 'orgName',
    width: 140,
  },
  {
    title: '用户状态',
    dataIndex: 'enableFlag',
    width: 100,
  },
  {
    title: '是否脱敏',
    dataIndex: 'isDesensitization',
    width: 100,
    customRender({ text }) {
      return text === 1 ? '是' : text === 0 ? '否' : '';
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 100,
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'username',
    component: 'Input',
    label: '登录账号',
    colProps: { span: 6 },
  },
  {
    field: 'employeeName',
    component: 'Input',
    label: '用户姓名',
    colProps: { span: 6 },
  },
  {
    field: 'enableFlag',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 0 },
        { label: '禁用', value: 1 },
      ],
    },
    label: '用户状态',
    colProps: { span: 6 },
  },
];

export const CreateModalSchemas = (orgId: string): FormSchema[] => {
  return [
    // 登录账号 用户名称 用户职称 用户科室 用户角色 用户机构 用户状态
    {
      field: 'id',
      label: '用户ID',
      component: 'Input',
      show: false,
    },
    {
      field: 'username',
      label: '登录账号',
      component: 'Input',
      required: true,
      colProps: { span: 12 },
    },
    {
      field: 'employeeNo',
      label: '工号',
      component: 'Input',
      colProps: { span: 12 },
    },
    {
      field: 'employeeName',
      label: '用户姓名',
      component: 'Input',
      required: true,
      colProps: { span: 12 },
    },
    {
      field: 'deptId',
      label: '用户科室',
      component: 'ApiSelect',
      componentProps: ({ formModel }) => {
        return {
          api: () => getDeptList({ orgId }),
          labelField: 'deptName',
          valueField: 'deptId',
          disabledFn: (optionItem) => {
            return optionItem?.enableFlag === 1;
          },
          getPopupContainer: () => document.body,
          onChange: (_, opt) => {
            formModel.deptName = opt?.label;
          },
        };
      },
      required: true,
      colProps: { span: 12 },
    },
    {
      field: 'deptName',
      label: '用户科室',
      component: 'Input',
      show: false,
    },
    {
      field: 'jobTitleCode',
      label: '用户职称',
      component: 'ApiSelect',
      componentProps: ({ formModel }) => {
        return {
          api: () => getDictItemList(DictEnum.JOB_TITLE),
          labelField: 'dictItemName',
          valueField: 'dictItemCode',
          onChange: (_, opt) => {
            formModel.jobTitleName = opt?.label;
          },
          getPopupContainer: () => document.body,
        };
      },
      colProps: { span: 12 },
    },
    {
      field: 'jobTitleName',
      label: '用户职称',
      component: 'Input',
      show: false,
    },
    {
      field: 'orgId',
      label: '用户机构',
      component: 'Input',
      show: false,
    },
    {
      field: 'orgName',
      label: '用户机构',
      component: 'Input',
      show: false,
    },
    {
      field: 'roleIdList',
      label: '用户角色',
      component: 'ApiSelect',
      componentProps: {
        mode: 'multiple',
        showSearch: true,
        optionFilterProp: 'label',
        api: getRoleList,
        labelField: 'roleName',
        valueField: 'id',
        maxTagCount: 2,
        getPopupContainer: () => document.body,
      },
      required: true,
      colProps: { span: 12 },
    },
    // phone
    {
      field: 'phone',
      label: '联系电话',
      component: 'Input',
      required: true,
      rules: [
        {
          pattern: /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
          message: '请输入正确的手机号码',
        },
      ],
      colProps: { span: 12 },
    },
    // userType
    {
      field: 'userType',
      label: '用户类型',
      component: 'Select',
      componentProps: {
        // 	用户类型：1 医护，2 患者
        options: [
          { label: '医护', value: 1 },
          { label: '患者', value: 2 },
        ],
      },
      defaultValue: 1,
      colProps: { span: 12 },
      required: true,
    },
    // idCard
    {
      field: 'idCard',
      label: '身份证号',
      component: 'Input',
      rules: [
        {
          pattern:
            /^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
          message: '请输入正确的身份证号',
        },
      ],
      colProps: { span: 12 },
    },
    {
      field: 'enableFlag',
      label: '用户状态',
      component: 'Select',
      componentProps: {
        options: [
          { label: '启用', value: 0 },
          { label: '禁用', value: 1 },
        ],
      },
      defaultValue: 0,
      colProps: { span: 12 },
    },
    {
      field: 'isDesensitization',
      label: '是否脱敏',
      component: 'Select',
      componentProps: {
        options: [
          { label: '否', value: 0 },
          { label: '是', value: 1 },
        ],
      },
      defaultValue: 0,
      colProps: { span: 12 },
    },
    {
      field: 'userSignImg',
      label: '签名',
      component: 'UploadAsset',
      componentProps: {
        uploadType: 'image',
        resultField: 'data.url',
        maxCount: 1,
        action: USER_SIGN_IMG_UPLOAD_URL,
      },
      colProps: { span: 12 },
    },
  ];
};
