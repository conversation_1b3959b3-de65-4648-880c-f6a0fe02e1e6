<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { InputSearch, Select } from 'ant-design-vue';
  import { StyledList } from '@ft/components';
  import UserTable from './UserTable.vue';
  import { useRequest } from '@ft/request';
  import { queryOrganizationList } from '/@/api/org';

  const activeOption = ref('1');
  const options = [
    { label: '医疗卫生服务机构', value: '1' },
    { label: '公共卫生服务机构', value: '2' },
  ];

  const activeItem = ref('');
  const searchOrgName = ref('');

  const { data } = useRequest(
    () => queryOrganizationList({ orgName: searchOrgName.value, orgType: activeOption.value }),
    {
      refreshDeps: [activeOption, searchOrgName],
      onSuccess(data) {
        if (data.length) {
          activeItem.value = data[0].id || '';
        }
      },
    },
  );

  const items = computed(() => {
    return data.value?.map((item) => {
      return {
        value: item.id || '',
        label: item.orgName || '',
      };
    });
  });

  const orgItem = computed(() => {
    const type = options?.find((item) => item.value === activeOption.value);
    const org = items.value?.find((item) => item.value === activeItem.value);
    return {
      id: activeItem.value,
      name: org?.label || '',
      typeLabel: type?.label || '',
      typeId: type?.value || '',
    };
  });
</script>
<template>
  <div class="h-[calc(100%-8px)] w-[calc(100%-12px)] flex bg-white">
    <div
      class="w-200px p-16px flex flex-col"
      style="flex-shrink: 0; border-right: 1px solid #edeef0"
    >
      <div class="text-16px font-500 title-label mb-4">用户列表</div>
      <Select class="styled-select" :options="options" v-model:value="activeOption" />
      <InputSearch v-model:value="searchOrgName" class="mt-4 mb-2" placeholder="请输入关键字" />
      <div class="flex-1 basis-0 of-y-auto of-x-hidden">
        <StyledList :items="items" v-model="activeItem" />
      </div>
    </div>
    <div class="of-hidden">
      <UserTable :org="orgItem" />
    </div>
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .ft-main-table .vben-basic-form--compact .ant-col .ant-form-item {
      margin-bottom: 6px !important;
    }

    .styled-select {
      width: 100%;

      .ant-select-selector {
        border: none;
        box-shadow: none !important;
        padding: 0;
      }

      .ant-select-selection-item {
        border-bottom: 2px solid @primary-color;
        max-width: fit-content;
        padding-right: 0;
      }

      .ant-select-arrow {
        right: 0;
      }
    }
  }
</style>
