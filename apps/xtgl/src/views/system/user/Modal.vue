<script setup lang="ts">
  import { computed, ref } from 'vue';

  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import type { UserEditModalDataType } from './data';
  import { CreateModalSchemas } from './data';
  import { useRequest } from '@ft/request';
  import { getUserDetail, saveUser, updateUser } from '/@/api/user';

  const emit = defineEmits(['register', 'success']);

  const props = defineProps<{
    org: {
      id: string;
      name: string;
      typeLabel: string;
      typeId: string;
    };
  }>();

  const [registerForm, formAction] = useForm({
    labelWidth: 150,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });

  const mode = ref('');
  const getTitle = computed(() => {
    return mode.value === 'add' ? '新增用户' : '编辑用户';
  });

  const { runAsync } = useRequest(getUserDetail, { manual: true });

  const [register, { closeModal }] = useModalInner<UserEditModalDataType>((data) => {
    mode.value = data.mode;
    formAction.resetSchema(CreateModalSchemas(props.org.id));
    formAction.setFieldsValue({
      orgId: props.org.id,
      orgName: props.org.name,
    });
    if (!data.record) return;
    runAsync(data.record.id).then((values) => {
      values.roleIdList === null && (values.roleIdList = []);
      formAction.setFieldsValue(values);
    });
  });

  const { loading, runAsync: userSaveRunAsync } = useRequest(
    (params) => (mode.value === 'edit' ? updateUser(params) : saveUser(params)),
    {
      manual: true,
      showSuccessMessage: true,
    },
  );

  async function handleOk() {
    formAction.validate().then((values) => {
      userSaveRunAsync(values).then(() => {
        emit('success');
        closeModal();
      });
    });
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="getTitle"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    width="768px"
    @register="register"
    @ok="handleOk"
  >
    <div class="color-#5C5F66 mb-4">{{ org?.typeLabel }}-{{ org?.name }}</div>
    <BasicForm style="transform: translateX(-36px)" @register="registerForm" />
  </BasicModal>
</template>
