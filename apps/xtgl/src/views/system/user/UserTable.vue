<script setup lang="ts">
  import { nextTick, ref, watch } from 'vue';
  import type { ActionItem, BasicColumn, TableRowSelection } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { exportUtil } from '@ft/internal/utils';
  import { useModal } from '@ft/internal/components/Modal';
  import { Popconfirm, Switch, Upload } from 'ant-design-vue';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import { useRequest } from '@ft/request';
  import type { UploadRequestOption } from 'ant-design-vue/lib/vc-upload/interface';
  import type { UserEditModalDataType } from './data';
  import { columns, formSchema } from './data';
  import Modal from './Modal.vue';
  import type { IUser } from '/@/api/user';
  import {
    deleteUser,
    downloadUserTemplate,
    exportUser,
    getUsersPage,
    importUser,
    removeMultipleUser,
    resetPwd,
    updateUserStatus,
  } from '/@/api/user';
  import type { ListSwitchUnionType } from '/@/types';

  const props = defineProps<{
    org: {
      id: string;
      name: string;
      typeLabel: string;
      typeId: string;
    };
  }>();

  const [registerTable, tableIns] = useTable({
    api: getUsersPage,
    inset: true,
    columns,
    formConfig: {
      colon: true,
      labelWidth: 90,
      showAdvancedButton: false,
      actionColOptions: {
        span: 6,
        style: {
          textAlign: 'right',
          paddingRight: '16px',
        },
      },
      schemas: formSchema,
    },
    useSearchForm: true,
    showIndexColumn: false,
    bordered: false,
    size: 'small',
    resizeHeightOffset: 20,
    actionColumn: {
      width: 180,
      title: '操作',
      dataIndex: 'action',
    },
    immediate: false,
    beforeFetch: (params) => {
      params.orgId = props.org.id;
      params.orgType = props.org.typeId;
      return params;
    },
    afterFetch: (data) => {
      data.forEach((item) => {
        item.loading = false;
      });
      return data;
    },
    rowKey: 'id',
  });
  watch(
    () => props.org,
    (val, oldVal) => {
      if (val.id === oldVal?.id && val.typeId === oldVal.typeId) return;
      if (!val.id) return;
      nextTick(() => {
        tableIns?.reload();
      });
    },
    {
      immediate: true,
      deep: true,
    },
  );

  const [registerModal, { openModal }] = useModal();

  const selectedRowKeys = ref<TableRowSelection['selectedRowKeys']>([]);

  const handleSelectionChange: TableRowSelection['onChange'] = (keys) => {
    selectedRowKeys.value = keys;
  };

  function handleAdd() {
    openModal<UserEditModalDataType>(true, {
      mode: 'add',
    });
  }

  function onEdit(record, _column) {
    openModal<UserEditModalDataType>(true, {
      mode: 'edit',
      record,
    });
  }

  const { createConfirm, createSuccessModal } = useMessage();
  const { runAsync } = useRequest(resetPwd, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: (data) => {
      createSuccessModal({
        title: '重置密码成功',
        content: data,
      });
    },
  });

  function onResetPwd(record: IUser, _column) {
    createConfirm({
      title: `你确定要重置用户：【${record.employeeName || record.username}】的登录密码吗？`,
      iconType: 'warning',
      onOk: async () => {
        runAsync(record.id);
      },
    });
  }

  const { run } = useRequest(deleteUser, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      tableIns?.reload();
    },
  });

  function onDelete(record: IUser, _column) {
    run(record.id);
  }

  const { loading: templateLoading, runAsync: runAsyncDownloadUserTemplate } = useRequest(
    downloadUserTemplate,
    {
      manual: true,
    },
  );

  async function onTemplateDown() {
    try {
      await exportUtil(runAsyncDownloadUserTemplate(props.org.id));
    } catch (error) {
      console.error(error);
    } finally {
    }
  }

  const { loading: exportLoading, runAsync: runAsyncExport } = useRequest(exportUser, {
    manual: true,
  });
  function onBatchExport() {
    exportUtil(
      runAsyncExport({
        orgId: props.org.id,
        orgType: props.org.typeId,
        ...tableIns.getForm().getFieldsValue(),
      }),
    );
  }

  function createActions(record: IUser, column: BasicColumn): ActionItem[] {
    const actions: ActionItem[] = [];

    actions.push(
      {
        label: '编辑',
        type: 'link',
        onClick: onEdit.bind(null, record, column),
      },
      {
        label: '重置密码',
        onClick: onResetPwd.bind(null, record, column),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: onDelete.bind(null, record, column),
        },
      },
    );
    return actions;
  }

  const { runAsync: runAsyncUpdateStatus } = useRequest(updateUserStatus, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      tableIns?.reload();
    },
  });

  function onSwitchChange(checked, record: ListSwitchUnionType<IUser>) {
    record.loading = true;
    runAsyncUpdateStatus({ id: record.id, enableFlag: checked }).finally(() => {
      record.loading = false;
    });
  }

  const { runAsync: removeMultipleUserRun } = useRequest(removeMultipleUser, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      selectedRowKeys.value = [];
      tableIns?.reload();
    },
  });
  function onBatchDelete() {
    removeMultipleUserRun({
      orgId: props.org.id,
      idList: selectedRowKeys.value as unknown as string[],
    });
  }

  const { loading: importLoading, runAsync: importUserRun } = useRequest(importUser, {
    manual: true,
    showSuccessMessage: true,
    onSuccess() {
      tableIns.reload();
    },
  });

  function onImportUser(e: UploadRequestOption<any>) {
    const { file } = e;
    importUserRun({
      // @ts-ignore
      file,
      orgId: props.org.id,
    });
  }
</script>

<template>
  <BasicTable
    class="ft-main-table"
    :row-selection="{
      selectedRowKeys,
      onChange: handleSelectionChange,
    }"
    @register="registerTable"
  >
    <template #headerTop>
      <div class="text-16px font-500 title-label mb-4">用户列表</div>
    </template>
    <template #tableTitle>
      <div class="flex gap-2">
        <Button type="primary" @click="handleAdd"> 新增 </Button>
        <Popconfirm
          title="确定删除选中项吗？"
          okText="确定"
          cancelText="取消"
          @confirm="onBatchDelete"
        >
          <Button :disabled="selectedRowKeys?.length === 0"> 批量删除 </Button>
        </Popconfirm>
      </div>
    </template>
    <template #toolbar>
      <Button :loading="templateLoading" @click="onTemplateDown"> 模板下载 </Button>
      <Upload :max-count="1" :show-upload-list="false" :customRequest="onImportUser">
        <Button :loading="importLoading"> 批量导入 </Button>
      </Upload>
      <Button :loading="exportLoading" @click="onBatchExport"> 批量导出 </Button>
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'enableFlag'">
        <Switch
          :loading="record.loading"
          :checked-value="0"
          :un-checked-value="1"
          v-model:checked="record.enableFlag"
          @change="onSwitchChange($event, record)"
        />
      </template>
      <template v-if="column.dataIndex === 'action'">
        <TableAction :divider="false" :actions="createActions(record, column)" />
      </template>
      <template v-if="column.dataIndex === 'roleNameList'">
        {{ (record[column.dataIndex] || []).join(',') }}
      </template>
    </template>
  </BasicTable>
  <Modal :org="org" @register="registerModal" @success="tableIns.reload" />
</template>

<style lang="less" scoped>
  :deep {
    .ft-main-table .vben-basic-form--compact .ant-col .ant-form-item {
      margin-bottom: 6px !important;
    }

    .ant-form {
      margin-bottom: 0 !important;
    }
  }
</style>
