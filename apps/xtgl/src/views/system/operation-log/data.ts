import { DictEnum, getDictItemList } from '@ft/internal/api';
import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
/**
操作时间
操作人员
操作人所在机构
操作人所在部门
操作模块
操作项
操作描述
 */
export const columns: BasicColumn[] = [
  {
    title: '操作时间',
    dataIndex: 'operationTime',
    width: 180,
    align: 'left',
  },
  {
    title: '操作人员',
    dataIndex: 'operatorName',
    width: 130,
    align: 'left',
  },
  {
    title: '操作人所在机构',
    dataIndex: 'operatorOrgName',
    width: 160,
    align: 'left',
  },
  {
    title: '操作人所在部门',
    dataIndex: 'operatorDeptName',
    width: 160,
    align: 'left',
  },
  {
    title: '系统名称',
    dataIndex: 'systemName',
    width: 160,
    align: 'left',
  },
  {
    title: '操作模块',
    dataIndex: 'operationModule',
    width: 160,
    align: 'left',
  },
  {
    title: '操作项',
    dataIndex: 'operationType',
    width: 100,
    align: 'left',
  },
  {
    title: '操作描述',
    dataIndex: 'operationDesc',
    width: 200,
    align: 'left',
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'operationTime',
    component: 'RangePicker',
    label: '操作时间',
    colProps: { span: 6 },
  },
  {
    field: 'operatorName',
    component: 'Input',
    label: '操作人员',
    colProps: { span: 6 },
  },
  {
    field: 'systemType',
    component: 'ApiSelect',
    label: '系统名称',
    componentProps: {
      api: () => getDictItemList(DictEnum.SYSTEM_TYPE),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      getPopupContainer: () => document.body,
      showSearch: true,
      optionFilterProp: 'label',
    },
    colProps: { span: 6 },
  },
  {
    field: 'operationModule',
    component: 'Input',
    label: '操作模块',
    colProps: { span: 6 },
  },
];
