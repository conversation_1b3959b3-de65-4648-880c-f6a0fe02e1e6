import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { getAdministrativeTree } from '/@/api/division';

/**
 *
 * @param tree tree data
 * @param filterKey node key
 * @param filterValue search string
 */
export function filterTree(tree: any[], key: string, searchString: string) {
  // 如果搜索字符串为空，则返回整个树
  if (!searchString) {
    return tree;
  }

  // 递归函数来过滤树
  function filterNode(node) {
    // 如果节点匹配搜索字符串，则返回该节点
    if (new RegExp(searchString, 'gui').test(node[key])) {
      return { ...node };
    }

    // 如果有子节点，则递归过滤子节点
    if (node.children && node.children.length > 0) {
      const filteredChildren = node.children.map(filterNode).filter((child) => child != null);

      // 如果过滤后的子节点不为空，则返回当前节点和过滤后的子节点
      if (filteredChildren.length > 0) {
        return { ...node, children: filteredChildren };
      }
    }

    // 如果当前节点和子节点都不匹配，返回null
    return null;
  }

  // 对树中的每个节点应用过滤函数
  return tree.map(filterNode).filter((node) => node != null);
}
export const columns: BasicColumn[] = [
  {
    title: '行政区划编码',
    dataIndex: 'code',
    align: 'left',
    ellipsis: false,
    width: 'auto',
  },
  {
    title: '行政区划名称',
    dataIndex: 'name',
    align: 'left',
    ellipsis: false,
    width: 'auto',
  },
  {
    title: '上级行政区划',
    dataIndex: 'pname',
    align: 'left',
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'name',
    component: 'Input',
    label: '行政区划名称',
    componentProps: {
      placeholder: '请输入行政区划名称',
    },
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 20 } },
  },
  // {
  //   field: 'name',
  //   component: 'Input',
  //   label: '行政区划名称',
  //   colProps: { span: 6 },
  // },
  // {
  //   field: 'pname',
  //   component: 'Input',
  //   label: '上级行政区划',
  //   colProps: { span: 6 },
  // },
];
export const addForm: FormSchema[] = [
  {
    field: 'id',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'code',
    label: '行政区划编码',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: 'name',
    label: '行政区划名称',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: 'pcode',
    label: '上级菜单',
    component: 'ApiTreeSelect',
    required: true,
    colProps: { span: 24 },
    componentProps: () => {
      return {
        api: async () => {
          const data = await getAdministrativeTree({ pcode: '420500' });
          const root = [
            {
              children: [...data],
              code: '420500',
              name: '宜昌市',
              pcode: null,
              deleteFlag: 0,
              id: '',
              pname: '420000',
            },
          ];
          return root;
        },
        getPopupContainer: () => document.body,
        showSearch: true,
        treeNodeFilterProp: 'name',
        optionFilterProp: 'label',
        fieldNames: { children: 'children', label: 'name', value: 'code' },
        height: 340,
        listHeight: 340,
      };
    },
    itemProps: { wrapperCol: { span: 24 } },
  },
  // {
  //   field: 'menuNote',
  //   label: '备注',
  //   component: 'Input',
  //   colProps: { span: 12 },
  //   itemProps: { wrapperCol: { span: 24 } },
  // },
];
