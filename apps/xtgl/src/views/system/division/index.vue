<script setup lang="ts">
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import type { ActionItem, BasicColumn } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { useModal } from '@ft/internal/components/Modal';
  import { ref } from 'vue';
  import { useRequest } from '@ft/request';
  import DivisionEdit from './DivisionEdit.vue';
  import { columns, filterTree, formSchema } from './data';
  import { delAdministrative, getAdministrativeTree } from '/@/api/division';
  /**
   * 行政区划
   */

  const [register, { openModal }] = useModal();

  function addMenu() {
    openModal(true, {
      mode: 'add',
    });
  }
  function onEditMenu(record) {
    openModal(true, {
      mode: 'edit',
      record,
    });
  }

  // const { hasPermission } = usePermission();
  const {
    loading: loading,
    data: treeData,
    runAsync: getTree,
  } = useRequest(() => getAdministrativeTree({ code: '420500' }), {
    onSuccess(data) {
      setTableData(data);
    },
  });
  const [registerTable, { setTableData, getForm }] = useTable({
    // api: getAdministrativeTree,
    immediate: false,
    loading: loading,
    formConfig: {
      colon: true,
      labelWidth: 100,
      showAdvancedButton: false,
      actionColOptions: {
        span: 18,
        style: {
          textAlign: 'right',
        },
      },
      submitFunc: async () => {
        setTableData(filterTree(treeData.value || [], 'name', getForm().getFieldsValue().name));
      },
      resetFunc: async () => {
        setTableData(treeData.value || []);
      },
      schemas: formSchema,
    },
    useSearchForm: true,
    showIndexColumn: false,
    pagination: false,
    columns,
    scroll: {
      x: 1600,
    },
    beforeFetch: (params) => {
      params.code = '420500';
      return params;
    },
    bordered: false,
    size: 'small',
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
  });

  const expandedRowKeys = ref<string[]>([]);

  function createActions(record, column: BasicColumn): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '编辑',
        type: 'link',
        size: 'small',
        onClick: onEditMenu.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: handleDel.bind(null, record, column),
          okButtonProps: {
            danger: true,
          },
        },
      },
    ];
    return actions;
  }

  const { runAsync: deleteRunAsync } = useRequest(delAdministrative, {
    manual: true,
    showSuccessMessage: true,
    onSuccess() {
      getTree();
    },
  });

  function handleDel(record, _column) {
    deleteRunAsync(record.id);
  }
</script>

<template>
  <div class="w-full h-full pr-4">
    <BasicTable
      row-key="id"
      v-model:expandedRowKeys="expandedRowKeys"
      @register="registerTable"
      class="ft-main-table"
    >
      <template #tableTitle>
        <Button type="primary" @click="addMenu"> 新增 </Button>
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record, column)" />
        </template>
        <!-- <template v-if="column.dataIndex === 'menuType'">
          <div>{{ MenuTypeMap[record.menuType] }}</div>
        </template> -->
      </template>
    </BasicTable>
    <DivisionEdit @register="register" @success="getTree" />
  </div>
</template>
