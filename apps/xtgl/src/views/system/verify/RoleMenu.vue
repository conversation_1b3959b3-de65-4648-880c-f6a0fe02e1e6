<script setup lang="ts">
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { useRequest } from '@ft/request';
  import type { IRoleMenu } from '/@/api/verify';
  import { getRoleMenu, verifyRoleMenuSignData } from '/@/api/verify';

  const [registerTable] = useTable({
    api: getRoleMenu,
    columns: [
      /**
       * roleName  角色名称  string
       * menuName  菜单名称  string
       */
      {
        title: '角色名称',
        dataIndex: 'roleName',
      },
      {
        title: '菜单名称',
        dataIndex: 'menuName',
      },
      // createUser
      {
        title: '创建人',
        dataIndex: 'createUser',
      },
      // createTime
      //创建时间
      {
        title: '创建时间',
        dataIndex: 'createTime',
        minWidth: 180,
      },
    ],
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
    afterFetch: (data) => {
      data.forEach((item) => {
        item.loading = false;
      });
      return data;
    },
  });

  const { runAsync: handleVerify } = useRequest(verifyRoleMenuSignData, {
    manual: true,
    showSuccessMessage: () => '验证通过',
  });

  function createActions(record: IRoleMenu): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '验证完整性',
        type: 'link',
        loading: record._loading,
        onClick: () => {
          record._loading = true;
          handleVerify(record.id).finally(() => {
            record._loading = false;
          });
        },
      },
    ];

    return actions;
  }
</script>

<template>
  <div class="h-[calc(100%-8px)] w-[calc(100%-12px)] flex bg-white">
    <BasicTable @register="registerTable">
      <template #headerTop>
        <div class="text-16px font-500 title-label">角色菜单列表</div>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
