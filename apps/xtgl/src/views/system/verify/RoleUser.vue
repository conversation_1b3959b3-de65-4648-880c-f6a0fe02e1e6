<script setup lang="ts">
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { useRequest } from '@ft/request';
  import type { IUserRole } from '/@/api/verify';
  import { getUserRole, verifyUserRoleSignData } from '/@/api/verify';

  const [registerTable] = useTable({
    api: getUserRole,
    columns: [
      /**
       * roleName  角色名称  string
       * username  用户名  string
       */

      {
        title: '用户名',
        dataIndex: 'username',
      },
      {
        title: '角色名称',
        dataIndex: 'roleName',
      },
      // createUser
      {
        title: '创建人',
        dataIndex: 'createUser',
      },
      // createTime
      //创建时间
      {
        title: '创建时间',
        dataIndex: 'createTime',
        minWidth: 180,
      },
    ],
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
    afterFetch: (data) => {
      data.forEach((item) => {
        item.loading = false;
      });
      return data;
    },
  });

  const { runAsync: handleVerify } = useRequest(verifyUserRoleSignData, {
    manual: true,
    showSuccessMessage: () => '验证通过',
  });

  function createActions(record: IUserRole): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '验证完整性',
        type: 'link',
        loading: record._loading,
        onClick: () => {
          record._loading = true;
          handleVerify(record.id).finally(() => {
            record._loading = false;
          });
        },
      },
    ];

    return actions;
  }
</script>

<template>
  <div class="h-[calc(100%-8px)] w-[calc(100%-12px)] flex bg-white">
    <BasicTable @register="registerTable">
      <template #headerTop>
        <div class="text-16px font-500 title-label">用户角色列表</div>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>
