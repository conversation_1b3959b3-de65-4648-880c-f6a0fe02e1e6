import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { DEPT_SIGNATURE_UPLOAD_URL } from '/@/api/dept';
import { DictEnum, getDictItemList, getUserList } from '@ft/internal/api';

export const columns: BasicColumn[] = [
  // 科室编码 科室名称 科室介绍 科室病种 科室地址 科室负责人 科室联系电话 科室状态 创建时间
  {
    title: '科室编码',
    dataIndex: 'deptCode',
    width: 100,
    align: 'left',
  },
  {
    title: '科室名称',
    dataIndex: 'deptName',
    width: 100,
    align: 'left',
  },
  {
    title: '科室介绍',
    dataIndex: 'deptIntroduction',
    width: 100,
    align: 'left',
  },
  {
    title: '科室病种',
    dataIndex: 'deptDiseaseType',
    width: 100,
    align: 'left',
  },
  {
    title: '科室地址',
    dataIndex: 'deptAddress',
    width: 100,
    align: 'left',
  },
  {
    title: '科室分类',
    dataIndex: 'deptType',
    width: 100,
    align: 'left',
  },
  {
    title: '科主任',
    dataIndex: 'deptManager',
    width: 100,
    align: 'left',
  },
  {
    title: '科室联系电话',
    dataIndex: 'deptTelephone',
    width: 100,
    align: 'left',
  },
  {
    title: '科室状态',
    dataIndex: 'enableFlag',
    width: 100,
    align: 'left',
  },
  // divisionName
  {
    title: '辖区',
    dataIndex: 'divisionName',
    width: 100,
    align: 'left',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150,
    align: 'left',
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'deptCode',
    component: 'Input',
    label: '科室编码',
    colProps: { span: 6 },
  },
  {
    field: 'deptName',
    component: 'Input',
    label: '科室名称',
    colProps: { span: 6 },
  },
  {
    field: 'deptType',
    component: 'ApiSelect',
    componentProps: {
      api: () => getDictItemList(DictEnum.DEPT_TYPE),
      labelField: 'dictItemName',
      valueField: 'dictItemName',
    },
    label: '科室分类',
    colProps: { span: 6 },
  },
  {
    field: 'enableFlag',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 0 },
        { label: '禁用', value: 1 },
      ],
    },
    label: '科室状态',
    colProps: { span: 6 },
  },
];

export const modalSchema: FormSchema[] = [
  {
    field: 'orgId',
    component: 'Input',
    label: 'orgId',
    show: false,
    colProps: { span: 12 },
  },
  {
    field: 'deptId',
    component: 'Input',
    label: 'deptId',
    show: false,
    colProps: { span: 12 },
  },
  {
    field: 'deptCode',
    component: 'Input',
    label: '科室编码',
    required: true,
    colProps: { span: 12 },
  },
  {
    field: 'deptName',
    component: 'Input',
    label: '科室名称',
    required: true,
    colProps: { span: 12 },
  },
  {
    field: 'deptType',
    component: 'ApiSelect',
    label: '科室分类',
    componentProps: {
      api: () => getDictItemList(DictEnum.DEPT_TYPE),
      labelField: 'dictItemName',
      valueField: 'dictItemName',
    },
    required: true,
    colProps: { span: 12 },
  },
  {
    field: 'deptIntroduction',
    component: 'Input',
    label: '科室介绍',
    colProps: { span: 24 },
  },
  {
    field: 'deptDiseaseType',
    component: 'ApiSelect',
    label: '科室病种',
    componentProps: ({ formModel }) => {
      return {
        showSearch: true,
        api: () => getDictItemList(DictEnum.INFECTIOUS_DISEASE),
        labelField: 'dictItemName',
        valueField: 'dictItemName',
        onChange: (val, _) => {
          if (!val) {
            setTimeout(() => {
              formModel['deptDiseaseType'] = '';
            }, 0);
          }
        },
      };
    },
    colProps: { span: 24 },
  },
  {
    field: 'deptManager',
    component: 'Input',
    label: '',
    show: false,
  },
  {
    field: 'deptManagerId',
    component: 'ApiSelect',
    label: '科主任',
    colProps: { span: 12 },
    componentProps: ({ formModel }) => {
      return {
        api: () => {
          if (!formModel.deptId) {
            return Promise.resolve([]);
          }
          return getUserList({ deptId: formModel.deptId });
        },
        labelField: 'employeeName',
        valueField: 'id',
        keyField: 'id',
        showSearch: true,
        optionFilterProp: 'label',
        onChange: (val, opt) => {
          if (!val) {
            setTimeout(() => {
              formModel['deptManagerId'] = '';
              formModel['deptManager'] = '';
            }, 0);
          }
          if (opt?.label) formModel['deptManager'] = opt?.label;
        },
      };
    },
  },
  {
    field: 'deptTelephone',
    component: 'Input',
    label: '科室联系电话',
    rules: [
      {
        pattern: /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
        message: '请输入正确的手机号码',
      },
    ],
    colProps: { span: 12 },
  },
  {
    field: 'deptAddress',
    component: 'Input',
    label: '科室地址',
    colProps: { span: 24 },
  },
  {
    field: 'deptSignature',
    component: 'UploadAsset',
    componentProps: {
      uploadType: 'image',
      resultField: 'data.url',
      maxCount: 1,
      action: DEPT_SIGNATURE_UPLOAD_URL,
    },
    label: '科室签章',
    colProps: { span: 12 },
  },

  {
    field: 'enableFlag',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 0 },
        { label: '禁用', value: 1 },
      ],
      allowClear: false,
    },
    defaultValue: 0,
    label: '科室状态',
    colProps: { span: 12 },
  },
];
