<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { useToggle } from '@vueuse/core';
  import type { ActionItem, BasicColumn, TableRowSelection } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { exportUtil } from '@ft/internal/utils';
  import { useModal } from '@ft/internal/components/Modal';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import { Upload } from 'ant-design-vue';
  import type { UploadChangeParam, UploadFile } from 'ant-design-vue';
  import { getToken } from '@ft/internal/utils/auth';
  import { columns, formSchema } from './data';
  import Modal from './Modal.vue';
  import {
    UPLOAD_SYS_DEPT_IMPORT,
    downloadTemplate,
    sysDeptDelete,
    sysDeptExport,
    sysDeptPage,
  } from '/@/api/dept';

  const headers = {
    Authorization: `Bearer ${getToken()}`,
  };

  const props = defineProps({
    orgId: {
      type: String,
      default: '',
    },
    activeOption: {
      type: Number,
      default: 1,
    },
    activeItemName: {
      type: String,
      default: '',
    },
  });

  const [registerTable, ActionTable] = useTable({
    api: sysDeptPage,
    fetchSetting: {
      sizeField: 'pageSize',
      pageField: 'pageNum',
      listField: 'list',
    },
    inset: true,
    columns,
    formConfig: {
      colon: true,
      labelWidth: 90,
      showAdvancedButton: false,
      actionColOptions: {
        span: 24,
        style: {
          textAlign: 'right',
          paddingRight: '16px',
        },
      },
      schemas: formSchema,
    },
    useSearchForm: true,
    rowKey: (params) => params.deptId,
    beforeFetch: async (params) => {
      params.orgId = props.orgId;
      return params;
    },
    showIndexColumn: false,
    bordered: false,
    size: 'small',
    immediate: false,
    resizeHeightOffset: 20,
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
    },
  });
  const [registerModal, { openModal }] = useModal();
  const { createConfirm, createMessage } = useMessage();

  const selectedRowKeys = ref<TableRowSelection['selectedRowKeys']>([] as string[]);

  const handleSelectionChange: TableRowSelection['onChange'] = (keys) => {
    selectedRowKeys.value = keys;
  };

  function handleAdd() {
    openModal(true, {
      data: {
        orgId: props.orgId,
      },
    });
  }

  function handleDetail(record, _column) {
    openModal(true, {
      mode: 'edit',
      data: record,
    });
  }

  function handleDel(_record, _column) {
    sysDeptDelete([_record.deptId]).then(() => {
      createMessage.success('删除成功');
      ActionTable.reload();
    });
    console.log('删除', _record);
  }

  function createActions(record, column: BasicColumn): ActionItem[] {
    const actions: ActionItem[] = [];

    actions.push(
      {
        label: '编辑',
        type: 'link',
        onClick: handleDetail.bind(null, record, column),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: handleDel.bind(null, record, column),
        },
      },
    );
    return actions;
  }
  function delAll() {
    createConfirm({
      title: '删除',
      iconType: 'warning',
      content: `确定删除选中的${selectedRowKeys.value?.length}条记录吗？`,
      onOk: () => {
        sysDeptDelete(selectedRowKeys.value || []).then(() => {
          createMessage.success('删除成功');
          ActionTable.reload();
        });
      },
    });
  }
  const [exportLoading, toggleExport] = useToggle(false);
  async function handleDownloadTemplate() {
    await exportUtil(downloadTemplate());
  }

  async function handleImport() {
    try {
      toggleExport(true);
      await exportUtil(
        sysDeptExport({ ...ActionTable.getForm().getFieldsValue(), orgId: props.orgId }),
      );
    } catch (error) {
      console.error(error);
    } finally {
      toggleExport(false);
    }
  }
  const [importLoading, toggleImport] = useToggle(false);

  function handleUploadChange(e: UploadChangeParam<UploadFile<any>>) {
    toggleImport(true);
    if (e.file.status === 'done' || e.file.status === 'error') {
      toggleImport(false);
      if (e.file.response.code === '0') {
        createMessage.success('导入成功');
        ActionTable.reload();
      } else {
        createMessage.error(`导入失败：${e.file.response.message}`);
      }
    }
  }
  const importUrl = computed(() => {
    return UPLOAD_SYS_DEPT_IMPORT + '?orgId=' + props.orgId;
  });
  // const [registerOperationLogModal, { openModal: openOperationLogModal }] = useModal();
  // function handleOperationLog() {
  //   openOperationLogModal(true);
  // }

  defineExpose({ ActionTable });
</script>

<template>
  <BasicTable
    class="ft-main-table"
    :row-selection="{
      selectedRowKeys,
      onChange: handleSelectionChange,
    }"
    @register="registerTable"
  >
    <template #headerTop>
      <div class="text-16px font-500 title-label mb-4">科室列表</div>
    </template>
    <template #tableTitle>
      <div class="flex gap-2">
        <Button type="primary" @click="handleAdd"> 新增 </Button>
        <!-- <Button type="primary" @click="handleOperationLog"> 操作日志 </Button> -->
        <Button :disabled="!selectedRowKeys?.length" @click="delAll"> 批量删除 </Button>
      </div>
    </template>
    <template #toolbar>
      <Button @click="handleDownloadTemplate"> 模板下载 </Button>
      <Upload
        :max-count="1"
        :headers="headers"
        :show-upload-list="false"
        :action="importUrl"
        @change="handleUploadChange"
      >
        <Button :loading="importLoading"> 批量导入 </Button>
      </Upload>
      <Button :loading="exportLoading" @click="handleImport"> 批量导出 </Button>
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'action'">
        <TableAction :actions="createActions(record, column)" />
      </template>
      <template v-else-if="column.dataIndex === 'enableFlag'">
        <span v-if="record.enableFlag == 0">启用</span>
        <span v-if="record.enableFlag == 1">禁用</span>
      </template>
    </template>
  </BasicTable>
  <Modal
    :activeOption="activeOption"
    :activeItemName="activeItemName"
    @register="registerModal"
    @success="ActionTable.reload()"
  />
  <!-- <OperationLogModal @register="registerOperationLogModal" /> -->
</template>

<style lang="less" scoped>
  :deep {
    .ft-main-table .vben-basic-form--compact .ant-col .ant-form-item {
      margin-bottom: 6px !important;
    }

    .ant-form {
      margin-bottom: 0 !important;
    }
  }
</style>
