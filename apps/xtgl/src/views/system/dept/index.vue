<script setup lang="ts">
  import { nextTick, onMounted, ref, watch } from 'vue';
  import { InputSearch, Select } from 'ant-design-vue';
  import { StyledList } from '@ft/components';
  import Right from './right.vue';
  import { queryOrganizationList } from '/@/api/org';
  const items = ref<any>([]);
  const activeItem = ref<any>('');
  const searchValue = ref<string>('');
  const activeItemName = ref<string>('');
  const rightRef = ref<InstanceType<typeof Right>>();

  const options = [
    { label: '医疗卫生服务机构', value: 1 },
    { label: '公共卫生服务机构', value: 2 },
  ];
  const activeOption = ref(1);
  onMounted(() => {
    getItemlist(activeOption.value);
  });
  function getItemlist(val) {
    queryOrganizationList({ orgType: val, orgName: searchValue.value }).then((res) => {
      items.value = res?.map((v) => {
        return {
          label: v.orgName,
          value: v.id,
        };
      });
      activeItem.value = res[0]?.id;
      activeItemName.value = res[0]?.orgName || '';
    });
  }
  watch(
    () => activeOption.value,
    (val) => {
      getItemlist(val);
    },
  );
  watch(
    () => activeItem.value,
    (val) => {
      if (!val) return false;
      nextTick(() => {
        rightRef?.value?.ActionTable.reload();
      });
    },
  );
  function handleChange(val) {
    activeItemName.value = val.label;
  }
</script>
<template>
  <div class="h-[calc(100%-8px)] w-[calc(100%-12px)] flex bg-white">
    <div
      class="w-200px p-16px flex flex-col"
      style="flex-shrink: 0; border-right: 1px solid #edeef0"
    >
      <div class="text-16px font-500 title-label mb-4">机构列表</div>
      <Select class="styled-select" :options="options" v-model:value="activeOption" />
      <InputSearch
        v-model:value="searchValue"
        class="mt-4 mb-2"
        @search="getItemlist(activeOption)"
        placeholder="请输入关键字"
        allowClear
      />
      <div class="flex-1 basis-0 of-y-auto of-x-hidden">
        <StyledList :items="items" v-model="activeItem" @change="handleChange" />
      </div>
    </div>
    <div class="flex-1 of-hidden">
      <Right
        ref="rightRef"
        :orgId="activeItem"
        :activeOption="activeOption"
        :activeItemName="activeItemName"
      />
    </div>
  </div>
</template>
<style lang="less" scoped>
  :deep {
    .styled-select {
      width: 100%;

      .ant-select-selector {
        border: none;
        box-shadow: none !important;
        padding: 0;
      }

      .ant-select-selection-item {
        border-bottom: 2px solid @primary-color;
        max-width: fit-content;
        padding-right: 0;
      }

      .ant-select-arrow {
        right: 0;
      }
    }
  }
</style>
