<script setup lang="ts">
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { BasicTable, useTable } from '@ft/internal/components/Table';

  const [register] = useModalInner();
  const [registerTable] = useTable({
    fetchSetting: {
      sizeField: 'pageSize',
      pageField: 'pageNum',
      listField: 'list',
    },
    columns: [
      {
        title: '操作人员',
        dataIndex: 'operator',
        width: 100,
        align: 'left',
      },
      {
        title: '操作信息',
        dataIndex: 'operationInformation',
        align: 'left',
      },
      {
        title: '操作时间',
        dataIndex: 'operationTime',
        width: 180,
        align: 'left',
      },
    ],
    useSearchForm: false,
    showIndexColumn: true,
    bordered: false,
    size: 'small',
  });
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    title="操作日志"
    :can-fullscreen="false"
    width="768px"
    @register="register"
    :show-ok-button="false"
    centered
  >
    <BasicTable class="ft-main-table" @register="registerTable" />
  </BasicModal>
</template>
