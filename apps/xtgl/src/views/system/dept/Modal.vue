<script setup lang="ts">
  import { computed, ref } from 'vue';

  import { useToggle } from '@vueuse/core';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import { modalSchema } from './data';

  import { sysDeptAdd, sysDeptUpdate } from '/@/api/dept';

  const emit = defineEmits(['register', 'success']);
  defineProps({
    activeOption: {
      type: Number,
      default: 1,
    },
    activeItemName: {
      type: String,
      default: '',
    },
  });

  const [loading, setLoading] = useToggle(false);

  const [registerForm, formAction] = useForm({
    schemas: modalSchema,
    labelWidth: 150,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });
  const mode = ref('');
  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data?.mode;
    formAction.setFieldsValue(data.data);
    if (mode.value === 'edit') {
      data.data.menuId = data?.data?.id || '';
      formAction.updateSchema({
        field: 'code',
        componentProps: {
          disabled: true,
        },
      });
      formAction.setFieldsValue(data.data);
    }
  });

  const { createMessage } = useMessage();

  async function handleOk() {
    formAction.validate().then((values) => {
      setLoading(true);
      const service = mode.value === 'edit' ? sysDeptUpdate : sysDeptAdd;
      service(values)
        .then(() => {
          createMessage.success(`${mode.value === 'edit' ? '保存' : '创建'}成功`);
          setLoading(false);
          closeModal();
          emit('success');
        })
        .finally(() => {
          setLoading(false);
        });
    });
  }
  const title = computed(() => {
    return mode.value === 'edit' ? '编辑科室' : '新增科室';
  });
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="title"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    width="768px"
    @register="register"
    @ok="handleOk"
  >
    <div class="color-#5C5F66 mb-4"
      >{{ activeOption == 1 ? '医疗卫生服务机构' : '公共卫生服务机构' }}-{{ activeItemName }}</div
    >
    <BasicForm style="transform: translateX(-36px)" @register="registerForm" />
  </BasicModal>
</template>
