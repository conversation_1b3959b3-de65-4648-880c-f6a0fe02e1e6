<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import { StyledList } from '@ft/components';
  import { Button } from '@ft/internal/components/Button';
  import { useRequest } from '@ft/request';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { DictEnum, getDictItemList } from '@ft/internal/api';
  import { schemasForm } from './data';
  import { editSysMsgReminderConfig, queryDetailByCode } from '/@/api/message-configuration';

  /**
   * 消息配置
   */

  const activeDictItemCode = ref<string>();
  const activeItem = ref({});
  const isEdit = ref(false);
  const [registerForm, formAction] = useForm({
    labelWidth: 150,
    disabled: computed(() => !isEdit.value),
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
    schemas: schemasForm,
  });

  const { loading, data: items } = useRequest(() => getDictItemList(DictEnum.SYS_MSG_TYPE), {
    onSuccess: (res) => {
      activeDictItemCode.value = res.length > 0 ? res[0].dictItemCode : '';
    },
  });
  const { runAsync: runAsync } = useRequest(queryDetailByCode, {
    manual: true,
    onSuccess: (data) => {
      formAction.setFieldsValue(data);
    },
  });
  watch(
    () => activeDictItemCode.value,
    (val) => {
      if (val) {
        runAsync(val);
      }
    },
  );

  const { runAsync: saveRunAsync, loading: saveLoading } = useRequest(editSysMsgReminderConfig, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      isEdit.value = false;
    },
  });
  function handleSave() {
    formAction.validate().then((value) => {
      saveRunAsync(value);
    });
  }
</script>
<template>
  <div class="flex gap-2 h-[calc(100%-10px)] w-[calc(100%-16px)]">
    <div class="flex flex-col gap-2 w-228px bg-#fff rounded rounded-lt-none p-3">
      <span class="text-#333333 fw-bold">消息类型</span>
      <div class="pr-4 of-y-auto of-x-hidden min-w-0">
        <StyledList
          :items="items"
          v-model="activeDictItemCode"
          v-model:value="activeItem"
          valueField="dictItemCode"
          label-field="dictItemName"
          class="flex-1"
          :width="216"
          :loading="loading"
        />
      </div>
    </div>
    <div class="flex-1 bg-#fff rounded p-3">
      <div class="flex justify-between items-center">
        <span class="text-#333333 fw-bold">消息提醒配置</span>
        <div>
          <Button
            v-if="!isEdit"
            type="link"
            pre-icon="ant-design:edit-outlined"
            @click="isEdit = !isEdit"
            >编辑</Button
          >
          <Button v-if="isEdit" type="default" class="mr-4" @click="isEdit = !isEdit">取消</Button>
          <Button v-if="isEdit" @click="handleSave" :loading="saveLoading" type="primary"
            >保存</Button
          >
        </div>
      </div>
      <div class="flex-1 basis-0 min-h-0 of-y-auto mt-4">
        <BasicForm style="transform: translateX(-36px)" @register="registerForm" />
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped></style>
