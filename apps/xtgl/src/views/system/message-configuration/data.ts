import { DictEnum, getDictItemList } from '@ft/internal/api';
import type { FormSchema } from '@ft/internal/components/Form';

export const schemasForm: FormSchema[] = [
  {
    field: 'id',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'msgTypeCode',
    label: '消息类型字典编码',
    component: 'Input',
    show: false,
  },
  {
    field: 'msgTypeName',
    label: '消息类型字典名称',
    component: 'Input',
    show: false,
  },
  {
    field: 'object',
    label: '提醒方式',
    component: 'Input',
    show: false,
  },
  {
    field: 'objectCode',
    label: '提醒对象',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.ORG_TYPE),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        valueToString: formModel.objectCode ? true : false,
        // options: [
        //   { label: '医疗卫生服务机构', value: 1 },
        //   { label: '公共卫生服务机构', value: 2 },
        // ],
        disabled: true,
        getPopupContainer: () => document.body,
        onChange: (_, opt) => {
          if (opt?.label) {
            formModel.object = opt?.label;
          }
        },
      };
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 8 } },
  },
  {
    field: 'comment',
    label: '提醒注释',
    component: 'InputTextArea',
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请输入提醒注释',
      autoSize: { minRows: 4, maxRows: 4 },
    },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    field: 'typeDesc',
    label: '提醒方式',
    component: 'Input',
    show: false,
  },
  {
    field: 'typeCode',
    label: '提醒方式',
    component: 'ApiRadioGroup',
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.MSG_REMINDER_TYPE),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        valueToString: true,
        // options: [
        //   { label: '宜健通推送', value: 1 },
        //   { label: '本平台推送弹窗', value: 2 },
        //   { label: '微信推送', value: 3 },
        //   { label: '短信推送', value: 4 },
        // ],
        getPopupContainer: () => document.body,
        onChange: (_, opt) => {
          if (opt?.label) {
            formModel.typeDesc = opt?.label;
          }
        },
      };
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 12 } },
  },
  {
    field: 'statusDesc',
    label: '是否启用',
    component: 'Input',
    show: false,
  },
  {
    field: 'status',
    label: '是否启用',
    component: 'ApiRadioGroup',
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.ENABLE_STATUS),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        valueToString: true,
        getPopupContainer: () => document.body,
        onChange: (_, opt) => {
          if (opt?.label) {
            formModel.typeDesc = opt?.label;
          }
        },
      };
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 8 } },
  },
];
