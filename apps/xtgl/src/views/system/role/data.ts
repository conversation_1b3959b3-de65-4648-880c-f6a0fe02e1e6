import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import type { IRole } from '/@/api/role';
import type { EditModalDataType } from '/@/types';
import { DictEnum, getDictItemList } from '@ft/internal/api';

export type RoleEditModalDataType = EditModalDataType<IRole>;

export const TreeTempPrefix = 'temp-';

export function removeTempIds(list: string[]) {
  return list.filter((item) => !item.startsWith(TreeTempPrefix));
}

// 角色名称 角色介绍 数据权限 功能权限 角色状态 创建时间
export const columns: BasicColumn[] = [
  {
    title: '角色名称',
    dataIndex: 'roleName',
    width: 150,
    align: 'left',
  },
  {
    title: '角色介绍',
    dataIndex: 'roleDesc',
    width: 150,
    align: 'left',
  },
  {
    title: '所属端口',
    dataIndex: 'endName',
    width: 100,
    align: 'left',
  },
  {
    title: '数据权限',
    dataIndex: 'dataPermission',
    width: 150,
    align: 'left',
  },
  {
    title: '功能权限',
    dataIndex: 'systemPermission',
    width: 150,
    align: 'left',
  },
  {
    title: '角色状态',
    dataIndex: 'enableFlag',
    width: 150,
    align: 'left',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 150,
    align: 'left',
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'roleName',
    component: 'Input',
    label: '角色名称',
    colProps: { span: 6 },
  },
  {
    field: 'enableFlag',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 0 },
        { label: '禁用', value: 1 },
      ],
    },
    label: '角色状态',
    colProps: { span: 6 },
  },
];

export const modalSchema: FormSchema[] = [
  {
    field: 'id',
    component: 'Input',
    label: 'ID',
    show: false,
  },
  {
    field: 'roleName',
    component: 'Input',
    label: '角色名称',
    required: true,
    colProps: { span: 12 },
  },
  {
    field: 'enableFlag',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 0 },
        { label: '禁用', value: 1 },
      ],
    },
    defaultValue: 0,
    label: '角色状态',
    colProps: { span: 12 },
  },
  // 所属端口
  {
    field: 'end',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.ROLE_END),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        onChange: (_, opt) => {
          formModel.endName = opt?.label;
        },
      };
    },
    label: '所属端口',
    colProps: { span: 12 },
  },
  {
    field: 'endName',
    component: 'Input',
    label: '端口名称',
    show: false,
  },
  {
    field: 'roleDesc',
    component: 'InputTextArea',
    label: '角色介绍',
    colProps: { span: 24 },
  },
  {
    field: 'orgIds',
    component: 'Input',
    label: '数据权限',
    colProps: { span: 24 },
    required: true,
    slot: 'orgIds',
  },
  {
    field: 'menuIds',
    component: 'Input',
    label: '功能权限',
    colProps: { span: 24 },
    required: true,
    slot: 'menuIds',
  },
];
