<script setup lang="ts">
  import { computed, ref } from 'vue';

  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { Tree } from 'ant-design-vue';
  import { buildShortUUID } from '@ft/internal/utils/uuid';
  import { treeMap } from '@ft/internal/utils/helper/treeHelper';
  import type { RoleEditModalDataType } from './data';
  import { TreeTempPrefix, modalSchema, removeTempIds } from './data';

  import { editRole, getRoleDetail, saveRole } from '/@/api/role';
  import { queryOrganizationTree } from '/@/api/org';
  import { getMenuTree } from '/@/api/menu';

  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm({
    schemas: modalSchema,
    labelWidth: 150,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });
  const mode = ref<'add' | 'edit'>('add');
  const title = computed(() => (mode.value === 'edit' ? '编辑角色' : '新增角色'));

  const roleDetail = useRequest(getRoleDetail, { manual: true });

  const menuTreeData = ref<any[]>([]);
  const { runAsync } = useRequest(getMenuTree, {
    manual: true,
    onSuccess(menuData) {
      menuTreeData.value = menuData;
    },
  });

  const menuIdsHalfCheckedKeys = ref<string[]>([]);

  const [register, { closeModal }] = useModalInner<RoleEditModalDataType>((data) => {
    mode.value = data?.mode;
    if (data.mode === 'add') {
      runAsync();
      return;
    }
    if (!data.record) return;
    roleDetail.runAsync(data.record.id).then((values) => {
      runAsync().then((menuData) => {
        let valuesMenuIds = values.menuIds;
        let halfCheckedKeys: string[] = [];
        treeMap(menuData, {
          conversion: (item) => {
            if (valuesMenuIds.includes(item.parentId)) {
              valuesMenuIds = valuesMenuIds.filter((id) => id !== item.parentId);
              halfCheckedKeys.push(item.parentId);
            }
          },
        });

        menuIdsHalfCheckedKeys.value = halfCheckedKeys;
        formAction.setFieldsValue({
          ...values,
          menuIds: valuesMenuIds,
        });
      });
    });
  });

  const { loading, runAsync: roleSaveRunAsync } = useRequest(
    (params) => (mode.value === 'edit' ? editRole(params) : saveRole(params)),
    {
      showSuccessMessage: true,
      manual: true,
    },
  );

  const { data: orgTreeData } = useRequest(queryOrganizationTree, {
    onSuccess: (data) => {
      treeMap(data, {
        conversion: (item) => {
          if (!item.id) {
            item.id = `${TreeTempPrefix}${buildShortUUID()}`;
          }
        },
      });
    },
  });

  async function handleOk() {
    formAction.validate().then((values) => {
      console.log(`values`, values);
      roleSaveRunAsync({
        ...values,
        orgIds: removeTempIds(values.orgIds),
        menuIds: menuIdsHalfCheckedKeys.value.concat(values.menuIds),
      }).then(() => {
        emit('success');
        closeModal();
      });
    });
  }

  function onCheck(_, e) {
    menuIdsHalfCheckedKeys.value = e.halfCheckedKeys;
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="title"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    width="768px"
    :min-height="500"
    :height="500"
    @register="register"
    @ok="handleOk"
  >
    <BasicForm style="transform: translateX(-36px)" @register="registerForm">
      <template #orgIds="{ model, field }">
        <Tree
          v-model:checked-keys="model[field]"
          :tree-data="orgTreeData"
          checkable
          :field-names="{
            children: 'children',
            title: 'orgName',
            key: 'id',
          }"
        />
      </template>
      <template #menuIds="{ model, field }">
        <Tree
          v-model:checked-keys="model[field]"
          :tree-data="menuTreeData"
          checkable
          :field-names="{
            children: 'children',
            title: 'menuName',
            key: 'id',
          }"
          @check="onCheck"
        >
          <template #title="treeNode">
            <span>{{ treeNode.menuName }}</span>
            <!-- 如果menuNote包含menuId则不展示 -->
            <span
              v-if="!treeNode?.menuNote?.includes('menuId')"
              class="ml-4 text-info-text-color"
              >{{ treeNode.menuNote }}</span
            >
          </template>
        </Tree>
      </template>
    </BasicForm>
  </BasicModal>
</template>
