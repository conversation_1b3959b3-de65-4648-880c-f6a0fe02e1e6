<script setup lang="ts">
  import { ref } from 'vue';
  import type { ActionItem, BasicColumn, TableRowSelection } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { useModal } from '@ft/internal/components/Modal';
  import { Popconfirm, Switch } from 'ant-design-vue';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import type { RoleEditModalDataType } from './data';
  import { columns, formSchema } from './data';
  import RoleEdit from './RoleEdit.vue';
  import type { IRole } from '/@/api/role';
  import {
    batchDeleteRole,
    deleteRole,
    exportRole,
    getRolePage,
    toggleRoleActivation,
  } from '/@/api/role';
  import type { ListSwitchUnionType } from '/@/types';

  const [registerTable, tableIns] = useTable({
    api: getRolePage,
    columns,
    formConfig: {
      colon: true,
      labelWidth: 90,
      showAdvancedButton: false,
      actionColOptions: {
        span: 12,
        style: {
          textAlign: 'right',
        },
      },
      schemas: formSchema,
    },
    useSearchForm: true,
    showIndexColumn: false,
    bordered: false,
    size: 'small',
    resizeHeightOffset: 10,
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
    afterFetch: (data) => {
      data.forEach((item) => {
        item.loading = false;
      });
      return data;
    },
  });
  const [registerModal, { openModal }] = useModal();

  const selectedRowKeys = ref<TableRowSelection['selectedRowKeys']>([]);

  const handleSelectionChange: TableRowSelection['onChange'] = (keys) => {
    selectedRowKeys.value = keys;
  };

  function handleAdd() {
    openModal(true, {
      mode: 'add',
    });
  }

  function onEdit(record, _column) {
    openModal<RoleEditModalDataType>(true, {
      mode: 'edit',
      record,
    });
  }
  const { run: runDelRole } = useRequest(deleteRole, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      tableIns.reload();
    },
  });

  // const { run: handleVerify } = useRequest(verifySignData, {
  //   manual: true,
  //   showSuccessMessage: () => '验证通过',
  // });

  function handleDel(record: IRole, _column) {
    runDelRole(record.id);
  }

  function createActions(record: IRole, column: BasicColumn): ActionItem[] {
    const actions: ActionItem[] = [
      // {
      //   label: '验证完整性',
      //   type: 'link',
      //   onClick: handleVerify.bind(null, { id: record.id }),
      // },
      {
        label: '编辑',
        type: 'link',
        onClick: onEdit.bind(null, record, column),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: handleDel.bind(null, record, column),
        },
      },
    ];

    return actions;
  }

  function onSuccess() {
    tableIns.reload();
  }
  const { runAsync: runBatchDeleteRole } = useRequest(batchDeleteRole, {
    manual: true,
    showSuccessMessage: true,
  });
  function onBatchDel() {
    runBatchDeleteRole({ ids: selectedRowKeys.value as string[] }).then(() => {
      selectedRowKeys.value = [];
      tableIns.reload();
    });
  }

  const { runAsync: runToggleRoleActivation } = useRequest(toggleRoleActivation, {
    manual: true,
    showSuccessMessage: true,
  });
  function onSwitchChange(record: ListSwitchUnionType<IRole>) {
    record.loading = true;
    const { id } = record;
    runToggleRoleActivation(id).finally(() => {
      record.loading = false;
    });
  }
  const { loading: exportLoading, runAsync: exportRunAsync } = useRequest(exportRole, {
    manual: true,
    showSuccessMessage: true,
  });
  function onExport() {
    exportUtil(
      exportRunAsync({
        idList: selectedRowKeys.value,
        ...tableIns.getForm().getFieldsValue(),
      }),
    ).then(() => {
      selectedRowKeys.value = [];
    });
  }
</script>

<template>
  <div class="w-full h-full pr-4 test">
    <BasicTable
      row-key="id"
      class="ft-main-table"
      :row-selection="{
        selectedRowKeys,
        onChange: handleSelectionChange,
      }"
      @register="registerTable"
    >
      <template #headerTop>
        <div class="text-16px font-500 title-label mb-4">角色列表</div>
      </template>
      <template #tableTitle>
        <div class="flex gap-2">
          <Button type="primary" @click="handleAdd"> 新增 </Button>
          <Popconfirm
            title="确定删除选中项吗？"
            okText="确定"
            cancelText="取消"
            :ok-button-props="{ danger: true }"
            @confirm="onBatchDel"
          >
            <Button :disabled="selectedRowKeys?.length === 0"> 批量删除 </Button>
          </Popconfirm>
        </div>
      </template>
      <template #toolbar>
        <Button :loading="exportLoading" @click="onExport">导出</Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'enableFlag'">
          <Switch
            :loading="record.loading"
            :checked-value="0"
            :un-checked-value="1"
            v-model:checked="record.enableFlag"
            @change="onSwitchChange(record)"
          />
        </template>
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
    <RoleEdit @register="registerModal" @success="onSuccess" />
  </div>
</template>
