<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import type { DictEditModalDataType } from './data';
  import { dictForm } from './data';
  import { postDict, putDict } from '/@/api/dict';

  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm({
    schemas: dictForm,
    labelWidth: 120,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });

  const mode = ref('');
  const getTitle = computed(() => (mode.value === 'edit' ? '编辑字典' : '新增字典'));

  const [register, { closeModal }] = useModalInner<DictEditModalDataType>((data) => {
    mode.value = data?.mode;
    if (!data.record) return;
    formAction.updateSchema({
      field: 'dictCode',
      componentProps: {
        disabled: true,
      },
    });
    formAction.setFieldsValue(data.record);
  });

  const { loading, runAsync } = useRequest(
    (params) => (mode.value === 'edit' ? putDict(params) : postDict(params)),
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess: () => {
        emit('success');
        closeModal();
      },
    },
  );

  async function handleOk() {
    formAction.validate().then((values) => {
      runAsync(values);
    });
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="getTitle"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    @register="register"
    @ok="handleOk"
    :min-height="120"
    centered
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
