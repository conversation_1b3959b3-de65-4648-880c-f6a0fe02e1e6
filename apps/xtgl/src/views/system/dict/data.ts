import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import type { IDict, IDictItem } from '/@/api/dict';
import type { EditModalDataType } from '/@/types';

export type DictEditModalDataType = EditModalDataType<IDict>;
export type DictItemEditModalDataType = EditModalDataType<IDictItem> & { dictId?: string };

export const leftColumns: BasicColumn[] = [
  {
    title: '字典编码',
    dataIndex: 'dictCode',
    width: 150,
  },
  {
    title: '字典名称',
    dataIndex: 'dictName',
    width: 130,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 100,
  },
];

export const rightColumns: BasicColumn[] = [
  {
    title: '编码值',
    dataIndex: 'dictItemCode',
    width: 100,
  },
  {
    title: '值名称',
    dataIndex: 'dictItemName',
    width: 100,
  },
  {
    title: '备注',
    dataIndex: 'dictItemRemark',
    width: 100,
  },
  {
    title: '排序值',
    dataIndex: 'sortNo',
    width: 100,
  },
];

export const leftFormSchema: FormSchema[] = [
  {
    field: 'dictName',
    label: '字典名称',
    component: 'Input',
    colProps: { span: 12 },
  },
];

export const rightFormSchema: FormSchema[] = [
  {
    field: 'dictItemName',
    label: '字典值名称',
    component: 'Input',
    colProps: { span: 12 },
  },
];

export const dictForm: FormSchema[] = [
  {
    field: 'dictId',
    label: ' ',
    component: 'Input',
    show: false,
  },
  {
    field: 'dictCode',
    label: '字典编码',
    component: 'Input',
    required: true,
    componentProps: {
      disabled: false,
    },
    colProps: { span: 18 },
  },
  {
    field: 'dictName',
    label: '字典名称',
    component: 'Input',
    required: true,
    colProps: { span: 18 },
  },
  {
    field: 'remark',
    label: '备注信息',
    component: 'Input',
    colProps: { span: 18 },
  },
];
export const dictItemForm: FormSchema[] = [
  {
    field: 'dictId',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'dictItemId',
    label: ' ',
    component: 'Input',
    show: false,
  },
  {
    field: 'dictItemCode',
    label: '编码值',
    component: 'Input',
    required: true,
    componentProps: {
      disabled: false,
    },
    colProps: { span: 18 },
  },
  {
    field: 'dictItemName',
    label: '值名称',
    component: 'Input',
    required: true,
    colProps: { span: 18 },
  },
  {
    field: 'dictItemRemark',
    label: '备注',
    component: 'Input',
    colProps: { span: 18 },
  },
  {
    field: 'sortNo',
    label: '排序值',
    component: 'InputNumber',
    componentProps: {
      min: 0,
    },
    colProps: { span: 18 },
  },
];
