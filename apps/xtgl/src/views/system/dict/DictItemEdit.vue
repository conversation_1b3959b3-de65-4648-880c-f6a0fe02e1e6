<script setup lang="ts">
  import { computed, ref } from 'vue';

  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import type { DictItemEditModalDataType } from './data';
  import { dictItemForm } from './data';

  import { useRequest } from '@ft/request';
  import { postDictItem, putDictItem } from '/@/api/dict';

  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm({
    schemas: dictItemForm,
    labelWidth: 120,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });

  const mode = ref('');
  const getTitle = computed(() => (mode.value === 'edit' ? '编辑字典项' : '新增字典项'));

  const [register, { closeModal }] = useModalInner<DictItemEditModalDataType>((data) => {
    mode.value = data.mode;
    formAction.setFieldsValue({ dictId: data.dictId });
    if (!data.record) return;
    formAction.updateSchema({
      field: 'dictItemCode',
      componentProps: {
        disabled: true,
      },
    });
    formAction.setFieldsValue(data.record);
  });

  const { loading, runAsync } = useRequest(
    (params) => (mode.value === 'edit' ? putDictItem(params) : postDictItem(params)),
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess: () => {
        emit('success');
        closeModal();
      },
    },
  );

  async function handleOk() {
    formAction.validate().then((values) => {
      runAsync(values);
    });
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="getTitle"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    @register="register"
    @ok="handleOk"
    centered
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
