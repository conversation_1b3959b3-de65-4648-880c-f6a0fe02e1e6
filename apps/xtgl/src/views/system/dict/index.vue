<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import type { ActionItem, BasicColumn, TableRowSelection } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { useModal } from '@ft/internal/components/Modal';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import { useAppHeader } from '@ft/internal/utils/auth/useAppHeader';
  import { useToggle } from '@vueuse/core';
  import type { UploadChangeParam, UploadFile } from 'ant-design-vue';
  import { Upload } from 'ant-design-vue';
  import DictItemEdit from './DictItemEdit.vue';
  import DictEdit from './DictEdit.vue';
  import type { DictEditModalDataType, DictItemEditModalDataType } from './data';
  import { leftColumns, leftFormSchema, rightColumns, rightFormSchema } from './data';
  import type { IDict, IDictItem } from '/@/api/dict';
  import {
    DICT_ITEM_IMPORT,
    delDict,
    delDictItem,
    downloadDictItemTemplate,
    getDictItemPage,
    getDictPage,
  } from '/@/api/dict';

  const { createMessage } = useMessage();
  const selectedRowKeys = ref<TableRowSelection['selectedRowKeys']>([]);
  const dictId = computed(() => (selectedRowKeys.value?.[0] || '') as string);
  const [registerLeftTable, { reload }] = useTable({
    inset: true,
    api: getDictPage,
    columns: leftColumns,
    resizeHeightOffset: 16,
    formConfig: {
      labelAlign: 'left',
      colon: true,
      labelWidth: 70,
      showAdvancedButton: false,
      actionColOptions: {
        span: 12,
      },
      schemas: leftFormSchema,
    },
    useSearchForm: true,
    showIndexColumn: true,
    bordered: false,
    size: 'small',
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
    rowKey: 'dictId',
    afterFetch: (data) => {
      selectedRowKeys.value = [data[0]?.dictId || ''];
      return data;
    },
  });
  const [registerRightTable, { reload: reloadDictItem }] = useTable({
    inset: true,
    api: getDictItemPage,
    columns: rightColumns,
    size: 'small',
    bordered: false,
    showIndexColumn: false,
    immediate: false,
    canResize: false,
    formConfig: {
      labelAlign: 'left',
      colon: true,
      labelWidth: 80,
      showAdvancedButton: false,
      actionColOptions: {
        span: 12,
      },
      schemas: rightFormSchema,
    },
    useSearchForm: true,
    beforeFetch: (params) => {
      params.dictId = dictId.value || '';
      return params;
    },
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
  });

  watch(dictId, (val) => {
    val && reloadDictItem();
  });

  const rowSelection = computed<TableRowSelection>(() => {
    return {
      type: 'radio',
      selectedRowKeys: selectedRowKeys.value,
      onChange: (keys: any, _) => {
        if (keys.length === 0) {
          return;
        }
        selectedRowKeys.value = keys;
      },
    };
  });

  const [register, { openModal: openDictModal }] = useModal();
  const [registerEditCode, { openModal: openDictItemModal }] = useModal();

  function addDict() {
    openDictModal<DictEditModalDataType>(true, {
      mode: 'add',
    });
  }
  function addDictItem() {
    if (dictId.value !== '') {
      openDictItemModal<DictItemEditModalDataType>(true, {
        mode: 'add',
        dictId: dictId.value,
      });
    } else {
      createMessage.error('请先选择左侧字典列表某一项进行添加');
    }
  }
  function editDict(record: IDict) {
    openDictModal<DictEditModalDataType>(true, {
      mode: 'edit',
      record,
    });
  }
  function editDictItem(record) {
    openDictItemModal<DictItemEditModalDataType>(true, {
      record,
      dictId: dictId.value,
      mode: 'edit',
    });
  }

  function handleSuccess() {
    reload();
  }

  function handleSuccessItem() {
    reloadDictItem();
  }
  function createActions(record: IDict, column: BasicColumn): ActionItem[] {
    const actions: ActionItem[] = [];
    actions.push(
      {
        label: '编辑',
        type: 'link',
        size: 'small',
        onClick: editDict.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: handleDel.bind(null, record, column),
          okButtonProps: {
            danger: true,
          },
        },
      },
    );
    return actions;
  }
  function createActionsItem(record, column: BasicColumn): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '编辑',
        type: 'link',
        size: 'small',
        onClick: editDictItem.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: handleDelItem.bind(null, record, column),
          okButtonProps: {
            danger: true,
          },
        },
      },
    ];
    return actions;
  }

  const { run: delDictRun } = useRequest(delDict, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      reload();
    },
  });

  function handleDel(record: IDict, _column) {
    delDictRun(record.dictId);
  }

  const { run: delDictItemRun } = useRequest(delDictItem, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      reloadDictItem();
    },
  });

  function handleDelItem(record: IDictItem, _column) {
    delDictItemRun(record.dictItemId);
  }

  const { loading: downloadTemplateLoading, runAsync: downloadTemplateRunAsync } = useRequest(
    downloadDictItemTemplate,
    {
      manual: true,
    },
  );
  function onDownLoadTemplate() {
    exportUtil(downloadTemplateRunAsync());
  }

  const headers = useAppHeader();
  const dictItemImportUrl = computed(() => `${DICT_ITEM_IMPORT}?dictId=${dictId.value}`);

  const [importLoading, setImportLoading] = useToggle(false);
  function handleUploadChange(info: UploadChangeParam<UploadFile<any>>) {
    setImportLoading(true);
    if (info.file.status === 'done' || info.file.status === 'error') {
      setImportLoading(false);
      if (info.file.response.code === '0') {
        createMessage.success('导入成功');
        reload();
      } else {
        createMessage.error(`导入失败：${info.file.response.message}`);
      }
    }
  }
</script>

<template>
  <div class="h-full w-full pr-2 pb-2 flex gap-2">
    <div class="h-full bg-white p-4 flex-1 of-hidden">
      <div class="title-label text-16px font-500">字典列表</div>
      <BasicTable :row-selection="rowSelection" @register="registerLeftTable">
        <template #tableTitle>
          <Button type="primary" @click="addDict"> 新增字典 </Button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :divider="false" :actions="createActions(record, column)" />
          </template>
        </template>
      </BasicTable>
    </div>
    <div class="h-full bg-white p-4 flex-1 of-hidden">
      <div class="title-label text-16px font-500">字典值列表</div>
      <BasicTable @register="registerRightTable">
        <template #tableTitle>
          <Button type="primary" @click="addDictItem"> 新增字典值 </Button>
        </template>
        <template #toolbar>
          <Button :loading="downloadTemplateLoading" @click="onDownLoadTemplate">下载模板</Button>
          <Upload
            :max-count="1"
            :headers="headers"
            :action="dictItemImportUrl"
            :show-upload-list="false"
            @change="handleUploadChange"
          >
            <Button :loading="importLoading"> 导入 </Button>
          </Upload>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :divider="false" :actions="createActionsItem(record, column)" />
          </template>
        </template>
      </BasicTable>
      <DictEdit @register="register" @success="handleSuccess" />
      <DictItemEdit @register="registerEditCode" @success="handleSuccessItem" />
    </div>
  </div>
</template>

<style lang="less" scoped></style>
