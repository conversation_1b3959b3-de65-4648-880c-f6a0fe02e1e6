<script setup lang="ts">
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import type { ActionItem, BasicColumn } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { useModal } from '@ft/internal/components/Modal';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import { ref } from 'vue';
  import MenuEdit from './MenuEdit.vue';
  import type { MenuEditModalDataType } from './data';
  import { MenuTypeMap, columns } from './data';
  import { deleteMenu, getMenuTree } from '/@/api/menu';

  const { createMessage } = useMessage();
  const [register, { openModal }] = useModal();

  function addMenu() {
    openModal<MenuEditModalDataType>(true, {
      mode: 'add',
    });
  }
  function onEditMenu(record) {
    openModal<MenuEditModalDataType>(true, {
      mode: 'edit',
      record,
    });
  }

  // const { hasPermission } = usePermission();

  const [registerTable, { reload }] = useTable({
    api: getMenuTree,
    showIndexColumn: false,
    pagination: false,
    columns,
    scroll: {
      x: 1600,
    },
    beforeFetch: (params) => {
      return params;
    },
    bordered: false,
    size: 'small',
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
  });

  const expandedRowKeys = ref<string[]>([]);

  function createActions(record, column: BasicColumn): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '编辑',
        type: 'link',
        size: 'small',
        onClick: onEditMenu.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: handleDel.bind(null, record, column),
          okButtonProps: {
            danger: true,
          },
        },
      },
    ];
    return actions;
  }

  function handleSuccess() {
    reload();
  }

  function handleDel(record, _column) {
    deleteMenu(record.id).then(() => {
      createMessage.success('删除成功');
      reload();
    });
  }
</script>

<template>
  <div class="w-full h-full pr-4">
    <BasicTable
      row-key="id"
      v-model:expandedRowKeys="expandedRowKeys"
      @register="registerTable"
      class="ft-main-table"
    >
      <template #tableTitle>
        <Button type="primary" @click="addMenu"> 新增 </Button>
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record, column)" />
        </template>
        <template v-if="column.dataIndex === 'menuType'">
          <div>{{ MenuTypeMap[record.menuType] }}</div>
        </template>
      </template>
    </BasicTable>
    <MenuEdit @register="register" @success="handleSuccess" />
  </div>
</template>
