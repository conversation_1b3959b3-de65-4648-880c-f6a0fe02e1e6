<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import type { MenuEditModalDataType } from './data';
  import { addForm } from './data';

  import type { IMenu } from '/@/api/menu';
  import { postMenu, putMenu } from '/@/api/menu';

  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm({
    schemas: addForm,
    labelWidth: 100,
    baseColProps: { span: 6 },
    actionColOptions: { span: 24 },
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });
  const mode = ref('');
  const getTitle = computed(() => {
    return mode.value === 'add' ? '新增菜单' : '编辑菜单';
  });

  const [register, { closeModal }] = useModalInner<MenuEditModalDataType>((data) => {
    mode.value = data?.mode;
    if (mode.value === 'edit' && data.record) {
      console.log(`data.record`, data.record);
      formAction.setFieldsValue(data.record);
    }
  });

  const { loading, runAsync } = useRequest(
    (params: IMenu) => {
      return mode.value === 'edit' ? putMenu(params) : postMenu(params);
    },
    { manual: true, showSuccessMessage: true },
  );
  async function handleOk() {
    formAction.validate().then((values) => {
      runAsync(values).then(() => {
        emit('success');
        closeModal();
      });
    });
  }
</script>

<template>
  <BasicModal
    width="60%"
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="getTitle"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    @register="register"
    @ok="handleOk"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
