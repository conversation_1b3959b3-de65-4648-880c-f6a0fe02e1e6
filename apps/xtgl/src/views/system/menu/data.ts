import type { IMenu } from '/@/api/menu';
import { getMenuTree } from '/@/api/menu';
import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';

//菜单类型 (1-目录 2-菜单 3-按钮 4-路由)
export const MenuTypeMap = {
  1: '目录',
  2: '菜单',
  3: '按钮',
  4: '内页',
};

export interface MenuEditModalDataType {
  mode: 'add' | 'edit';
  record?: IMenu;
}

export const columns: BasicColumn[] = [
  {
    title: '菜单名称',
    dataIndex: 'menuName',
    align: 'left',
    ellipsis: false,
    width: 'auto',
  },
  // {
  //   title: '菜单编号',
  //   dataIndex: 'id',
  //   width: 190,
  //   align: 'left',
  // },
  {
    title: '上级菜单',
    dataIndex: 'parentName',
    width: 80,
    align: 'left',
  },
  {
    title: '菜单类型',
    dataIndex: 'menuType',
    width: 80,
    align: 'left',
  },
  {
    title: '备注',
    dataIndex: 'menuNote',
    width: 80,
    align: 'left',
  },
  {
    title: '路由路径',
    dataIndex: 'menuRoutePath',
    align: 'left',
  },
  {
    title: '菜单图标',
    dataIndex: 'menuIcon',
    width: 120,
    align: 'left',
  },
  {
    title: '排序值',
    dataIndex: 'sortNo',
    width: 80,
    align: 'left',
  },
  {
    title: '组件',
    dataIndex: 'menuComponent',
    align: 'left',
    width: 'auto',
  },
];

export const addForm: FormSchema[] = [
  {
    field: 'id',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'menuName',
    label: '菜单名称',
    component: 'Input',
    required: true,
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: 'parentId',
    label: '上级菜单',
    component: 'ApiTreeSelect',
    required: true,
    colProps: { span: 12 },
    componentProps: () => {
      return {
        api: async () => {
          const data = await getMenuTree({});
          const root = [
            {
              children: [...data],
              hasChildren: true,
              id: '-1',
              menuName: '根目录',
              parentId: null,
              path: '',
              sortNo: 0,
              menuType: 1,
            },
          ];
          return root;
        },
        getPopupContainer: () => document.body,
        showSearch: true,
        treeNodeFilterProp: 'menuName',
        optionFilterProp: 'label',
        fieldNames: { children: 'children', label: 'menuName', value: 'id' },
        height: 340,
        listHeight: 340,
      };
    },
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: 'menuType',
    label: '菜单类型',
    component: 'Select',
    componentProps: {
      options: [
        { label: '目录', value: 1 },
        { label: '菜单', value: 2 },
        { label: '内页', value: 4 },
        { label: '按钮', value: 3 },
      ],
      getPopupContainer: () => document.body,
    },
    required: true,
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: 'menuRoutePath',
    label: '路由路径',
    component: 'Input',
    required: true,
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: 'menuIcon',
    label: '菜单图标',
    component: 'Input',
    colProps: { span: 12 },
    componentProps: {},
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: 'sortNo',
    label: '排序值',
    component: 'InputNumber',
    required: true,
    colProps: { span: 12 },
    componentProps: {
      min: 0,
      // max: 100,
    },
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: 'menuComponent',
    label: '组件',
    required: true,
    component: 'Input',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  // menuNote
  {
    field: 'menuNote',
    label: '备注',
    component: 'Input',
    colProps: { span: 12 },
    itemProps: { wrapperCol: { span: 24 } },
  },
];
