import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { type IOrganization, PRG_UPLOAD_SIGNATURE } from '/@/api/org';
import { getAdministrativeList } from '/@/api/dict';
import { DictEnum, getDictItemList } from '@ft/internal/api';

export interface OrgEditModalDataType {
  mode: 'add' | 'edit';
  record?: IOrganization;
}

// 机构编码 机构名称 机构分类 机构等级 行政区划 用户数量 机构地址 机构状态 创建时间
export const columns = (orgType: number): BasicColumn[] => {
  return [
    {
      title: '机构编码',
      dataIndex: 'orgCode',
      width: 100,
      align: 'left',
    },
    {
      title: '机构名称',
      dataIndex: 'orgName',
      width: 180,
      align: 'left',
    },
    // {
    //   title: '机构分类',
    //   dataIndex: 'orgCategoryName',
    //   width: 100,
    //   align: 'left',
    //   ifShow: () => orgType === 2,
    // },
    // {
    //   title: '机构等级',
    //   dataIndex: 'orgLevelName',
    //   width: 100,
    //   align: 'left',
    //   ifShow: () => orgType === 2,
    // },
    // {
    //   title: '机构等级',
    //   dataIndex: 'orgLevelName',
    //   width: 100,
    //   align: 'left',
    //   ifShow: () => orgType === 1,
    // },
    {
      title: '咨询问诊服务',
      dataIndex: 'isConsultation',
      width: 100,
      align: 'left',
      ifShow: () => orgType === 1,
      // 0: 是 1: 否
      customRender: ({ record }) => {
        return record.isConsultation === 1 ? '否' : record.isConsultation === 0 ? '是' : '-';
      },
    },
    {
      title: '医服到家服务',
      dataIndex: 'isMedicalService',
      width: 100,
      align: 'left',
      ifShow: () => orgType === 1,
      // 0: 是 1: 否
      customRender: ({ record }) => {
        return record.isMedicalService === 1 ? '否' : record.isMedicalService === 0 ? '是' : '-';
      },
    },
    {
      title: '行政区划',
      dataIndex: 'divisionName',
      width: 140,
      align: 'left',
    },
    // {
    //   title: '用户数量',
    //   dataIndex: 'userCount',
    //   width: 100,
    //   align: 'left',
    //   ifShow: () => orgType === 2,
    // },
    // {
    //   title: '机构地址',
    //   dataIndex: 'orgAddress',
    //   width: 100,
    //   align: 'left',
    //   ifShow: () => orgType === 2,
    // },
    // {
    //   title: '机构地址纬度',
    //   dataIndex: 'latitude',
    //   width: 100,
    //   align: 'left',
    // },
    // {
    //   title: '机构地址经度',
    //   dataIndex: 'longitude',
    //   width: 100,
    //   align: 'left',
    // },
    // 机构负责人
    // {
    //   title: '机构负责人',
    //   dataIndex: 'orgManager',
    //   width: 100,
    //   align: 'left',
    // },
    // // 机构联系电话
    // {
    //   title: '机构联系电话',
    //   dataIndex: 'orgTelephone',
    //   width: 140,
    //   align: 'left',
    // },
    //专病管理联系电话
    // {
    //   title: '专病管理联系电话',
    //   dataIndex: 'specificDiseaseTelephone',
    //   width: 140,
    //   align: 'left',
    //   // ifShow: () => orgType === 2,
    // },
    {
      title: '机构状态',
      dataIndex: 'enableFlag',
      width: 100,
      align: 'left',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 180,
      align: 'left',
    },
  ];
};

export const createFormSchemas = (orgType: number): FormSchema[] => {
  return [
    {
      field: 'orgCategory',
      component: 'ApiSelect',
      componentProps: {
        api: () =>
          getDictItemList(orgType === 1 ? DictEnum.YLWS_ORG_CATEGORY : DictEnum.GGWS_ORG_CATEGORY),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
      },
      label: '机构分类',
      colProps: { span: 6 },
    },
    {
      field: 'divisionId',
      component: 'ApiSelect',
      componentProps: {
        api: getAdministrativeList,
        labelField: 'name',
        valueField: 'code',
      },
      label: '行政区划',
      colProps: { span: 6 },
    },
    {
      field: 'orgName',
      component: 'Input',
      label: '机构名称',
      colProps: { span: 6 },
    },
    {
      field: 'orgLevel',
      component: 'ApiSelect',
      componentProps: {
        api: () => getDictItemList(DictEnum.ORG_LEVEL),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
      },
      label: '机构等级',
      colProps: { span: 6 },
      ifShow: () => orgType === 2,
    },
    {
      field: 'isConsultation',
      component: 'Select',
      componentProps: {
        options: [
          { label: '是', value: 0 },
          { label: '否', value: 1 },
        ],
      },
      label: '咨询问诊服务',
      colProps: { span: 6 },
      ifShow: () => orgType === 1,
    },
    {
      field: 'isMedicalService',
      component: 'Select',
      componentProps: {
        options: [
          { label: '是', value: 0 },
          { label: '否', value: 1 },
        ],
      },
      label: '医服到家服务',
      colProps: { span: 6 },
      ifShow: () => orgType === 1,
    },
    {
      field: 'enableFlag',
      component: 'Select',
      componentProps: {
        options: [
          { label: '启用', value: 0 },
          { label: '禁用', value: 1 },
        ],
      },
      label: '机构状态',
      colProps: { span: 6 },
    },
    // 创建时间
    {
      field: 'createTime',
      component: 'RangePicker',
      componentProps: {
        valueFormat: 'YYYY-MM-DD',
        format: 'YYYY-MM-DD',
        style: { width: '100%' },
      },
      label: '创建时间',
      colProps: { span: 6 },
    },
  ];
};

// 机构分类 行政区划 机构编码 机构名称 机构地址 机构等级 机构状态
export const createModalSchema = (orgType: number): FormSchema[] => {
  return [
    {
      field: 'id',
      component: 'Input',
      label: 'id',
      show: false,
    },
    {
      field: 'orgCategory',
      component: 'ApiSelect',
      required: true,
      componentProps: ({ formModel }) => {
        return {
          api: () =>
            getDictItemList(
              orgType === 1 ? DictEnum.YLWS_ORG_CATEGORY : DictEnum.GGWS_ORG_CATEGORY,
            ),
          labelField: 'dictItemName',
          valueField: 'dictItemCode',
          getPopupContainer: () => document.body,
          onChange: (_, opt) => {
            formModel.orgCategoryName = opt?.label;
          },
        };
      },
      label: '机构分类',
      colProps: { span: 12 },
    },
    {
      field: 'orgCategoryName',
      component: 'Input',
      label: '机构分类',
      show: false,
    },
    {
      field: 'divisionId',
      component: 'ApiSelect',
      required: true,
      componentProps: ({ formModel }) => {
        return {
          api: getAdministrativeList,
          labelField: 'name',
          valueField: 'code',
          getPopupContainer: () => document.body,
          onChange: (_, opt) => {
            if (opt?.label) formModel.divisionName = opt?.label;
          },
        };
      },
      label: '行政区划',
      colProps: { span: 12 },
      ifShow: () => orgType === 2,
    },
    {
      field: 'divisionName',
      component: 'Input',
      label: '行政区划',
      show: false,
    },
    {
      field: 'orgCode',
      component: 'Input',
      label: '机构编码',
      required: true,
      colProps: { span: 12 },
    },
    {
      field: 'orgName',
      component: 'Input',
      label: '机构名称',
      required: true,
      colProps: { span: 12 },
    },
    // {
    //   field: 'orgName',
    //   component: 'Input',
    //   label: '机构等级',
    //   required: true,
    //   colProps: { span: 12 },
    //   ifShow: () => orgType === 1,
    // },
    {
      field: 'orgLevel',
      component: 'ApiSelect',
      componentProps: ({ formModel }) => {
        return {
          api: () => getDictItemList(DictEnum.ORG_LEVEL),
          labelField: 'dictItemName',
          valueField: 'dictItemCode',
          getPopupContainer: () => document.body,
          onChange: (_, opt) => {
            formModel.orgLevelName = opt?.label;
          },
        };
      },
      label: '机构等级',
      colProps: { span: 12 },
    },
    {
      field: 'orgLevelName',
      component: 'Input',
      label: '机构等级',
      show: false,
    },
    {
      field: 'omitId',
      label: '所在地区',
      required: true,
      component: 'Select',
      defaultValue: '湖北省',
      componentProps: () => {
        return {
          placeholder: '请选择省市',
          showSearch: true,
          optionFilterProp: 'label',
          options: [
            {
              label: '湖北省',
              value: '湖北省',
            },
          ],
        };
      },
      colProps: {
        span: 5,
      },
      itemProps: {
        wrapperCol: {
          span: 18,
        },
      },
      ifShow: () => orgType === 1,
    },
    {
      field: 'cityId',
      label: ' ',
      required: true,
      component: 'Select',
      defaultValue: '宜昌市',
      disabledLabelWidth: true,
      componentProps: () => {
        return {
          placeholder: '请选择省市',
          showSearch: true,
          optionFilterProp: 'label',
          options: [
            {
              label: '宜昌市',
              value: '宜昌市',
            },
          ],
        };
      },
      colProps: {
        span: 3,
      },
      itemProps: {
        // class: 'city-code',
        wrapperCol: {
          span: 24,
        },
      },
      ifShow: () => orgType === 1,
    },
    {
      field: 'divisionId',
      label: ' ',
      required: true,
      component: 'ApiSelect',
      disabledLabelWidth: true,
      componentProps: ({ formModel }) => {
        return {
          api: getAdministrativeList,
          labelField: 'name',
          valueField: 'code',
          showSearch: true,
          optionFilterProp: 'label',
          getPopupContainer() {
            return document.body;
          },
          onChange(_, opt) {
            if (opt?.label) formModel.divisionName = opt?.label;

            formModel.townId = undefined;
            formModel.villageId = undefined;
          },
        };
      },
      colProps: {
        span: 3,
      },
      itemProps: {
        wrapperCol: {
          span: 24,
        },
      },
      ifShow: () => orgType === 1,
    },
    {
      field: 'townName',
      label: '所属乡镇/街道名称',
      component: 'Input',
      show: false,
    },
    {
      field: 'townId',
      label: ' ',
      required: true,
      component: 'ApiSelect',
      disabledLabelWidth: true,
      componentProps: ({ formModel }) => {
        return {
          placeholder: '请选择所属乡镇/街道名称',
          api: () => formModel.divisionId && getAdministrativeList(formModel.divisionId),
          labelField: 'name',
          valueField: 'code',
          showSearch: true,
          optionFilterProp: 'label',
          getPopupContainer() {
            return document.body;
          },
          onChange(_, opt) {
            if (opt?.label) formModel.townName = opt?.label;
            formModel.villageId = '';
          },
        };
      },
      colProps: {
        span: 3,
      },
      itemProps: {
        // class: 'city-code',
        wrapperCol: {
          span: 24,
        },
      },
      ifShow: () => orgType === 1,
    },
    {
      field: 'villageName',
      label: '所属社区/村名称',
      component: 'Input',
      show: false,
    },
    {
      field: 'villageId',
      label: ' ',
      // required: true,
      component: 'ApiSelect',
      disabledLabelWidth: true,
      componentProps: ({ formModel }) => {
        return {
          api: () => formModel.townId && getAdministrativeList(formModel.townId),
          labelField: 'name',
          valueField: 'code',
          showSearch: true,
          optionFilterProp: 'label',
          getPopupContainer() {
            return document.body;
          },
          placeholder: '请选择所属社区/村名称',
          onChange(_, opt) {
            if (opt?.label) formModel.villageName = opt?.label;
          },
        };
      },
      colProps: {
        span: 3,
      },
      itemProps: {
        wrapperCol: {
          span: 24,
        },
      },
      ifShow: () => orgType === 1,
    },
    {
      field: 'orgStats',
      component: 'ApiSelect',
      required: true,
      componentProps: ({ formModel }) => {
        return {
          api: () => getDictItemList(DictEnum.ORG_STATS),
          labelField: 'dictItemName',
          valueField: 'dictItemCode',
          getPopupContainer: () => document.body,
          onChange: (_, opt) => {
            formModel.orgStatsName = opt?.label;
          },
        };
      },
      label: '机构属性',
      colProps: { span: 12 },
      ifShow: () => orgType === 1,
    },

    {
      field: 'orgStatsName',
      component: 'Input',
      label: '机构属性',
      ifShow: () => orgType === 1,
      show: false,
    },

    // 机构负责人
    {
      field: 'orgManager',
      component: 'Input',
      label: '机构负责人',
      colProps: { span: 12 },
    },
    // 机构联系电话
    {
      field: 'orgTelephone',
      // required: true,
      component: 'Input',
      label: '机构联系电话',
      rules: [
        {
          // 支持座机的验证规则
          pattern:
            /^((0\d{2,3}-\d{7,8})|(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8})$/,
          message: '请输入正确的联系电话',
        },
      ],
      colProps: { span: 12 },
    },
    // 专病管理联系电话
    {
      field: 'specificDiseaseTelephone',
      // required: true,
      component: 'Input',
      label: '专病管理联系电话',
      rules: [
        {
          // 支持座机的验证规则
          pattern:
            /^((0\d{2,3}-\d{7,8})|(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8})$/,
          message: '请输入正确的联系电话',
        },
      ],
      colProps: { span: 12 },
    },
    {
      field: 'orgImg',
      component: 'UploadAsset',
      // required: true,
      componentProps: {
        uploadType: 'image',
        resultField: 'data.url',
        maxCount: 1,
        action: PRG_UPLOAD_SIGNATURE,
        accept: '.png, .jpg, .jpeg',
      },
      label: '机构院徽',
      colProps: { span: 12 },
      ifShow: () => orgType === 1,
    },
    //门诊时间
    {
      field: 'consultingHours',
      component: 'Input',
      label: '门诊时间',
      // required: true,
      colProps: { span: 12 },
      ifShow: () => orgType === 1,
      // componentProps: {
      //   showTime: false,
      //   valueFormat: 'YYYY-MM-DD',
      //   format: 'YYYY-MM-DD',
      //   getPopupContainer: () => document.body,
      // },
    },

    {
      field: 'enableFlag',
      component: 'Select',
      componentProps: {
        options: [
          { label: '启用', value: 0 },
          { label: '禁用', value: 1 },
        ],
      },
      defaultValue: 0,
      label: '机构状态',
      colProps: { span: 12 },
    },
    {
      field: 'isConsultation',
      component: 'Select',
      componentProps: {
        options: [
          { label: '是', value: 0 },
          { label: '否', value: 1 },
        ],
      },
      defaultValue: 0,
      label: '咨询问诊服务',
      colProps: { span: 12 },
      ifShow: () => orgType === 1,
    },
    {
      field: 'isMedicalService',
      component: 'Select',
      componentProps: {
        options: [
          { label: '是', value: 0 },
          { label: '否', value: 1 },
        ],
      },
      defaultValue: 0,
      label: '医服到家服务',
      ifShow: () => orgType === 1,
      colProps: { span: 12 },
    },
    {
      field: 'orgAddress',
      component: 'InputTextArea',
      label: '机构地址',
      required: true,
      componentProps: {
        rows: 4,
      },
      colProps: { span: 12 },
    },
    {
      field: 'trafficRoute',
      component: 'InputTextArea',
      label: '来院交通路线',
      componentProps: {
        rows: 4,
      },
      colProps: { span: 12 },
    },
    {
      field: 'orgIntroduce',
      component: 'InputTextArea',
      label: '机构简介',
      componentProps: {
        rows: 4,
      },
      colProps: { span: 12 },
      ifShow: () => orgType === 1,
    },
    {
      field: 'latitude',
      component: 'Input',
      label: '机构地址纬度',
      // required: true,
      colProps: { span: 12 },
    },
    {
      field: 'longitude',
      component: 'Input',
      label: '机构地址经度',
      // required: true,
      colProps: { span: 12 },
    },
  ];
};
