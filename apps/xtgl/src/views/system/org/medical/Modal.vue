<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import type { OrgEditModalDataType } from './data';
  import { createModalSchema } from './data';
  import { getOrganizationDetail, saveOrganization, updateOrganization } from '/@/api/org';

  const props = withDefaults(
    defineProps<{
      orgType?: number;
    }>(),
    {
      orgType: 1,
    },
  );

  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm({
    schemas: computed(() => createModalSchema(props.orgType)),
    labelWidth: 150,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });

  const mode = ref('');
  const getTitle = computed(() => {
    const perfix = mode.value === 'add' ? '新增' : '编辑';
    const suffix = props.orgType === 1 ? '医疗卫生机构' : '公共卫生机构';
    return perfix + suffix;
  });

  const { runAsync } = useRequest(getOrganizationDetail, { manual: true });

  const [register, { closeModal }] = useModalInner<OrgEditModalDataType>((data) => {
    mode.value = data?.mode;
    if (!data.record) return;
    runAsync(data.record.id).then((values) => {
      formAction.setFieldsValue(values);
    });
  });

  const { loading, runAsync: runAsyncSaveOrg } = useRequest(
    (params) => (mode.value === 'edit' ? updateOrganization(params) : saveOrganization(params)),
    { manual: true, showSuccessMessage: true },
  );

  async function handleOk() {
    formAction.validate().then((values) => {
      runAsyncSaveOrg({
        ...values,
        orgType: props.orgType,
      }).then(() => {
        emit('success');
        closeModal();
      });
    });
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="getTitle"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    width="768px"
    @register="register"
    @ok="handleOk"
  >
    <BasicForm style="transform: translateX(-36px)" @register="registerForm" />
  </BasicModal>
</template>
