<script setup lang="ts">
  import { computed, ref } from 'vue';
  import type { ActionItem, BasicColumn, TableRowSelection } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { useModal } from '@ft/internal/components/Modal';
  import { Popconfirm, Switch, Upload } from 'ant-design-vue';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import type { UploadRequestOption } from 'ant-design-vue/lib/vc-upload/interface';
  import { useRouter } from 'vue-router';
  import type { OrgEditModalDataType } from './data';
  import { columns, createFormSchemas } from './data';
  import Modal from './Modal.vue';
  import type { IOrganization } from '/@/api/org';
  import {
    batchDeleteOrganization,
    deleteOrganization,
    downloadOrganizationTemplate,
    enableOrganization,
    exportOrganization,
    importOrganization,
    queryOrganizationPage,
  } from '/@/api/org';
  import type { ListSwitchUnionType } from '/@/types';

  const props = withDefaults(
    defineProps<{
      orgType?: number;
    }>(),
    {
      orgType: 1,
    },
  );

  const [registerTable, tableIns] = useTable({
    api: queryOrganizationPage,
    columns: computed(() => columns(props.orgType)),
    formConfig: {
      colon: true,
      labelWidth: 100,
      showAdvancedButton: false,
      actionColOptions: {
        // span: 18,
        // @ts-ignore
        span: computed(() => (props.orgType === 2 ? 12 : 6)),
        style: {
          textAlign: 'right',
        },
      },
      schemas: createFormSchemas(props.orgType),
      fieldMapToTime: [['createTime', ['startTime', 'endTime'], 'YYYY-MM-DD']],
    },
    useSearchForm: true,
    showIndexColumn: false,
    bordered: false,
    size: 'small',
    resizeHeightOffset: 20,
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
    },
    beforeFetch: (params) => {
      params.orgType = props.orgType;
      return params;
    },
    afterFetch: (data) => {
      data.forEach((item) => {
        item.loading = false;
      });
      return data;
    },
  });
  const [registerModal, { openModal }] = useModal();

  const selectedRowKeys = ref<TableRowSelection['selectedRowKeys']>([]);

  const handleSelectionChange: TableRowSelection['onChange'] = (keys) => {
    selectedRowKeys.value = keys;
  };

  const router = useRouter();
  function handleAdd() {
    if (props.orgType === 2) {
      openModal<OrgEditModalDataType>(true, {
        mode: 'add',
      });
    } else {
      router.push({
        name: 'OrgAddOrg',
        query: {
          orgType: props.orgType,
          mode: 'add',
        },
      });
    }
  }

  function handleEdit(record: IOrganization, _column) {
    if (props.orgType === 2) {
      openModal<OrgEditModalDataType>(true, {
        mode: 'edit',
        record,
      });
    } else {
      router.push({
        name: 'OrgAddOrg',
        query: {
          orgType: props.orgType,
          id: record.id,
          mode: 'edit',
        },
      });
    }
  }

  const { runAsync: deleteOrganizationRunAsync } = useRequest(deleteOrganization, {
    manual: true,
    showSuccessMessage: true,
    onSuccess() {
      tableIns.reload();
    },
  });

  function handleDel(record: IOrganization, _column) {
    deleteOrganizationRunAsync(record.id);
  }

  const { loading: exportLoading, runAsync: runAsyncExport } = useRequest(exportOrganization, {
    manual: true,
  });
  async function batchExport() {
    exportUtil(runAsyncExport({ orgType: props.orgType, ...tableIns.getForm().getFieldsValue() }));
  }

  function createActions(record, column: BasicColumn): ActionItem[] {
    const actions: ActionItem[] = [];

    actions.push(
      {
        label: '编辑',
        type: 'link',
        onClick: handleEdit.bind(null, record, column),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          okButtonProps: { danger: true },
          confirm: handleDel.bind(null, record, column),
        },
      },
    );
    return actions;
  }

  const { runAsync: runAsyncEnableOrganization } = useRequest(enableOrganization, {
    manual: true,
  });
  function onSwitchChange(checked: string, record: ListSwitchUnionType<IOrganization>) {
    record.loading = true;
    runAsyncEnableOrganization({
      id: record.id,
      enableFlag: checked as unknown as number,
    })
      .then(() => {
        tableIns.reload();
      })
      .finally(() => {
        record.loading = false;
      });
  }

  const { runAsync } = useRequest(batchDeleteOrganization, {
    manual: true,
    showSuccessMessage: true,
  });

  function onBatchDel() {
    runAsync({ orgIdList: selectedRowKeys.value as string[] }).then(() => {
      selectedRowKeys.value = [];
      tableIns.reload();
    });
  }

  const { loading: orgTemplateLoading, runAsync: orgTemplate } = useRequest(
    downloadOrganizationTemplate,
    {
      manual: true,
    },
  );

  function onDownloadTemplate() {
    exportUtil(orgTemplate(props.orgType.toString()));
  }

  const { loading: importLoading, runAsync: importOrgRun } = useRequest(importOrganization, {
    manual: true,
    showSuccessMessage: true,
    onSuccess() {
      tableIns.reload();
    },
  });

  function importOrg(e: UploadRequestOption<any>) {
    const { file } = e;
    importOrgRun({
      // @ts-ignore
      file,
      orgType: props.orgType.toString(),
    });
  }

  const title = computed(() => {
    return props.orgType === 1 ? '医疗卫生' : '公共卫生';
  });
</script>

<template>
  <div class="w-full h-full pr-4">
    <BasicTable
      class="ft-main-table"
      :row-selection="{
        selectedRowKeys,
        onChange: handleSelectionChange,
      }"
      row-key="id"
      @register="registerTable"
    >
      <template #headerTop>
        <div class="text-16px font-500 title-label mb-4">{{ title }}服务机构列表</div>
      </template>
      <template #tableTitle>
        <div class="flex gap-2">
          <Button :loading="exportLoading" type="primary" @click="handleAdd"> 新增 </Button>
          <Popconfirm
            trigger="click"
            title="批量操作"
            @confirm="onBatchDel"
            :ok-button-props="{ danger: true }"
          >
            <Button :disabled="selectedRowKeys?.length === 0"> 批量删除 </Button>
          </Popconfirm>
        </div>
      </template>
      <template #toolbar>
        <Button :loading="orgTemplateLoading" @click="onDownloadTemplate"> 模板下载 </Button>

        <Upload :max-count="1" :show-upload-list="false" :custom-request="importOrg">
          <Button :loading="importLoading"> 批量导入 </Button>
        </Upload>
        <Button :loading="exportLoading" @click="batchExport"> 批量导出 </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'enableFlag'">
          <Switch
            :loading="record.loading"
            :checked-value="0"
            :un-checked-value="1"
            :checked="record.enableFlag"
            @change="onSwitchChange($event as unknown as any, record)"
          />
        </template>
        <template v-if="column.dataIndex === 'action'">
          <TableAction :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
    <Modal :orgType="orgType" @register="registerModal" @success="tableIns.reload" />
  </div>
</template>

<style lang="less" scoped>
  .ft-main-table .vben-basic-form--compact .ant-col .ant-form-item {
    margin-bottom: 6px !important;
  }
</style>
