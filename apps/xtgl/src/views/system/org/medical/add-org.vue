<script setup lang="ts">
  import { computed } from 'vue';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { useRequest } from '@ft/request';
  import { useRouter } from 'vue-router';
  import { createModalSchema } from './data';
  import { Icon } from '@ft/internal/components/Icon';
  import { Button } from 'ant-design-vue';
  import { useRouteQuery } from '@ft/internal/hooks/web/useRouteQuery';
  import { getOrganizationDetail, saveOrganization, updateOrganization } from '/@/api/org';

  const router = useRouter();
  function goPath(values: any) {
    router.push({
      name: values,
    });
  }
  const emit = defineEmits(['register', 'success']);
  const id = useRouteQuery('id', '', { transform: String });
  const mode = useRouteQuery('mode', '', { transform: String });
  // eslint-disable-next-line vue/no-dupe-keys
  const orgType = useRouteQuery('orgType', '', { transform: Number });

  const [registerForm, formAction] = useForm({
    schemas: computed(() => createModalSchema(orgType.value)),
    labelWidth: 150,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });

  const getTitle = computed(() => {
    const perfix = mode.value === 'add' ? '新增医疗卫生机构' : '编辑医疗卫生机构';
    return perfix;
  });

  const { loading: formLoading } = useRequest(() => getOrganizationDetail(id.value), {
    // manual: true,
    ready: mode.value === 'edit',
    onSuccess: (res) => {
      formAction.setFieldsValue(res);
    },
  });

  const { loading, runAsync: runAsyncSaveOrg } = useRequest(
    (params) => (mode.value === 'edit' ? updateOrganization(params) : saveOrganization(params)),
    { manual: true, showSuccessMessage: true },
  );

  async function handleSave() {
    formAction.validate().then((values) => {
      runAsyncSaveOrg({
        ...values,
        orgType: orgType.value,
      }).then(() => {
        emit('success');
        goPath('OrgMedical');
      });
    });
  }
</script>

<template>
  <div class="px-4 h-full flex flex-col">
    <div v-if="false" class="bread-crumbs py-3">
      <span class="cursor-pointer hover:text-primary" @click="goPath('OrgMedical')">
        <Icon icon="ic:twotone-chevron-left" :size="16" />
        <span class="text-base"> {{ getTitle }}</span>
      </span>
    </div>
    <div class="pt-6 of-auto min-h-0 flex-1 basis-0 bg-#fff rounded-lg">
      <BasicForm
        style="transform: translateX(-36px)"
        :loading="formLoading"
        @register="registerForm"
      />
    </div>
    <div
      class="fixed-btn-group fixed bottom-0 h-80px right-16px flex justify-end gap-4 items-center pr-4"
    >
      <Button @click="goPath('OrgMedical')"> 取消 </Button>
      <Button type="primary" :loading="loading" @click="handleSave"> 保存 </Button>
    </div>
  </div>
</template>

<style lang="less" scoped>
  :deep {
    .ant-form-item-label > label[for='form_item_cityId'],
    .ant-form-item-label > label[for='form_item_divisionId'],
    .ant-form-item-label > label[for='form_item_villageId'],
    .ant-form-item-label > label[for='form_item_townId'] {
      width: 0 !important;
      margin-left: 8px;
    }
  }
</style>
