import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
/**
 * 登录结果
 * 登录时间
 * 用户名
 * 客户端ip
 */
export const columns: BasicColumn[] = [
  {
    title: '登录结果',
    dataIndex: 'loginResult',
    width: 60,
    align: 'left',
  },
  {
    title: '登录时间',
    dataIndex: 'loginTime',
    width: 180,
    align: 'left',
  },
  {
    title: '用户名',
    dataIndex: 'username',
    width: 130,
    align: 'left',
  },
  //用户姓名
  {
    title: '用户姓名',
    dataIndex: 'employeeName',
    width: 130,
    align: 'left',
  },
  {
    title: '客户端ip',
    dataIndex: 'remoteIp',
    width: 160,
    align: 'left',
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'loginTime',
    component: 'RangePicker',
    label: '登陆时间',
    colProps: { span: 6 },
  },
  {
    field: 'username',
    component: 'Input',
    label: '用户名',
    colProps: { span: 6 },
  },
];
