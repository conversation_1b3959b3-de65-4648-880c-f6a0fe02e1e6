<script setup lang="ts">
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { useRequest } from '@ft/request';
  import { columns, formSchema } from './data';
  import type { ILoginLogPage } from '/@/api/login-log';
  import { getLoginLogPage, verifySignData } from '/@/api/login-log';

  const [registerTable] = useTable({
    api: getLoginLogPage,
    fetchSetting: {
      sizeField: 'pageSize',
      pageField: 'pageNum',
      listField: 'list',
    },
    inset: true,
    columns,
    formConfig: {
      colon: true,
      labelWidth: 90,
      showAdvancedButton: false,
      actionColOptions: {
        span: 12,
        style: {
          textAlign: 'right',
          paddingRight: '16px',
        },
      },
      schemas: formSchema,
      fieldMapToTime: [['loginTime', ['loginStartTime', 'loginEndTime'], 'YYYY-MM-DD']],
    },
    useSearchForm: true,
    showIndexColumn: true,
    bordered: false,
    size: 'small',
    actionColumn: {
      width: 80,
      title: '操作',
      dataIndex: 'action',
    },
  });

  const { run: handleVerify } = useRequest(verifySignData, {
    manual: true,
    showSuccessMessage: () => '验证通过',
  });

  function createActions(record: ILoginLogPage): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '验证完整性',
        type: 'link',
        onClick: handleVerify.bind(null, { id: record.id }),
      },
    ];

    return actions;
  }
</script>

<template>
  <div class="w-full h-full pr-2">
    <BasicTable class="ft-main-table" @register="registerTable">
      <template #headerTop>
        <div class="text-16px font-500 title-label mb-4">登陆日志列表</div>
      </template>
      <template #tableTitle> </template>
      <template #toolbar> </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>

<style lang="less" scoped></style>
