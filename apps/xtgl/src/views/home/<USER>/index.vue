<script lang="ts" setup>
  import { Dropdown, Menu } from 'ant-design-vue';
  import type { MenuInfo } from 'ant-design-vue/lib/menu/src/interface';
  import { computed } from 'vue';

  import { useUserStore } from '@ft/internal/store/modules/user';
  import { useDesign } from '@ft/internal/hooks/web/useDesign';

  import { Icon } from '@ft/internal/components/Icon';
  import { useGo } from '@ft/internal/hooks/web/usePage';
  import defaultAvatar from '/src/assets/images/default-avatar.png';
  import { useModal } from '@ft/internal/components/Modal';
  import ModifyPwd from './ModifyPwd.vue';
  import ModifyInfo from './ModifyInfo.vue';
  import { useMessage } from '/@@/hooks/web/useMessage';

  type MenuEvent = 'logout' | 'modifyPwd' | 'modifyInfo' | 'system';

  defineProps<{ hasSys: boolean }>();

  const { createConfirm } = useMessage();
  const { prefixCls } = useDesign('header-user-dropdown');
  const [register, { openModal }] = useModal();
  const [registerInfo, { openModal: openInfoModal }] = useModal();
  const go = useGo();
  const userStore = useUserStore();
  const getUserInfo = computed(() => {
    const { employeeName, userImg } = userStore.getUserInfo || {};
    return { nickName: employeeName, avatar: userImg || defaultAvatar };
  });

  //  login out
  function handleLoginOut() {
    createConfirm({
      content: '确定要退出登录吗？',
      iconType: 'warning',
      onOk: () => {
        userStore.logout(true);
      },
    });
  }

  async function openSystem() {
    go('/menu');
  }

  function handleMenuClick(e: MenuInfo) {
    switch (e.key as MenuEvent) {
      case 'logout':
        handleLoginOut();
        break;
      case 'modifyPwd':
        openModal();
        break;
      case 'modifyInfo':
        openInfoModal(true, {});
        break;
      case 'system':
        openSystem();
        break;
    }
  }
</script>

<template>
  <Dropdown placement="bottomRight" :overlay-class-name="`${prefixCls}-dropdown-overlay`">
    <span :class="[prefixCls, `${prefixCls}--light`]" class="flex">
      <img :class="`${prefixCls}__header`" :src="getUserInfo.avatar" />
      <span :class="`${prefixCls}__info flex items-center text-white`">
        <span :class="`${prefixCls}__name  `" class="truncate">
          {{ getUserInfo.nickName || '用户' }}
        </span>
        <svg
          class="ml-1"
          xmlns="http://www.w3.org/2000/svg"
          width="12"
          height="12"
          viewBox="0 0 1024 1024"
        >
          <!-- Icon from Ant Design Icons by HeskeyBaozi - https://github.com/ant-design/ant-design-icons/blob/master/LICENSE -->
          <path
            fill="currentColor"
            d="M840.4 300H183.6c-19.7 0-30.7 20.8-18.5 35l328.4 380.8c9.4 10.9 27.5 10.9 37 0L858.9 335c12.2-14.2 1.2-35-18.5-35"
          />
        </svg>
      </span>
    </span>

    <template #overlay>
      <Menu @click="handleMenuClick">
        <Menu.Item v-if="hasSys" key="system">
          <span class="flex items-center">
            <Icon icon="carbon:cloud-service-management" class="mr-2" />
            <span>系统管理</span>
          </span>
        </Menu.Item>
        <Menu.Item key="modifyInfo">
          <span class="flex items-center">
            <Icon icon="ant-design:edit-outlined" class="mr-2" />
            <span>修改资料</span>
          </span>
        </Menu.Item>
        <Menu.Item key="modifyPwd">
          <span class="flex items-center">
            <Icon icon="ant-design:edit-outlined" class="mr-2" />
            <span>修改密码</span>
          </span>
        </Menu.Item>
        <Menu.Item key="logout">
          <span class="flex items-center text-red">
            <Icon icon="power-outline|svg" class="mr-2" />
            <span>退出登录</span>
          </span>
        </Menu.Item>
      </Menu>
    </template>
  </Dropdown>
  <ModifyPwd @register="register" />
  <ModifyInfo @register="registerInfo" />
</template>

<style lang="less">
  @prefix-cls: ~'@{namespace}-header-user-dropdown';

  .@{prefix-cls} {
    height: @header-height;
    overflow: hidden;
    font-size: 12px;
    cursor: pointer;
    align-items: center;

    img {
      width: 32px;
      height: 32px;
      margin-right: 12px;
    }

    &__header {
      border-radius: 50%;
    }

    &__name {
      font-size: 14px;
      color: #fff;
    }

    &-dropdown-overlay {
      min-width: 100px !important;
    }
  }
</style>
