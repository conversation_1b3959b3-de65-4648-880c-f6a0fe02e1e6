import type { FormSchema } from '@ft/internal/components/Table';
import { type IUser } from '/@/api/user';
import type { EditModalDataType } from '/@/types';
import { DictEnum, getDictItemList } from '@ft/internal/api';
export type UserEditModalDataType = EditModalDataType<IUser>;

export const ModifyInfoModalSchemas: FormSchema[] = [
  {
    field: 'id',
    label: '用户ID',
    component: 'Input',
    show: false,
  },
  {
    field: 'roleIdList',
    label: '用户角色',
    component: 'Input',
    show: false,
  },
  {
    field: 'username',
    label: '账号',
    component: 'Input',
    required: true,
    componentProps: {
      disabled: true,
    },
    colProps: { span: 12 },
  },
  {
    field: 'employeeNo',
    label: '工号',
    component: 'Input',
    colProps: { span: 12 },
  },
  {
    field: 'employeeName',
    label: '用户姓名',
    component: 'Input',
    required: true,
    colProps: { span: 12 },
  },
  {
    field: 'jobTitleCode',
    label: '用户职称',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: () => getDictItemList(DictEnum.JOB_TITLE),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        onChange: (_, opt) => {
          formModel.jobTitleName = opt?.label;
        },
        getPopupContainer: () => document.body,
      };
    },
    colProps: { span: 12 },
  },
  {
    field: 'jobTitleName',
    label: '用户职称',
    component: 'Input',
    show: false,
  },
  {
    field: 'orgId',
    label: '用户机构',
    component: 'Input',
    show: false,
  },
  {
    field: 'orgName',
    label: '用户机构',
    component: 'Input',
    show: false,
  },
  // phone
  {
    field: 'phone',
    label: '联系电话',
    component: 'Input',
    required: true,
    rules: [
      {
        pattern: /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
        message: '请输入正确的手机号码',
      },
    ],
    colProps: { span: 12 },
  },
  // idCard
  {
    field: 'idCard',
    label: '身份证号',
    component: 'Input',
    rules: [
      {
        pattern:
          /^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
        message: '请输入正确的身份证号',
      },
    ],
    colProps: { span: 12 },
  },
  {
    field: 'userSignImg',
    label: 'CA签名',
    component: 'Input',
    slot: 'userSignImg',
    colProps: { span: 12 },
  },
];
