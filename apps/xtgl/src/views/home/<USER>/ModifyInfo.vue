<script setup lang="ts">
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { Button, message } from 'ant-design-vue';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { defHttp, useRequest } from '@ft/request';
  import { USER_SIGN_IMG_UPLOAD_URL, updatePersonalInfo } from '/@/api/user';
  import { useUserStore } from '@ft/internal/store/modules/user';
  import { useEventListener } from '@vueuse/core';
  import { ref } from 'vue';
  import { ModifyInfoModalSchemas } from './ModifyInfo.data';
  import { getRandom } from '/@/api/cert';

  const emit = defineEmits(['register', 'success']);
  const userStore = useUserStore();
  const random = ref('');
  const [registerForm, formAction] = useForm({
    schemas: ModifyInfoModalSchemas,
    labelWidth: 150,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });

  const getTitle = '修改资料';

  const [register, { closeModal }] = useModalInner(() => {
    console.log(userStore.getUserInfo);
    formAction.setFieldsValue(userStore.getUserInfo);
  });

  const { loading, runAsync: userSaveRunAsync } = useRequest(updatePersonalInfo, {
    manual: true,
    showSuccessMessage: true,
  });

  async function handleOk() {
    formAction.validate().then((values) => {
      userSaveRunAsync(values).then(() => {
        userStore.getUserInfoAction().then(() => {
          emit('success');
          closeModal();
        });
      });
    });
  }

  function base64ToFile(base64String: string, filename, mimeType = 'image/png') {
    // 解码 Base64 数据
    const byteCharacters = atob(base64String);
    const byteNumbers = new Uint8Array(byteCharacters.length);

    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }

    // 创建 Blob 对象
    const blob = new Blob([byteNumbers], { type: mimeType });

    // 创建 File 对象
    const file = new File([blob], filename, { type: mimeType });

    return file;
  }

  async function onUploadCA() {
    if (!random.value) random.value = await getRandom();
    const width = 400;
    const height = 400;
    const screenLeft =
      window.screenLeft !== undefined ? window.screenLeft : (window.screen as any).left;
    const screenTop =
      window.screenTop !== undefined ? window.screenTop : (window.screen as any).top;
    const screenWidth = window.innerWidth
      ? window.innerWidth
      : document.documentElement.clientWidth;
    const screenHeight = window.innerHeight
      ? window.innerHeight
      : document.documentElement.clientHeight;

    // 计算居中位置的左、上坐标
    const left = screenLeft + (screenWidth - width) / 2;
    const top = screenTop + (screenHeight - height) / 2;
    const features = `width=${width},height=${height},top=${top},left=${left},scrollbars=no,menubar=no,toolbar=no,location=no,status=no,fullscreen=no`;
    const publicPath = import.meta.env.VITE_PUBLIC_PATH || '/';
    const pathPrefix = publicPath.endsWith('/') ? publicPath : publicPath + '/';
    window.open(
      `${pathPrefix}ukey/index.html#${random.value}@${userStore.getUserInfo.idCard}`,
      'ukey',
      features,
    );
  }

  async function signatureListener(event) {
    const eventData = event.data;
    if (eventData.type === 'ukey') {
      loading.value = true;
      try {
        const data = eventData.data;
        if (!data.base64Cert) {
          message.warning('获取证书信息失败');
          return;
        }
        if (data.random === random.value) {
          const file = base64ToFile(data.picture, 'img.png');
          console.log(data, file, 'file');
          const res = await defHttp.uploadFile(
            {
              url: USER_SIGN_IMG_UPLOAD_URL,
            },
            {
              file,
              filename: file.name,
            },
          );
          const url = res.data?.data?.url;
          if (!url) {
            message.error('上传签名失败');
          } else {
            formAction.setFieldsValue({
              userSignImg: url,
            });
          }
        } else {
          message.warning('会话超时，请重新提交');
        }
      } catch (error: any) {
        console.log(error);
        message.warning(error.message);
      } finally {
        loading.value = false;
      }
    }
  }
  useEventListener('message', signatureListener);
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="getTitle"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    :maskClosable="false"
    width="768px"
    @register="register"
    @ok="handleOk"
  >
    <BasicForm style="transform: translateX(-36px)" @register="registerForm">
      <template #userSignImg="{ values, field }">
        <template v-if="values[field]">
          <div class="flex gap-4">
            <img :src="values[field]" class="max-w-[80px] max-h-[80px]" />
            <Button type="primary" @click="onUploadCA">重新导入签名</Button>
          </div>
        </template>
        <Button type="primary" v-else @click="onUploadCA">导入签名</Button>
      </template>
    </BasicForm>
  </BasicModal>
</template>
