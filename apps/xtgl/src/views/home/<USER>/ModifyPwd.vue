<script setup lang="ts">
  import { reactive, ref } from 'vue';
  import { Form, FormItem, InputPassword } from 'ant-design-vue';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useUserStore } from '@ft/internal/store/modules/user';
  import { modifyPassword } from '/@/api/user';
  import { encrypt } from '@ft/internal/utils/rsa';

  const title = ref<string>('修改密码');

  const formState = reactive({
    password: '',
    newPassword: '',
    verifyPassword: '',
  });
  const [register, { closeModal }] = useModalInner();
  const formRef = ref();
  const userStore = useUserStore();

  const { createMessage } = useMessage();

  function handlePutPassword() {
    formRef.value
      .validate()
      .then(() => {
        modifyPassword({
          username: userStore?.getUserInfo?.username,
          password: encrypt(formState.password),
          newPassword: encrypt(formState.newPassword),
          verifyPassword: encrypt(formState.verifyPassword),
        }).then(() => {
          createMessage.success('修改成功');
          closeModal();
          userStore.logout(true);
        });
      })
      .catch((error) => {
        console.log('error', error);
      });
  }

  async function validatePass(_, value) {
    if (value !== formState.newPassword) return Promise.reject(new Error('两次输入密码不一致'));
    else return Promise.resolve();
  }
</script>

<template>
  <BasicModal
    width="30%"
    :title="title"
    :keyboard="false"
    :mask-closable="false"
    v-bind="$attrs"
    :can-fullscreen="false"
    show-cancel-btn
    closable
    :draggable="true"
    :min-height="90"
    @register="register"
    @ok="handlePutPassword"
  >
    <div>
      <Form
        ref="formRef"
        :model="formState"
        name="basic"
        :body-style="{ height: '130px' }"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="on"
      >
        <FormItem
          class="mt-4"
          label="旧密码"
          name="password"
          :rules="[{ required: true, message: '请输入!' }]"
        >
          <InputPassword v-model:value="formState.password" />
        </FormItem>

        <FormItem
          label="新密码"
          name="newPassword"
          :rules="[
            { required: true, message: '确认密码不能为空!' },
            { min: 8, max: 20, message: '用户密码长度必须介于 8 和 20 之间', trigger: 'blur' },
            {
              required: true,
              pattern:
                /^(?:(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])|(?=.*[A-Z])(?=.*[a-z])(?=.*[\W_])|(?=.*[A-Z])(?=.*[0-9])(?=.*[\W_])|(?=.*[a-z])(?=.*[0-9])(?=.*[\W_])).+$/,
              message: '用户密码必须包含大小写字母、数字和特殊符号至少3种',
            },
          ]"
        >
          <InputPassword v-model:value="formState.newPassword" />
        </FormItem>
        <FormItem
          label="确认密码"
          name="verifyPassword"
          :rules="[
            { required: true, message: '确认密码不能为空!' },
            { min: 8, max: 20, message: '用户密码长度必须介于 8 和 20 之间', trigger: 'blur' },
            {
              required: true,
              pattern:
                /^(?:(?=.*[A-Z])(?=.*[a-z])(?=.*[0-9])|(?=.*[A-Z])(?=.*[a-z])(?=.*[\W_])|(?=.*[A-Z])(?=.*[0-9])(?=.*[\W_])|(?=.*[a-z])(?=.*[0-9])(?=.*[\W_])).+$/,
              message: '用户密码必须包含大小写字母、数字和特殊符号至少3种',
            },
            { validator: validatePass, trigger: 'blur', required: true },
          ]"
        >
          <InputPassword v-model:value="formState.verifyPassword" />
        </FormItem>
      </Form>
    </div>
  </BasicModal>
</template>

<style lang="less" scoped></style>
