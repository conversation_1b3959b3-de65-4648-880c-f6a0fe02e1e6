<template>
  <div :class="prefixCls" class="relative w-full h-full flex items-center justify-center">
    <div class="right-4 top-4 absolute flex justify-between items-center">
      <div class="mr-20px mt-4px">
        <Badge
          :number-style="{
            height: '14px',
            lineHeight: '14px',
            textAlign: 'center',
            padding: '0 3px',
          }"
        >
          <Icon icon="ant-design:bell-outlined" :size="20" color="#fff" />
        </Badge>
      </div>
      <UserDropDown :hasSys="hasSys" />
    </div>

    <div class="absolute flex items-center top-8 left-8 scale-50 scale-80 xxl:scale-95">
      <img class="w-64px h-64px" :src="logo" />
      <div class="ml-12px color-#fff text-40px leading-48px font-bold">
        {{ title }}
      </div>
    </div>
    <div class="flex flex-col justify-center lg:px-10 scale-80 2xl:scale-95">
      <div
        v-for="(group, groupIndex) in groupedAppList"
        :key="groupIndex"
        class="grid grid-cols-12 gap-30px place-items-center"
        :class="{ 'mt-7.5': groupIndex > 0 }"
      >
        <div
          v-for="(item, itemIndex) in group"
          :key="item.name"
          class="applicationBox py-12 px-31 enter-y transition flex flex-1 flex-col items-center cursor-pointer hover:scale-105"
          :class="{
            [`applicationBox-${item.icon.split('|')[0]}`]: true,
            'col-span-6 col-start-4': group.length === 1 || groupIndex === 1,
            '!col-span-4 !col-start-3':
              (groupIndex === 0 && itemIndex === 0 && group.length != 1) ||
              (groupIndex === 1 && itemIndex === 0 && group.length === 2),
            '!col-span-4 !col-start-7':
              (groupIndex === 0 && itemIndex === 1) ||
              (groupIndex === 1 && itemIndex === 1 && group.length === 2),
            '!col-span-4 ': group.length === 3,
          }"
          @click="goPath(item)"
        >
          <Icon :icon="item.icon" size="150" />
          <div
            class="text-24px font-medium text-center leading-8 mt-3.5 text-white truncate max-w-full"
          >
            {{ item.name }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { Icon } from '@ft/internal/components/Icon';

  import { Badge, message } from 'ant-design-vue';
  import { useDesign } from '@ft/internal/hooks/web/useDesign';
  import logo from '/@@/assets/images/ylyw-logo.svg';
  import { getToken } from '@ft/internal/utils/auth';
  import { useRequest } from '@ft/request';
  import { computed, ref } from 'vue';
  import type { AuthorizeMenuListItem } from '@ft/internal/router/helper/buildRoute';
  import { getApp } from '@ft/internal/api';
  import UserDropDown from './user-dropdown/index.vue';

  interface AppListItem {
    id: string;
    icon: string;
    name: string;
    path: string;
  }

  const title = '宜昌市区域传染病防控和救治信息化平台';
  const { prefixCls } = useDesign('home');
  const hasSys = ref(false);
  const { data } = useRequest<AuthorizeMenuListItem[], any>(getApp, {
    onSuccess: (data) => {
      if (data && data.length > 0) {
        hasSys.value = data.some((v) => v.menuRoutePath === '/');
      }
    },
  });

  const map = {
    '/jcyj/': {
      icon: 'monitoringWarning|svg',
      path: import.meta.env.DEV ? getLocalAddress('http://localhost:5710') : `/crb-jcyj`,
    },
    '/ylyw/': {
      icon: 'medicalBusiness|svg',
      path: import.meta.env.DEV ? getLocalAddress('http://localhost:5511') : `/crb-ylyw/`,
    },
    '/sjyy/': {
      icon: 'dataApplication|svg',
      path: import.meta.env.DEV ? getLocalAddress('http://localhost:5120') : `/crb-sjyy/`,
    },
    '/jzgf/': {
      icon: 'treatmentStandards|svg',
      path: import.meta.env.DEV ? getLocalAddress('http://localhost:5130') : `/crb-jzgf/`,
    },
    '/fwpt/': {
      icon: 'smartHealthcare|svg',
      path: import.meta.env.DEV ? getLocalAddress('http://localhost:5140') : `/crb-fwpt/`,
    },
  };

  const appList = computed<AppListItem[]>(() => {
    const list = (data.value || []).filter((item) => item.menuRoutePath !== '/');
    return list.map((item) => {
      const temp = map[item.menuRoutePath];
      return {
        id: item?.id || '',
        icon: temp?.icon || '',
        name: item?.menuName || '',
        path: temp?.path || '',
      };
    });
  });

  const groupedAppList = computed<AppListItem[][]>(() => {
    const list = appList.value;
    if (!list || list.length === 0) return [];
    const groups: AppListItem[][] = [];
    groups.push(list.slice(0, 2));
    if (list.length > 2) {
      groups.push(list.slice(2, 5));
    }
    return groups.filter((group) => group.length > 0);
  });

  function goPath(item: AppListItem) {
    if (item.path) {
      window.open(
        `${item.path}?id=${item.id}` + (import.meta.env.DEV ? '&token=' + getToken() : ''),
      );
    } else {
      message.info('暂未开放');
    }
  }

  function getLocalAddress(address: string) {
    const url = new URL(address);
    return `${url.protocol}//${window.location.hostname}:${url.port}${url.pathname}`;
  }
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-home';
  .@{prefix-cls} {
    background: url('/@/assets/images/home-bg.png') 0% 0%/100% 34% no-repeat,
      url('/@/assets/images/home-left-decorate.png') left bottom 40px / auto no-repeat,
      url('/@/assets/images/home-right-decorate.png') right bottom 40px / auto no-repeat, #f4f4f4 !important;
  }

  .applicationBox {
    cursor: pointer;
    flex-direction: column;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 1;
    box-sizing: border-box;
    aspect-ratio: 1.37 / 1;
    border: 4px solid rgb(255 255 255);
    border-radius: 16px;
    box-shadow: 8px 8px 20px 0 rgb(33 8 22 / 16%);

    &:hover {
      transform: translateY(-10px);
    }
  }

  .applicationBox-monitoringWarning {
    background: linear-gradient(180deg, rgb(155 213 255), rgb(104 149 255) 100%);
  }

  .applicationBox-medicalBusiness {
    background: linear-gradient(180deg, rgb(207 201 255), rgb(136 122 238) 100%);
  }

  .applicationBox-dataApplication {
    background: linear-gradient(180deg, rgb(255 222 146) 2.29%, rgb(252 169 74) 100%);
  }

  .applicationBox-treatmentStandards {
    background: linear-gradient(180deg, rgb(255 213 226) 0.763%, rgb(255 126 166) 100%);
  }

  .applicationBox-smartHealthcare {
    background: linear-gradient(180deg, rgb(165 245 226), rgb(57 196 162) 100%);
  }
</style>
