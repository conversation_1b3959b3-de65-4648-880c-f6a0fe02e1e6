<template>
  <div class="text-24px leading-32px color-#333333 text-left mt-4 mb-12">
    <span
      class="relative after:content-[''] -after:bottom-5px after:left-50% -after:ml-10px after:absolute after:w-20px after:h-2px after:bg-#4D55E7"
    >
      账号登录
    </span>
  </div>

  <Form
    class="p-4 enter-x"
    :model="formData"
    :rules="{
      username: [{ required: true, message: '请输入账号' }],
      password: [{ required: true, message: '请输入密码' }],
    }"
    ref="formRef"
    @keypress.enter="handleLogin"
  >
    <FormItem name="username">
      <Input size="large" v-model:value="formData.username" placeholder="请输入账号" />
    </FormItem>
    <FormItem name="password">
      <InputPassword
        size="large"
        visibilityToggle
        v-model:value="formData.password"
        placeholder="请输入密码"
      />
    </FormItem>
    <FormItem v-if="smsCheck" name="telephone" class="enter-x">
      <CountdownInput
        v-model:value="formData.telephone"
        size="large"
        placeholder="请获取手机号"
        disabled
        class="countdown-input"
        btn-text="获取手机号"
        :count="3"
        :send-code-api="onGetTelephone"
      />
    </FormItem>
    <FormItem v-if="smsCheck" name="code" class="enter-x">
      <CountdownInput
        v-model:value="formData.verifyCode"
        size="large"
        class="countdown-input"
        :count="300"
        placeholder="请输入验证码"
        :send-code-api="onGetSmsCode"
      />
    </FormItem>
    <div class="flex justify-between">
      <Checkbox v-model:checked="rememberMe" size="small"> 记住密码 </Checkbox>
      <div v-if="!noAdminBtn" class="cursor-pointer" @click="handelAdminLogin">管理员登录</div>
    </div>
    <FormItem>
      <Button class="my-4" size="large" block @click="handleLogin" :loading="loading">
        登录
      </Button>
    </FormItem>
  </Form>
</template>
<script lang="ts" setup>
  import { useEventListener, useToggle } from '@vueuse/core';
  import { computed, reactive, ref } from 'vue';
  import type { FormInstance } from 'ant-design-vue';
  import { Button, Checkbox, Form, Input, message } from 'ant-design-vue';
  import { createLocalStorage } from '@ft/internal/utils/persistent';
  import { useRequest } from '@ft/request';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import { useUserStore } from '@ft/internal/store/modules/user';
  import { encrypt } from '@ft/internal/utils/rsa';
  import { getRandom } from '/@/api/cert';
  import { useGlobSetting } from '@ft/internal/hooks/setting';
  import { CountdownInput } from '@ft/internal/components/CountDown';
  import { isEmpty } from 'lodash-es';
  import { getSmsCode, getTelephone } from '/@/api/user';

  const { smsCheck: sourceSmsCheck } = useGlobSetting();
  const smsCheck = computed(() => sourceSmsCheck === 'true');

  const userStore = useUserStore();
  const ACCOUNT = 'ACCOUNT';
  const PASSWORD = 'PASSWORD';
  const rememberMe = ref(true);
  const ls = createLocalStorage({ encrypt: true });
  const formData = reactive({
    username: ls.get(ACCOUNT) || '',
    password: ls.get(PASSWORD) || '',
    telephone: '',
    verifyCode: '',
  });
  const { createErrorModal, createMessage } = useMessage();

  const formRef = ref<FormInstance>();
  const loading = ref(false);

  const FormItem = Form.Item;
  const InputPassword = Input.Password;
  const [isClickedGetSMS, toggleIsClickedGetSMS] = useToggle(false);
  const { run: handleLogin } = useRequest(
    () =>
      userStore.login({
        username: formData.username,
        password: encrypt(formData.password),
        telephone: formData.telephone,
        verifyCode: formData.verifyCode,
      }),
    {
      manual: true,
      showSuccessMessage: false,
      async onBefore() {
        try {
          if (!isClickedGetSMS.value && smsCheck.value) {
            createMessage.warning('请先获取验证码');
            return false;
          }
          await formRef.value?.validate();
          return true;
        } catch (e) {
          return false;
        }
      },
      onSuccess() {
        if (rememberMe.value) {
          ls.set(ACCOUNT, formData.username);
          ls.set(PASSWORD, formData.password);
        } else {
          ls.remove(ACCOUNT);
          ls.remove(PASSWORD);
        }
      },
      onError(e) {
        createErrorModal({
          title: '错误提示',
          content: e.message,
        });
      },
    },
  );
  const random = ref('');
  const noAdminBtn = ref(window['noAdminBtn']);
  async function adminLoginPageListener(event) {
    const eventData = event.data;
    if (eventData.type === 'ukey') {
      loading.value = true;
      try {
        const data = eventData.data;
        if (!data.base64Cert) {
          message.warning('获取证书信息失败');
          return;
        }
        if (data.random === random.value) {
          await userStore.loginByKey(data);
        } else {
          message.warning('会话超时，请重新点击管理员登录');
        }
      } catch (error: any) {
        createErrorModal({
          title: '错误提示',
          content: error.message,
        });
      } finally {
        loading.value = false;
      }
    }
  }
  async function handelAdminLogin() {
    if (!random.value) random.value = await getRandom();

    const width = 400;
    const height = 400;
    const screenLeft =
      window.screenLeft !== undefined ? window.screenLeft : (window.screen as any).left;
    const screenTop =
      window.screenTop !== undefined ? window.screenTop : (window.screen as any).top;
    const screenWidth = window.innerWidth
      ? window.innerWidth
      : document.documentElement.clientWidth;
    const screenHeight = window.innerHeight
      ? window.innerHeight
      : document.documentElement.clientHeight;

    // 计算居中位置的左、上坐标
    const left = screenLeft + (screenWidth - width) / 2;
    const top = screenTop + (screenHeight - height) / 2;
    const publicPath = import.meta.env.VITE_PUBLIC_PATH || '/';
    const pathPrefix = publicPath.endsWith('/') ? publicPath : publicPath + '/';
    // 设置弹窗窗口的特性
    const features = `width=${width},height=${height},top=${top},left=${left},scrollbars=no,menubar=no,toolbar=no,location=no,status=no,fullscreen=no`;
    window.open(`${pathPrefix}ukey/index.html#${random.value}@login`, 'ukey', features);
  }
  useEventListener('message', adminLoginPageListener);

  async function setTelephone(username: string) {
    if (isEmpty(username)) return;
    formData.telephone = await getTelephone(username);
  }
  async function onGetTelephone() {
    if (isEmpty(formData.username)) {
      createMessage.warning('请输入用户名');
      return false;
    }
    await setTelephone(formData.username);
    return true;
  }

  async function onGetSmsCode() {
    if (isEmpty(formData.telephone)) {
      createMessage.warning('请获取手机号');
      return false;
    }
    toggleIsClickedGetSMS(true);
    await getSmsCode(formData.telephone);
    return true;
  }
</script>
