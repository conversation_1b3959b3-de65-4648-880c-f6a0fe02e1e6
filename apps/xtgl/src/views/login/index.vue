<template>
  <div :class="prefixCls" class="relative w-full h-full flex items-center justify-end">
    <div class="absolute flex items-center top-8 left-10">
      <img class="w-64px h-64px" :src="logo" />
      <div class="ml-12px color-#282B32 text-40px leading-48px font-bold">
        {{ title }}
      </div>
    </div>
    <img class="w-234px absolute flex items-center top-8 right-10" :src="lmLogin" />
    <div
      class="bg-#FFFFFF rounded-4 w-400px px-10 pt-8 shadow-[0_0_30px_0_rgba(171,180,194,0.25)] relative z-2 mr-306px"
    >
      <img :src="loginFormBg" class="absolute w-89px h-106px top-26px right-23px" />
      <LoginForm />
      <div class="text-10px text-center mb-24px">
        Copyright © 2023 FUSIONTECH.All rights reserved.
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { useDesign } from '@ft/internal/hooks/web/useDesign';
  import logo from '/@@/assets/images/ylyw-logo.svg';
  import lmLogin from '/@/assets/images/lmLogin.png';

  import LoginForm from './LoginForm.vue';
  import loginFormBg from '/@/assets/images/login-form-bg.png';
  const title = '宜昌市区域传染病防控和救治信息化平台';
  const { prefixCls } = useDesign('login');
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-login';
  .@{prefix-cls} {
    background: #fff;
    background-image: url('/@/assets/images/login-bg.png');
    background-size: 100% 100%;
    background-position: 0% 0%;
    background-repeat: no-repeat;

    .ant-btn-loading-icon {
      .anticon-loading {
        color: #333;
      }
    }

    .anticon {
      color: #a8a8a8;
    }

    .ant-input-affix-wrapper {
      padding: 0 !important;
      background: transparent !important;
      border: none;
      box-shadow: none !important;
      border-radius: 4px;
      overflow: hidden;
    }

    .ant-input {
      caret-color: #a8a8a8 !important;
      -webkit-text-fill-color: #a8a8a8 !important;
      padding: 0 16px !important;
      height: 48px;
      font-size: 14px !important;
      color: #a8a8a8;
      background: #fff !important;
      border: 1px solid #dcdee1;
      border-radius: 4px;
    }

    .ant-input-password {
      .ant-input {
        border-radius: 4px 0 0 4px !important;
        padding-left: 16px;
        border: 1px solid #dcdee1;
        border-right: none;
      }
    }

    .ant-input-prefix {
      padding-left: 16px;
      margin-right: 0;
      background: rgb(0 0 0 / 20%) !important;
    }

    .ant-input-suffix {
      padding-right: 16px;
      margin-left: 0;
      background: #fff !important;
      border: 1px solid #dcdee1;
      border-left: none;
      border-radius: 0 4px 4px 0;
    }

    .ant-form-item-has-error .ant-input-suffix {
      border-color: #f15226;
    }

    .ant-checkbox-wrapper {
      .ant-checkbox {
        top: 3.3px;
      }
    }

    .ant-btn {
      font-size: 16px;
      color: #fff;
      border-radius: 4px;
      background: #4d55e7;
      height: 48px;
    }

    .cancel-btn {
      background: rgb(255 255 255 / 50%);
      color: #fff;
      border: none;
    }
  }
</style>
