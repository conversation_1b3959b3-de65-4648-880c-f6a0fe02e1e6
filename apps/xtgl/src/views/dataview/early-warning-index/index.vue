<script setup lang="ts">
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { useModal } from '@ft/internal/components/Modal';
  import { ref } from 'vue';
  import { Switch } from 'ant-design-vue';
  import { useRequest } from '@ft/request';
  import IndexEdit from './IndexEdit.vue';
  import type { IndexEditModalDataType } from './data';
  import { columns, searchSchemas } from './data';
  import { earlyWarningApi } from '/@/api/early-warning';
  const [register, { openModal }] = useModal();
  const { run: onSwitchChange } = useRequest(earlyWarningApi.editEnable, {
    manual: true,
    showSuccessMessage: true,
    onSuccess() {
      reload();
    },
  });
  const { run: onDefaultFlagSwitchChange } = useRequest(earlyWarningApi.editDefaultFlag, {
    manual: true,
    showSuccessMessage: true,
    onSuccess() {
      reload();
    },
  });
  const { run: onDelete } = useRequest(earlyWarningApi.delete, {
    manual: true,
    showSuccessMessage: true,
    onSuccess() {
      reload();
    },
  });
  function onAddIndex() {
    openModal<IndexEditModalDataType>(true, {
      mode: 'add',
    });
  }
  function onEditMenu(record) {
    openModal<IndexEditModalDataType>(true, {
      mode: 'edit',
      record,
    });
  }

  const [registerTable, { reload }] = useTable({
    api: earlyWarningApi.page,
    useSearchForm: true,
    formConfig: {
      labelWidth: 100,
      schemas: searchSchemas,
    },
    columns,
    bordered: false,
    size: 'small',
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
  });

  const expandedRowKeys = ref<string[]>([]);

  function createActions(record): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '编辑',
        type: 'link',
        size: 'small',
        onClick: onEditMenu.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: () => onDelete(record.id),
          okButtonProps: {
            danger: true,
          },
        },
      },
    ];
    return actions;
  }

  function handleSuccess() {
    reload();
  }
</script>

<template>
  <div class="w-full h-full pr-4">
    <BasicTable
      row-key="id"
      v-model:expandedRowKeys="expandedRowKeys"
      @register="registerTable"
      class="ft-main-table"
    >
      <template #tableTitle>
        <Button type="primary" @click="onAddIndex"> 新增指标 </Button>
      </template>

      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record)" />
        </template>
        <template v-if="column.dataIndex === 'warningLevel'">
          <div>
            {{ record[column.dataIndex] }}
          </div>
        </template>
        <template v-if="column.dataIndex === 'enableFlag'">
          <Switch
            :checked="record.enableFlag"
            @change="onSwitchChange(record.id)"
            :checked-value="1"
            :un-checked-value="0"
          />
        </template>
        <template v-if="column.dataIndex === 'defaultFlag'">
          <Switch
            :checked="record.defaultFlag"
            @change="onDefaultFlagSwitchChange(record.id)"
            :checked-value="1"
            :un-checked-value="0"
          />
        </template>
      </template>
    </BasicTable>
    <IndexEdit @register="register" @success="handleSuccess" />
  </div>
</template>
