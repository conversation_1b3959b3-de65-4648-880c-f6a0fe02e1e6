import { h } from 'vue';
import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { Input } from 'ant-design-vue';
import { infectiousDiseaseClassificationRelevanceQueryByCode } from '@ft/internal/api';

function getInfectiousDiseaseList() {
  return infectiousDiseaseClassificationRelevanceQueryByCode({ code: '002' });
}

const monitorDimensionOptions = [
  { label: '学校', value: 1 },
  { label: '学校班级', value: 2 },
  { label: '社区', value: 3 },
  { label: '单位', value: 4 },
  { label: '就诊机构', value: 5 },
];

// 红色 橙色 黄色 蓝色
const earlyWarnLevelOptions = [
  { label: '红色', value: '红色', color: 'red' },
  { label: '橙色', value: '橙色', color: 'orange' },
  { label: '黄色', value: '黄色', color: 'yellow' },
  { label: '蓝色', value: '蓝色', color: 'blue' },
];
export interface IndexEditModalDataType {
  mode: 'add' | 'edit';
  record?: Recordable;
}

/**
 * 监测维度 Select
 * 预警等级 Select
 * 监测状态 Select
 * 指标名称 Input
 * 监测疾病 ApiSelect
 */
export const searchSchemas: FormSchema[] = [
  // {
  //   field: 'monitorDimension',
  //   label: '监测维度',
  //   component: 'Select',
  //   colProps: { span: 6 },
  //   componentProps: {
  //     options: monitorDimensionOptions,
  //   },
  // },
  {
    field: 'earlyWarnLevel',
    label: '预警等级',
    component: 'Select',
    colProps: { span: 6 },
    componentProps: {
      options: earlyWarnLevelOptions,
    },
  },
  {
    field: 'enableFlag',
    label: '监测状态',
    component: 'Select',
    colProps: { span: 6 },
    componentProps: {
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 },
      ],
    },
  },
  {
    field: 'indexName',
    label: '指标名称',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    field: 'monitorDiseaseName',
    label: '监测疾病',
    component: 'ApiSelect',
    colProps: { span: 6 },
    componentProps: {
      api: getInfectiousDiseaseList,
      labelField: 'infectiousDiseaseName',
      valueField: 'infectiousDiseaseName',
      showSearch: true,
      optionFilterProp: 'label',
    },
  },
];

/**
 * 监测维度
 * 指标名称
 * 监测疾病
 * 监测时间周期
 * 判断条件
 * 绝对数阈值
 * 预警等级
 * 预警说明
 * 参考来源
 * 监测状态
 */
export const columns: BasicColumn[] = [
  {
    title: '监测维度',
    dataIndex: 'monitorDimension',
    width: 120,
    customRender: ({ record }) => {
      return monitorDimensionOptions.find((item) => item.value === record.monitorDimension)?.label;
    },
  },
  {
    title: '指标名称',
    dataIndex: 'indexName',
    width: 140,
  },
  {
    title: '监测疾病',
    dataIndex: 'monitorDiseaseName',
    width: 120,
  },
  {
    title: '监测时间周期',
    dataIndex: 'monitorCycle',
    width: 120,
  },
  {
    title: '判断条件',
    dataIndex: 'judgeCondition',
    width: 120,
  },
  {
    title: '绝对数阈值',
    dataIndex: 'threshold',
    width: 120,
  },
  {
    title: '预警等级',
    dataIndex: 'earlyWarnLevel',
    width: 120,
    customRender: ({ text }) => {
      return (
        <div
          style={{
            backgroundColor: earlyWarnLevelOptions.find((item) => item.value === text)?.color,
          }}
        >
          {text}
        </div>
      );
    },
  },
  {
    title: '是否向妇幼推送',
    dataIndex: 'pushFlag',
    width: 120,
    format: (pushFlag) => (pushFlag ? '是' : '否'),
  },
  {
    title: '提醒内容',
    dataIndex: 'reminderContent',
    width: 200,
  },
  {
    title: '预警说明',
    dataIndex: 'earlyWarnExplain',
    width: 200,
  },
  {
    title: '参考来源',
    dataIndex: 'referenceInstruction',
    width: 160,
  },
  {
    title: '默认展示状态',
    dataIndex: 'defaultFlag',
    width: 120,
  },
  {
    title: '监测状态',
    dataIndex: 'enableFlag',
    width: 120,
  },
];

/**
 * id
 * 监测维度 Select
 * 指标名称 Input
 * 监测疾病 ApiInput
 * 监测时间周期 InputNumber
 * 判断条件 Select
 * 绝对数阈值 InputNumber
 * 预警等级 ApiSelect
 * 是否启用 Select
 * 预警说明 InputTextArea
 * 参考来源 InputTextArea
 */
export const IndexEditSchemas: FormSchema[] = [
  {
    field: 'id',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'monitorDiseaseName',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'monitorDimension',
    label: '监测维度',
    component: 'Input',
    defaultValue: 5,
    colProps: { span: 12 },
    render() {
      return h(Input, { disabled: true, value: '就诊机构' });
    },
    required: true,
  },
  {
    field: 'indexName',
    label: '指标名称',
    component: 'Input',
    colProps: { span: 12 },
    required: true,
  },
  {
    field: 'monitorDiseaseCode',
    label: '监测疾病',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => ({
      api: getInfectiousDiseaseList,
      labelField: 'infectiousDiseaseName',
      valueField: 'infectiousDiseaseCode',
      showSearch: true,
      optionFilterProp: 'label',
      onChange: (_, opt) => {
        opt && (formModel.monitorDiseaseName = opt?.label);
      },
    }),
    colProps: { span: 12 },
    required: true,
  },
  {
    field: 'monitorCycle',
    label: '监测时间周期',
    component: 'InputNumber',
    colProps: { span: 12 },
    componentProps: {
      addonAfter: '天',
      min: 0,
    },
    required: true,
  },
  {
    field: 'judgeCondition',
    label: '判断条件',
    component: 'Select',
    componentProps: {
      options: [
        { label: '>', value: '>' },
        { label: '>=', value: '>=' },
        { label: '=', value: '=' },
      ],
    },
    colProps: { span: 12 },
    required: true,
  },
  {
    field: 'threshold',
    label: '绝对数阈值',
    component: 'InputNumber',
    colProps: { span: 12 },
    componentProps: {
      min: 0,
    },
    required: true,
  },
  {
    field: 'earlyWarnLevel',
    label: '预警等级',
    component: 'Select',
    componentProps: {
      options: earlyWarnLevelOptions,
    },
    colProps: { span: 12 },
    required: true,
  },
  {
    field: 'enableFlag',
    label: '是否启用',
    component: 'Select',
    componentProps: {
      allowClear: false,
      options: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 },
      ],
    },
    defaultValue: 1,
    colProps: { span: 12 },
  },
  {
    field: 'pushFlag',
    label: '是否向妇幼推送',
    component: 'Select',
    componentProps: {
      allowClear: false,
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 },
      ],
    },
    defaultValue: 0,
    colProps: { span: 12 },
  },
  {
    field: 'defaultFlag',
    label: '默认展示状态',
    component: 'Select',
    componentProps: {
      allowClear: false,
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 },
      ],
    },
    defaultValue: 0,
    colProps: { span: 12 },
  },
  {
    field: 'reminderContent',
    label: '提醒内容',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 6 },
    },
    colProps: { span: 24 },
  },
  {
    field: 'earlyWarnExplain',
    label: '预警说明',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 6 },
    },
    colProps: { span: 24 },
  },
  {
    field: 'referenceInstruction',
    label: '参考来源',
    component: 'InputTextArea',
    componentProps: {
      autoSize: { minRows: 4, maxRows: 6 },
    },
    colProps: { span: 24 },
  },
];
