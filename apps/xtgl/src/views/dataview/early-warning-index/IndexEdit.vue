<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import type { IndexEditModalDataType } from './data';
  import { IndexEditSchemas } from './data';
  import { earlyWarningApi } from '/@/api/early-warning';

  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm({
    schemas: IndexEditSchemas,
    labelWidth: 120,
    baseColProps: { span: 6 },
    actionColOptions: { span: 24 },
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });
  const mode = ref('');
  const getTitle = computed(() => {
    return mode.value === 'add' ? '新增指标' : '编辑指标';
  });

  const [register, { closeModal }] = useModalInner<IndexEditModalDataType>((data) => {
    mode.value = data?.mode;
    if (mode.value === 'edit' && data.record) {
      formAction.setFieldsValue(data.record);
    }
  });

  const { loading, runAsync } = useRequest(
    () => {
      // 处理表单值，将 undefined 转换为 null
      const formValues = formAction.getFieldsValue();
      const processedValues = Object.keys(formValues).reduce((acc, key) => {
        acc[key] = formValues[key] === undefined ? '' : formValues[key];
        return acc;
      }, {});

      return mode.value === 'edit'
        ? earlyWarningApi.edit(processedValues)
        : earlyWarningApi.add(processedValues);
    },
    {
      manual: true,
      showSuccessMessage: true,
      onBefore() {
        return formAction.validate();
      },
      onSuccess() {
        emit('success');
        closeModal();
      },
    },
  );
</script>

<template>
  <BasicModal
    :width="800"
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="getTitle"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    @register="register"
    @ok="runAsync()"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
