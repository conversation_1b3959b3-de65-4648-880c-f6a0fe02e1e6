<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { leftEditForm } from './data';
  import {
    addInfectiousDiseaseClassification,
    updateInfectiousDiseaseClassification,
  } from '/@/api/infectious-class';

  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm({
    schemas: leftEditForm,
    labelWidth: 120,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });

  const mode = ref('');
  const getTitle = computed(() => (mode.value === 'edit' ? '编辑传染病应用' : '新增传染病应用'));

  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data?.mode;
    formAction.setFieldsValue(data);
  });

  const { loading, runAsync } = useRequest(
    (params) =>
      mode.value === 'edit'
        ? updateInfectiousDiseaseClassification(params)
        : addInfectiousDiseaseClassification(params),
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess: () => {
        emit('success');
        closeModal();
      },
    },
  );

  async function handleOk() {
    formAction.validate().then((values) => {
      runAsync(values);
    });
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="getTitle"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    @register="register"
    @ok="handleOk"
    :min-height="120"
    centered
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
