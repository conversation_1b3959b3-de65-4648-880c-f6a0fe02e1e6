<script setup lang="ts">
  import { computed, ref, watch } from 'vue';
  import type { ActionItem, TableRowSelection } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { useModal } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import RightEdit from './RightEdit.vue';
  import LeftEdit from './LeftEdit.vue';
  import { leftColumns, leftFormSchema, rightColumns, rightFormSchema } from './data';
  import type {
    IInfectiousDiseaseClassificationPage,
    IInfectiousDiseaseClassificationRelationPage,
  } from '/@/api/infectious-class';
  import {
    delInfectiousDiseaseClassification,
    delInfectiousDiseaseClassificationRelation,
    getInfectiousDiseaseClassificationPage,
    getInfectiousDiseaseClassificationRelationPage,
  } from '/@/api/infectious-class';
  const selectedRowKeys = ref<TableRowSelection['selectedRowKeys']>([]);
  const classId = computed(() => (selectedRowKeys.value?.[0] || '') as string);
  const classificationName = computed(
    () => getDataSourceLeft().find((item) => item.id === classId.value)?.classificationName || '',
  );
  const [registerLeftTable, { reload, getDataSource: getDataSourceLeft }] = useTable({
    inset: true,
    columns: leftColumns,
    api: getInfectiousDiseaseClassificationPage,
    resizeHeightOffset: 16,
    formConfig: {
      labelAlign: 'right',
      colon: true,
      labelWidth: 80,
      showAdvancedButton: false,
      actionColOptions: {
        span: 12,
      },
      schemas: leftFormSchema,
    },
    useSearchForm: true,
    showIndexColumn: true,
    bordered: false,
    size: 'small',
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
    rowKey: 'id',
    afterFetch: (data) => {
      selectedRowKeys.value = [data[0]?.id || ''];
      return data;
    },
  });
  const [registerRightTable, { reload: reloadRightTable }] = useTable({
    api: getInfectiousDiseaseClassificationRelationPage,
    columns: rightColumns,
    inset: true,
    size: 'small',
    bordered: false,
    showIndexColumn: true,
    immediate: false,
    formConfig: {
      labelAlign: 'left',
      colon: true,
      labelWidth: 80,
      showAdvancedButton: false,
      actionColOptions: {
        span: 12,
      },
      schemas: rightFormSchema,
    },
    useSearchForm: true,
    beforeFetch: (params) => {
      params.classificationId = classId.value || '';
      return params;
    },
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
  });

  watch(classId, () => {
    reloadRightTable();
  });

  const rowSelection = computed<TableRowSelection>(() => {
    return {
      type: 'radio',
      selectedRowKeys: selectedRowKeys.value,
      onChange: (keys: any, _) => {
        if (keys.length === 0) {
          return;
        }
        selectedRowKeys.value = keys;
      },
    };
  });

  const [register, { openModal: openDiseasesModal }] = useModal();
  const [registerRightEdit, { openModal: openTargetModal }] = useModal();

  function addLeft() {
    openDiseasesModal(true, {
      mode: 'add',
    });
  }
  function addRight() {
    openTargetModal(true, {
      mode: 'add',
      classificationId: classId.value,
      classificationName: classificationName.value,
    });
  }
  function editLeft(record: IInfectiousDiseaseClassificationPage) {
    openDiseasesModal(true, {
      mode: 'edit',
      ...record,
    });
  }
  function editRight(record: IInfectiousDiseaseClassificationRelationPage) {
    openTargetModal(true, {
      ...record,
      classificationId: classId.value,
      classificationName: classificationName.value,
      mode: 'edit',
    });
  }

  function handleSuccessLeft() {
    reload();
  }

  function handleSuccessRight() {
    reloadRightTable();
  }
  function createActionsLeft(record: IInfectiousDiseaseClassificationPage): ActionItem[] {
    const actions: ActionItem[] = [];
    actions.push(
      {
        label: '编辑',
        type: 'link',
        size: 'small',
        onClick: editLeft.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: handleDelLeft.bind(null, record),
          okButtonProps: {
            danger: true,
          },
        },
      },
    );
    return actions;
  }
  function createActionsRight(record: IInfectiousDiseaseClassificationRelationPage): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '编辑',
        type: 'link',
        size: 'small',
        onClick: editRight.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: handleDelRight.bind(null, record),
          okButtonProps: {
            danger: true,
          },
        },
      },
    ];
    return actions;
  }

  const { runAsync: delLeftRunAsync } = useRequest(delInfectiousDiseaseClassification, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      reload();
    },
  });

  function handleDelLeft(record: IInfectiousDiseaseClassificationPage) {
    delLeftRunAsync(record.id);
  }

  const { runAsync: delRightRunAsync } = useRequest(delInfectiousDiseaseClassificationRelation, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      reloadRightTable();
    },
  });

  function handleDelRight(record: IInfectiousDiseaseClassificationRelationPage) {
    delRightRunAsync(record.id);
  }
</script>

<template>
  <div class="h-full w-full pr-2 pb-2 flex gap-2">
    <div class="h-full bg-white p-4 flex-1 of-hidden rounded">
      <div class="title-label text-16px font-500">传染病分类列表</div>
      <BasicTable :row-selection="rowSelection" @register="registerLeftTable">
        <template #tableTitle>
          <Button type="primary" @click="addLeft"> 新增传染病应用 </Button>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :divider="false" :actions="createActionsLeft(record)" />
          </template>
        </template>
      </BasicTable>
    </div>
    <div class="h-full bg-white p-4 flex-1 of-hidden rounded">
      <div class="title-label text-16px font-500 mb-5px">传染病列表</div>
      <BasicTable @register="registerRightTable">
        <template #tableTitle>
          <Button type="primary" @click="addRight" :disabled="!classId"> 新增传染病 </Button>
        </template>
        <template #toolbar> </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'action'">
            <TableAction :divider="false" :actions="createActionsRight(record)" />
          </template>
        </template>
      </BasicTable>
      <LeftEdit @register="register" @success="handleSuccessLeft" />
      <RightEdit @register="registerRightEdit" @success="handleSuccessRight" />
    </div>
  </div>
</template>

<style lang="less" scoped></style>
