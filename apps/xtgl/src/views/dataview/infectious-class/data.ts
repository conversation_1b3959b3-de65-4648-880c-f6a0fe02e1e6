import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { getInfectiousDiseaseList } from '/@/api/infectious-diagnostic-library';

/**
传染病分类编码
传染病分类
备注
 */
export const leftColumns: BasicColumn[] = [
  {
    title: '传染病分类编码',
    dataIndex: 'classificationCode',
    width: 150,
  },
  {
    title: '传染病分类',
    dataIndex: 'classificationName',
    width: 150,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 150,
  },
];
/**
传染病编码
传染病名称
排序值
 */
export const rightColumns: BasicColumn[] = [
  {
    title: '传染病编码',
    dataIndex: 'infectiousDiseaseCode',
    width: 150,
  },
  {
    title: '传染病名称',
    dataIndex: 'infectiousDiseaseName',
    width: 150,
  },
  {
    title: '排序值',
    dataIndex: 'sort',
    width: 150,
  },
];

export const leftFormSchema: FormSchema[] = [
  {
    field: 'classificationName',
    label: '传染病分类',
    component: 'Input',
    colProps: { span: 12 },
  },
];

export const rightFormSchema: FormSchema[] = [
  {
    field: 'infectiousDiseaseName',
    label: '传染病名称',
    component: 'Input',
    colProps: { span: 12 },
  },
];

export const leftEditForm: FormSchema[] = [
  {
    field: 'id',
    label: ' ',
    component: 'Input',
    show: false,
  },
  {
    field: 'classificationCode',
    label: '传染病应用编码',
    component: 'Input',
    required: true,
    componentProps: {},
    colProps: { span: 22 },
  },
  {
    field: 'classificationName',
    label: '传染病应用名称',
    component: 'Input',
    required: true,
    componentProps: {},
    colProps: { span: 22 },
  },
  {
    field: 'remark',
    label: '备注',
    component: 'InputTextArea',
    colProps: { span: 22 },
    componentProps: {
      autoSize: { minRows: 3, maxRows: 3 },
    },
  },
];
export const rightEditForm = (mode): FormSchema[] => {
  return [
    {
      field: 'id',
      label: '',
      component: 'Input',
      show: false,
    },
    {
      field: 'classificationId',
      label: '传染病应用id',
      component: 'Input',
      show: false,
    },
    {
      field: 'classificationName',
      label: '传染病应用名称',
      component: 'Input',
      componentProps: {
        disabled: true,
      },
      colProps: { span: 22 },
    },
    {
      field: 'infectiousDiseaseIdList',
      label: '传染病名称',
      component: 'ApiSelect',
      required: true,
      ifShow: mode === 'add',
      componentProps: () => {
        return {
          api: () => getInfectiousDiseaseList(),
          labelField: 'infectiousDiseaseName',
          valueField: 'id',
          mode: 'multiple',
          showSearch: true,
          optionFilterProp: 'label',
          getPopupContainer: () => document.body,
        };
      },
      colProps: { span: 22 },
    },
    {
      field: 'infectiousDiseaseId',
      label: '传染病名称',
      component: 'ApiSelect',
      required: true,
      ifShow: mode === 'edit',
      componentProps: () => {
        return {
          api: () => getInfectiousDiseaseList(),
          labelField: 'infectiousDiseaseName',
          valueField: 'id',
          disabled: true,
          showSearch: true,
          optionFilterProp: 'label',
          getPopupContainer: () => document.body,
        };
      },
      colProps: { span: 22 },
    },
    {
      field: 'sort',
      label: '排序值',
      component: 'InputNumber',
      required: true,
      colProps: { span: 22 },
      componentProps: {
        // min: 0,
      },
    },
  ];
};
