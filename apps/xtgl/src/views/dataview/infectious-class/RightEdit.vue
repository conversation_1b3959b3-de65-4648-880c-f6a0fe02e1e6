<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { rightEditForm } from './data';
  import {
    addInfectiousDiseaseClassificationRelation,
    updateInfectiousDiseaseClassificationRelation,
  } from '/@/api/infectious-class';

  const emit = defineEmits(['register', 'success']);
  const mode = ref('add');
  const [registerForm, formAction] = useForm({
    schemas: computed(() => rightEditForm(mode.value)),
    labelWidth: 120,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });

  const getTitle = computed(() => (mode.value === 'edit' ? '编辑传染病' : '新增传染病'));

  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data.mode;
    formAction.setFieldsValue(data);
    // if (mode.value === 'edit') {
    //   formAction.setFieldsValue({
    //     classificationId: data.infectiousDiseaseId,
    //   });
    // }
  });

  const { loading, runAsync } = useRequest(
    (params) =>
      mode.value === 'edit'
        ? updateInfectiousDiseaseClassificationRelation(params)
        : addInfectiousDiseaseClassificationRelation(params),
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess: () => {
        emit('success');
        closeModal();
      },
    },
  );

  async function handleOk() {
    formAction.validate().then((values) => {
      runAsync(values);
    });
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="getTitle"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    @register="register"
    @ok="handleOk"
    centered
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
