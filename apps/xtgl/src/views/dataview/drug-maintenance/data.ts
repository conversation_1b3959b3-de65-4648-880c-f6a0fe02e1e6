import { DictEnum, getDictItemList } from '@ft/internal/api';
import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';
import { getInfectionDrugList } from '/@/api/Infectious-drugs-mg';

/**
用药方案名称
用药方案缩写
创建人
创建时间
 */
export const columns: BasicColumn[] = [
  {
    title: '用药方案名称',
    dataIndex: 'planName',
    align: 'left',
  },

  {
    title: '用药方案缩写',
    dataIndex: 'planAbbreviation',
    align: 'left',
  },
  {
    title: '创建人',
    dataIndex: 'createUser',
    align: 'left',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    align: 'left',
  },
];

/**
 * 用药方案名称
 * 用药方案缩写
 */
export const formSchema: FormSchema[] = [
  {
    field: 'planName',
    component: 'Input',
    label: '用药方案名称',
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 20 } },
  },
  {
    field: 'planAbbreviation',
    component: 'Input',
    label: '用药方案缩写',
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 20 } },
  },
];

/**
 * 传染病名称
 * 组合药品 多选
 */
export const addForm: FormSchema[] = [
  {
    field: 'id',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'infectionTypeName',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'infectionTypeCode',
    label: '传染病类型',
    component: 'ApiSelect',
    componentProps: {
      disabled: true,
      api: () => getDictItemList(DictEnum.INFECTION_DRUG_TYPE),
      labelField: 'dictItemName',
      valueField: 'dictItemCode',
      getPopupContainer: () => document.body,
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: 'drugIds',
    label: '组合药品',
    component: 'ApiSelect',
    required: true,
    componentProps: ({ formModel }) => {
      return {
        mode: 'multiple',
        api: () => getInfectionDrugList({ infectionTypeCode: formModel.infectionTypeCode }),
        labelField: 'drugName',
        valueField: 'id',
        showSearch: true,
        optionFilterProp: 'label',
        getPopupContainer: () => document.body,
      };
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },
];
