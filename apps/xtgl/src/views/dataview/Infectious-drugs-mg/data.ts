import { DictEnum, getDictItemList } from '@ft/internal/api';
import type { BasicColumn, FormSchema } from '@ft/internal/components/Table';

/**
药品编码
药品名称
药品缩写
是否免费药
药品备注
创建人
创建时间
 */
export const columns: BasicColumn[] = [
  {
    title: '药品编码',
    dataIndex: 'drugCode',
    align: 'left',
  },
  {
    title: '药品名称',
    dataIndex: 'drugName',
    align: 'left',
  },
  {
    title: '药品缩写',
    dataIndex: 'abbreviation',
    align: 'left',
  },
  {
    title: '是否免费药',
    dataIndex: 'freeDrugFlag',
    align: 'left',
    customRender: ({ record }) => {
      return record.freeDrugFlag === 0
        ? '否'
        : record.freeDrugFlag === 1
        ? '是'
        : record.freeDrugFlag;
    },
  },
  {
    title: '药品备注',
    dataIndex: 'remark',
    align: 'left',
  },
  {
    title: '创建人',
    dataIndex: 'createUser',
    align: 'left',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    align: 'left',
  },
];

/**
 * 药品名称
 * 药品缩写
 */
export const formSchema: FormSchema[] = [
  {
    field: 'drugName',
    component: 'Input',
    label: '药品名称',
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 20 } },
  },
  {
    field: 'abbreviation',
    component: 'Input',
    label: '药品缩写',
    colProps: { span: 6 },
    itemProps: { wrapperCol: { span: 20 } },
  },
];

/**
 * 传染病名称
 * 药品编码
 * 药品名称
 * 药品缩写
 * 是否免费药
 * 药品备注
 */
export const addForm: FormSchema[] = [
  {
    field: 'id',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'infectionTypeName',
    label: '',
    component: 'Input',
    show: false,
  },
  {
    field: 'infectionTypeCode',
    label: '传染病类型',
    component: 'ApiSelect',
    componentProps: () => {
      return {
        disabled: true,
        api: () => getDictItemList(DictEnum.INFECTION_DRUG_TYPE),
        labelField: 'dictItemName',
        valueField: 'dictItemCode',
        getPopupContainer: () => document.body,
        // onchange: (_, opt) => {
        //   if (opt?.label) formModel.infectionTypeName = opt?.label;
        // },
      };
    },
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: 'drugCode',
    label: '药品编码',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: 'drugName',
    label: '药品名称',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: 'abbreviation',
    label: '药品缩写',
    component: 'Input',
    required: true,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: 'freeDrugFlag',
    label: '是否免费药',
    component: 'Select',
    componentProps: {
      allowClear: false,
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 },
      ],
    },
    defaultValue: 0,
    colProps: { span: 24 },
    itemProps: { wrapperCol: { span: 24 } },
  },
  {
    field: 'remark',
    label: '药品备注',
    component: 'InputTextArea',
    colProps: { span: 24 },
    componentProps: {
      autoSize: { minRows: 4, maxRows: 4 },
    },
    itemProps: { wrapperCol: { span: 24 } },
  },
];
