<script setup lang="ts">
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import type { ActionItem, BasicColumn } from '@ft/internal/components/Table';
  import { Button } from '@ft/internal/components/Button';
  import { useModal } from '@ft/internal/components/Modal';
  import { Tabs } from 'ant-design-vue';
  import { computed, ref } from 'vue';
  import { useRequest } from '@ft/request';
  import { DictEnum, getDictItemList } from '@ft/internal/api';
  import EditModel from './EditModel.vue';
  import { columns, formSchema } from './data';
  import { deleteInfectionDrug, getInfectionDrugPage } from '/@/api/Infectious-drugs-mg';

  /**
   * 传染病药品库
   */
  const activeKey = ref();
  const { data: tabsList } = useRequest(() => getDictItemList(DictEnum.INFECTION_DRUG_TYPE), {
    onSuccess: (data) => {
      activeKey.value = data?.length > 0 ? data[0].dictItemCode : undefined;
      tableIns.reload();
    },
  });

  const [registerTable, tableIns] = useTable({
    api: getInfectionDrugPage,
    formConfig: {
      colon: true,
      labelWidth: 100,
      showAdvancedButton: false,
      actionColOptions: {
        span: 12,
        style: {
          textAlign: 'right',
        },
      },
      schemas: formSchema,
    },
    useSearchForm: true,
    showIndexColumn: false,
    columns,
    beforeFetch: (params) => {
      params.infectionTypeCode = activeKey.value || '';
      return params;
    },
    bordered: false,
    size: 'small',
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
  });

  function createActions(record, column: BasicColumn): ActionItem[] {
    const actions: ActionItem[] = [
      {
        label: '编辑',
        type: 'link',
        size: 'small',
        onClick: handleEdit.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: handleDel.bind(null, record, column),
          okButtonProps: {
            danger: true,
          },
        },
      },
    ];
    return actions;
  }
  const [register, { openModal }] = useModal();
  const infectionTypeName = computed(
    () => tabsList.value?.find((item) => item.dictItemCode === activeKey.value)?.dictItemName,
  );
  function handleAdd() {
    openModal(true, {
      mode: 'add',
      infectionTypeCode: activeKey.value,
      infectionTypeName: infectionTypeName.value,
    });
  }
  function handleEdit(record) {
    openModal(true, {
      mode: 'edit',
      infectionTypeCode: activeKey.value,
      infectionTypeName: infectionTypeName.value,
      ...record,
    });
  }

  const { runAsync: delRunAsync } = useRequest(deleteInfectionDrug, {
    manual: true,
    showSuccessMessage: true,
    onSuccess: () => {
      tableIns.reload();
    },
  });
  function handleDel(record, _column) {
    delRunAsync(record.id);
  }
</script>

<template>
  <div class="w-full h-full pr-4">
    <BasicTable @register="registerTable" class="ft-main-table">
      <template #headerTop>
        <div class="text-16px font-500 title-label mb-4">传染病药品列表</div>
        <Tabs v-model:activeKey="activeKey" @change="tableIns.reload()">
          <Tabs.TabPane
            v-for="item in tabsList"
            :key="item.dictItemCode"
            :tab="item.dictItemName"
          />
        </Tabs>
      </template>
      <template #tableTitle>
        <Button type="primary" @click="handleAdd"> 新增 </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
    <EditModel @register="register" @success="tableIns.reload()" />
  </div>
</template>
