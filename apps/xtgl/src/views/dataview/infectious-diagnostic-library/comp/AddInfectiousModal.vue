<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { addInfectiousModal } from './data';
  import {
    addInfectiousDisease,
    updateInfectiousDisease,
  } from '/@/api/infectious-diagnostic-library';

  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm({
    schemas: addInfectiousModal,
    labelWidth: 100,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });
  const mode = ref<'add' | 'edit'>('add');
  const title = computed(() => {
    return mode.value === 'edit' ? '编辑传染病' : '新增传染病';
  });
  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data?.mode;
    formAction.setFieldsValue(data.data);
  });
  const { loading, runAsync: infectiousSaveRunAsync } = useRequest(
    (params) =>
      mode.value === 'add' ? addInfectiousDisease(params) : updateInfectiousDisease(params),
    {
      showSuccessMessage: true,
      manual: true,
      onSuccess: () => {
        emit('success');
        closeModal();
      },
    },
  );

  async function handleOk() {
    formAction.validate().then((values) => {
      infectiousSaveRunAsync({
        ...values,
      });
    });
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="title"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    @register="register"
    @ok="handleOk"
    centered
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
