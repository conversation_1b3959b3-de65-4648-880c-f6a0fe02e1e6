import type { BasicColumn } from '@ft/internal/components/Table';
import type { FormSchema } from '@ft/internal/components/Form';

/**
诊断编码
诊断名称
关联病种
诊断备注
诊断状态
创建人
创建时间
 */
export const columns: BasicColumn[] = [
  {
    title: '诊断编码',
    dataIndex: 'diagnosticCode',
    align: 'left',
    width: 100,
  },
  {
    title: '诊断名称',
    dataIndex: 'diagnosticName',
    align: 'left',
    width: 100,
  },
  {
    title: '关联病种',
    dataIndex: 'infectiousDiseaseName',
    align: 'left',
  },
  {
    title: '诊断备注',
    dataIndex: 'remarks',
    align: 'left',
    width: 200,
  },
  {
    title: '诊断状态',
    dataIndex: 'statusDesc',
    align: 'left',
    width: 100,
  },
  {
    title: '创建人',
    dataIndex: 'createUser',
    align: 'left',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    align: 'left',
    width: 180,
  },
];
/**
 * 计划名称：
 * 计划状态：
 */
export const searchSchema: FormSchema[] = [
  {
    field: 'diagnosticCode',
    label: '诊断编码',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    field: 'diagnosticName',
    label: '诊断名称',
    component: 'Input',
    colProps: { span: 8 },
  },
];
/**
 * 目录名称
 * 药品类型
 * 药品层级
 */
export const addInfectiousModal: FormSchema[] = [
  {
    field: 'id',
    component: 'Input',
    label: 'id',
    colProps: { span: 24 },
    componentProps: {},
    show: false,
  },
  {
    field: 'infectiousDiseaseCode',
    component: 'Input',
    label: '传染病编码',
    colProps: { span: 24 },
    required: true,
  },
  {
    field: 'infectiousDiseaseName',
    component: 'Input',
    label: '传染病名称',
    colProps: { span: 24 },
    required: true,
  },
];
export const addModal: FormSchema[] = [
  {
    field: 'id',
    component: 'Input',
    label: 'id',
    show: false,
  },
  {
    field: 'infectiousDiseaseId',
    component: 'Input',
    label: '传染病id',
    show: false,
  },
  {
    field: 'infectiousDiseaseName',
    component: 'Input',
    label: '传染病',
    componentProps: {
      disabled: true,
    },
    colProps: { span: 22 },
  },
  {
    field: 'diagnosticCode',
    component: 'Input',
    label: '诊断编码',
    required: true,
    colProps: { span: 22 },
  },
  {
    field: 'diagnosticName',
    component: 'Input',
    label: '诊断名称',
    required: true,
    colProps: { span: 22 },
  },
  {
    field: 'remarks',
    component: 'InputTextArea',
    label: '诊断备注',
    colProps: { span: 22 },
    componentProps: {
      autoSize: { minRows: 4, maxRows: 6 },
    },
  },
  {
    field: 'status',
    component: 'Select',
    componentProps: {
      options: [
        { label: '启用', value: 0 },
        { label: '禁用', value: 1 },
      ],
    },
    label: '诊断状态',
    colProps: { span: 22 },
    defaultValue: 0,
  },
];
