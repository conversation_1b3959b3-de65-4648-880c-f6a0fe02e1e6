<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { BasicForm, useForm } from '@ft/internal/components/Form';
  import { BasicModal, useModalInner } from '@ft/internal/components/Modal';
  import { useRequest } from '@ft/request';
  import { addModal } from './data';
  import {
    addInfectiousDiagnosis,
    updateInfectiousDiagnosis,
  } from '/@/api/infectious-diagnostic-library';
  // import { addDrugInfo, editDrugInfo } from '/@/api/drug-management/dict';

  const emit = defineEmits(['register', 'success']);

  const [registerForm, formAction] = useForm({
    schemas: addModal,
    labelWidth: 120,
    colon: true,
    autoSubmitOnEnter: false,
    showActionButtonGroup: false,
  });
  const mode = ref<'add' | 'edit'>('add');
  const title = computed(() => {
    return mode.value === 'edit' ? '编辑诊断' : '新增诊断';
  });
  const [register, { closeModal }] = useModalInner((data) => {
    mode.value = data?.mode;
    formAction.setFieldsValue({ ...data.data });
  });
  const { loading, runAsync: drugSaveRunAsync } = useRequest(
    (params) =>
      mode.value === 'add' ? addInfectiousDiagnosis(params) : updateInfectiousDiagnosis(params),
    {
      showSuccessMessage: true,
      manual: true,
      onSuccess: () => {
        emit('success');
        closeModal();
      },
    },
  );
  async function handleOk() {
    formAction.validate().then((values) => {
      drugSaveRunAsync({
        ...values,
      });
    });
  }
</script>

<template>
  <BasicModal
    :destroy-on-close="true"
    v-bind="$attrs"
    :title="title"
    :can-fullscreen="false"
    :ok-button-props="{
      loading,
    }"
    @register="register"
    @ok="handleOk"
    centered
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
