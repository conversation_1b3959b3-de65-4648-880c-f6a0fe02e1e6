<script setup lang="ts">
  // import DataSetModal from '../../StandardDataset/comp/dateModal.vue';
  import type { ActionItem } from '@ft/internal/components/Table';
  import { BasicTable, TableAction, useTable } from '@ft/internal/components/Table';
  import { useModal } from '@ft/internal/components/Modal';
  import { Button, Upload } from 'ant-design-vue';
  import { watch } from 'vue';
  import { BasicTitle } from '@ft/internal/components/Basic';
  import type { UploadRequestOption } from 'ant-design-vue/lib/vc-upload/interface';
  import { useRequest } from '@ft/request';
  import { exportUtil } from '@ft/internal/utils';
  import { columns, searchSchema } from './data';
  import AddModal from './AddModal.vue';
  import {
    delInfectiousDiagnosis,
    getInfectiousDiagnosisPage,
    importInfectiousDiagnosis,
    infectiousDiagnosisExport,
    infectiousDiagnosisTemplateDownload,
  } from '/@/api/infectious-diagnostic-library';

  const props = defineProps<{
    activeItem: any;
  }>();

  const [register, ActionTable] = useTable({
    api: getInfectiousDiagnosisPage,
    showIndexColumn: true,
    useSearchForm: true,
    formConfig: {
      labelWidth: 80,
      schemas: searchSchema,
    },
    inset: true,
    immediate: false,
    beforeFetch(params) {
      params.infectiousDiseaseId = props.activeItem.id || '';
      return params;
    },
    fetchSetting: {
      sizeField: 'pageSize',
      pageField: 'pageNum',
      listField: 'list',
    },
    actionColumn: {
      width: 100,
      title: '操作',
      align: 'left',
      dataIndex: 'action',
    },
    columns,
  });

  watch(
    () => props.activeItem,
    () => {
      ActionTable.getForm().resetFields();
    },
    {
      deep: true,
    },
  );

  function createActions(record, column) {
    const actions: ActionItem[] = [
      {
        label: '编辑',
        onClick: handleRange.bind(null, record, column),
      },
      {
        label: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除该项吗？',
          placement: 'topRight',
          confirm: handleDel.bind(null, record),
          okButtonProps: {
            danger: true,
          },
        },
      },
    ];

    return actions;
  }
  const [registerModal, { openModal: openModal }] = useModal();

  function handleSuccess() {
    ActionTable.reload();
  }
  function handleRange(record, _) {
    openModal(true, {
      data: {
        ...record,
        pkCatalogue: props.activeItem.id,
      },
      mode: 'edit',
    });
  }

  function handleAdd() {
    openModal(true, {
      data: {
        infectiousDiseaseId: props.activeItem.id,
        infectiousDiseaseName: props.activeItem.infectiousDiseaseName,
      },
      mode: 'add',
    });
  }

  const { runAsync: runAsyncDelInfectiousDiagnosis } = useRequest(delInfectiousDiagnosis, {
    manual: true,
    showSuccessMessage: true,
  });
  function handleDel(record) {
    runAsyncDelInfectiousDiagnosis(record.id).then(() => {
      ActionTable.reload();
    });
  }
  const { loading: importLoading, runAsync: importRunAsync } = useRequest(
    importInfectiousDiagnosis,
    {
      manual: true,
      showSuccessMessage: true,
      onSuccess() {
        ActionTable.reload();
      },
    },
  );

  function onImportExpert(e: UploadRequestOption<any>) {
    const { file } = e;
    importRunAsync({
      // @ts-ignore
      file,
      infectiousDiseaseId: props.activeItem.id,
    });
  }
  const { loading: templateLoading, runAsync: runAsyncDownloadTemplate } = useRequest(
    infectiousDiagnosisTemplateDownload,
    {
      manual: true,
    },
  );
  async function onTemplateDown() {
    try {
      await exportUtil(runAsyncDownloadTemplate());
    } catch (error) {
      console.error(error);
    } finally {
    }
  }
  const { loading: exportLoading, runAsync: exportExpertRunAsync } = useRequest(
    infectiousDiagnosisExport,
    {
      manual: true,
      showSuccessMessage: true,
    },
  );
  function onExportExpert() {
    exportUtil(
      exportExpertRunAsync({
        ...ActionTable.getForm().getFieldsValue(),
        infectiousDiseaseId: props.activeItem.id,
      }),
    );
  }
</script>

<template>
  <div class="h-full">
    <BasicTable class="role-list" @register="register">
      <template #headerTop>
        <BasicTitle class="mb-3" span normal
          >{{ activeItem?.infectiousDiseaseName }}-诊断库列表</BasicTitle
        >
      </template>
      <template #tableTitle>
        <Button type="primary" @click="handleAdd"> 新增诊断 </Button>
      </template>
      <template #toolbar>
        <Button :loading="templateLoading" @click="onTemplateDown"> 模板下载 </Button>
        <Upload
          accept=".xlsx,.xls"
          :max-count="1"
          :show-upload-list="false"
          :custom-request="onImportExpert"
        >
          <Button :loading="importLoading"> 批量导入 </Button>
        </Upload>
        <Button :loading="exportLoading" @click="onExportExpert"> 批量导出 </Button>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'action'">
          <TableAction :divider="false" :actions="createActions(record, column)" />
        </template>
      </template>
    </BasicTable>
    <AddModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<style scoped lang="less">
  :deep {
    .vben-basic-table-form-container {
      padding: 0;
    }

    .vben-basic-table-form-container .ant-form {
      padding: 0;
      margin-bottom: 10px;
    }
  }
</style>
