<script setup lang="ts">
  import { computed, ref } from 'vue';
  import { Dropdown, Input } from 'ant-design-vue';
  import { Icon } from '@ft/internal/components/Icon';
  import { useModal } from '@ft/internal/components/Modal';
  import { useMessage } from '@ft/internal/hooks/web/useMessage';
  import { StyledList } from '@ft/components';
  import { useRequest } from '@ft/request';
  import type { TreeDropMenuItem } from './comp/DropMenu.vue';
  import TreeDropMenu from './comp/DropMenu.vue';
  import DateList from './comp/DateList.vue';
  import AddInfectiousModal from './comp/AddInfectiousModal.vue';
  import type { IInfectiousDiseasePage } from '/@/api/infectious-diagnostic-library';
  import {
    delInfectiousDisease,
    getInfectiousDiseaseList,
  } from '/@/api/infectious-diagnostic-library';

  const searchValue = ref('');

  const activeId = ref<string>('1');
  const activeItem = ref<IInfectiousDiseasePage>();
  const {
    loading,
    data: infectiousDiseaseList,
    runAsync,
  } = useRequest(getInfectiousDiseaseList, {
    onSuccess: (data) => {
      activeId.value = data.length > 0 ? data[0].id : '';
      activeItem.value = data?.[0] || {};
    },
  });
  const items = computed(() => {
    return infectiousDiseaseList.value?.filter((item) => {
      return item?.infectiousDiseaseName?.includes(searchValue.value);
    });
  });

  function createMenu(item): TreeDropMenuItem[] {
    return [
      {
        label: '编辑',
        icon: 'ant-design:edit-outlined',
        onClick: handleEdit.bind(null, item),
      },
      {
        label: '删除',
        icon: 'ant-design:delete-outlined',
        onClick: handleDel.bind(null, item),
      },
    ];
  }

  const [registerAddInfectiousModal, { openModal: openAddInfectiousModal }] = useModal();

  function handleAddInfectious() {
    openAddInfectiousModal(true, {
      mode: 'add',
    });
  }
  function handleEdit(item) {
    openAddInfectiousModal(true, {
      mode: 'edit',
      data: {
        ...item,
      },
    });
  }
  const { createConfirm } = useMessage();
  const { runAsync: delInfectiousDiseaseRunAsync } = useRequest(delInfectiousDisease, {
    showSuccessMessage: true,
    manual: true,
    onSuccess: () => {
      runAsync();
    },
  });

  function handleDel(item) {
    createConfirm({
      iconType: 'warning',
      title: '删除目录',
      content: `确定删除该条记录吗？`,
      onOk: () => {
        delInfectiousDiseaseRunAsync(item.id);
      },
    });
  }
</script>

<template>
  <div class="flex gap-2 w-[calc(100%-10px)] rounded rounded-lt-none">
    <div class="flex flex-col gap-3 w-228px bg-#fff rounded-[0px_8px_8px_8px] p-3">
      <span class="text-#333333 fw-bold">传染病列表</span>
      <div class="search flex items-center text-center gap-9px">
        <Input allow-clear placeholder="输入关键字" v-model:value="searchValue">
          <template #suffix>
            <Icon icon="ant-design:search-outlined" />
          </template>
        </Input>
        <div
          @click="handleAddInfectious"
          class="w-20px h-20px rounded-50% bg-primary-color cursor-pointer p-1 flex items-center"
        >
          <Icon color="#fff" icon="ant-design:plus-outlined" size="12" />
        </div>
      </div>
      <div class="pr-4 of-y-auto of-x-hidden min-w-0 h-[calc(100vh-13.3rem)]">
        <StyledList
          :items="items || []"
          v-model="activeId"
          v-model:value="activeItem"
          valueField="id"
          label-field="infectiousDiseaseName"
          class="flex-1"
          :width="216"
          :loading="loading"
        >
          <template #default="item">
            <div class="flex justify-between items-center w-full relative">
              <div class="w-[calc(100%-40px)] overflow-hidden truncate"
                >{{ item?.infectiousDiseaseName }}
              </div>
              <Dropdown placement="bottom" :class="[activeId === item?.id ? '!block' : '!hidden']">
                <div
                  class="hover:bg-#f5f5f5 absolute right-16px rounded-2px flex items-center px-4px py-2px pt-5px"
                >
                  <Icon @click.prevent color="#303133" :size="18" icon="ant-design:more-outlined" />
                </div>
                <template #overlay>
                  <div
                    class="bg-#fff color-#333 w-fit min-w-80px rounded-3px text-left cursor-pointer shadow-md"
                  >
                    <TreeDropMenu :createMenu="createMenu(item)" />
                  </div>
                </template>
              </Dropdown>
            </div>
          </template>
        </StyledList>
      </div>
    </div>
    <div class="flex flex-col gap-2 flex-1 min-w-757px rounded-2 bg-white p-4">
      <DateList ref="dateListRef" :activeItem="activeItem" />
      <AddInfectiousModal @register="registerAddInfectiousModal" @success="runAsync()" />
    </div>
  </div>
</template>

<style lang="less" scoped></style>
